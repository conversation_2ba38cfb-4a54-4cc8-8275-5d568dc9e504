﻿@page "/listersample"

@using CCTC_Lib.Contracts.Data
@using CCTC_Components_UI.Helpers.Models
@using CCTC_Components_UI.Helpers.Services
@using CCTC_Lib.Contracts.Interaction
@using Common.Helpers
@using Constants = CCTC_Components_UI.Helpers.Constants
@using System.ComponentModel
@using CCTC_Lib.Enums.Data
@inject IDelayService DelayService
@inject NavigationManager NavManager

@{
    var description = new List<string>
    {
        "The Lister provides a general purpose container for listing items. It supports selection and filtering and can be populated with data from a preloaded source, " +
        "can load its own data via a loading function, or can use an IItemsService implementation to load on demand. The Lister supports virtualization whichever method " +
        "is chosen",
        "Consider your data before choosing a loading method; " +
        "<ul>" +
        "<li><span class='emphasis'>IItemsService</span> - this is better suited to larger data sets as data can be paged. " +
        "Be mindful that a total count is required for paging</li>" +
        "<li><span class='emphasis'>Loading function</span> - this offloads the loading to component and prevents the ui freezing if the collection is large</li>" +
        "<li><span class='emphasis'>Preloaded</span> - data is preloaded in the lister container and passed as a collection to the component</li>" +
        "</ul>",
    };

    var features = new List<(string, string)>
    {
        ("Virtualization", "By default, the Lister uses a <code>Virtualize</code> component to render the items. Virtualization can be turned off"),
        ("Cancellation", "Data loading can be cancelled when using IItemsService or a loading function"),
        ("Filtering", "Items can be filtered. The filter throttle can be modified (defaults to 1000ms). Pressing Enter applies the filter immediately. " +
                      "An external filter can be applied. Note that the removal of a filter will automatically sort the data by " +
                      "the default as determined in <code>ISortable</code>. This will undo any reordering."),
        ("Deletion", "Items can be deleted from the list"),
        ("Reordering", "Items can be reordered as required. Only currently supports moving an item up or down by one place"),
        ("Selection", "Items can be selected"),
        ("Counts", "The current counts for the Lister are configurable, and can show totals, totals and filtered and totals, filtered and selected"),
        ("Sorting", "The collection is sorted by default based on the implementation of <code>ISortable</code> (see below). Note that the SortBy references a " +
                    "<em>property name</em> or <em>field</em> to sort on"),
    };

    var gotchas = new List<(string, string)>
    {
        ("Required implementations", "The model object should implement <code>IUniqueIdentity&lt;string></code> for the modal key, and <code>ISortable</code> " +
                                     "to provide static properties for SortDir and SortBy"),
        ("Optional implementations", "The model can optionally implement <code>ISearchable</code> to provide searchable text for the filter"),
        ("Placeholder", "Using a Placeholder element within the Virtualize component breaks the formatting"),
        ("Keyboard navigation", "This is not perfect. There are issues with focusing on the appropriate item."),
    };

    var usageCode = new List<(string title, string description, string code, RenderFragment? fragment)>
    {
        ("IItemsService", "using the IItemsService to load on demand",
            @"<Lister
    TData=""NamedNodeIdentifier""
    Id=""lister-with-items-service""
    ItemsService=""@_itemsService""
    CanCancelThenRefresh=""true""
    ItemSizePixels=""50""
    ItemsContainerHeightPixels=""300""
    OverscanCount=""50""
    CountDisplay=""ListerCountDisplay.FilteredAndTotalAndSelected""
    ShowLoadingDisplay=""true""
    CanFilter=""true""
    FilterThrottleMs=""1500""
    CountDisplayClass=""text-info""
    ReadOnly=""false""
    ReadOnlyIconPlacement=""ListerReadOnlyIconPlacement.Start"">
    <Template Context=""listerModel"">
        <div style=""height:50px"">
            @listerModel.AsName()
            @listerModel.AsShortId()
        </div>
    </Template>
</Lister>

@code {
    IItemsService<NamedNodeIdentifier>? _itemsService;

    protected override void OnInitialized()
    {
        _itemsService = new NamedNodeItemsService(DelayService);
    }
}", @<Lister
         TData="NamedNodeIdentifier"
         Id="lister-with-items-service"
         ItemsService="@_itemsService"
         CanCancelThenRefresh="true"
         ItemSizePixels="50"
         ItemsContainerHeightPixels="300"
         OverscanCount="50"
         CountDisplay="ListerCountDisplay.FilteredAndTotalAndSelected"
         ShowLoadingDisplay="true"
         CanFilter="true"
         FilterThrottleMs="1500"
         ReadOnly="false"
         ReadOnlyIconPlacement="ListerReadOnlyIconPlacement.Start">
        <Template Context="listerModel">
            <div>
                @listerModel.AsName()
                @listerModel.AsShortId()
            </div>
        </Template>
    </Lister>),
        
        ("Loading function", "using a loading function to load from the component",
            @"<Lister
    TData=""NamedNodeIdentifier""
    Id=""lister-with-loading-function""
    ListerLoadingFunc=""@LoadingFunc""
    CanCancelThenRefresh=""true""
    ItemSizePixels=""50""
    ItemsContainerHeightPixels=""300""
    OverscanCount=""50""
    CountDisplay=""ListerCountDisplay.FilteredAndTotalAndSelected""
    ShowLoadingDisplay=""true""
    CanFilter=""true""
    ReadOnly=""false""
    ReadOnlyIconPlacement=""ListerReadOnlyIconPlacement.Start""
    CanReOrder=""true"">
    <Template Context=""listerModel"">
        <div>
            @listerModel.AsName()
            @listerModel.AsShortId()
        </div>
    </Template>
</Lister>

@code {
    async Task<List<NamedNodeIdentifier>> LoadingFunc(CancellationToken token)
    {
        //this implementation is better and non-blocking as it uses DeserializeAsyncEnumerable which allows the
        //task delays to be be run which releases the process to the ui and hence unblocks
        var client = new HttpClient();
        client.BaseAddress = new Uri(Constants.ApiBase);
        await using var data = await client.GetStreamAsync(""./data/circa_15k_crf_namedNodes.json"", token)
            .ConfigureAwait(false);

        var items = Json.deserializeAsyncEnumerable<NamedNodeIdentifier>(data, token);

        var ret = new List<NamedNodeIdentifier>();
        var counter = 0;
        await foreach (var item in items)
        {
            counter++;
            ret.Add(item!);

            //required to keep the ui responsive
            //see: https://stackoverflow.com/questions/65131456/blazor-ui-locking
            if (counter % 100 == 0)
            {
                await DelayService.Delay(TimeSpan.FromMilliseconds(1), token);
            }

        }
        return ret;
    }
}", @<Lister
         TData="NamedNodeIdentifier"
         Id="lister-with-loading-function"
         ListerLoadingFunc="@(token => LoadingFunc(token, 500))"
         CanCancelThenRefresh="true"
         ItemSizePixels="50"
         ItemsContainerHeightPixels="300"
         OverscanCount="50"
         CountDisplay="ListerCountDisplay.FilteredAndTotalAndSelected"
         ShowLoadingDisplay="true"
         CanFilter="true"
         ReadOnly="false"
         ReadOnlyIconPlacement="ListerReadOnlyIconPlacement.Start"
         CanReOrder="true">
        <Template Context="listerModel">
            <div>
                @listerModel.AsName()
                @listerModel.AsShortId()
            </div>
        </Template>
    </Lister>),
        ("Preloaded data", "using a preloaded dataset that is passed to the component. The UI may be unresponsive whilst the data is loaded from its source.",
            @"<Lister
    TData=""NamedNodeIdentifier""
    Id=""lister-with-preloaded-data""
    Data=""@_givenData""
    SelectionType=""ListerSelectionType.Single""
    CanSelect=""@(_ => true)""
    CanClick=""@(_ => true)""
    ItemSizePixels=""50""
    ItemsContainerHeightPixels=""300""
    OverscanCount=""50""
    CountDisplay=""ListerCountDisplay.FilteredAndTotalAndSelected""
    ShowLoadingDisplay=""true""
    CanFilter=""true""
    ReadOnly=""false""
    ReadOnlyIconPlacement=""ListerReadOnlyIconPlacement.Start""
    CanReOrder=""true"">
    <Template Context=""listerModel"">
        <div>
            @listerModel.AsName()
            @listerModel.AsShortId()
        </div>
    </Template>
</Lister>

@code {
    List<NamedNodeIdentifier>? _givenData;

    protected override Task OnInitializedAsync()
    {
        _givenData = new List<NamedNodeIdentifier>(); // replace with some service providing the data
    }
}", @<Lister
         TData="NamedNodeIdentifier"
         Id="lister-with-preloaded-data"
         Data="@_givenData"
         SelectionType="ListerSelectionType.Single"
         CanSelect="@(_ => true)"
         CanClick="@(_ => true)"
         ItemSizePixels="50"
         ItemsContainerHeightPixels="300"
         OverscanCount="50"
         CountDisplay="ListerCountDisplay.FilteredAndTotalAndSelected"
         ShowLoadingDisplay="true"
         CanFilter="true"
         ReadOnly="false"
         ReadOnlyIconPlacement="ListerReadOnlyIconPlacement.Start"
         CanReOrder="true">
        <Template Context="listerModel">
            <div>
                @listerModel.AsName()
                @listerModel.AsShortId()
            </div>
        </Template>
    </Lister>),
    ("Preloaded data with deletion", "using a preloaded dataset that is passed to the component, supporting deletion of items.",
            @"<Lister
    TData=""NamedNodeIdentifier""
    Id=""lister-preloaded-with-deletion""
    Data=""@_givenData""
    SelectionType=""ListerSelectionType.Single""
    CanSelect=""@(_ => true)""
    CanClick=""@(_ => true)""
    ItemSizePixels=""50""
    ItemsContainerHeightPixels=""300""
    OverscanCount=""50""
    CountDisplay=""ListerCountDisplay.FilteredAndTotalAndSelected""
    ShowLoadingDisplay=""true""
    CanFilter=""true""
    ReadOnly=""false""
    ReadOnlyIconPlacement=""ListerReadOnlyIconPlacement.Start""
    CanReOrder=""true""
    CanDeleteItems=""true""
    <Template Context=""listerModel"">
        <div>
            @listerModel.AsName()
            @listerModel.AsShortId()
        </div>
    </Template>
</Lister>

@code {
    List<NamedNodeIdentifier>? _givenData;

    protected override async Task OnInitializedAsync()
    {
        _givenData = await LoadingFunc(CancellationToken.None, 100);
    }
}",
        (@<Lister
            TData="NamedNodeIdentifier"
            Id="lister-preloaded-with-deletion"
            Data="@_givenData"
            SelectionType="ListerSelectionType.Single"
            CanSelect="@(_ => true)"
            CanClick="@(_ => true)"
            ItemSizePixels="50"
            ItemsContainerHeightPixels="300"
            OverscanCount="50"
            CountDisplay="ListerCountDisplay.FilteredAndTotalAndSelected"
            ShowLoadingDisplay="true"
            CanFilter="true"
            ReadOnly="false"
            ReadOnlyIconPlacement="ListerReadOnlyIconPlacement.Start"
            CanReOrder="true"
            CanDeleteItems="true">
            <Template Context="listerModel">
                <div>
                    @listerModel.AsName()
                    @listerModel.AsShortId()
                </div>
            </Template>
            </Lister>)),
    };

    var subParts = new List<string>()
    {
        "-select-all-icon",
        "-filter-input-container",
        "-cancel-button",
        "-refresh-button",
        "-cancel-refresh-placeholder",
        "-working-template",
        "-display-counts",
        "-select-@i",
        "-moveup-@i",
        "-movedown-@i",
        "-delete-@i",
        "-virtualize",
        "-no-virtualize",
        "-read-only-icon"
    };
}

<div required-so-deep-works>
    @if (_givenData is not null)
    {
        <Sampler
            ComponentName="Lister"
            ComponentCssName="lister"
            Description="@description"
            UsageText="Typical usages of the <code>Lister</code> component are shown below"
            UsageCodeList="@usageCode"
            SubParts="subParts"
            ContentHeightPixels="600"
            Features="@features"
            Gotchas="@gotchas">
            <ExampleTemplate>
                <div class="tab-item-content">
                    <Lister
                        TData="NamedNodeIdentifier"
                        Id="lister-example"
                        @ref="@_listerRef"
                        Virtualize="true"
                        ListerLoadingFunc="@(_ => LoadingFunc(CancellationToken.None, 500))"
                        KeyDownOnItem="@(args => SetMessage($"key interaction on item: {args.item.AsName()}"))"
                        OnClickedItem="@(args => SetMessage($"click interaction on item: {args.item.AsName()}"))"
                        CanCancelThenRefresh="false"
                        ItemSizePixels="50"
                        OverscanCount="10"
                        ItemsContainerHeightPixels="400"
                        CountDisplay="ListerCountDisplay.FilteredAndTotalAndSelected"
                        ShowLoadingDisplay="true"
                        CanFilter="true"
                        SelectionType="ListerSelectionType.None"
                        CanSelectAll="false"
                        CountDisplayPostText="named node items"
                        ReadOnly="false"
                        ReadOnlyIconPlacement="ListerReadOnlyIconPlacement.End"
                        CanReOrder="true"
                        FocusItemOption="FocusItemOption.Allow">
                        <Template Context="listerModel">
                            <div class="truncate">
                                @listerModel.AsName()
                                @listerModel.AsShortId()
                            </div>
@*                             <div>
                                Shows flex is not applied to children
                            </div>
                            <Button
                                Id="usage1"
                                ButtonIcon="circle"
                                ButtonText="Click"
                                IconPosition="PositionX.Left"
                                StyledWithBorder="true"
                                OnClick="@(() => Console.WriteLine("click"))">
                            </Button> *@
                        </Template>

                    </Lister>
                </div>
                <hr/>
                <div class="d-flex gap-2">
                    <div>example of sorting - sort by:</div>
                    <div>
                        <DropDown
                            TData="string"
                            Id="lister-sampler-sortby"
                            Style="width: 8rem;"
                            Data="@(new List<string>{ "NameAsString", "Id" })"
                            Value="@_sortBy"
                            ValueChanged="@(newVal => ChangeSortBy(newVal))">
                        </DropDown>
                    </div>
                    <div> - sort direction:</div>
                    <div>
                        <DropDown
                            TData="SortDir"
                            Id="lister-sampler-sortby"
                            Style="width: 4rem;"
                            Data="@(new List<SortDir>{ SortDir.Asc, SortDir.Desc })"
                            Value="@_sortDir"
                            ValueChanged="@(newVal => ChangeSortDir(newVal))">
                        </DropDown>
                    </div>
                </div>
                <hr/>
                <div>interaction: @_message</div>
                <hr/>
                <div>
                    For content overflow use css similar to the below to truncate and prevent the overflow from affecting the rendering
                </div>

                <div class="mb-1">e.g. css</div>
                <code>
                    <pre>
.truncate {
    line-height: 1rem;
    max-height: 50px;
    margin-right: 5px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
}
</pre>
                </code>
                <div class="mb-1">
                    apply to template e.g.
                </div>
                <code>
                    <pre style="tab-size: 1rem">
                @("<Template Context=\"listerModel\">\n\t<div class=\"truncate\">\n\t\t content here\n\t<div/>\n</Template>")
            </pre>
                </code>
            </ExampleTemplate>
        </Sampler>
    }
</div>


@code {

    IItemsService<NamedNodeIdentifier>? _itemsService;
    string _sortBy = "NameAsString";
    SortDir _sortDir = SortDir.Asc;

    Lister<NamedNodeIdentifier>? _listerRef;

    string? _message = "none yet";
    void SetMessage(string message)
    {
        _message = message;
        StateHasChanged();
    }

    async Task RunSort()
    {
        await _listerRef!.SortData();
        //StateHasChanged();
    }

    async Task ChangeSortBy(string newValue)
    {
        _sortBy = newValue;
        NamedNodeIdentifier.SortBy = newValue;
        await RunSort();
    }

    async Task ChangeSortDir(SortDir newValue)
    {
        _sortDir = newValue;
        NamedNodeIdentifier.SortDir = newValue;
        await RunSort();
    }


    async Task<List<NamedNodeIdentifier>> LoadingFunc(CancellationToken token, int? limitTo = null, int? startAt = null)
    {
        //this implementation is better and non-blocking as it uses DeserializeAsyncEnumerable which allows the
        //task delays to be be run which releases the process to the ui and hence unblocks
        if (token.IsCancellationRequested)
        {
            return new List<NamedNodeIdentifier>();
        }

        try
        {
            var client = new HttpClient();
            client.BaseAddress = new Uri(NavManager.BaseUri);
            await using var data = await client.GetStreamAsync("./data/circa_15k_crf_namedNodes.json", token)
                .ConfigureAwait(false);

            var items = Json.deserializeAsyncEnumerable<NamedNodeIdentifier>(data, token);
            var ret = new List<NamedNodeIdentifier>();
            var counter = 0;
            var i = 0;

            await foreach (var item in items)
            {
                if (limitTo.HasValue)
                {
                    if (counter >= limitTo.Value)
                    {
                        break;
                    }
                }

                if (startAt.HasValue && i >= startAt)
                {
                    counter++;
                    ret.Add(item!);
                }

                if(startAt is null)
                {
                    counter++;
                    ret.Add(item!);
                }

                //required to keep the ui responsive
                //see: https://stackoverflow.com/questions/65131456/blazor-ui-locking
                //TODO: not implemented in .net 8 - hopefully coming at some point
                if (counter % 100 == 0)
                {
                    await DelayService.Delay(TimeSpan.FromMilliseconds(1), token);
                }

                i++;
            }

            return ret;
        }
        catch (OperationCanceledException)
        {
            return new List<NamedNodeIdentifier>();
        }
        catch (Exception)
        {
            throw;
        }
    }

    List<NamedNodeIdentifier>? _givenData;

    protected override void OnInitialized()
    {
        _itemsService = new NamedNodeItemsService(DelayService, NavManager);
    }

    protected override async Task OnInitializedAsync()
    {
        _givenData = await LoadingFunc(CancellationToken.None, 100);
    }
}
