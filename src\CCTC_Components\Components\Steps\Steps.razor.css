cctc-steps {
    color: var(--cctc-steps-color);
    background-color: var(--cctc-steps-background-color);
}

cctc-steps-headers {
    display: flex;
    white-space: nowrap;
    overflow: hidden;
    border-bottom: 1px solid var(--cctc-steps-icon-disabled-color);
    /*margin-top: 0.5rem;*/
    padding-bottom: 0.5rem;
    background-color: var(--cctc-steps-header-background-color);
}

cctc-steps-current-content {
    overflow-y: auto;
}

.controls {
    margin-left: 1rem;
    margin-right: 1rem;
    display: flex;
    justify-content: space-between;
}

.button {
    display: flex;
    transform: scale(1.8);
    align-items: center;
}

.prev-next-control {
    display: flex;
    align-items: center;
    user-select: none;
}

.next-control {
    border-left: 2px solid var(--cctc-steps-icon-active-color);
}

.prev-control {
    border-right: 2px solid var(--cctc-steps-icon-active-color);
}

.next-control-disabled {
    border-left: 2px solid var(--cctc-steps-icon-disabled-color);
}

.prev-control-disabled {
    border-right: 2px solid var(--cctc-steps-icon-disabled-color);
}

.button-enabled {
    color: var(--cctc-steps-color);
    cursor: pointer;
}

.button-disabled {
    color: var(--cctc-steps-icon-disabled-color);
    cursor: default;
}




