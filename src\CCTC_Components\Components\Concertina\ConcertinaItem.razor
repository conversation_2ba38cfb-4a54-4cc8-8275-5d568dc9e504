﻿@using CCTC_Components.Components.Concertina
@implements IAsyncDisposable
@inherits CCTC_Components.Components.__CCTC.CCTCBase

<hr/>

<cctc-concertinaitem id="@Id" class="@CssClass" style="@Style" data-author="cctc">
    <cctc-concertinaitem-header class="d-flex flex-column">
        <div class="item-header" @onclick="CollapseExpand">
            <div>@HeaderContent</div>
            @if (CanCollapseExpand)
            {
                <div class="collapse-expand-button ms-auto">
                    <div class="material-icons">@_collapseExpandIcon</div>
                </div>
            }
        </div>

    </cctc-concertinaitem-header>

    @if (SubHeaderContent is not null)
    {
        <cctc-concertinaitem-subheader class="item-sub-header" @onclick="CollapseExpand">
            @SubHeaderContent
        </cctc-concertinaitem-subheader>
    }

    @if (_isExpanded)
    {
        <cctc-concertinaitem-content class="item-content">
            @ContentTemplate
        </cctc-concertinaitem-content>
    }
</cctc-concertinaitem>

@code {

    const string CollapseIcon = "expand_less";
    const string ExpandIcon = "expand_more";
    bool _isExpanded;
    string _collapseExpandIcon = CollapseIcon;

    /// <summary>
    /// The parent Concertina component
    /// </summary>
    [CascadingParameter]
    public required Concertina Parent { get; set; }

    /// <summary>
    /// The content to display in the header
    /// </summary>
    [Parameter, EditorRequired]
    public required RenderFragment HeaderContent { get; set; }

    /// <summary>
    /// The content to display in the sub header
    /// </summary>
    [Parameter]
    public RenderFragment? SubHeaderContent { get; set; }

    /// <summary>
    /// The content to display in the body of the item
    /// </summary>
    [Parameter]
    public RenderFragment? ContentTemplate { get; set; }


    /// <summary>
    /// If the ContentTemplate is not null then the item can be collapsed or expanded
    /// </summary>
    public bool CanCollapseExpand => ContentTemplate is not null;

    void UpdateCollapseExpandIcon()
    {
        _collapseExpandIcon = _isExpanded ? CollapseIcon : ExpandIcon;
        StateHasChanged();
    }

    async Task CollapseExpand()
    {
        _isExpanded = !_isExpanded;
        UpdateCollapseExpandIcon();

        if (_isExpanded && Parent.ShouldCollapseOthers())
        {
            Parent.CollapseOthersOnSelected(Id);
        }
        else
        {
            Parent.CheckIconState();
        }

        if (_isExpanded)
        {
            _scrollIntoViewRequested = true;
        }

        await Parent.OnCollapseOrExpand.InvokeAsync(this);
    }

    /// <summary>
    /// Forces the item to be collapsed
    /// </summary>
    public void ForceCollapse()
    {
        _isExpanded = false;
        UpdateCollapseExpandIcon();
    }

    /// <summary>
    /// Forces the item to be expanded
    /// </summary>
    public void ForceExpand()
    {
        _isExpanded = true;
        UpdateCollapseExpandIcon();
    }

    /// <summary>
    /// True if the item is currently expanded
    /// </summary>
    public bool IsExpanded => _isExpanded;


    bool _scrollIntoViewRequested;

    /// <inheritdoc />
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (_scrollIntoViewRequested)
        {
            await ScrollIntoViewFromSelector($"#{Id}");
            _scrollIntoViewRequested = false;
        }
    }

    /// <inheritdoc />
    protected override void OnInitialized()
    {
        Parent.RegisterChild(this);
    }

}