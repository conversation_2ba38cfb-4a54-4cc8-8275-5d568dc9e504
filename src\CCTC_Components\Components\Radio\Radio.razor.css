﻿cctc-input {
    max-height: 100%;
    width: 100%;
}

.component-wrapper {
    display: flex;
    align-items: start;
    height: 100%;
    position: relative;
}

/*Note use of outline with negative offset instead of border. This allows the height of each Radio component row to equal --cctc-input-height*/
.radio-group-wrapper {
    flex: 0 0 100%;
    height: 100%;
    overflow-y: auto;
    scrollbar-gutter: stable;
    display: flex;
    padding-left: var(--cctc-input-padding-left);
    padding-right: max(var(--cctc-input-padding-right), 1.5rem);
    color: var(--cctc-input-color);
    background-color: var(--cctc-input-background-color);
    border-radius: var(--cctc-input-border-radius);
    outline: var(--cctc-input-border-color) var(--cctc-input-border-style) var(--cctc-input-border-width);
    outline-offset: calc(var(--cctc-input-border-width) * -1);
    z-index: 3;
}

.radio-group-wrapper.label-nowrap ::deep cctc-tooltip, .radio-group-wrapper.label-scroll ::deep cctc-tooltip {
    display: flex;
    min-width: 0;
}

.radio-group-wrapper.right-justify {
    padding-left: var(--cctc-input-padding-left);
}

.radio-group-wrapper.disabled {
    color: var(--cctc-input-disabled-color);
    outline: none;
    background-color: var(--cctc-input-disabled-background-color);
}

.radio-group-row {
    flex-wrap: wrap;
    flex-direction: row;
    column-gap: 2rem;
}

.radio-group-column {
    flex-wrap: nowrap;
    flex-direction: column;
}

.radio-group-column.right-justify .radio-parent {
    flex-direction: row-reverse;
}

.radio-parent {
    min-width: 0;
    min-height: var(--cctc-input-height);
    display: flex;
    align-items: center;
    column-gap: 1rem;
}

.radio-parent:has(input:disabled) {
    color: var(--cctc-input-disabled-color);
}

.radio-group-column.right-justify .radio-parent {
    justify-content: end;
}

.icon-wrapper {
    z-index: 3
}

.icon-wrapper:has(.lock) {
    color: var(--cctc-input-readonly-icon-color);
    margin-top: 0.3rem;
    margin-left: -1.35rem;
}

.icon-wrapper:has(.backspace) {
    color: var(--cctc-input-icon-color);
    margin-top: 0.3rem;
    margin-left: -1.45rem;
    cursor: default;
}

.icon-wrapper:has(.backspace):hover {
    color: var(--cctc-input-icon-hover-color);
}

.label-wrap label {
    display: -webkit-box;
    -webkit-line-clamp: var(--cctc-input-webkit-line-clamp);
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.label-nowrap label {
    white-space: nowrap;
    overflow-x: hidden;
    text-overflow: ellipsis;
}

.label-scroll label {
    white-space: nowrap;
    overflow-x: auto;
    scrollbar-gutter: stable;
    z-index: 2;
}

.radio-group-column input, .radio-group-row input {
    flex: 0 0 auto;
    transform: scale(1);
    height: calc(var(--cctc-input-height) * 0.677);
    width: calc(var(--cctc-input-height) * 0.677);
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar {
    width: 0.4rem;
    height: 0.4rem;
}

/*prevent content from obscuring top and bottom outline when scrolling*/
.radio-group-wrapper::before, .radio-group-wrapper::after {
    position: absolute;
    left: 0.5rem;
    content: "";
    height: 0.2rem;
    width: calc(100% - 1rem);
    border-style: var(--cctc-input-border-style);
    border-color: var(--cctc-input-border-color);
}

.radio-group-wrapper::before {
    top: 0;
    z-index: 1;
    background: linear-gradient(var(--cctc-input-background-color), transparent);
    border-width: var(--cctc-input-border-width) 0 0 0;
}

.radio-group-wrapper::after {
    bottom: 0;
    background: linear-gradient(transparent, var(--cctc-input-background-color));
    border-width: 0 0 var(--cctc-input-border-width) 0;
}

.radio-group-wrapper.disabled::before, .radio-group-wrapper.disabled::after {
    background: var(--cctc-input-disabled-background-color);
    border-style: none;
}

.material-icons.backspace.hide-clear-icon {
    visibility: hidden;
    opacity: 0;
    transition: opacity 0.5s ease-out, visibility 0s linear 0.5s;
}

.material-icons.backspace.show-clear-icon {
    visibility: visible;
    opacity: 1;
    transition: opacity 0.5s ease-out, visibility 0s linear 0s;
}

