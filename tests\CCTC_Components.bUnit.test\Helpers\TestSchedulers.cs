﻿using Microsoft.Reactive.Testing;
using System.Reactive.Concurrency;
using CCTC_Lib.Contracts.Reactive;

namespace CCTC_Components.bUnit.test.Helpers
{
    public sealed class TestSchedulers : ISchedulerProvider
    {
        private readonly TestScheduler _currentThread = new TestScheduler();

        private readonly TestScheduler _immediate = new TestScheduler();

        private readonly TestScheduler _default = new TestScheduler();

        #region Explicit interface implementation of ISchedulerProvider
        IScheduler ISchedulerProvider.CurrentThread => _currentThread;

        IScheduler ISchedulerProvider.Immediate => _immediate;

        IScheduler ISchedulerProvider.Default => _default;
        #endregion

        public TestScheduler CurrentThread => _currentThread;

        public TestScheduler Immediate => _immediate;

        public TestScheduler Default => _default;
    }
}
