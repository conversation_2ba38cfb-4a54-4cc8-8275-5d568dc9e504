## CCTC_Components_functional_tests

![Test](https://github.com/CCTC-team/CCTC_Components_functional_tests/workflows/Test/badge.svg)

A repository for writing function end-to-end tests using Cucumber Gherkin scripts with <PERSON><PERSON> using Typescript. This repository uses this starter template https://github.com/Tallyb/cucumber-playwright.

### Includes the following

- Typescript setup for writing steps with eslint/typescript and prettier
- Launching of Playwright browser before running all tests
- Launching new context and page for each scenario
- Running feature with video recording option
- Report generated with last good image attached
- Allure reports
- Utilies function to help you with writing steps
- VScode configuration to debug a single feature or an only scenario (run when located on the feature file)

### To run your tests

`npm run test` runs all tests
`npm run only <feature tag>` runs features with the given tag

### Browser selection

Uses chromium by default. You can define an environment variable called BROWSER and
set the name of the browser. Available options: chromium, firefox, webkit

for Windows use the following to run in a different browser (two separate commands)

cmd
```
set BROWSER=firefox
npm run test
```
powershell
```
$env:BROWSER='firefox'
npm run test
```

### From CLI

- `npm run debug` - headful mode with APIs enables both APIs and debug options
- `npm run api` - headless mode with debug apis
- `npm run video` - headless mode vith video

To run using tags to limit the tests run

e.g.

- `npm run only "@foo"` - scenarios tagged with `foo`
- `npm run only "@foo and not @bar"` - scenarios tagged with `foo` that aren't also tagged with `bar`
- `npm run only "@foo or @bar"` - scenarios tagged with `foo` or `bar`

see https://cucumber.io/docs/cucumber/api/?lang=javascript#tags

### In Visual Studio Code

- Open the feature
- Select the debug options in the VSCode debugger
- Set breakpoints in the code

To stop the feature, you can add the `Then debug` step inside your feature. It will stop your debugger.

### To choose a reporter

The last reporter/formatter found on the cucumber-js command-line wins:

```text
--format summary --format @cucumber/pretty-formatter --format cucumber-console-formatter
```

In [cucumber.mjs](cucumber.mjs) file, modify the options.

To use Allure reporting, you can run with env param: `set USE_ALLURE=1`, and then use the `npm run allure` to show the report.

### To ignore a scenario

- tag the scenario with `@ignore`

### To check for typescript, linting and gherkin errors

- run the command `npm run build`.

### To view the steps usage

- run the command `npm run steps-usage`.

### To view the html report of the last run

- run the command `npm run report`.

### To view allure report

- run the command `npm run allure`.

## Note

- Running the allure script does not work as expected. Unsure why but not required really so don't use it

- Also running build had the original implementation

`"build": "rimraf build && npm run format && npm run lint && tsc && npm run cucumber-check",`

however, the `tsc` command caused errors such as

```
node_modules/@cucumber/cucumber/node_modules/type-fest/source/merge-deep.d.ts:197:5 - error TS2321: Excessive stack depth comparing types 'PickRestType<ArrayTail<ArrayTail<Destination>>>[number]' and 'UnknownArrayOrTuple'.

197 > = [
        ~
198  ...MergeArrayTypeAndTuple<Destination[number], OmitRestType<Source>, Options>,
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
199  ...MergeDeepArrayOrTupleElements<PickRestType<Destination>, PickRestType<Source>, Options>,
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
200 ];
```

so it was edited to exclude the typescript `tsc` command and now builds successfully.
Hopefully that can be reinstated - it is possible a later version of typescript may resolve this.
