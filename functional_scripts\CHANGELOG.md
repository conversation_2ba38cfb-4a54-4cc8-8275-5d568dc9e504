# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

## [3.0.0](https://github.com/tallyb/cucumber7-playwright/compare/v3.0.0...v2.0.0) (2021-07-24)
Migrating to eslint 9 and typescript eslint 8.
Scripts are now compiled to esm and not to commonjs


## [1.2.0](https://github.com/tallyb/cucumber7-playwright/compare/v1.1.1...v1.2.0) (2021-07-24)


### Features

* **app:** Add console.log to reports ([4e176d0](https://github.com/tallyb/cucumber7-playwright/commit/4e176d0fa7f2d120ea10db0caa1f84084c92a7c6))

### [1.1.1](https://github.com/tallyb/cucumber7-playwright/compare/v1.1.0...v1.1.1) (2021-07-24)


### Bug Fixes

* **app:** Extract config to separate file ([b49a6b1](https://github.com/tallyb/cucumber7-playwright/commit/b49a6b18625fb3c5ec1d99152989b135855ce008))
* **app:** Fix browser global error ([e24230c](https://github.com/tallyb/cucumber7-playwright/commit/e24230ced2d2b0deaf387c1f200a70d1c2c33a64))
* **app:** Fix messages type error ([b1866bc](https://github.com/tallyb/cucumber7-playwright/commit/b1866bcce7b207966d2c43715cc58b454c9d67bc))
* **app:** Update hooks to match node types 16 ([ff08e71](https://github.com/tallyb/cucumber7-playwright/commit/ff08e712b65125aa399ed13d52531a084a099568))

## [1.1.0](https://github.com/tallyb/cucumber7-playwright/compare/v1.0.1...v1.1.0) (2021-05-12)


### Features

* add firefox user prefs ([ab066aa](https://github.com/tallyb/cucumber7-playwright/commit/ab066aa2c05a80d394768bcca4aa6414bc0ac430))
* force node minimum version when running npm i ([5dd7dbc](https://github.com/tallyb/cucumber7-playwright/commit/5dd7dbc044b3bb886fd7275916db0720da113fb6))


### Bug Fixes

* build command broken ([9e4c380](https://github.com/tallyb/cucumber7-playwright/commit/9e4c380b1b21457a5902165c0e031d8cb8b10955))

### 1.0.1 (2021-05-12)


### Bug Fixes

* **config:** Fix deprecated eslint ([327da2e](https://github.com/tallyb/cucumber7-playwright/commit/327da2e751d8168bac0ebb140d4b798314bee959))
* **steps:** Use join to fix windows problem ([c2af3b3](https://github.com/tallyb/cucumber7-playwright/commit/c2af3b3074b6160700fe6cbd21a95e4acdcb6e33))
