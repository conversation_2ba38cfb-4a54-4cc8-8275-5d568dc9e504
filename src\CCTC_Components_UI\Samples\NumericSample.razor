﻿@page "/numericsample"

@{
    var description = new List<string>
    {
        "A component for numeric input. Support Reactive input and format validation"
    };

    var features = new List<(string, string)>
    {
        ("Interaction", "Can be made read-only and / or disabled. The read-only icon is optional"),
        ("Constrained input", "Apply a number format (see <code>CCTC_Components.Helpers.Constants.NumberFormatPattern</code> for the format regular expression), specify the mathematical rounding method, prevent whitespace and / or set a max length"),
        ("Input display", "Has the facility to redact text and / or add a placeholder"),
        ("Binding", "The component throttle speed can be changed")
    };

    var gotchas = new List<(string, string)>
    {
        ("ThrottleMs", "1000 ms is the minimum <code>ThrottleMs</code> and will be applied automatically unless a higher value is provided"),
        ("--cctc-input-webkit-line-clamp", "CSS variable not used by this component")
    };

    var tips = new List<(string, string)>
    {
        ("Target the cctc-input component(s) on a page ignoring any child cctc-input components. For example, setting a uniform input component width adjusting according to screen size",
@"
<code>
    <pre>

    ::deep cctc-input:not(cctc-input cctc-input) {
        width: 100%;
    }

    @media (min-width: 1200px) {
        ::deep cctc-input:not(cctc-input cctc-input) {
            width: 35%;
        }
    }
    </pre>
</code>")
    };

    var usageCode = new List<(string title, string description, string code, RenderFragment fragment)>
    {
        ("Format: -##00", "",
@"<Numeric
    Id=""usage1""
    Value=""@NumericIntString""
    Format=""-##00""
    ValueChanged=""@(args => NumericIntChanged(args))""
    NumberChanged=""@(args => Console.WriteLine(args.Value))""
    PreventWhitespace=""true"">
</Numeric

@code {

    string NumericIntString { get; set; } = ""123"";

    void NumericIntChanged(string newValue)
    {
        NumericIntString = newValue;
        Console.WriteLine($""Numeric int new value is: {newValue}"");
    }
}",
        @<Numeric
            Id="usage1"
            Value="@NumericIntString"
            Format="-##00"
            ValueChanged="@(NumericIntChanged)"
            NumberChanged="@(args => Console.WriteLine(args.Value))"
            PreventWhitespace="true">
        </Numeric>),
        ("Format: -#000.0, rounding to even, CssClass applied", "",
@"<Numeric
    Id=""usage2""
    @bind-Value=""@NumericDecimalString""
    CssClass=""info-background""
    Format=""-#000.0""
    Rounding=""MidpointRounding.ToEven""
    NumberChanged=""@(args => Console.WriteLine(args.Value))""
    PreventWhitespace=""true"">
</Numeric>

@code {

    string NumericDecimalString { get; set; } = ""1.25"";
}",
        @<Numeric
            Id="usage2"
            @bind-Value="@NumericDecimalString"
            CssClass="info-background"
            Format="-#000.0"
            Rounding="MidpointRounding.ToEven"
            NumberChanged="@(args => Console.WriteLine(args.Value))"
            PreventWhitespace="true">
        </Numeric>),
        ("No format with a null initial value, redact text, allows whitespace, max length", "",
@"<Numeric
    Id=""usage3""
    @bind-Value=""@NumericNullInt""
    Placeholder=""123""
    RedactText=""true""
    MaxLength=""7""
    NumberChanged=""@(args => Console.WriteLine(args.Value ?? ""value cleared""))"">
</Numeric>

@code {

    string? NumericNullInt { get; set; } = null;
}",
        @<Numeric
            Id="usage3"
            @bind-Value="@NumericNullInt"
            Placeholder="123"
            RedactText="true"
            MaxLength="7"
            NumberChanged="@(args => Console.WriteLine(args.Value ?? "value cleared"))">
        </Numeric>),
        ("Exponential Format: E4", "",
@"<Numeric
    Id=""usage4""
    @bind-Value=""@NumericDecimalString""
    Format=""E4""
    NumberChanged=""@(args => Console.WriteLine(args.Value))""
    PreventWhitespace=""true"">
</Numeric>

@code {

    string NumericDecimalString { get; set; } = ""1.25"";
}",
        @<Numeric
            Id="usage4"
            @bind-Value="@NumericDecimalString"
            Format="E4"
            NumberChanged="@(args => Console.WriteLine(args.Value))"
            PreventWhitespace="true">
        </Numeric>),
        ("Disabled", "",
@"<Numeric
    Id=""usage5""
    @bind-Value=""@NumericDecimalString""
    Format=""E4""
    NumberChanged=""@(args => Console.WriteLine(args.Value))""
    PreventWhitespace=""true""
    Disabled=""true"">
</Numeric>

@code {

    string NumericDecimalString { get; set; } = ""1.25"";
}",
        @<Numeric
            Id="usage5"
            @bind-Value="@NumericDecimalString"
            Format="E4"
            NumberChanged="@(args => Console.WriteLine(args.Value))"
            PreventWhitespace="true"
            Disabled="true">
        </Numeric>),
        ("Read-only", "",
@"<Numeric
    Id=""usage6""
    @bind-Value=""@NumericDecimalString""
    Format=""E4""
    NumberChanged=""@(args => Console.WriteLine(args.Value))""
    PreventWhitespace=""true""
    ReadOnly=""true"">
</Numeric>

@code {

    string NumericDecimalString { get; set; } = ""1.25"";
}",
        @<Numeric
            Id="usage6"
            @bind-Value="@NumericDecimalString"
            Format="E4"
            NumberChanged="@(args => Console.WriteLine(args.Value))"
            PreventWhitespace="true"
            ReadOnly="true">
        </Numeric>),
        ("Disabled and read-only", "",
@"<Numeric
    Id=""usage7""
    @bind-Value=""@NumericDecimalString""
    Format=""E4""
    NumberChanged=""@(args => Console.WriteLine(args.Value))""
    PreventWhitespace=""true""
    ReadOnly=""true""
    Disabled=""true"">
</Numeric>

@code {

    string NumericDecimalString { get; set; } = ""1.25"";
}",
        @<Numeric
            Id="usage7"
            @bind-Value="@NumericDecimalString"
            Format="E4"
            NumberChanged="@(args => Console.WriteLine(args.Value))"
            PreventWhitespace="true"
            ReadOnly="true"
            Disabled="true">
        </Numeric>),
        ("Disabled and read-only (hide read-only icon)", "",
@"<Numeric
    Id=""usage8""
    @bind-Value=""@NumericDecimalString""
    Format=""E4""
    NumberChanged=""@(args => Console.WriteLine(args.Value))""
    PreventWhitespace=""true""
    ReadOnly=""true""
    Disabled=""true""
    HideReadOnlyIcon=""true"">
</Numeric>

@code {

    string NumericDecimalString { get; set; } = ""1.25"";
}",
        @<Numeric
            Id="usage8"
            @bind-Value="@NumericDecimalString"
            Format="E4"
            NumberChanged="@(args => Console.WriteLine(args.Value))"
            PreventWhitespace="true"
            ReadOnly="true"
            Disabled="true"
            HideReadOnlyIcon="true">
        </Numeric>),
        ("Read-only long", "",
@"<Numeric
    Id=""usage9""
    @bind-Value=""@NumericDecimalStringLong""
    CssClass=""narrow-numeric""
    NumberChanged=""@(args => Console.WriteLine(args.Value))""
    PreventWhitespace=""true""
    ReadOnly=""true"">
</Numeric>

@code {

    string NumericDecimalStringLong { get; set; } = ""1.255555555555556789"";
}",
        @<Numeric
            Id="usage9"
            @bind-Value="@NumericDecimalStringLong"
            CssClass="narrow-numeric"
            NumberChanged="@(args => Console.WriteLine(args.Value))"
            PreventWhitespace="true"
            ReadOnly="true">
        </Numeric>),
        ("Read-only long (hide read-only icon)", "",
@"<Numeric
    Id=""usage10""
    @bind-Value=""@NumericDecimalStringLong""
    CssClass=""narrow-numeric""
    NumberChanged=""@(args => Console.WriteLine(args.Value))""
    PreventWhitespace=""true""
    ReadOnly=""true""
    HideReadOnlyIcon=""true"">
</Numeric>

@code {

    string NumericDecimalStringLong { get; set; } = ""1.255555555555556789"";
}",
        @<Numeric
            Id="usage10"
            @bind-Value="@NumericDecimalStringLong"
            CssClass="narrow-numeric"
            NumberChanged="@(args => Console.WriteLine(args.Value))"
            PreventWhitespace="true"
            ReadOnly="true"
            HideReadOnlyIcon="true">
        </Numeric>)
    };
}
@* The wrapper div is necessary here for ::deep isolated css rules to be applied to the Numeric component *@
<div>
    <Sampler
        ComponentName="Numeric"
        ComponentCssName="input"
        ComponentTypeName="numeric"
        Description="@description"
        Features="@features"
        UsageText="Typical usages of the <code>Numeric</code> component are shown below"
        UsageCodeList="@usageCode"
        Gotchas="@gotchas"
        Tips="@tips"
        ContentHeightPixels="450">
        <ExampleTemplate>
            <Numeric
                Id="example1"
                Value="@NumericIntString"
                Format="-##00"
                ValueChanged="@(args => NumericIntChanged(args))"
                NumberChanged="@(args => Console.WriteLine(args.Value))"
                PreventWhitespace="true">
            </Numeric>
        </ExampleTemplate>
    </Sampler>
</div>

@code {

    string NumericIntString { get; set; } = "123";

    string? NumericNullInt { get; set; } = null;

    string NumericDecimalString { get; set; } = "1.25";

    string NumericDecimalStringLong { get; set; } = "1.255555555555556789";

    void NumericIntChanged(string newValue)
    {
        NumericIntString = newValue;
        Console.WriteLine($"Numeric int new value is: {newValue}");
    }
}
