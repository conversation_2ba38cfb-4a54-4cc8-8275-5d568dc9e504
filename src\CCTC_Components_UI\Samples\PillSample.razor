﻿@page "/pillsample"
@using PillColorStyle = CCTC_Lib.Enums.UI.FillStyle

@{
    var description = new List<string>
    {
        "The Pill component"
    };

    var features = new List<(string, string)>
    {
        ("Display", "The pill can be filled or outline. Limit the max width if required"),
        ("Pill content", "Both the icon (optional) and text content are configurable. The <code>Size</code> can be changed"),
        ("Contextual variations", "Optionally provide a contextual variation e.g. Success"),
        ("Tooltip", "Tooltip behaviour can be set to EnabledOnTextOverflow (default), Enabled or Disabled. Tooltip placement is configurable"),
        ("Interaction", "Register an <code>OnClick</code> callback")
    };

    var tips = new List<(string, string)>
    {
        ("Contextual variations", "When a <code>PillContext</code> is given e.g. Success, defaults to theme success colors. Without a <code>PillContext</code> uses default theme colors. These can be overridden e.g. --cctc-pill-color: orange;")
    };

    var usageCode = new List<(string title, string description, string code, RenderFragment fragment)>
    {
        ("PillContext: None, ColorStyle: Fill, Size: Medium, Icon: circle, with OnClick callback", "",
@"<Pill
    Id=""usage1""
    Content=""@PillShortContent""
    Icon=""circle""
    ColorStyle=""PillColorStyle.Fill""
    Size=""Size.Medium""
    OnClick=""@(() => Console.WriteLine(""Pill clicked""))"">
</Pill>

@code {

    string PillShortContent { get; set; } = ""Pill content"";
}",
        @<Pill
            Id="usage1"
            Content="@PillShortContent"
            Icon="circle"
            ColorStyle="PillColorStyle.Fill"
            Size="Size.Medium"
            OnClick="@(() => Console.WriteLine("Pill clicked"))">
        </Pill>),
        ("PillContext: Success, ColorStyle: Fill, Size: Small, Icon: done_outline", "",
@"<Pill
    Id=""usage2""
    Content=""@PillShortContent""
    Icon=""done_outline""
    PillContext=""PillContext.Success""
    ColorStyle=""PillColorStyle.Fill""
    Size=""Size.Small"">
</Pill>

@code {
    
    string PillShortContent { get; set; } = ""Pill content longer - Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."";
}",
        @<Pill
            Id="usage2"
            Content="@PillShortContent"
            Icon="done_outline"
            PillContext="PillContext.Success"
            ColorStyle="PillColorStyle.Fill"
            Size="Size.Small">
        </Pill>),
        ("PillContext: Success, ColorStyle: Outline, Size: Small, Icon: done_outline", "",
@"<Pill
    Id=""usage3""
    Content=""@PillShortContent""
    Icon=""done_outline""
    PillContext=""PillContext.Success""
    ColorStyle=""PillColorStyle.Outline""
    Size=""Size.Small"">
</Pill>

@code {

    string PillShortContent { get; set; } = ""Pill content longer - Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."";
}",
        @<Pill
            Id="usage3"
            Content="@PillShortContent"
            Icon="done_outline"
            PillContext="PillContext.Success"
            ColorStyle="PillColorStyle.Outline"
            Size="Size.Small">
        </Pill>),
        ("PillContext: Info, ColorStyle: Fill, Size: Small, Icon: info, with OnClick callback", "",
@"<Pill
    Id=""usage4""
    Content=""@PillShortContent""
    Icon=""info""
    PillContext=""PillContext.Info""
    ColorStyle=""PillColorStyle.Fill""
    Size=""Size.Small""
    OnClick=""@(() => Console.WriteLine(""Pill clicked""))"">
</Pill>

@code {

    string PillShortContent { get; set; } = ""Pill content longer - Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."";
}",
        @<Pill
            Id="usage4"
            Content="@PillShortContent"
            Icon="info"
            PillContext="PillContext.Info"
            ColorStyle="PillColorStyle.Fill"
            Size="Size.Small"
            OnClick="@(() => Console.WriteLine("Pill clicked"))">
        </Pill>),
        ("PillContext: Warning, ColorStyle: Fill, Size: Small, Icon: warning", "",
@"<Pill
    Id=""usage5""
    Content=""@PillShortContent""
    Icon=""warning""
    PillContext=""PillContext.Warning""
    ColorStyle=""PillColorStyle.Fill""
    Size=""Size.Small"">
</Pill>

@code {
    
    string PillShortContent { get; set; } = ""Pill content longer - Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."";
}",
        @<Pill
            Id="usage5"
            Content="@PillShortContent"
            Icon="warning"
            PillContext="PillContext.Warning"
            ColorStyle="PillColorStyle.Fill"
            Size="Size.Small">
        </Pill>),
        ("PillContext: Danger, ColorStyle: Fill, Size: Small, Icon: dangerous", "",
@"<Pill
    Id=""usage6""
    Content=""@PillShortContent""
    Icon=""dangerous""
    PillContext=""PillContext.Danger""
    ColorStyle=""PillColorStyle.Fill""
    Size=""Size.Small"">
</Pill>

@code {
    
    string PillShortContent { get; set; } = ""Pill content longer - Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."";
}",
        @<Pill
            Id="usage6"
            Content="@PillShortContent"
            Icon="dangerous"
            PillContext="PillContext.Danger"
            ColorStyle="PillColorStyle.Fill"
            Size="Size.Small">
        </Pill>),
        ("PillContext: None, ColorStyle: Fill, Size: Medium, Icon: no icon", "",
@"<Pill
    Id=""usage7""
    Content=""@PillShortContent""
    ColorStyle=""PillColorStyle.Fill""
    Size=""Size.Medium"">
</Pill>

@code {

    string PillShortContent { get; set; } = ""Pill content"";
}",
        @<Pill
            Id="usage7"
            Content="@PillShortContent"
            ColorStyle="PillColorStyle.Fill"
            Size="Size.Medium">
        </Pill>),
        ("PillContext: None, ColorStyle: Fill, Size: Medium, Icon: circle, MaxWidth: 25rem, PillTooltipBehaviour: EnabledOnTextOverflow, PillTooltipPlacement: Right", "",
@"<Pill
    Id=""usage8""
    Content=""@PillLongContent""
    Icon=""circle""
    ColorStyle=""PillColorStyle.Fill""
    Size=""Size.Medium""
    MaxWidth=""25rem""
    PillTooltipBehaviour=""PillTooltipBehaviour.EnabledOnTextOverflow""
    PillTooltipPlacement=""TooltipPlacement.Right"">
</Pill>

@code {

    string PillLongContent { get; set; } = ""Pill content longer - Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."";
}
",
        @<Pill
            Id="usage8"
            Content="@PillLongContent"
            Icon="circle"
            ColorStyle="PillColorStyle.Fill"
            Size="Size.Medium"
            MaxWidth="25rem"
            PillTooltipBehaviour="PillTooltipBehaviour.EnabledOnTextOverflow"
            PillTooltipPlacement="TooltipPlacement.Right">
        </Pill>),
    ("PillContext: None, ColorStyle: Fill, Size: Medium, Icon: circle, MaxWidth: 25rem, PillTooltipBehaviour: Enabled, PillTooltipPlacement: Top", "",
@"<Pill
    Id=""usage9""
    Content=""@PillLongContent""
    Icon=""circle""
    ColorStyle=""PillColorStyle.Fill""
    Size=""Size.Medium""
    MaxWidth=""25rem""
    PillTooltipBehaviour=""PillTooltipBehaviour.Enabled""
    PillTooltipPlacement=""TooltipPlacement.Top"">
</Pill>

@code {

    string PillLongContent { get; set; } = ""Pill content longer - Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."";
}
",
        @<Pill
            Id="usage9"
            Content="@PillLongContent"
            Icon="circle"
            ColorStyle="PillColorStyle.Fill"
            Size="Size.Medium"
            MaxWidth="25rem"
            PillTooltipBehaviour="PillTooltipBehaviour.Enabled"
            PillTooltipPlacement="TooltipPlacement.Top">
        </Pill>),
        ("PillContext: None, ColorStyle: Fill, Size: Medium, Icon: circle, MaxWidth: 25rem, PillTooltipBehaviour: Disabled", "",
@"<Pill
    Id=""usage10""
    Content=""@PillLongContent""
    Icon=""circle""
    ColorStyle=""PillColorStyle.Fill""
    Size=""Size.Medium""
    MaxWidth=""25rem""
    PillTooltipBehaviour=""PillTooltipBehaviour.Disabled"">
</Pill>

@code {

    string PillLongContent { get; set; } = ""Pill content longer - Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."";
}
",
        @<Pill
            Id="usage10"
            Content="@PillLongContent"
            Icon="circle"
            ColorStyle="PillColorStyle.Fill"
            Size="Size.Medium"
            MaxWidth="25rem"
            PillTooltipBehaviour="PillTooltipBehaviour.Disabled">
        </Pill>)
    };
}

<Sampler
    ComponentName="Pill"
    ComponentCssName="pill"
    Description="@description"
    Features="@features"
    UsageText="Typical usages of the <code>Pill</code> component are shown below"
    Tips="@tips"
    UsageCodeList="@usageCode"
    ContentHeightPixels="450">
    <ExampleTemplate>
        <Pill
            Id="example1"
            Content="@PillShortContent"
            Icon="circle"
            ColorStyle="PillColorStyle.Fill"
            Size="Size.Medium"
            OnClick="@(() => _counter++)">
        </Pill>
        <p class="mt-3">Counter: @_counter</p>
    </ExampleTemplate>
</Sampler>

@code {

    int _counter;

    string PillShortContent { get; set; } = "Pill content";

    string PillLongContent { get; set; } = "Pill content longer - Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.";
}
