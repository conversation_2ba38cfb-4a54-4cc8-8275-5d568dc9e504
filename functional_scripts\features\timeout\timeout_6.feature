@component @timeout @timeout_6
Feature: the timeout component can automatically restart when initiated while already running

    Scenario: the timeout component restarts when initiated again while running
        Given the user is at the home page
        And the user selects the "Time out" component
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Timeout restart"
        And the timeout component reserves space
        And the timeout component content is not visible
        When the user clicks the timeout component initiate button
        Then the timeout component displays content
        And the timeout component content is visible
        And the timeout component reserves space
        When the user waits for 1000 milliseconds
        And the user clicks the timeout component initiate button
        Then the timeout component displays content
        And the timeout component content is visible
        And the timeout component reserves space
        When the user waits for 2500 milliseconds
        Then the timeout component is fading
        When the user waits for 1000 milliseconds
        Then the timeout component is not fading
        And the timeout component content is not visible
        And the timeout component reserves space