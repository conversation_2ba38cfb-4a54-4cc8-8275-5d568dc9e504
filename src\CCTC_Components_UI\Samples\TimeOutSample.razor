﻿@page "/timeoutsample"

@{
    var description = new List<string>
    {
        "The TimeOut component is a simple component that shows content on screen for a predefined period of time before fading out."
    };

    var features = new List<(string, string)>
    {
        ("Initial display", "The content can reserve space or initialise in a collapsed state"),
        ("Display on completion", "The content can either collapse or reserve its consumed space on completion, irrespective of its " +
                                  "intialisation state"),
        ("Restart functionality", "AutoRestart allows the timeout to be restarted when initiated while already running"),
        ("Stop functionality", "Manual Stop method allows immediate termination of the timeout with callback notification"),
    };

    var usageCode = new List<(string title, string description, string code, RenderFragment fragment)>
    {
        ("Reserved and no collapse", "Space is reserved and does not collapse on completion",
            @"<TimeOut
    Id=""sample1""
    @ref=""@_sample1""
    ShowContentFor=""TimeSpan.FromSeconds(1)""
    FadeFor=""TimeSpan.FromSeconds(2)""
    AfterShowContent=""@(() => Console.WriteLine(""Show content finished""))""
    AfterFadeFinished=""@(() => Console.WriteLine(""Fade finished""))""
    ReserveSpace=""true""
    CollapseOnFadeComplete=""false"">
    <div class=""sample-content"">
        <div class=""d-flex flex-column"">
            <div class=""material-icons"" style=""font-size: 24px"">thumb_up</div>
            <div>Some content to display in the message</div>
        </div>
    </div>
</TimeOut>

@code {

    TimeOut? _sample1;

    void RunSample1()
    {
        _sample1?.Initiate();
    }
}",
            @<div>
                <hr/>
                <TimeOut
                    Id="sample1"
                    @ref="@_sample1"
                    ShowContentFor="TimeSpan.FromSeconds(1)"
                    FadeFor="TimeSpan.FromSeconds(2)"
                    AfterShowContent="@(() => Console.WriteLine("Show content finished"))"
                    AfterFadeFinished="@(() => Console.WriteLine("Fade finished"))"
                    ReserveSpace="true"
                    CollapseOnFadeComplete="false">
                    <div class="sample-content">
                        <div class="d-flex flex-column">
                            <div class="material-icons" style="font-size: 24px">thumb_up</div>
                            <div>Some content to display in the message</div>
                        </div>
                    </div>
                </TimeOut>
                <hr/>
                <div class="initiate" @onclick="RunSample1">Initiate</div>
            </div>),

        ("Reserved and collapses", "Space is reserved initially, but then collapses on completion",
            @"<TimeOut
    Id=""sample2""
    @ref=""@_sample2""
    ShowContentFor=""TimeSpan.FromSeconds(1)""
    FadeFor=""TimeSpan.FromSeconds(2)""
    AfterShowContent=""@(() => Console.WriteLine(""Show content finished""))""
    AfterFadeFinished=""@(() => Console.WriteLine(""Fade finished""))""
    ReserveSpace=""true""
    CollapseOnFadeComplete=""true"">
    <div class=""sample-content"">
        <div class=""d-flex flex-column"">
            <div class=""material-icons"" style=""font-size: 24px"">thumb_up</div>
            <div>Some content to display in the message</div>
        </div>
    </div>
</TimeOut>

@code {

    TimeOut? _sample2;

    void RunSample2()
    {
        _sample2?.Initiate();
    }
}",
            @<div>
                <hr/>
                <TimeOut
                    Id="sample2"
                    @ref="@_sample2"
                    ShowContentFor="TimeSpan.FromSeconds(1)"
                    FadeFor="TimeSpan.FromSeconds(2)"
                    AfterShowContent="@(() => Console.WriteLine("Show content finished"))"
                    AfterFadeFinished="@(() => Console.WriteLine("Fade finished"))"
                    ReserveSpace="true"
                    CollapseOnFadeComplete="true">
                    <div class="sample-content">
                        <div class="d-flex flex-column">
                            <div class="material-icons" style="font-size: 24px">thumb_up</div>
                            <div>Some content to display in the message</div>
                        </div>
                    </div>
                </TimeOut>
                <hr/>
                <div class="initiate" @onclick="RunSample2">Initiate</div>
            </div>),

        ("Not reserved and collapses", "Space is not reserved initially and collapses on completion",
            @"<TimeOut
    Id=""sample3""
    @ref=""@_sample3""
    ShowContentFor=""TimeSpan.FromSeconds(1)""
    FadeFor=""TimeSpan.FromSeconds(2)""
    AfterShowContent=""@(() => Console.WriteLine(""Show content finished""))""
    AfterFadeFinished=""@(() => Console.WriteLine(""Fade finished""))""
    ReserveSpace=""false""
    CollapseOnFadeComplete=""true"">
    <div class=""sample-content"">
        <div class=""d-flex flex-column"">
            <div class=""material-icons"" style=""font-size: 24px"">thumb_up</div>
            <div>Some content to display in the message</div>
        </div>
    </div>
</TimeOut>

@code {

    TimeOut? _sample3;

    void RunSample3()
    {
        _sample3?.Initiate();
    }
}",
            @<div>
                <hr/>
                <TimeOut
                    Id="sample3"
                    @ref="@_sample3"
                    ShowContentFor="TimeSpan.FromSeconds(1)"
                    FadeFor="TimeSpan.FromSeconds(2)"
                    AfterShowContent="@(() => Console.WriteLine("Show content finished"))"
                    AfterFadeFinished="@(() => Console.WriteLine("Fade finished"))"
                    ReserveSpace="false"
                    CollapseOnFadeComplete="true">
                    <div class="sample-content">
                        <div class="d-flex flex-column">
                            <div class="material-icons" style="font-size: 24px">thumb_up</div>
                            <div>Some content to display in the message</div>
                        </div>
                    </div>
                </TimeOut>
                <hr/>
                <div class="initiate" @onclick="RunSample3">Initiate</div>
            </div>),

        ("Not reserved and does not collapse", "Space is not reserved initially and space stays reserved on completion",
            @"<TimeOut
    Id=""sample4""
    @ref=""@_sample4""
    ShowContentFor=""TimeSpan.FromSeconds(1)""
    FadeFor=""TimeSpan.FromSeconds(2)""
    AfterShowContent=""@(() => Console.WriteLine(""Show content finished""))""
    AfterFadeFinished=""@(() => Console.WriteLine(""Fade finished""))""
    ReserveSpace=""false""
    CollapseOnFadeComplete=""true"">
    <div class=""sample-content"">
        <div class=""d-flex flex-column"">
            <div class=""material-icons"" style=""font-size: 24px"">thumb_up</div>
            <div>Some content to display in the message</div>
        </div>
    </div>
</TimeOut>

@code {

    TimeOut? _sample4;

    void RunSample4()
    {
        _sample4?.Initiate();
    }
}",
            @<div>
                <hr/>
                <TimeOut
                    Id="sample4"
                    @ref="@_sample4"
                    ShowContentFor="TimeSpan.FromSeconds(1)"
                    FadeFor="TimeSpan.FromSeconds(2)"
                    AfterShowContent="@(() => Console.WriteLine("Show content finished"))"
                    AfterFadeFinished="@(() => Console.WriteLine("Fade finished"))"
                    ReserveSpace="false"
                    CollapseOnFadeComplete="false">
                    <div class="sample-content">
                        <div class="d-flex flex-column">
                            <div class="material-icons" style="font-size: 24px">thumb_up</div>
                            <div>Some content to display in the message</div>
                        </div>
                    </div>
                </TimeOut>
                <hr/>
                <div class="initiate" @onclick="RunSample4">Initiate</div>
            </div>),

        ("Timeout restart", "Demonstrates AutoRestart functionality and manual Stop method",
            @"<TimeOut
    Id=""sample5""
    @ref=""@_sample5""
    ShowContentFor=""TimeSpan.FromSeconds(2)""
    FadeFor=""TimeSpan.FromSeconds(1)""
    AfterShowContent=""@(() => Console.WriteLine(""Show content finished""))""
    AfterFadeFinished=""@(() => Console.WriteLine(""Fade finished""))""
    WhenStopped=""@(auto => Console.WriteLine($""Stopped. Was auto restart? {auto}""))""
    ReserveSpace=""true""
    CollapseOnFadeComplete=""false""
    AutoRestart=""true"">
    <div class=""sample-content"">
        <div class=""d-flex flex-column"">
            <div class=""material-icons"" style=""font-size: 24px"">refresh</div>
            <div>AutoRestart enabled - try clicking Initiate multiple times quickly</div>
        </div>
    </div>
</TimeOut>

@code {
    TimeOut? _sample5;
    void RunSample5()
    {
        _sample5?.Initiate();
    }
    void StopSample5()
    {
        _sample5?.Stop();
    }
}",
            @<div>
                <hr/>
                <TimeOut
                    Id="sample5"
                    @ref="@_sample5"
                    ShowContentFor="TimeSpan.FromSeconds(2)"
                    FadeFor="TimeSpan.FromSeconds(1)"
                    AfterShowContent="@(() => Console.WriteLine("Show content finished"))"
                    AfterFadeFinished="@(() => Console.WriteLine("Fade finished"))"
                    WhenStopped="@(auto => Console.WriteLine($"Stopped. Was auto restart? {auto}"))"
                    ReserveSpace="true"
                    CollapseOnFadeComplete="false"
                    AutoRestart="true">
                    <div class="sample-content">
                        <div class="d-flex flex-column">
                            <div class="material-icons" style="font-size: 24px">refresh</div>
                            <div>AutoRestart enabled - try clicking Initiate multiple times quickly</div>
                        </div>
                    </div>
                </TimeOut>
                <hr/>
                <div class="initiate" @onclick="RunSample5">Initiate</div>
                <div class="stop" @onclick="StopSample5" style="margin-left: 10px; background-color: #dc3545;">Stop</div>
            </div>)
    };
}

<div required-so-deep-works>
    <Sampler
        ComponentName="TimeOut"
        ComponentCssName="timeout"
        Description="@description"
        UsageText="Typical usages of the <code>TimeOut</code> component are shown below"
        UsageCodeList="@usageCode"
        ContentHeightPixels="400"
        Features="@features">
        <ExampleTemplate>

            <div class="m-1">
                <div>The timeout is located between the horizontal lines.</div>
                <div>The space is collapsed until the timer is initiated, and once elapsed, the space is collapsed again.
                    Auto restart is true, so restarting (pressing Initiate) when the timeout is running will restart it</div>
                <hr/>

                <TimeOut
                    Id="example"
                    @ref="@_example"
                    ShowContentFor="TimeSpan.FromSeconds(1)"
                    FadeFor="TimeSpan.FromSeconds(2)"
                    AfterShowContent="@(() => Console.WriteLine("Show content finished"))"
                    AfterFadeFinished="@(() => Console.WriteLine("Fade finished"))"
                    WhenStopped="@(auto => Console.WriteLine($"Stopped. Was auto restart? {auto}"))"
                    ReserveSpace="false"
                    CollapseOnFadeComplete="true"
                    AutoRestart="true"
                    >
                    <div class="sample-content">
                        <div class="d-flex flex-column">
                            <div class="material-icons" style="font-size: 24px">thumb_up</div>
                            <div>Some content to display in the message</div>
                        </div>
                    </div>
                </TimeOut>

                <hr/>

                <div class="initiate" @onclick="RunExample">Initiate</div>

            </div>

        </ExampleTemplate>
    </Sampler>
</div>

@code {

    TimeOut? _sample1;

    void RunSample1()
    {
        _sample1?.Initiate();
    }

    TimeOut? _sample2;

    void RunSample2()
    {
        _sample2?.Initiate();
    }

    TimeOut? _sample3;

    void RunSample3()
    {
        _sample3?.Initiate();
    }

    TimeOut? _sample4;

    void RunSample4()
    {
        _sample4?.Initiate();
    }

    TimeOut? _sample5;

    void RunSample5()
    {
        _sample5?.Initiate();
    }

    void StopSample5()
    {
        _sample5?.Stop();
    }

    TimeOut? _example;

    void RunExample()
    {
        _example?.Initiate();
    }
}