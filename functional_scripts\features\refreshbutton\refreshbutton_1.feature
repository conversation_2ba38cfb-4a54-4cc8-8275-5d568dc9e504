@component @refreshbutton @refreshbutton_1
Feature: clicking the refresh button is registered and generates a tick
    Scenario: the refresh button sample page is available
        Given the user is at the home page
        And the user selects the "Refresh Button" component in the container "Buttons"
        Then the url ending is "refreshbuttonsample"

    Scenario: the refresh button can be clicked and registers this click
        Given the user is at the home page
        And the user selects the "Refresh Button" component in the container "Buttons"
        And the button counter number is 0
        And the user presses the button component
        And the button counter number is 1
        When the user presses the button component
        Then the button counter number is 2

    Scenario: the refresh button has a tick-timeout feature
        Given the user is at the home page
        When the user selects the "Refresh Button" component in the container "Buttons"
        Then the refresh button component has a tick time out