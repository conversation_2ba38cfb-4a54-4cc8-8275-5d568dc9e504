@component @infoicon @infoicon_4
Feature: the tooltip can be to the right or to the top of the info icon
    Scenario: the info icon tooltip is to the right
        Given the user is at the home page
        And the user selects the "Info icon" component
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "InfoType: Tooltip, Size: XSmall, image source (svg), InfoPlacement: Right, with OnClick callback"
        Then the info icon has tooltip or popver in position "right"


    Scenario: the info icon tooltip is on top
        Given the user is at the home page
        And the user selects the "Info icon" component
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "InfoType: Tooltip, Size: Medium, image icon (material icon), InfoPlacement: Top, overridden background color via CssClass"
        Then the info icon has tooltip or popver in position "top"

