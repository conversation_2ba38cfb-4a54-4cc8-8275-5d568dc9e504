@component @radio @radio_2
Feature: the radio component option can be changed using the keyboard
    Scenario: the radio component option can be changed using the keyboard
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        And the current selected radio option has the text "Option 1"
        When the user clicks on the radio option button with the associated label text "Option 1"
        And the user presses the down arrow key once
        Then the current selected radio option has the text "Option 2"