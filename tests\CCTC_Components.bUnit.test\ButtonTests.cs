﻿using CCTC_Components.Components.Buttons;
using CCTC_Lib.Enums.UI;

namespace CCTC_Components.bUnit.test
{
    public class ButtonTests : TestContext
    {
        [Fact]
        public void ButtonClickInvokesCallback()
        {
            var mockDummyService = new Mock<IDummyService>();
            var cut = RenderComponent<Button>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.OnClick, async () => { await mockDummyService.Object.MethodOneAsync(); })
            );

            var buttonWrapper = cut.Find(".button-wrapper");
            buttonWrapper.Click();

            mockDummyService.Verify(m => m.MethodOneAsync(), Times.Once);
        }

        [Fact]
        public void ButtonClickWhenDisabledDoesNotInvokeCallback()
        {
            var mockDummyService = new Mock<IDummyService>();
            var cut = RenderComponent<Button>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.OnClick, async () => { await mockDummyService.Object.MethodOneAsync(); })
                .Add(p => p.Disabled, true)
            );

            var buttonWrapper = cut.Find(".button-wrapper");
            buttonWrapper.Click();

            mockDummyService.Verify(m => m.MethodOneAsync(), Times.Never);
        }

        [Fact]
        public void IconPositionClassesConfiguredCorrectly()
        {
            var cut = RenderComponent<Button>(parameters => parameters
                .Add(p => p.Id, "test-id")
            );

            var buttonWrapper = cut.Find(".button-wrapper");
            string leftPositionClass = "icon-left";
            string rightPositionClass = "icon-right";

            TestHelpers.AssertDoesNotHaveClass(buttonWrapper, rightPositionClass);
            TestHelpers.AssertHasClass(buttonWrapper, leftPositionClass);

            cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.IconPosition, PositionX.Right)
            );

            TestHelpers.AssertDoesNotHaveClass(buttonWrapper, leftPositionClass);
            TestHelpers.AssertHasClass(buttonWrapper, rightPositionClass);
        }

        [Fact]
        public void DisabledClassConfiguredCorrectly()
        {
            var cut = RenderComponent<Button>(parameters => parameters
                .Add(p => p.Id, "test-id")
            );

            var buttonWrapper = cut.Find(".button-wrapper");
            string disabledClass = "disabled";

            TestHelpers.AssertDoesNotHaveClass(buttonWrapper, disabledClass);

            cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.Disabled, true)
            );

            TestHelpers.AssertHasClass(buttonWrapper, disabledClass);
        }

        [Fact]
        public void BorderClassConfiguredCorrectly()
        {
            var cut = RenderComponent<Button>(parameters => parameters
                .Add(p => p.Id, "test-id")
            );

            var buttonWrapper = cut.Find(".button-wrapper");
            string borderClass = "button-border";

            TestHelpers.AssertDoesNotHaveClass(buttonWrapper, borderClass);

            cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.StyledWithBorder, true)
            );

            TestHelpers.AssertHasClass(buttonWrapper, borderClass);
        }
    }
}
