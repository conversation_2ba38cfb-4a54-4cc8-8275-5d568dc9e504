﻿@page "/tabssample"

@{
    var description = new List<string>
    {
        "The Tabs component can be used to select from a choice of ui displayed in child TabItems. " +
        "Only one tab is displayed at a time. The Sampler component makes use of the Tabs component.",
        "The Example tab shows a further implementation that allows a user to select a tab programmatically without clicking the header.",
    };

    var features = new List<(string, string)>
    {
        ("select a tab on open", "any tab can be preselected using the <code>PreSelectTabName</code> parameter so it is opened by default when the Tabs is rendered. " +
                                 "If no preselection is made, the first tab item is automatically selected"),
        ("header placement", "using <code>TabHeaderPlacement</code> parameter, the headers can be placed in different locations in relation to the content"),
        ("tab header tooltips", "tab header tooltips show when the tab header text is truncated")
    };

    var gotchas = new List<(string, string)>
    {
        ("ContentHeightPixels", "setting an appropriate <code>ContentHeightPixels</code> is necessary to display following mark up in the correct document flow. " +
                                "Changing the placement of the headers can impact the total height of the control depending on whether there are sufficient headers to require them to wrap"),
        ("Tab header tooltips", "On mobile, tab header tooltips only show when pressing on a tab header which is not currently in focus. " +
                                "It may be necessary to click away from the selected tab before pressing the tab again to show the tooltip")
    };

    var usageCode = new List<(string title, string description, string code, RenderFragment fragment)>
    {
        ("Typical usage", "example showing how to pre-select items",
            @"<Tabs Id=""tabs-sample"" CssClass=""tabs-default"" ContentHeightPixels=""150"" PreSelectTabId=""@_preSelect"">
    <TabItem Id=""tab1"" Header=""Tab 1"">
        This is some content in tab 1
    </TabItem>
    <TabItem Id=""tab2"" Header=""Tab 2"">
        This is some content in tab 2
    </TabItem>
    <TabItem Id=""tab3"" Header=""Tab 3"">
        This is some content in tab 3
    </TabItem>
</Tabs>

@code {
    string? _preSelect;

    protected override void OnInitialized()
    {
        _preSelect = ""tab2"";
    }
}", @<Tabs Id="tabs-sample" CssClass="" ContentHeightPixels="150" PreSelectTabId="@_preSelect">
        <TabItem Id="tab1" Header="Tab 1">
            This is some content in tab 1
        </TabItem>
        <TabItem Id="tab2" Header="Tab 2">
            This is some content in tab 2
        </TabItem>
        <TabItem Id="tab3" Header="Tab 3">
            This is some content in tab 3
        </TabItem>
    </Tabs>)
    };

    var subParts = new List<string>()
    {
        "-tabs-container",
        "-tabs-headers",
        "-tabs-selected-content"
    };

    var relatedComponents = new List<(Relation relation, string display, string url, string description)>
    {
        (Relation.Child, "TabItem", "tabitemsample", "The Tabs component contains at least one TabItem component")
    };

}

<Sampler
    ComponentName="Tabs"
    ComponentCssName="tabs"
    Description="@description"
    RelatedComponents="@relatedComponents"
    UsageText="Typical usages of the <code>Tabs</code> component are shown below"
    UsageCodeList="@usageCode"
    ContentHeightPixels="460"
    Features="@features"
    Gotchas="@gotchas"
    SubParts="@subParts">
    <ExampleTemplate>
        <Tabs @ref="_exampleTabs" Id="tabs-example" PreSelectTabId="@_preSelect" ContentHeightPixels="200"
              TabHeaderPlacement="@_tabHeaderPlacement">
            <TabItem Id="tabs-example-tab1" Header="1 Tab">
                <h1 class="tab-item-content-margin">
                    Lorem ipsum dolor sit amet consectetur adipisicing elit. Praesentium, officia! Numquam ex saepe mollitia ut nam doloribus dolor ea amet magni voluptates. Placeat laudantium voluptatum sint vel nisi dolores mollitia!
                </h1>
            </TabItem>
            <TabItem Id="tabs-example-tab2" Header="2 Tab">
                <div class="tab-item-content-margin" style="height: 100%; background-color: dimgrey">
                    This is some content in tab 2 styled with a dim grey background
                </div>
            </TabItem>
            <TabItem Id="tabs-example-tab3" Header="3 Tab">
                <div class="tab-item-content-margin" style="height: 500px; background-color: dimgrey">
                    Tab 3
                </div>
            </TabItem>
            <TabItem Id="tabs-example-tab4" Header="4 Longer header that will truncate">
                <div class="tab-item-content-margin">
                    This is some content in tab 4
                </div>
            </TabItem>
            <TabItem Id="tabs-example-tab5" Header="5 Another longer header that will truncate">
                <div class="tab-item-content-margin">
                    This is some content in tab 5
                </div>
            </TabItem>
            <TabItem Id="tabs-example-tab6" Header="6 This header may cause wrapping">
                <div class="tab-item-content-margin">
                    This is some content in tab 6
                </div>
            </TabItem>

        </Tabs>

        <div>
            <hr/>

            <div class="mt-1 d-flex gap-4">
                <div>Header placement</div>
                <div style="width: 100px">
                    <DropDown
                        TData="TabHeaderPlacement"
                        Id="select-a-placement"
                        Data="@(new List<TabHeaderPlacement> { TabHeaderPlacement.Top, TabHeaderPlacement.Left, TabHeaderPlacement.Right, TabHeaderPlacement.Bottom })"
                        Value="@_tabHeaderPlacement"
                        ValueChanged="@ChangeHeaderPlacement">
                    </DropDown>
                </div>
            </div>

            <hr/>

            <i>Use the drop down to change the selected tab to demonstrate how this is done programmatically</i>
            <div class="mt-1 d-flex gap-4">
                <div>Selected tab</div>
                <div style="width: 200px">
                    <DropDown
                        TData="string"
                        Id="select-a-tab"
                        Data="@_dropDownData"
                        Value="@_selectedItem"
                        ValueChanged="@ChangeSelection">
                    </DropDown>
                </div>
                <div class="ml-5">the selected tab was: @_selectedItem</div>
            </div>

        </div>
    </ExampleTemplate>
</Sampler>


@code {

    TabHeaderPlacement _tabHeaderPlacement = TabHeaderPlacement.Top;

    void ChangeHeaderPlacement(TabHeaderPlacement newPlacement)
    {
        _tabHeaderPlacement = newPlacement;
        StateHasChanged();
    }

    string? _preSelect;
    string? _selectedItem;

    void ChangeSelection(string newSelection)
    {
        _selectedItem = newSelection;
        _preSelect = newSelection;
        _exampleTabs!.TabItemSelected(newSelection);
        StateHasChanged();
    }

    Tabs? _exampleTabs;

    readonly List<string> _dropDownData = new();

    protected override void OnInitialized()
    {
        _preSelect = "tabs-example-tab3";
        _selectedItem = _preSelect;

        for (var i = 1; i < 6; i++)
        {
            _dropDownData.Add($"tabs-example-tab{i}");
        }
    }

}