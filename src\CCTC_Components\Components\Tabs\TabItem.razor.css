.tab-item-header {
    color: var(--cctc-tabs-header-color);
    font-size: medium;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: clip;
    line-height: 1rem;
    user-select: none;
}

.top-item-header {
    border-bottom: 1px solid var(--cctc-tabs-header-selected-background-color);
    margin-bottom: 0.1rem;
    margin-right: 0.2rem;
    padding: 0.3rem 0.4rem 0.3rem 0.2rem;
    max-width: 150px;
    max-height: 2rem;
}

.bottom-item-header {
    border-top: 1px solid var(--cctc-tabs-header-selected-background-color);
    margin-top: 0.1rem;
    margin-right: 0.2rem;
    padding: 0.3rem 0.4rem 0.3rem 0.2rem;
    max-width: 150px;
    max-height: 2rem;
}

.left-item-header {
    border-right: 1px solid var(--cctc-tabs-header-selected-background-color);
    margin-right: 0.1rem;
    margin-bottom: 0.1rem;
    padding: 0.3rem 0.2rem 0.3rem 0.4rem;
    max-width: 120px;
    max-height: 2rem;
}

.right-item-header {
    border-left: 1px solid var(--cctc-tabs-header-selected-background-color);
    margin-left: 0.1rem;
    margin-bottom: 0.1rem;
    padding: 0.3rem 0.4rem 0.3rem 0.4rem;
    max-width: 120px;
    max-height: 2rem;
}

cctc-tabitem:last-child .top-item-header, cctc-tabitem:last-child .bottom-item-header {
    max-width: initial;
}

.tab-item-header-selected {
    color: var(--cctc-tabs-header-selected-color);
    background-color: var(--cctc-tabs-header-selected-background-color);
    cursor: None;
}

.top-item-header-selected {
    border-top-right-radius: var(--bs-border-radius-xl);
    border-top-left-radius: var(--bs-border-radius-sm);
    border-bottom: 1px solid var(--cctc-tabs-header-highlight-color);
}

.bottom-item-header-selected {
    border-bottom-right-radius: var(--bs-border-radius-xl);
    border-bottom-left-radius: var(--bs-border-radius-sm);
    border-top: 1px solid var(--cctc-tabs-header-highlight-color);
}

.left-item-header-selected {
    border-top-left-radius: var(--bs-border-radius-xl);
    border-bottom-left-radius: var(--bs-border-radius-sm);
    border-right: 1px solid var(--cctc-tabs-header-highlight-color);
}

.right-item-header-selected {
    border-top-right-radius: var(--bs-border-radius-xl);
    border-bottom-right-radius: var(--bs-border-radius-sm);
    border-left: 1px solid var(--cctc-tabs-header-highlight-color);
}

.tab-item-header:hover {
    color: var(--cctc-tabs-header-highlight-color);
    cursor: pointer;
}

.top-item-header:hover {
    border-bottom: 1px solid var(--cctc-tabs-header-highlight-color);
}

.bottom-item-header:hover {
    border-top: 1px solid var(--cctc-tabs-header-highlight-color);
}

.left-item-header:hover {
    border-right: 1px solid var(--cctc-tabs-header-highlight-color);
}

.right-item-header:hover {
    border-left: 1px solid var(--cctc-tabs-header-highlight-color);
}

.tab-item-header-selected:hover {
    color: var(--cctc-tabs-header-selected-color);
    cursor: None;
}

/*desktop small*/
@media (min-width: 1200px) {
    .tab-item-header {
        font-size: larger;
    }

    .top-item-header {
        margin-bottom: 0.3rem;
        margin-right: 0.4rem;
        padding: 0.4rem 0.8rem 0.4rem 0.4rem;
        max-width: 300px;
    }

    .bottom-item-header {
        margin-top: 0.3rem;
        margin-right: 0.4rem;
        padding: 0.4rem 0.8rem 0.4rem 0.4rem;
        max-width: 300px;
    }

    .left-item-header {
        margin-right: 0.2rem;
        margin-bottom: 0.2rem;
        padding: 0.4rem 0.4rem 0.4rem 0.8rem;
        max-width: 240px;
    }

    .right-item-header {
        margin-left: 0.2rem;
        margin-bottom: 0.2rem;
        padding: 0.4rem 0.4rem 0.4rem 0.8rem;
        max-width: 240px;
    }
}

/*desktop large*/
@media (min-width: 1920px) {
    .tab-item-header {
        font-size: large;
    }

    .top-item-header {
        margin-bottom: 0.4rem;
        margin-right: 0.6rem;
        padding: 0.5rem 1.2rem 0.4rem 0.6rem;
        max-width: 450px;
    }

    .bottom-item-header {
        margin-top: 0.4rem;
        margin-right: 0.6rem;
        padding: 0.5rem 1.2rem 0.4rem 0.6rem;
        max-width: 450px;
    }

    .left-item-header {
        margin-right: 0.3rem;
        margin-bottom: 0.3rem;
        padding: 0.5rem 0.6rem 0.4rem 1.2rem;
        max-width: 360px;
    }

    .right-item-header {
        margin-left: 0.3rem;
        margin-bottom: 0.3rem;
        padding: 0.5rem 0.6rem 0.4rem 1.2rem;
        max-width: 360px;
    }
}
