﻿@page "/animatedplaceholdersample"

@{
    var description = new List<string>
    {
        "A component for displaying an animated placeholder"
    };

    var features = new List<(string, string)>
    {
        ("Display", "Configure the width, height and margin")
    };

    var usageCode = new List<(string title, string description, string code, RenderFragment fragment)>
    {
        ("Height: 50px, Width: 200px, Default margin: 0", "",
@"<AnimatedPlaceholder
    Id=""usage1""
    Height=""50px""
    Width=""200px"">
</AnimatedPlaceholder>

@code {

}",
        @<AnimatedPlaceholder
            Id="usage1"
            Height="50px"
            Width="200px">
        </AnimatedPlaceholder>),
        ("Height: 5rem, Width: 10rem, Margin: 1.25rem", "",
@"<AnimatedPlaceholder
    Id=""usage2""
    Height=""5rem""
    Width=""10rem""
    Margin=""1.25rem""
</AnimatedPlaceholder>

@code {

}",
        @<AnimatedPlaceholder
            Id="usage2"
            Height="5rem"
            Width="10rem"
            Margin="1.25rem">
        </AnimatedPlaceholder>),
        ("Height: 50px, Width: 100px, Margin: 5px, Two animated placeholders", "",
@"<div>
    <AnimatedPlaceholder
        Id=""usage3a""
        Height=""50px""
        Width=""100px""
        Margin=""5px"">
    </AnimatedPlaceholder>
    <AnimatedPlaceholder
        Id=""usage3b""
        Height=""50px""
        Width=""100px""
        Margin=""5px"">
    </AnimatedPlaceholder>
</div>

@code {

}",
        @<div>
            <AnimatedPlaceholder
                Id="usage3a"
                Height="50px"
                Width="100px"
                Margin="5px">
            </AnimatedPlaceholder>
            <AnimatedPlaceholder
                Id="usage3b"
                Height="50px"
                Width="100px"
                Margin="5px">
            </AnimatedPlaceholder>
        </div>)
    };
}

<Sampler
    ComponentName="AnimatedPlaceholder"
    ComponentCssName="animatedplaceholder"
    Description="@description"
    Features="@features"
    UsageText="Typical usages of the <code>AnimatedPlaceholder</code> component are shown below"
    UsageCodeList="@usageCode"
    ContentHeightPixels="275">
    <ExampleTemplate>
        <AnimatedPlaceholder
            Id="example1"
            Height="50px"
            Width="200px">
        </AnimatedPlaceholder>
    </ExampleTemplate>
</Sampler>

@code {

}
