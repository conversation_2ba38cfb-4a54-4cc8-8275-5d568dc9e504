@component @lister @lister_5
Feature: the lister can be sorted by multiple factors in ascending or descending order
    Scenario: the lister component can be sorted in ascending or descending order
        Given the user is at the home page
        And the user selects the "Lister" component
        And the lister component image matches the base image "default lister"
        And the row with content "000000 : TRT | 1 | PaclitaxTrt | 3 | 08/06/2016 e5f5f586" has row index 0
        When the user selects "Desc" from the dropdown showing "Asc"
        Then the row with content "000499 : TRT | 4 | PreCycBlood | 1 | 30/01/2019" has row index 0
        And the lister component image matches the base image "sorted in descending order"
        When the user selects "Asc" from the dropdown showing "Desc"
        Then the row with content "000000 : TRT | 1 | PaclitaxTrt | 3 | 08/06/2016 e5f5f586" has row index 0
        And the lister component image matches the base image "default lister"
        
    Scenario: the lister component can be sorted by multiple factors
        Given the user is at the home page
        And the user selects the "Lister" component
        And the lister component image matches the base image "default lister"
        And the row with content "000000 : TRT | 1 | PaclitaxTrt | 3 | 08/06/2016 e5f5f586" has row index 0
        When the user selects "Id" from the dropdown showing "NameAsString"
        Then the row with content "000133 : TRT | 4 | PreCycBlood | 1 | 07/07/2017 01d15899" has row index 0
        And the lister component image matches the base image "sorted by Id"
        When the user selects "NameAsString" from the dropdown showing "Id"
        Then the row with content "000000 : TRT | 1 | PaclitaxTrt | 3 | 08/06/2016 e5f5f586" has row index 0
        And the lister component image matches the base image "default lister"



