﻿@using System.Runtime.CompilerServices
@using CCTC_Components.Components.CheckBox
@implements IAsyncDisposable
@inherits CCTC_Components.Components.__CCTC.CCTCBase

<CascadingValue Value="this">
    <cctc-concertina id="@Id" class="@CssClass" style="@Style" data-author="cctc">
        <cctc-concertina-header>
            @if (HeaderContent is not null)
            {
                <div>@HeaderContent</div>
            }

            <div class="ms-auto d-flex align-items-center">
                @if (CollapseOthersOption == CollapseOthersOption.UserOption)
                {
                    <CheckBox
                        TValue="bool"
                        Id="@($"{Id}-should-collapse-others")"
                        @bind-Value="@_collapseOthers"
                        Label="collapse others?"
                        CheckBoxLabelTooltipBehaviour="CheckBoxLabelTooltipBehaviour.Disabled">
                    </CheckBox>
                }
                @if (CanCollapseOrExpandAll)
                {
                    <div class="config-button" @onclick="ToggleExpandCollapseAll">
                        <div class="material-icons">@_collapseExpandIcon</div>
                    </div>
                }
            </div>
        </cctc-concertina-header>

        @if (SubHeaderContent is not null)
        {
            <cctc-concertina-subheader>@SubHeaderContent</cctc-concertina-subheader>
        }

        <div>@ChildContent</div>
    </cctc-concertina>
</CascadingValue>

@code {

    const string CollapseIcon = "keyboard_double_arrow_up";
    const string ExpandIcon = "keyboard_double_arrow_down";
    string _collapseExpandIcon = CollapseIcon;
    bool _collapseOthers;

    /// <summary>
    /// The content to display in the header
    /// </summary>
    [Parameter]
    public RenderFragment? HeaderContent { get; set; }

    /// <summary>
    /// The content to display in the sub header
    /// </summary>
    [Parameter]
    public RenderFragment? SubHeaderContent { get; set; }

    /// <summary>
    /// The content of the concertina item
    /// </summary>
    [Parameter, EditorRequired]
    public required RenderFragment ChildContent { get; set; }

    /// <summary>
    /// When true, items are expanded
    /// </summary>
    [Parameter]
    public bool Expanded { get; set; }

    /// <summary>
    /// When true, all items can be expanded or collapsed at once
    /// </summary>
    [Parameter]
    public bool CanCollapseOrExpandAll { get; set; }

    /// <summary>
    /// Determines how to handle the other items when an item is selected <see cref="CollapseOthersOption"/>
    /// </summary>
    [Parameter]
    public CollapseOthersOption CollapseOthersOption { get; set; }

    /// <summary>
    /// Invoked when an item is either collapsed or expanded
    /// </summary>
    [Parameter]
    public EventCallback<ConcertinaItem> OnCollapseOrExpand { get; set; }

    /// <summary>
    /// Determines how to handle collapsing others when an item is selected
    /// </summary>
    /// <returns>True if other items should be collapsed</returns>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    public bool ShouldCollapseOthers()
    {
        return CollapseOthersOption switch
        {
            CollapseOthersOption.DoNotCollapseOthers => false,
            CollapseOthersOption.CollapseOthers => true,
            CollapseOthersOption.UserOption => _collapseOthers,
            _ => throw new ArgumentOutOfRangeException(nameof(CollapseOthersOption))
        };
    }

    /// <summary>
    /// Returns true if all items are expanded
    /// </summary>
    public bool AllExpanded { get; private set; }

    void DoExpandCollapse(ConcertinaItem concertinaItem)
    {
        if (AllExpanded)
        {
            concertinaItem.ForceExpand();
        }
        else
        {
            concertinaItem.ForceCollapse();
        }
    }

    void UpdateIcon()
    {
        _collapseExpandIcon = AllExpanded ? CollapseIcon : ExpandIcon;
        StateHasChanged();
    }

    void ToggleExpandCollapseAll()
    {
        AllExpanded = !AllExpanded;
        UpdateIcon();

        foreach (var concertinaItem in _children)
        {
            DoExpandCollapse(concertinaItem);
        }

        StateHasChanged();
    }

    List<ConcertinaItem> _children = new();

    /// <summary>
    /// Used by a child to register itself
    /// </summary>
    /// <param name="child">The child <see cref="ConcertinaItem"/> to register</param>
    public void RegisterChild(ConcertinaItem child)
    {
        if (!_children.Exists(r => r.Id == child.Id))
        {
            _children.Add(child);
            DoExpandCollapse(child);
        }
    }

    List<ConcertinaItem> CollapsableItems => _children.Where(x => x.CanCollapseExpand).ToList();

    /// <summary>
    /// Checks the icon to show the correct state
    /// </summary>
    public void CheckIconState()
    {
        var collapsable = CollapsableItems;

        if (collapsable.All(x => x.IsExpanded))
        {
            AllExpanded = true;
            UpdateIcon();
        }
        else if (collapsable.All(x => !x.IsExpanded))
        {
            AllExpanded = false;
            UpdateIcon();
        }

        StateHasChanged();
    }

    /// <summary>
    /// Allows a child to collapse the others when selected
    /// </summary>
    /// <param name="selectedId">The id of the selected child item</param>
    public void CollapseOthersOnSelected(string selectedId)
    {
        foreach (var collapsableItem in CollapsableItems)
        {
            if (collapsableItem.Id != selectedId)
            {
                collapsableItem.ForceCollapse();
            }
        }
    }

    /// <inheritdoc />
    protected override void OnInitialized()
    {
        AllExpanded = Expanded;
        UpdateIcon();
    }
}