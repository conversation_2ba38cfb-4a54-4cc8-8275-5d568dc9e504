﻿@using System.Reactive.Subjects
@using System.Reactive.Linq
@using CCTC_Components.Components.__CCTC.Models
@using static Common.Helpers.StringMask
@using CCTC_Lib.Contracts.Reactive
@using CCTC_Lib.Enums.UI
@inject ISchedulerProvider SchedulerProvider
@inherits CCTC_Components.Components.__CCTC.CCTCBase
@implements IDisposable

@namespace CCTC_Components.Components.TextBox

@code {

    /// <summary>
    /// The input value
    /// </summary>
    [Parameter]
    public string? Value { get; set; }

    /// <summary>
    /// A callback which fires when the input value changes
    /// </summary>
    [Parameter]
    public EventCallback<string?> ValueChanged { get; set; }

    /// <summary>
    /// A second callback which fires when the input value changes. Useful when consuming using @bind-Value
    /// </summary>
    [Parameter]
    public EventCallback<ChangeEventArgs> TextChanged { get; set; }

    /// <summary>
    /// Sets the bind event. Defaults to OnChange except when the component is Reactive or a Mask is applied
    /// </summary>
    [Parameter]
    public BindEvent BindEvent { get; set; }

    /// <summary>
    /// Disabled if true
    /// </summary>
    [Parameter]
    public bool Disabled { get; set; }

    /// <summary>
    /// Read-only if true
    /// </summary>
    [Parameter]
    public bool ReadOnly { get; set; }

    /// <summary>
    /// Hides the read-only icon when <see cref="ReadOnly"/> is true
    /// </summary>
    [Parameter]
    public bool HideReadOnlyIcon { get; set; }

    /// <summary>
    /// The maximum character length
    /// </summary>
    [Parameter]
    public int MaxLength { get; set; }

    /// <summary>
    /// The Throttle speed
    /// </summary>
    [Parameter]
    public int ThrottleMs { get; set; }

    /// <summary>
    /// When set to true will not allow all existing text to be cleared
    /// </summary>
    [Parameter]
    public bool PreventWhitespace { get; set; }

    /// <summary>
    /// Adds a placeholder
    /// </summary>
    [Parameter]
    public string? Placeholder { get; set; }

    /// <summary>
    /// Constrains input to the supplied Mask)
    /// </summary>
    [Parameter]
    public string? Mask { get; set; }

    /// <summary>
    /// Apply attributes to the input element
    /// </summary>
    [Parameter]
    public Dictionary<string, object>? InputAttributes { get; set; }

    /// <summary>
    /// The <see cref="ElementRef"/> of the component
    /// </summary>
    public ElementReference ElementRef { get; set; }
    Subject<string?>? _reactiveValue = new();
    bool _isReactive;
    BindEvent _bindEvent;
    string? _textValue;
    int _throttleMs;

    /// <summary>
    /// The text value
    /// </summary>
    protected string? TextValue
    {
        get => _textValue;
        set
        {
            _textValue = value;

            if (_isReactive && _reactiveValue is not null)
            {
                _reactiveValue.OnNext(value);
            }
        }
    }

    int GetThrottleMs()
    {
        //500 ms is the minimum throttle speed for masked input
        return !string.IsNullOrWhiteSpace(Mask) && ThrottleMs <= 500 ? 500 : ThrottleMs;
    }

    /// <summary>
    /// Processes the previous and current value and returns the value to be supplied to registered callbacks
    /// </summary>
    /// <param name="previousValue"></param>
    /// <param name="currentValue"></param>
    /// <param name="preventWhitespace"></param>
    /// <param name="mask"></param>
    /// <returns></returns>
    string? ProcessValues(string? previousValue, string? currentValue, bool preventWhitespace, string? mask)
    {
        if (!preventWhitespace && string.IsNullOrWhiteSpace(currentValue))
        {
            return null;
        }
        else if (string.IsNullOrWhiteSpace(mask))
        {
            return preventWhitespace && string.IsNullOrWhiteSpace(currentValue)
                ? previousValue
                : currentValue;
        }
        else
        {
            return preventWhitespace && string.IsNullOrWhiteSpace(currentValue)
                ? toMasked(Mask, previousValue)
                : toMasked(Mask, currentValue);
        }
    }

    /// <summary>
    /// Runs when the value is changed
    /// </summary>
    /// <param name="args">The <see cref="ChangeEventArgs"/> from the event</param>
    /// <param name="actualBindEvent"></param>
    protected async Task OnValueChanged(ChangeEventArgs args, BindEvent actualBindEvent)
    {
        if (_bindEvent == actualBindEvent)
        {
            string? previousValue = TextValue;
            string? currentValue = args.Value!.ToString();
            TextValue = currentValue;

            if (!_isReactive)
            {
                var callbackValue = ProcessValues(previousValue, currentValue, PreventWhitespace, Mask);
                await ValueChanged.InvokeAsync(callbackValue);
                await TextChanged.InvokeAsync(new ChangeEventArgs() { Value = callbackValue });
            }
        }
    }

    /// <summary>
    /// Invokes the Focus callback
    /// </summary>
    /// <param name="args"><see cref="FocusEventArgs"/></param>
    protected async Task OnFocus(FocusEventArgs args)
    {
        await Focus.InvokeAsync(args);
    }

    /// <summary>
    /// Invokes the Blur callback
    /// </summary>
    /// <param name="args"><see cref="FocusEventArgs"/></param>
    protected async Task OnBlur(FocusEventArgs args)
    {
        await Blur.InvokeAsync(args);
        ForceComplete();
    }

    /// <summary>
    /// Forces the reactive Subject to complete
    /// </summary>
    /// <remarks>Only applies when the component is using Reactive framework</remarks>
    public void ForceComplete()
    {
        if (_isReactive && _reactiveValue is not null)
        {
            _reactiveValue.OnCompleted();
        }

        SetSubscription();
    }

    void SetSubscription()
    {
        _reactiveValue = new Subject<string?>();

        var seed = TextValue;

        _reactiveValue
            .Throttle(TimeSpan.FromMilliseconds(_throttleMs), SchedulerProvider.Default)
            .DistinctUntilChanged()
            .Scan((seed, seed),
                (acc, current) => (acc.Item2, current))
            .Subscribe(prevCurr =>
            {
                var (prev, curr) = prevCurr;
                var callbackValue = ProcessValues(prev, curr, PreventWhitespace, Mask);

                InvokeAsync(() => ValueChanged.InvokeAsync(callbackValue));
                InvokeAsync(() => TextChanged.InvokeAsync(new ChangeEventArgs { Value = callbackValue }));
            });
    }

    List<string> ReservedInputAttributes =>
        new()
        {
            "id", "type", "value", "change", "input", "focus", "blur", "class", "disabled", "readonly",
            "maxlength", "placeholder", "autocomplete", "rows",
        };

    /// <summary>
    /// Checks that only legitimate attributes are used when passing additional attributes
    /// </summary>
    /// <param name="reservedInputAttributes">The reserved list of attributes</param>
    /// <returns>A tuple of (valid, list of issues if any)</returns>
    protected (bool valid, List<string>? issues) CheckInputAttributesAreValid(IEnumerable<string> reservedInputAttributes)
    {
        var disallowedAttributes = InputAttributes?.Keys.Intersect(reservedInputAttributes).ToList();
        return (disallowedAttributes is null || !disallowedAttributes.Any(), disallowedAttributes);
    }

    /// <inheritdoc/>
    protected override void OnInitialized()
    {
        _throttleMs = GetThrottleMs();
        _isReactive = _throttleMs > 0;
        _bindEvent = _isReactive || !string.IsNullOrWhiteSpace(Mask) ? BindEvent.OnInput : BindEvent;

        var (valid, disallowedAttributes) = CheckInputAttributesAreValid(ReservedInputAttributes);
        if(!valid)
        {
            throw new ArgumentException($"One or more reserved attributes have been used and therefore will not be applied. " +
                                        $"Use a component parameter instead, if available. " +
                                        $"The following reserved attributes have been used: " +
                                        $"{string.Join(", ", disallowedAttributes!)}", nameof(InputAttributes));
        }
    }

    /// <inheritdoc/>
    protected override void OnParametersSet()
    {
        _textValue = toMasked(Mask, Value);

        if (_isReactive)
        {
            SetSubscription();
        }
    }

    /// <inheritdoc />
    public void Dispose()
    {
        if (_reactiveValue is not null)
        {
            _reactiveValue.Dispose();
            _reactiveValue = null;
        }
    }
}