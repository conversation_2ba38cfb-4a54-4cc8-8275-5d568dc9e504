﻿@page "/progresssample"
@using System.Reactive.Linq
@implements IDisposable

@{
    var description = new List<string>
    {
        "A component for displaying progress"
    };

    var features = new List<(string, string)>
    {
        ("Progress bar", "The progress bar can be indeterminate or display the provided value"),
        ("Display", "Show or hide the progress content. Optionally provide a label and choose whether to display a progress percentage value in addition to the progress bar")
    };

    var usageCode = new List<(string title, string description, string code, RenderFragment fragment)>
    {
        ("Value: variable, Max: 100 (default), show value (default)", "",
@"<div>
    <Progress
        Id=""usage1""
        Show=""true""
        Value=""@_usage1Value""
        Label=""@Label""
        ShowValue=""true"">
    </Progress>
    <div class=""mt-3 control-button"" @onclick=""RunUsage1"">Initiate</div>
</div>

@code {

    IObservable<long>? _usage1Observable;
    IDisposable? _usage1SubscriptionHandle;
    bool _usage1Active;
    int _usage1Value = 0;

    string Label { get; set; } = ""Example progress bar label"";

    void RunUsage1()
    {
        SetUsage1Subscription();
    }

    void SetUsage1Subscription()
    {
        if (!_usage1Active)
        {
            _usage1Active = true;
            _usage1Observable =
               Observable
                   .Interval(TimeSpan.FromSeconds(0.25))
                   .Take(101)
                   .Finally(() => _usage1Active = false);

            _usage1SubscriptionHandle = _usage1Observable.Subscribe(x =>
            {
                _usage1Value = (int)x;
                StateHasChanged();
            });
        }
    }

    public void Dispose()
    {
        if (_usage1SubscriptionHandle is not null)
        {
            _usage1SubscriptionHandle.Dispose();
        }
    }
}",
        @<div>
            <Progress
                Id="usage1"
                Show="true"
                Value="@_usage1Value"
                Label="@Label"
                ShowValue="true">
            </Progress>
            <div class="mt-3 control-button" @onclick="RunUsage1">Initiate</div>
        </div>),
        ("Value: 50, Max: 500, show or hide progress content", "",
@"<div>
    <Progress
        Id=""usage2""
        Show=""_showUsage2""
        Max=""500""
        Value=""50""
        Label=""@Label""
        ShowValue=""true"">
    </Progress>
    <div class=""mt-3 control-button"" @onclick=""ToggleUsage2Show"">@_usage2ControlButtonText</div>
</div>

@code {

    string Label { get; set; } = ""Example progress bar label"";
    bool _showUsage2 = true;
    string? _usage2ControlButtonText = ""Hide"";

    void ToggleUsage2Show()
    {
        _showUsage2 = !_showUsage2;
        _usage2ControlButtonText = _usage2ControlButtonText switch
        {
            ""Hide"" => ""Show"",
            ""Show"" => ""Hide"",
            _ => string.Empty
        };
    }
}",
        @<div>
            <Progress
                Id="usage2"
                Show="_showUsage2"
                Max="500"
                Value="50"
                Label="@Label"
                ShowValue="true">
            </Progress>
            <div class="mt-3 control-button" @onclick="ToggleUsage2Show">@_usage2ControlButtonText</div>
        </div>),
        ("Value: 50, no label and no value", "",
@"<Progress
    Id=""usage3""
    Show=""true""
    Value=""50""
    ShowValue=""false"">
</Progress>

@code {

}",
        @<Progress
            Id="usage3"
            Show="true"
            Value="50"
            ShowValue="false">
        </Progress>),
        ("Value: not set, indeterminate", "",
@"<Progress
    Id=""usage4""
    Show=""true""
    Label=""@Label""
    IsIndeterminate=""true"">
</Progress>

@code {

    string Label { get; set; } = ""Example progress bar label"";
}",
        @<Progress
            Id="usage4"
            Show="true"
            Label="@Label"
            IsIndeterminate="true">
    </Progress>)
    };
}

<Sampler
    ComponentName="Progress"
    ComponentCssName="progress"
    Description="@description"
    Features="@features"
    UsageText="Typical usages of the <code>Progress</code> component are shown below"
    UsageCodeList="@usageCode"
    ContentHeightPixels="325">
    <ExampleTemplate>
        <div>
            <Progress
                Id="example1"
                Show="true"
                Value="@_example1Value"
                Label="@Label"
                ShowValue="true">
            </Progress>
            <div class="mt-3 control-button" @onclick="RunExample1">Initiate</div>
        </div>
    </ExampleTemplate>
</Sampler>

@code {

    IObservable<long>? _example1Observable;
    IDisposable? _example1SubscriptionHandle;
    bool _example1Active;
    int _example1Value = 0;

    IObservable<long>? _usage1Observable;
    IDisposable? _usage1SubscriptionHandle;
    bool _usage1Active;
    int _usage1Value = 0;

    string Label { get; set; } = "Example progress bar label";
    bool _showUsage2 = true;
    string? _usage2ControlButtonText = "Hide";

    void RunExample1()
    {
        SetExample1Subscription();
    }

    void RunUsage1()
    {
        SetUsage1Subscription();
    }

    void ToggleUsage2Show()
    {
        _showUsage2 = !_showUsage2;
        _usage2ControlButtonText = _usage2ControlButtonText switch
        {
            "Hide" => "Show",
            "Show" => "Hide",
            _ => string.Empty
        };
    }

    void SetExample1Subscription()
    {
        if (!_example1Active)
        {
            _example1Active = true;
            _example1Observable =
               Observable
                   .Interval(TimeSpan.FromSeconds(0.25))
                   .Take(101)
                   .Finally(() => _example1Active = false);

            _example1SubscriptionHandle = _example1Observable.Subscribe(x =>
            {
                _example1Value = (int)x;
                StateHasChanged();
            });
        }
    }

    void SetUsage1Subscription()
    {
        if (!_usage1Active)
        {
            _usage1Active = true;
            _usage1Observable =
               Observable
                   .Interval(TimeSpan.FromSeconds(0.25))
                   .Take(101)
                   .Finally(() => _usage1Active = false);

            _usage1SubscriptionHandle = _usage1Observable.Subscribe(x =>
            {
                _usage1Value = (int)x;
                StateHasChanged();
            });
        }
    }

    public void Dispose()
    {
        if (_example1SubscriptionHandle is not null)
        {
            _example1SubscriptionHandle.Dispose();
        }

        if (_usage1SubscriptionHandle is not null)
        {
            _usage1SubscriptionHandle.Dispose();
        }
    }
}
