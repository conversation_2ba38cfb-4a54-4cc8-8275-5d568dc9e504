@component @numeric @numeric_5
Feature: the numeric component can be made read-only and / or disabled. The read-only icon is optional
    Scenario: the numeric component can be made read-only
        Given the user is at the home page
        Given the user selects the "Numeric" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Read-only"
        Then the Numeric component is not editable
        And the Numeric component image matches the base image "numeric-readonly"

    Scenario: the numeric component can be disabled
        Given the user is at the home page
        Given the user selects the "Numeric" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Disabled"
        Then the Numeric component is disabled
        And the Numeric component image matches the base image "numeric-disabled"

    Scenario: the numeric component can be made read-only and disabled
        Given the user is at the home page
        Given the user selects the "Numeric" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Disabled and read-only"
        Then the Numeric component is not editable
        And the Numeric component is disabled
        And the Numeric component image matches the base image "numeric-readonly-disabled"

    Scenario: the numeric component can be made read-only without a read-only icon
        Given the user is at the home page
        Given the user selects the "Numeric" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Read-only long (hide read-only icon)"
        Then the Numeric component is not editable
        And the Numeric component image matches the base image "numeric-readonly-no-icon"

    Scenario: the numeric component can be made read-only and disabled without a read-only icon
        Given the user is at the home page
        Given the user selects the "Numeric" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Disabled and read-only (hide read-only icon)"
        Then the Numeric component is not editable
        And the Numeric component is disabled
        And the Numeric component image matches the base image "numeric-readonly-disabled-no-icon"