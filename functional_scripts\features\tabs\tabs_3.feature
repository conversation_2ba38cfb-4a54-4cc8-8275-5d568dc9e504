@component @tabs @tabs_3
Feature: the tabs headers can be placed in different locations in relation to the content
    Scenario: the tabs can be aligned top 
        Given the user is at the home page
        When the user selects the "Tabs" component in the container "Tabs"
        Then the "3 Tab" tab has position "top-item-header"
        And the Tabs component image matches the base image "tabs-3"

    Scenario: the tabs can be aligned left
        Given the user is at the home page
        When the user selects the "Tabs" component in the container "Tabs"
        And the user selects "Left" from the dropdown showing "Top"
        Then the "3 Tab" tab has position "left-item-header"
        And the Tabs component image matches the base image "tabs-3-left"

    Scenario: the tabs can be aligned right and tabs display the same text
        Given the user is at the home page
        When the user selects the "Tabs" component in the container "Tabs"
        And the user selects "Right" from the dropdown showing "Top"
        And the "3 Tab" tab has position "right-item-header"
        And the user clicks the "2 Tab" tab
        Then the tab content contains "This is some content in tab 2 styled with a dim grey background"
        And the Tabs component image matches the base image "tabs-2-right"
