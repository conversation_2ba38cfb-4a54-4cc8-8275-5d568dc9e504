﻿@page "/stepssample"

@{
    var description = new List<string>
    {
        "The Steps component represents a workflow. The user is presented with a series of Steps with each step representing some form " +
        "of data collection. The content of each step is governed by the child content in a <code>ProgressStep</code> whereas the configuration " +
        "governing each step is managed by a collection of <code>Step</code>. As the user completes data in each step, the progress continues until " +
        "the last step at which point the workflow can be considered 'done'. Data captured as part of the workflow is available to the caller once the " +
        "workflow is complete."
    };

    var features = new List<(string, string)>
    {
        ("Required", "A step can be marked as required or not"),
        ("Updateable", "A step can be updated based on the previous step. For example, the next step's name can be updated based on responses to the current step"),
        ("Data consistency", "As a user steps backwards through the steps, previously entered data in later steps can optionally be wiped"),
        ("Header names", "A header can have an updateable name (Name), or a fixed name (FixedName) which can never be updated")
    };

    var gotchas = new List<(string, string)>
    {
        ("Duplicate step numbers", "Steps must be given a unique step number"),
        ("Contiguous step numbers", "Steps must have consecutive step numbers beginning with 0")
    };

    var usageCode = new List<(string title, string description, string code, RenderFragment fragment)>
    {
        ("Required data", "Steps can be required or not required - the second step 'Motherboard' must have a value before progress can continue",
@"<Steps Id=""steps-sample-required"" @ref=""_stepsRef""
    OnStepCompleted=""StepCompleted""
    OnMovePrevious=""MovedPrevious""
    OnProgressCompleted=""Complete""
    ContentHeightPixels=""200""
    CircleRadius=""28""
    RequiredText=""req""
    OptionalText=""opt""
    ShowProgressControlsAtTop=""false"">

    <ProgressStep Step=""@_steps[0]"">
        <div class=""progress-step-content"">
            <label class=""data-label"">Case</label>
            <DropDown
                TData=""string""
                Id=""case_1""
                Data=""@(new List<string> { ""ASUS Prime AP201 - Black"", ""Fractal Design Focus 2 - White"", ""Corsair 3000D AIRFLOW - White"" })""
                @bind-Value=""@_steps[0].Value""
                SelectionChanged=""OnCaseChanged"">
            </DropDown>
        </div>
    </ProgressStep>
    <ProgressStep Step=""@_steps[1]"">
        <div class=""progress-step-content"">
            <label class=""data-label"">Motherboard</label>
            <DropDown
                TData=""string""
                Id=""motherboard_1""
                Data=""@(new List<string> { ""MSI PRO B760M-A WIFI DDR4"", ""Gigabyte B760M AORUS ELITE AX"", ""ASUS Prime B760M-A WIFI"" })""
                @bind-Value=""@_steps[1].Value""
                SelectionChanged=""OnMotherboardChanged"">
            </DropDown>
        </div>
    </ProgressStep>
    <ProgressStep Step=""@_steps[2]"">
        <div class=""progress-step-content"">
            <label class=""data-label"">CPU</label>
            <DropDown
                TData=""string""
                Id=""cpu_1""
                Data=""@(new List<string> { ""Intel Core i3 12100F"", ""Intel Core i5 12400F"", ""Intel Core i7 13700F"" })""
                @bind-Value=""@_steps[2].Value""
                SelectionChanged=""OnProcessorChanged"">
            </DropDown>
        </div>
    </ProgressStep>
</Steps>

@code {

    Steps? _stepsRef;
    List<Step> _steps = new();

    protected override void OnInitialized()
    {
        CreateSteps();
    }

    void StepCompleted(Step completedStep)
    {
        Console.WriteLine($""step completed {completedStep}"");
    }

    void MovedPrevious(Step movedFrom)
    {
        Console.WriteLine($""step back from {movedFrom}"");
    }

    void Complete(IEnumerable<Step> completedSteps)
    {
        Console.WriteLine($""progress complete {completedSteps}"");
    }

    void CreateSteps()
    {
        _steps.Clear();
        _steps =
        [
            new Step(0, false, null, ""Case""),
            new Step(1, true, null, ""Motherboard""),
            new Step(2, false, null, ""CPU"")
        ];
    }

    void OnCaseChanged(ChangeEventArgs args)
    {
        var value = args.Value?.ToString();

        var step = _steps[0];
        step.Value = value;
        step.Data = value;
        _stepsRef?.MarkStepAsDone(step);
    }

    void OnMotherboardChanged(ChangeEventArgs args)
    {
        var value = args.Value?.ToString();

        var step = _steps[1];
        step.Value = value;
        step.Data = value;
        _stepsRef?.MarkStepAsDone(step);
    }

    void OnProcessorChanged(ChangeEventArgs args)
    {
        var value = args.Value?.ToString();

        var step = _steps[2];
        step.Value = value;
        step.Data = value;
        _stepsRef?.MarkStepAsDone(step);
    }
}",
        @<Steps Id="steps-sample-required" @ref="_stepsRef"
            OnStepCompleted="StepCompleted"
            OnMovePrevious="MovedPrevious"
            OnProgressCompleted="Complete"
            ContentHeightPixels="200"
            CircleRadius="28"
            RequiredText="req"
            OptionalText="opt"
            ShowProgressControlsAtTop="false">

            <ProgressStep Step="@_steps[0]">
                <div class="progress-step-content">
                    <label class="data-label">Case</label>
                    <DropDown
                        TData="string"
                        Id="case_1"
                        Data="@(new List<string> { "ASUS Prime AP201 - Black", "Fractal Design Focus 2 - White", "Corsair 3000D AIRFLOW - White" })"
                        @bind-Value="@_steps[0].Value"
                        SelectionChanged="OnCaseChanged">
                    </DropDown>
                </div>
            </ProgressStep>
            <ProgressStep Step="@_steps[1]">
                <div class="progress-step-content">
                    <label class="data-label">Motherboard</label>
                    <DropDown
                        TData="string"
                        Id="motherboard_1"
                        Data="@(new List<string> { "MSI PRO B760M-A WIFI DDR4", "Gigabyte B760M AORUS ELITE AX", "ASUS Prime B760M-A WIFI" })"
                        @bind-Value="@_steps[1].Value"
                        SelectionChanged="OnMotherboardChanged">
                    </DropDown>
                </div>
            </ProgressStep>
            <ProgressStep Step="@_steps[2]">
                <div class="progress-step-content">
                    <label class="data-label">CPU</label>
                    <DropDown
                        TData="string"
                        Id="cpu_1"
                        Data="@(new List<string> { "Intel Core i3 12100F", "Intel Core i5 12400F", "Intel Core i7 13700F" })"
                        @bind-Value="@_steps[2].Value"
                        SelectionChanged="OnProcessorChanged">
                    </DropDown>
                </div>
            </ProgressStep>
        </Steps>),
        ("UI configurable", "Smaller headers, larger content area, progress controls at top, changed optional and required tabs",
@"<Steps Id=""steps-sample-ui"" @ref=""_stepsRef""
    OnStepCompleted=""StepCompleted""
    OnMovePrevious=""MovedPrevious""
    OnProgressCompleted=""Complete""
    ContentHeightPixels=""300""
    CircleRadius=""20""
    RequiredText=""required""
    OptionalText=""optional""
    ShowProgressControlsAtTop=""true"">

    <ProgressStep Step=""@_steps[0]"">
        <div class=""progress-step-content"">
            <label class=""data-label"">Case</label>
            <DropDown
                TData=""string""
                Id=""case_2""
                Data=""@(new List<string> { ""ASUS Prime AP201 - Black"", ""Fractal Design Focus 2 - White"", ""Corsair 3000D AIRFLOW - White"" })""
                @bind-Value=""@_steps[0].Value""
                SelectionChanged=""OnCaseChanged"">
            </DropDown>
        </div>
    </ProgressStep>
    <ProgressStep Step=""@_steps[1]"">
        <div class=""progress-step-content"">
            <label class=""data-label"">Motherboard</label>
            <DropDown
                TData=""string""
                Id=""motherboard_2""
                Data=""@(new List<string> { ""MSI PRO B760M-A WIFI DDR4"", ""Gigabyte B760M AORUS ELITE AX"", ""ASUS Prime B760M-A WIFI"" })""
                @bind-Value=""@_steps[1].Value""
                SelectionChanged=""OnMotherboardChanged"">
            </DropDown>
        </div>
    </ProgressStep>
    <ProgressStep Step=""@_steps[2]"">
        <div class=""progress-step-content"">
            <label class=""data-label"">CPU</label>
            <DropDown
                TData=""string""
                Id=""cpu_2""
                Data=""@(new List<string> { ""Intel Core i3 12100F"", ""Intel Core i5 12400F"", ""Intel Core i7 13700F"" })""
                @bind-Value=""@_steps[2].Value""
                SelectionChanged=""OnProcessorChanged"">
            </DropDown>
        </div>
    </ProgressStep>
</Steps>

@code {

    Steps? _stepsRef;
    List<Step> _steps = new();

    protected override void OnInitialized()
    {
        CreateSteps();
    }

    void StepCompleted(Step completedStep)
    {
        Console.WriteLine($""step completed {completedStep}"");
    }

    void MovedPrevious(Step movedFrom)
    {
        Console.WriteLine($""step back from {movedFrom}"");
    }

    void Complete(IEnumerable<Step> completedSteps)
    {
        Console.WriteLine($""progress complete {completedSteps}"");
    }

    void CreateSteps()
    {
        _steps.Clear();
        _steps =
        [
            new Step(0, false, null, ""Case""),
            new Step(1, true, null, ""Motherboard""),
            new Step(2, false, null, ""CPU"")
        ];
    }

    void OnCaseChanged(ChangeEventArgs args)
    {
        var value = args.Value?.ToString();

        var step = _steps[0];
        step.Value = value;
        step.Data = value;
        _stepsRef?.MarkStepAsDone(step);
    }

    void OnMotherboardChanged(ChangeEventArgs args)
    {
        var value = args.Value?.ToString();

        var step = _steps[1];
        step.Value = value;
        step.Data = value;
        _stepsRef?.MarkStepAsDone(step);
    }

    void OnProcessorChanged(ChangeEventArgs args)
    {
        var value = args.Value?.ToString();

        var step = _steps[2];
        step.Value = value;
        step.Data = value;
        _stepsRef?.MarkStepAsDone(step);
    }
}",
        @<Steps Id="steps-sample-ui" @ref="_stepsRef"
            OnStepCompleted="StepCompleted"
            OnMovePrevious="MovedPrevious"
            OnProgressCompleted="Complete"
            ContentHeightPixels="300"
            CircleRadius="20"
            RequiredText="required"
            OptionalText="optional"
            ShowProgressControlsAtTop="true">

            <ProgressStep Step="@_steps[0]">
                <div class="progress-step-content">
                    <label class="data-label">Case</label>
                    <DropDown
                        TData="string"
                        Id="case_2"
                        Data="@(new List<string> { "ASUS Prime AP201 - Black", "Fractal Design Focus 2 - White", "Corsair 3000D AIRFLOW - White" })"
                        @bind-Value="@_steps[0].Value"
                        SelectionChanged="OnCaseChanged">
                    </DropDown>
                </div>
            </ProgressStep>
            <ProgressStep Step="@_steps[1]">
                <div class="progress-step-content">
                    <label class="data-label">Motherboard</label>
                    <DropDown
                        TData="string"
                        Id="motherboard_2"
                        Data="@(new List<string> { "MSI PRO B760M-A WIFI DDR4", "Gigabyte B760M AORUS ELITE AX", "ASUS Prime B760M-A WIFI" })"
                        @bind-Value="@_steps[1].Value"
                        SelectionChanged="OnMotherboardChanged">
                    </DropDown>
                </div>
            </ProgressStep>
            <ProgressStep Step="@_steps[2]">
                <div class="progress-step-content">
                    <label class="data-label">CPU</label>
                    <DropDown
                        TData="string"
                        Id="cpu_2"
                        Data="@(new List<string> { "Intel Core i3 12100F", "Intel Core i5 12400F", "Intel Core i7 13700F" })"
                        @bind-Value="@_steps[2].Value"
                        SelectionChanged="OnProcessorChanged">
                    </DropDown>
                </div>
            </ProgressStep>
        </Steps>),
        ("Updateable name, without wiping data when moving back", "A step can be updated based on the previous step - the 'Application Submission' step name is updated based on responses to the 'Job Type' step",
@"<Steps Id=""steps-sample-updateable"" @ref=""_stepsRef""
    OnStepCompleted=""StepCompleted""
    OnMovePrevious=""MovedPrevious""
    OnProgressCompleted=""Complete""
    ContentHeightPixels=""200""
    CircleRadius=""28""
    RequiredText=""req""
    OptionalText=""opt""
    ShowProgressControlsAtTop=""false""
    WipeOnPrevious=""false"">

    <ProgressStep Step=""@_updateableSteps[0]"">
        <div class=""progress-step-content"">
            <label class=""data-label"">@_updateableSteps[0].FixedName</label>
            <DropDown TData=""string""
                Id=""job-type""
                Data=""@(new List<string> { ""Full-time"", ""Freelance"" })""
                Value=""@_updateableSteps[0].Value""
                SelectionChanged=""OnJobTypeChanged"">
            </DropDown>
        </div>
    </ProgressStep>
    <ProgressStep Step=""@_updateableSteps[1]"">
        <div class=""progress-step-content"">
            <label class=""data-label"">@_updateableSteps[1].Name</label>
            <DropDown TData=""string""
                Id=""upload-options""
                Data=""@(new List<string> { ""Upload document"", ""Send via email"" })""
                Value=""@_updateableSteps[1].Value""
                SelectionChanged=""OnApplicationSubmissionChanged"">
            </DropDown>
        </div>
</ProgressStep>
</Steps>

@code {

    Steps? _stepsRef;
    List<Step> _updateableSteps = new();

    protected override void OnInitialized()
    {
        CreateSteps();
    }

    void StepCompleted(Step completedStep)
    {
        Console.WriteLine($""step completed {completedStep}"");
    }

    void MovedPrevious(Step movedFrom)
    {
        Console.WriteLine($""step back from {movedFrom}"");
        _complete.Clear();
        StateHasChanged();
    }

    void Complete(IEnumerable<Step> completedSteps)
    {
        _complete.Clear();

        foreach (var step in completedSteps)
        {
            _complete.Add($""num: {step.StepNum}, value: {step.Data}"");
        }
    }

    void CreateSteps()
    {
        _updateableSteps.Clear();
        _updateableSteps =
        [
            new Step(0, true, null, ""Job Type""),
            new Step(1, false, ""Application Submission"")
        ];
    }

    void OnJobTypeChanged(ChangeEventArgs args)
    {
        var value = args.Value?.ToString();
        var step = _updateableSteps[0];
        step.Value = value;
        step.Data = value;
        _updateableSteps[1].Name =
            step.Value switch
            {
                ""Full-time"" => ""Share your CV"",
                _ => ""Share your Portfolio""
            };
        _stepsRef?.MarkStepAsDone(step);
    }

    void OnApplicationSubmissionChanged(ChangeEventArgs args)
    {
        var value = args.Value?.ToString();
        var step = _updateableSteps[1];
        step.Value = value;
        step.Data = value;
        _stepsRef?.MarkStepAsDone(step);
    }
}",
        @<Steps Id="steps-sample-updateable" @ref="_stepsRef"
            OnStepCompleted="StepCompleted"
            OnMovePrevious="MovedPrevious"
            OnProgressCompleted="Complete"
            ContentHeightPixels="200"
            CircleRadius="28"
            RequiredText="req"
            OptionalText="opt"
            ShowProgressControlsAtTop="false"
            WipeOnPrevious="false">

            <ProgressStep Step="@_updateableSteps[0]">
                <div class="progress-step-content">
                    <label class="data-label">@_updateableSteps[0].FixedName</label>
                    <DropDown TData="string"
                        Id="job-type"
                        Data="@(new List<string> { "Full-time", "Freelance" })"
                        Value="@_updateableSteps[0].Value"
                        SelectionChanged="OnJobTypeChanged">
                    </DropDown>
                </div>
            </ProgressStep>
            <ProgressStep Step="@_updateableSteps[1]">
                <div class="progress-step-content">
                    <label class="data-label">@_updateableSteps[1].Name</label>
                    <DropDown TData="string"
                        Id="upload-options"
                        Data="@(new List<string> { "Upload document", "Send via email" })"
                        Value="@_updateableSteps[1].Value"
                        SelectionChanged="OnApplicationSubmissionChanged">
                    </DropDown>
                </div>
        </ProgressStep>
        </Steps>)
    };

    var relatedComponents = new List<(Relation relation, string display, string url, string description)>
    {
        (Relation.Child, "ProgressStep", "progressstepsample", "A collection of ProgressSteps that together create a complete Steps workflow"),
    };
}

<div required-so-deep-works>
    <Sampler
        ComponentName="Steps"
        ComponentCssName="steps"
        Description="@description"
        UsageText="Typical usages of the <code>Steps</code> component are shown below"
        UsageCodeList="@usageCode"
        ContentHeightPixels="350"
        Features="@features"
        Gotchas="@gotchas"
        RelatedComponents="relatedComponents"
        OnTabItemSelected="TabItemSelected">
        <ExampleTemplate>

            <div class="m-1">
                <Steps Id="steps-sample" @ref="_stepsRef"
                    OnStepCompleted="StepCompleted"
                    OnMovePrevious="MovedPrevious"
                    OnProgressCompleted="Complete"
                    ContentHeightPixels="200"
                    CircleRadius="28"
                    RequiredText="req"
                    OptionalText="opt"
                    ShowProgressControlsAtTop="false">

                    <ProgressStep Step="@_steps[0]">
                        <div class="progress-step-content">
                            <label class="data-label">Case</label>
                            <DropDown
                                TData="string"
                                Id="case"
                                Data="@(new List<string> { "ASUS Prime AP201 - Black", "Fractal Design Focus 2 - White", "Corsair 3000D AIRFLOW - White" })"
                                @bind-Value="@_steps[0].Value"
                                SelectionChanged="OnCaseChanged">
                            </DropDown>
                        </div>
                    </ProgressStep>
                    <ProgressStep Step="@_steps[1]">
                        <div class="progress-step-content">
                            <label class="data-label">Motherboard</label>
                            <DropDown
                                TData="string"
                                Id="motherboard"
                                Data="@(new List<string> { "MSI PRO B760M-A WIFI DDR4", "Gigabyte B760M AORUS ELITE AX", "ASUS Prime B760M-A WIFI" })"
                                @bind-Value="@_steps[1].Value"
                                SelectionChanged="OnMotherboardChanged">
                            </DropDown>
                        </div>
                    </ProgressStep>
                    <ProgressStep Step="@_steps[2]">
                        <div class="progress-step-content">
                            <label class="data-label">CPU</label>
                            <DropDown
                                TData="string"
                                Id="cpu"
                                Data="@(new List<string> { "Intel Core i3 12100F", "Intel Core i5 12400F", "Intel Core i7 13700F" })"
                                @bind-Value="@_steps[2].Value"
                                SelectionChanged="OnProcessorChanged">
                            </DropDown>
                        </div>
                    </ProgressStep>
                    <ProgressStep Step="@_steps[3]">
                        <div class="progress-step-content">
                            <label class="data-label">Memory</label>
                            <DropDown
                                TData="string"
                                Id="memory"
                                Data="@(new List<string> { "Kingston FURY Beast 16GB", "Corsair Vengeance 32GB", "Kingston FURY Renegade 32GB", "Kingston FURY Renegade RGB 32GB" })"
                                @bind-Value="@_steps[3].Value"
                                SelectionChanged="OnMemoryChanged">
                            </DropDown>
                        </div>
                    </ProgressStep>
                    <ProgressStep Step="@_steps[4]">
                        <div class="progress-step-content">
                            <label class="data-label">SSD Storage</label>
                            <DropDown
                                TData="string"
                                Id="storage"
                                Data="@(new List<string> { "Solidigm P41 Plus M.2-2280 512GB", "Solidigm P41 Plus M.2-2280 1TB", "Samsung 990 PRO M.2-2280 1TB" })"
                                @bind-Value="@_steps[4].Value"
                                SelectionChanged="OnStorageChanged">
                            </DropDown>
                        </div>
                    </ProgressStep>
                    <ProgressStep Step="@_steps[5]">
                        <div class="progress-step-content">
                            <label class="data-label">Power Supply</label>
                            <DropDown
                                TData="string"
                                Id="powersupply"
                                Data="@(new List<string> { "Be Quiet! System Power 10 550W", "Corsair CX Series CX750 750W" })"
                                @bind-Value="@_steps[5].Value"
                                SelectionChanged="OnPowerChanged">
                            </DropDown>
                        </div>

                        <div class="progress-step-content mt-4">
                            @if (_complete.Any())
                            {
                                <div>Completed</div>
                                <ul>
                                    @foreach (var step in _complete)
                                    {
                                        <li>@step</li>
                                    }
                                </ul>
                            }
                            else
                            {
                                <div>Not yet complete</div>
                            }
                        </div>
                    </ProgressStep>
                </Steps>

                <hr/>

                @if (_stepsRef is not null)
                {
                    <div>current values: </div>
                    <div class="d-flex flex-column">
                        @foreach (var step in _stepsRef.GetChildren())
                        {
                            <div>@step.Step.ToString()</div>
                        }
                    </div>
                }

            </div>

        </ExampleTemplate>
    </Sampler>
</div>

@code {

    Steps? _stepsRef;
    List<Step> _steps = new();
    List<Step> _updateableSteps = new();
    readonly List<string> _complete = new();

    protected override void OnInitialized()
    {
        CreateSteps();
    }

    void StepCompleted(Step completedStep)
    {
        Console.WriteLine($"step completed {completedStep}");
    }

    void MovedPrevious(Step movedFrom)
    {
        Console.WriteLine($"step back from {movedFrom}");
        _complete.Clear();
        StateHasChanged();
    }

    void Complete(IEnumerable<Step> completedSteps)
    {
        _complete.Clear();

        foreach (var step in completedSteps)
        {
            _complete.Add($"num: {step.StepNum}, value: {step.Data}");
        }
    }

    void CreateSteps()
    {
        _steps.Clear();
        _steps =
        [
            new Step(0, false, null, "Case"),
            new Step(1, true, null, "Motherboard"),
            new Step(2, false, null, "CPU"),
            new Step(3, false, null, "Memory that is much longer than the rest"),
            new Step(4, false, null, "SSD Storage"),
            new Step(5, true, null, "Power Supply")
        ];

        _updateableSteps.Clear();
        _updateableSteps =
        [
            new Step(0, true, null, "Job Type"),
            new Step(1, false, "Application Submission")
        ];
    }

    void TabItemSelected()
    {
        CreateSteps();
    }

    void OnCaseChanged(ChangeEventArgs args)
    {
        var value = args.Value?.ToString();
        var step = _steps[0];
        step.Value = value;
        step.Data = value;
        _stepsRef?.MarkStepAsDone(step);
    }

    void OnMotherboardChanged(ChangeEventArgs args)
    {
        var value = args.Value?.ToString();
        var step = _steps[1];
        step.Value = value;
        step.Data = value;
        _stepsRef?.MarkStepAsDone(step);
    }

    void OnProcessorChanged(ChangeEventArgs args)
    {
        var value = args.Value?.ToString();
        var step = _steps[2];
        step.Value = value;
        step.Data = value;
        _stepsRef?.MarkStepAsDone(step);
    }

    void OnMemoryChanged(ChangeEventArgs args)
    {
        var value = args.Value?.ToString();
        var step = _steps[3];
        step.Value = value;
        step.Data = value;
        _stepsRef?.MarkStepAsDone(step);
    }

    void OnStorageChanged(ChangeEventArgs args)
    {
        var value = args.Value?.ToString();
        var step = _steps[4];
        step.Value = value;
        step.Data = value;
        _stepsRef?.MarkStepAsDone(step);
    }

    void OnPowerChanged(ChangeEventArgs args)
    {
        var value = args.Value?.ToString();
        var step = _steps[5];
        step.Value = value;
        step.Data = value;
        _stepsRef?.MarkStepAsDone(step);
    }

    void OnJobTypeChanged(ChangeEventArgs args)
    {
        var value = args.Value?.ToString();
        var step = _updateableSteps[0];
        step.Value = value;
        step.Data = value;
        _updateableSteps[1].Name =
            step.Value switch
            {
                "Full-time" => "Share your CV",
                _ => "Share your Portfolio"
            };
        _stepsRef?.MarkStepAsDone(step);
    }

    void OnApplicationSubmissionChanged(ChangeEventArgs args)
    {
        var value = args.Value?.ToString();
        var step = _updateableSteps[1];
        step.Value = value;
        step.Data = value;
        _stepsRef?.MarkStepAsDone(step);
    }
}