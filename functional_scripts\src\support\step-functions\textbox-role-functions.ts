import { ICustomWorld } from '../custom-world';
import { compareToBaseImage } from '../../utils/compareImages';
import * as Helpers from '../helper-functions';
import { expect } from '@playwright/test';

export async function focus(customWorld: ICustomWorld) {
  await Helpers.getSamplerTabsSelectedContent(customWorld).getByRole('textbox').focus();
}

export async function inputText(customWorld: ICustomWorld, value: string) {
  await Helpers.getSamplerTabsSelectedContent(customWorld).getByRole('textbox').fill(value);
}

export async function assertHasValue(customWorld: ICustomWorld, expectedValue: string) {
  const textInput = Helpers.getSamplerTabsSelectedContent(customWorld).getByRole('textbox');
  await expect(textInput).toHaveValue(expectedValue);
}

export async function assertHasStableValue(
  customWorld: ICustomWorld,
  expectedValue: string,
  maxWait = 1000
) {
  await customWorld.page!.waitForTimeout(maxWait);
  await assertHasValue(customWorld, expectedValue);
}

export async function assertHasPlaceholder(customWorld: ICustomWorld, placeholderText: string) {
  await expect(
    Helpers.getSamplerTabsSelectedContent(customWorld).getByRole('textbox')
  ).toHaveAttribute('placeholder', placeholderText);
}

export async function assertImageMatchesBaseImage(
  customWorld: ICustomWorld,
  selector: string,
  name: string
) {
  const screenshot = await Helpers.tryGetStableScreenshot(
    Helpers.getSamplerTabsSelectedContent(customWorld).locator(selector)
  );
  await compareToBaseImage(customWorld, name, screenshot);
}

export async function assertIsDisabled(customWorld: ICustomWorld) {
  await expect(
    Helpers.getSamplerTabsSelectedContent(customWorld).getByRole('textbox')
  ).toBeDisabled();
}

export async function assertIsNotEditable(customWorld: ICustomWorld) {
  await expect(
    Helpers.getSamplerTabsSelectedContent(customWorld).getByRole('textbox')
  ).toBeEditable({ editable: false });
}

export async function assertHasAttribute(
  customWorld: ICustomWorld,
  attributeName: string,
  attributeValue: string
) {
  await expect(
    Helpers.getSamplerTabsSelectedContent(customWorld).getByRole('textbox')
  ).toHaveAttribute(attributeName, attributeValue);
}
