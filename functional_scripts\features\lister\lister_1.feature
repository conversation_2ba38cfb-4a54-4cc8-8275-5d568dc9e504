@component @lister @lister_1
Feature: the lister can display data in a list which can be reordered
    Scenario: the lister component sample page is available
        Given the user is at the home page
        When the user selects the "Lister" component
        Then the url ending is "listersample"

    Scenario: the lister component can be reordered with upward and downward arrows
        Given the user is at the home page
        When the user selects the "Lister" component
        Then the row with content "000000 : TRT | 1 | PaclitaxTrt | 3 | 08/06/2016 e5f5f586" has row index 0
        When the user clicks the downward arrow for row with content "000000 : TRT | 1 | PaclitaxTrt | 3 | 08/06/2016 e5f5f586"
        Then the row with content "000000 : TRT | 1 | PaclitaxTrt | 3 | 08/06/2016 e5f5f586" has row index 1
        When the user clicks the upward arrow for row with content "000000 : TRT | 1 | PaclitaxTrt | 3 | 08/06/2016 e5f5f586"
        Then the row with content "000000 : TRT | 1 | PaclitaxTrt | 3 | 08/06/2016 e5f5f586" has row index 0


