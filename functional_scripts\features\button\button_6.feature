@component @button @button_6
Feature: the button and button text change colour when the user hovers over them 
    Scenario: the button component icon is a different colour when the user hovers over
        Given the user is at the home page
        And the user selects the "Button" component in the container "Buttons"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "ButtonIcon: circle, IconPosition: Left, styled with border"
        Then the button component icon has the RGB color model value "rgb(255, 255, 255)" when in normal state
        And the button icon has the RGB color model value "rgb(192, 192, 192)" when in the hover state

    Scenario: the button component text is a different colour when the user hovers over
        Given the user is at the home page
        And the user selects the "Button" component in the container "Buttons"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "ButtonIcon: circle, IconPosition: Left, styled with border"
        Then the button component text has the RGB color model value "rgb(255, 255, 255)" when in normal state
        And the button text has the RGB color model value "rgb(192, 192, 192)" when in the hover state

    Scenario: the button component border is a different colour when the user hovers over
        Given the user is at the home page
        And the user selects the "Button" component in the container "Buttons"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "ButtonIcon: circle, IconPosition: Left, styled with border"
        Then the button border the RGB color model value "rgb(255, 255, 255)" when in normal state
        And the button border has the RGB color model value "rgb(192, 192, 192)" when in the hover state

