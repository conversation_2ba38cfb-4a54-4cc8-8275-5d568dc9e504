@component @concertina @concertina_3
Feature: the concertina has a header

Scenario: the concertina has a header
    Given the user is at the home page
    And the user selects the "Concertina" component in the container "Concertina"
    And the user clicks the "Usage" tab
    When the user expands the concertina by clicking on the header with the text "Includes collapse/expand all"
    Then the header is "Concertina header"
    And the Concertina component image matches the base image "Concertina header"

Scenario: the concertina has no header
    Given the user is at the home page
    And the user selects the "Concertina" component in the container "Concertina"
    And the user clicks the "Usage" tab
    When the user expands the concertina by clicking on the header with the text "No collapse/expand all option"
    Then the concertina has 0 concertina headers
    And the Concertina component image matches the base image "Concertina no header"

