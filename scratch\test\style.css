body {
    padding: 0;
    margin: 0;
}

.header {
    grid-area: header;
}

.sidebar {
    grid-area: sidebar;
}

.main {
    grid-area: main;
}

.sub-main {
    grid-area: sub-main;
}

.right-sidebar {
    background-color: pink;
    grid-area: right-sidebar;
}

.footer {
    background-color: wheat;
    grid-area: footer;
}

.grid-container {
    display: grid;
    grid-template-areas:
        'header header header header header header'
        'sidebar main main main main main'
    ;
    background-color: #2196F3;
    gap: 0.2rem;
    height: calc(99.7vh);

    grid-template-columns: [sidebar] 15% [main] auto;
    grid-template-rows: [header] 5% [main] 95%;
}

.grid-container>div {
    background-color: rgba(255, 255, 255, 0.8);
    text-align: center;
    font-size: 30px;
}

.inner-grid-with-sidebar {
    display: grid;
    grid-template-areas:
        'sub-main right-sidebar'
        'footer footer'
    ;
    grid-template-columns: [sub-main] auto [right-sidebar] 20%;
    grid-template-rows: [sub-main] auto [footer] 10%;
    gap: 0.3rem;
}

.inner-grid {
    display: grid;
    grid-template-areas:
        'sub-main'
        'footer'
    ;
    grid-template-columns: [sub-main] auto;
    grid-template-rows: [sub-main] auto [footer] 10%;
    gap: 0.3rem;
}

