@using CCTC_Components.Components.Steps
@using BlazorComponentUtilities
@using System.Diagnostics.CodeAnalysis

<cctc-progressstep id="@($"{Parent.Id}-step-{Step.StepNum}")" data-author="cctc" class="@_css">
    <div class="d-flex align-items-center me-3" style="user-select: none">
        <svg height="@(CircleRadius * 2)"
             width="@(CircleRadius * 2)">
            <circle cx="@CircleRadius" cy="@CircleRadius"
                    r="@(CircleRadius * 0.8)"
                    stroke-width="@(CircleRadius * 0.05)"
            />
            <text x="50%" y="50%"
                  text-anchor="middle" alignment-baseline="central"
                  font-size="@(CircleRadius * 0.7)px">
                @($"{_step.StepNum + 1}")
            </text>


        </svg>

        <div class="ms-2 @_css" style="font-size: @(TextFontSize)px">@_step.DisplayName()</div>
        @if (Parent.RequiredText is not null)
        {
            <sup class="ms-1 @_css"><small>@(_step.Required ? Parent.RequiredText : Parent.OptionalText)</small></sup>
        }

    </div>

</cctc-progressstep>

@code {

    /// <summary>
    /// The parent <see cref="Steps"/> component
    /// </summary>
    [CascadingParameter]
    public required Steps Parent { get; set; }

    /// <summary>
    /// The content of the progress step
    /// </summary>
    [Parameter, EditorRequired]
    public required RenderFragment ChildContent { get; set; }

    /// <summary>
    /// The <see cref="Step"/> model that is relevant to this progress step
    /// </summary>
    [Parameter, EditorRequired]
    public required Step Step { get; set; }

    int CircleRadius => Parent.CircleRadius;

    int TextFontSize => (int)(Parent.CircleRadius * 0.7);

    bool IsCurrentStep()
    {
        return Parent.GetCurrentStepNum() == Step.StepNum;
    }

    /// <summary>
    /// Replaces the internal step
    /// </summary>
    /// <param name="step">The new step</param>
    public void ReplaceStep(Step step)
    {
        _step = step;
        InvokeAsync(StateHasChanged);
    }

    string? _css;

    /// <inheritdoc />
    protected override void OnParametersSet()
    {
        _css =
            new CssBuilder()
                .AddClass($"selected", IsCurrentStep())
                .AddClass($"not-selected", !IsCurrentStep())
                .Build();
    }

    Step _step = default!;

    /// <inheritdoc />
    protected override void OnInitialized()
    {
        _step = Step;
        Parent.Register(this);
    }
}