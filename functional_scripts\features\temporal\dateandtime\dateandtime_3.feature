@component @temporal @dateandtime @dateandtime_3
Feature: the date and time component date and time are validated (date / time format, min and / or max date / time) and there is feedback provided to the user via an icon
    Scenario: an incomplete date and time is shown as an error
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: default, allow clear, FeedbackIcon.Error"
        When the user enters "2021010" into the Date part of the Date and Time component
        And the user enters "11121" into the Time part of the Date and Time component
        Then the Date part of the Date and Time component has the value "2021-01-0"
        And the Time part of the Date and Time component has the value "11:12:1"
        And the Date part of the Date and Time component displays a red exclamation mark feedback icon
        And the Time part of the Date and Time component displays a red exclamation mark feedback icon
        And the Date and Time component image matches the base image "dateandtime-error"

    Scenario: a complete date and time is shown as valid
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: default, FeedbackIcon.Valid"
        When the user enters "20210105" into the Date part of the Date and Time component
        And the user enters "111215" into the Time part of the Date and Time component
        Then the Date part of the Date and Time component has the value "2021-01-05"
        And the Time part of the Date and Time component has the value "11:12:15"
        And the Date part of the Date and Time component displays a green tick feedback icon
        And the Time part of the Date and Time component displays a green tick feedback icon
        And the Date and Time component image matches the base image "dateandtime-valid"

    Scenario: a date earlier than the minimum date is shown as an error
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: default, null initial value, min (2022-01-05 14:33:20) and max (2023-01-05 14:33:22), FeedbackIcon.Both"
        When the user enters "143320" into the Time part of the Date and Time component
        And the user enters "20220104" into the Date part of the Date and Time component
        Then the Time part of the Date and Time component has the value "14:33:20"
        And the Date part of the Date and Time component has the value "2022-01-04"
        And the Time part of the Date and Time component displays a green tick feedback icon
        And the Date part of the Date and Time component displays a red exclamation mark feedback icon

    Scenario: a date later than the maximum date is shown as an error
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: default, null initial value, min (2022-01-05 14:33:20) and max (2023-01-05 14:33:22), FeedbackIcon.Both"
        When the user enters "143320" into the Time part of the Date and Time component
        And the user enters "20230106" into the Date part of the Date and Time component
        Then the Time part of the Date and Time component has the value "14:33:20"
        And the Date part of the Date and Time component has the value "2023-01-06"
        And the Time part of the Date and Time component displays a green tick feedback icon
        And the Date part of the Date and Time component displays a red exclamation mark feedback icon

    Scenario: a time earlier than the minimum time is shown as an error
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: default, null initial value, min (2022-01-05 14:33:20) and max (2023-01-05 14:33:22), FeedbackIcon.Both"
        When the user enters "20220105" into the Date part of the Date and Time component
        And the user enters "143319" into the Time part of the Date and Time component
        Then the Date part of the Date and Time component has the value "2022-01-05"
        And the Time part of the Date and Time component has the value "14:33:19"
        And the Date part of the Date and Time component displays a green tick feedback icon
        And the Time part of the Date and Time component displays a red exclamation mark feedback icon

    Scenario: a time later than the maximum time is shown as an error
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: default, null initial value, min (2022-01-05 14:33:20) and max (2023-01-05 14:33:22), FeedbackIcon.Both"
        When the user enters "20220105" into the Date part of the Date and Time component
        And the user enters "143323" into the Time part of the Date and Time component
        Then the Date part of the Date and Time component has the value "2022-01-05"
        And the Time part of the Date and Time component has the value "14:33:23"
        And the Date part of the Date and Time component displays a green tick feedback icon
        And the Time part of the Date and Time component displays a red exclamation mark feedback icon




