﻿@page "/textareasample"

@{
    var description = new List<string>
    {
        "A component for textarea input. Supports input Masks and Reactive input"
    };

    var features = new List<(string, string)>
    {
        ("Interaction", "Can be made read-only and / or disabled. The read-only icon is optional"),
        ("Constrained input", "Apply an input mask (refer to the Configuration tab), prevent whitespace and / or set a max length"),
        ("Input display", "Has the facility to add a placeholder and / or set an initial number of display rows"),
        ("Binding", "The <code>BindEvent</code> has a default value of <code>OnChange</code>. The component throttle speed can be changed")
    };

    var gotchas = new List<(string, string)>
    {
        ("BindEvent", "If the component is Reactive or if a <code>Mask</code> is applied, the <code>BindEvent</code> is set to <code>OnInput</code> automatically. " +
            "Note that the <code>OnChange</code> event occurs when the input loses focus after the value has changed"),
        ("--cctc-input-height", "This CSS variable does not apply to the TextArea component. Use the Rows parameter to set the height instead"),
        ("--cctc-input-webkit-line-clamp", "CSS variable not used by this component"),
        ("ThrottleMs", "500 ms is the minimum <code>ThrottleMs</code> for masked input and will be applied automatically unless a higher value is provided"),
        ("PreventWhitespace", "Ensure an appropriate throttle is set, otherwise the last character cannot be changed")
    };

    var tips = new List<(string, string)>
    {
        ("Target the cctc-input component(s) on a page ignoring any child cctc-input components. For example, setting a uniform input component width adjusting according to screen size",
@"
<code>
    <pre>

    ::deep cctc-input:not(cctc-input cctc-input) {
        width: 100%;
    }

    @media (min-width: 1200px) {
        ::deep cctc-input:not(cctc-input cctc-input) {
            width: 35%;
        }
    }
    </pre>
</code>")
    };

    var usageCode = new List<(string title, string description, string code, RenderFragment fragment)>
    {
        ("With callback and max length of 20", "",
@"<TextArea
    Id=""usage1""
    Value=""@TextValue""
    ValueChanged=""@TextChanged""
    Placeholder=""some placeholder""
    MaxLength=""20""
    BindEvent=""BindEvent.OnInput"">
</TextArea>

@code {

    string? TextValue { get; set; } = ""Some demo Text"";

    void TextChanged(string? newValue)
    {
        TextValue = newValue;
        Console.WriteLine($""New value: { newValue ?? ""value cleared"" }"");
    }
}",
        @<TextArea
            Id="usage1"
            Value="@TextValue"
            ValueChanged="@TextChanged"
            Placeholder="some placeholder"
            MaxLength="20"
            BindEvent="BindEvent.OnInput">
        </TextArea>),
        ("Reactive with prevent whitespace", "",
@"<TextArea
    Id=""usage2""
    @bind-Value=""@TextValue5""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    PreventWhitespace=""true""
    TextChanged=""@(args => Console.WriteLine(args.Value))"">
</TextArea>

@code {

    string TextValue5 { get; set; } = ""Demo TextArea"";
}",
        @<TextArea
            Id="usage2"
            @bind-Value="@TextValue5"
            ThrottleMs="@Constants.DefaultThrottleMs"
            PreventWhitespace="true"
            TextChanged="@(args => Console.WriteLine(args.Value))">
        </TextArea>),
        ("Read-only", "",
@"<TextArea
    Id=""usage3""
    Value=""Some long readonly text that helps to check that the lock icon does not overlap the text or the text area scrollbar""
    ReadOnly=""true"">
</TextArea>

@code {

}",
        @<TextArea
            Id="usage3"
            Value="Some long readonly text that helps to check that the lock icon does not overlap the text or the text area scrollbar"
            ReadOnly="true">
        </TextArea>),
        ("Disabled", "",
@"<TextArea
    Id=""usage4""
    Value=""@TextValue2""
    Disabled=""true"">
</TextArea>

@code {

    string? TextValue2 { get; set; } = ""Pellentesque sit amet tincidunt massa. Vivamus eget ex ullamcorper, pellentesque elit at, maximus erat. Etiam vel condimentum lectus."";
}",
        @<TextArea
            Id="usage4"
            Value="@TextValue2"
            Disabled="true">
        </TextArea>),
        ("Read-only (hide read-only icon)", "",
@"<TextArea
    Id=""usage5""
    Value=""@TextValue2""
    ReadOnly=""true""
    HideReadOnlyIcon=""true"">
</TextArea>

@code {

    string? TextValue2 { get; set; } = ""Pellentesque sit amet tincidunt massa. Vivamus eget ex ullamcorper, pellentesque elit at, maximus erat. Etiam vel condimentum lectus."";
}",
        @<TextArea
            Id="usage5"
            Value="@TextValue2"
            ReadOnly="true"
            HideReadOnlyIcon="true">
        </TextArea>),
        ("Disabled and read-only", "",
@"<TextArea
    Id=""usage6""
    Value=""Some long readonly and disabled text that helps to check that the lock icon does not overlap the text or the text area scrollbar""
    ReadOnly=""true""
    Disabled=""true"">
</TextArea>

@code {

}",
        @<TextArea
            Id="usage6"
            Value="Some long readonly and disabled text that helps to check that the lock icon does not overlap the text or the text area scrollbar"
            ReadOnly="true"
            Disabled="true">
        </TextArea>),
        ("Disabled and read-only (hide read-only icon)", "",
@"<TextArea
    Id=""usage7""
    Value=""@TextValue2""
    ReadOnly=""true""
    Disabled=""true""
    HideReadOnlyIcon=""true"">
</TextArea>

@code {

    string? TextValue2 { get; set; } = ""Pellentesque sit amet tincidunt massa. Vivamus eget ex ullamcorper, pellentesque elit at, maximus erat. Etiam vel condimentum lectus."";
}",
        @<TextArea
            Id="usage7"
            Value="@TextValue2"
            ReadOnly="true"
            Disabled="true"
            HideReadOnlyIcon="true">
        </TextArea>),
        ("With four given rows and CssClass applied", "",
@"<TextArea
    Id=""usage8""
    CssClass=""info-background""
    Value=""@TextValue""
    ValueChanged=""@TextChanged""
    Placeholder=""some placeholder""
    Rows=""4"">
</TextArea>

@code {

    string? TextValue { get; set; } = ""Some demo Text"";

    void TextChanged(string? newValue)
    {
        TextValue = newValue;
        Console.WriteLine($""New value: { newValue ?? ""value cleared"" }"");
    }
}",
        @<TextArea
            Id="usage8"
            CssClass="info-background"
            Value="@TextValue"
            ValueChanged="@TextChanged"
            Placeholder="some placeholder"
            Rows="4">
        </TextArea>),
        ("With ValueChanged, Focus, Blur and BeforeCopy callbacks plus additional input attributes", "",
@"<TextArea
    Id=""usage9""
    Value=""@TextValue""
    ValueChanged=""@TextChanged""
    Placeholder=""some placeholder""
    BindEvent=""BindEvent.OnInput""
    Focus=""OnFocus""
    Blur=""OnBlur""
    InputAttributes=""InputAttributes"">
</TextArea>

@code {

    string? TextValue { get; set; } = ""Some demo Text"";

    void TextChanged(string? newValue)
    {
        TextValue = newValue;
        Console.WriteLine($""New value: { newValue ?? ""value cleared"" }"");
    }

    Dictionary<string, object> InputAttributes => new()
    {
        { ""name"", ""test-name"" },
        { ""onbeforecopy"", EventCallback.Factory.Create<EventArgs>(this, OnBeforeCopy) }
    };

    void OnFocus(FocusEventArgs args)
    {
        Console.WriteLine(args.Type);
    }

    void OnBlur(FocusEventArgs args)
    {
        Console.WriteLine(args.Type);
    }

    void OnBeforeCopy(EventArgs args)
    {
        Console.WriteLine(""before copy"");
    }
}",
        @<TextArea
            Id="usage9"
            Value="@TextValue"
            ValueChanged="@TextChanged"
            Placeholder="some placeholder"
            BindEvent="BindEvent.OnInput"
            Focus="OnFocus"
            Blur="OnBlur"
            InputAttributes="InputAttributes">
        </TextArea>),
        ("With mask and style applied", "",
@"<TextArea
    Id=""usage10""
    Style=""--cctc-input-border-width: 2px;""
    @bind-Value=""@TextValue3""
    Placeholder=""(000)000/000/00""
    Mask=""(000)000/000/00"">
</TextArea>

@code {

    string? TextValue3 { get; set; } = ""Some demo Text"";
}",
        @<TextArea
            Id="usage10"
            Style="--cctc-input-border-width: 2px;"
            @bind-Value="@TextValue3"
            Placeholder="(000)000/000/00"
            Mask="(000)000/000/00">
        </TextArea>),
    ("Reactive with callback, null initial value and prevent whitespace", "",
@"<TextArea
    Id=""usage11""
    @bind-Value=""TextValue4""
    BindEvent=""BindEvent.OnInput""
    TextChanged=""@(args =>  Console.WriteLine(args.Value ?? ""value cleared""))""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    PreventWhitespace=""true"">
</TextArea>

@code {

    string? TextValue4 { get; set; } = null;
}
",
        @<TextArea Id="usage11"
            @bind-Value="TextValue4"
            BindEvent="BindEvent.OnInput"
            TextChanged="@(args =>  Console.WriteLine(args.Value ?? "value cleared"))"
            ThrottleMs="@Constants.DefaultThrottleMs"
            PreventWhitespace="true">
        </TextArea>)
    };

    var configuration = new List<(string, string)>
    {
        ("Masked input rules",
@"<pre>
    0 User must enter a digit (0 to 9)
    9 User can enter a digit (0 to 9)
    L User must enter a letter
    ? User can enter a letter
    A User must enter a letter or a digit
    a User can enter a letter or a digit
    & User must enter either a character or a space
    C User can enter characters or spaces
    > Coverts all characters that follow to uppercase
    < Converts all characters that follow to lowercase
</pre>"),
    ("Masked input further information", "refer to <code>Lib.Common.Helpers.StringMask</code>")
    };
}

@* The wrapper div is necessary here for ::deep isolated css rules to be applied to the TextArea component *@
<div>
    <Sampler
        ComponentName="TextArea"
        ComponentCssName="input"
        ComponentTypeName="textarea"
        Description="@description"
        Features="@features"
        UsageText="Typical usages of the <code>TextArea</code> component are shown below"
        UsageCodeList="@usageCode"
        Configuration="@configuration"
        Gotchas="@gotchas"
        Tips="@tips"
        ContentHeightPixels="450">
        <ExampleTemplate>
            <TextArea
                Id="example1"
                @bind-Value="@TextValue"
                TextChanged="@(args =>  Console.WriteLine(args.Value ?? "value cleared"))"
                Placeholder="some placeholder"
                BindEvent="BindEvent.OnInput">
            </TextArea>
        </ExampleTemplate>
    </Sampler>
</div>

@code {

    string? TextValue { get; set; } = "Some demo TextArea";

    string? TextValue2 { get; set; } = "Pellentesque sit amet tincidunt massa. Vivamus eget ex ullamcorper, pellentesque elit at, maximus erat. Etiam vel condimentum lectus.";

    string? TextValue3 { get; set; } = string.Empty;

    string? TextValue4 { get; set; } = null;

    string TextValue5 { get; set; } = "Demo TextArea";

    void TextChanged(string? newValue)
    {
        TextValue = newValue;
        Console.WriteLine($"New value: { newValue ?? "value cleared" }");
    }

    Dictionary<string, object> InputAttributes => new()
    {
        { "name", "test-name" },
        { "onbeforecopy", EventCallback.Factory.Create<EventArgs>(this, OnBeforeCopy) }
    };

    void OnFocus(FocusEventArgs args)
    {
        Console.WriteLine(args.Type);
    }

    void OnBlur(FocusEventArgs args)
    {
        Console.WriteLine(args.Type);
    }

    void OnBeforeCopy(EventArgs args)
    {
        Console.WriteLine("before copy");
    }
}