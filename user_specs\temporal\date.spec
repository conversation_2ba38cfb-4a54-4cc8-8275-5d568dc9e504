1 - the date component date can be typed in as text or can be entered via a date picker
2 - the date component can allow an empty value and an entered value can be optionally cleared
3 - the date component date is validated (date format, min and / or max date) and there is feedback provided to the user via an icon
4 - the date component can be made read-only and / or disabled. The read-only icon is optional
5 - the date component has a placeholder which matches the date format