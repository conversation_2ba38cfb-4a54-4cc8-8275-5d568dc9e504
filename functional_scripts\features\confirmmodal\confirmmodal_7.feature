@component @confirmmodal @confirmmodal_7

Feature: the confirm modal header color is customisable
Scenario: the confirm modal box items can have different colors 
    Given the user is at the home page
    And the user selects the "Confirm modal" component in the container "Modals"
    And the user clicks the "Usage" tab
    And the user expands the concertina by clicking on the header with the text "Default positive and negative responses, Modal options classes: UI theme plus custom class defining a max width"
    When the confirm modal button is clicked
    Then the confirm modal component image matches the base image "Confirm Modal Box with a variety of colors"

Scenario: the confirm modal ok response changes color when the user hovers over it 
    Given the user is at the home page
    And the user selects the "Confirm modal" component in the container "Modals"
    And the user clicks the "Usage" tab
    And the user expands the concertina by clicking on the header with the text "Default positive and negative responses, Modal options classes: UI theme plus custom class defining a max width"
    And the confirm modal button is clicked
    When the ok response button has the color "rgb(13, 202, 240)"
    Then the ok response button has the RGB color model value "rgb(255, 255, 255)" when in the hover state

<PERSON><PERSON>rio: the confirm modal cancel response changes color when the user hovers over it 
    Given the user is at the home page
    And the user selects the "Confirm modal" component in the container "Modals"
    And the user clicks the "Usage" tab
    And the user expands the concertina by clicking on the header with the text "Default positive and negative responses, Modal options classes: UI theme plus custom class defining a max width"
    And the confirm modal button is clicked
    When the cancel response button has the color "rgb(13, 202, 240)"
    Then the cancel response button has the RGB color model value "rgb(255, 255, 255)" when in the hover state
