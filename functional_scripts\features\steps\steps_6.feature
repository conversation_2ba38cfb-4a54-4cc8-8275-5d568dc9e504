@component @steps @steps_6
Feature: the small descriptive header text is configurable
    Scenario: The header size can be large with shorter text
        Given the user is at the home page
        And the user selects the "Steps" component in the container "Steps"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "Required data"
        Then the header superscript font size is "10.5px"
        And the steps headers image matches the base image "opt and req seen"

    Scenario: The header size can be smaller with longer text
        Given the user is at the home page
        And the user selects the "Steps" component in the container "Steps"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "UI configurable"
        Then the header superscript font size is "10.5px"
        And the steps headers image matches the base image "optional and required seen"

