﻿@page "/tooltipsample"

@{
    var description = new List<string>
    {
        "The Tooltip component can be used to show a tooltip for a child element"
    };

    var features = new List<(string, string)>
    {
        ("Tooltip", "Tooltip behaviour can be set to Enabled (default), Disabled or EnabledOnOverflow. Tooltip placement is configurable")
    };

    var gotchas = new List<(string, string)>
    {
        ("TooltipBehaviour", "If an instance of TooltipBehaviour.EnabledOnOverflow is passed as a component parameter a child element selector needs to be provided in the constructor to allow overflow checking")
    };

    var tips = new List<(string, string)>
    {
        ("width and overflow", "If the cctc-tooltip child component needs to wrap, scroll or be truncated when it overflows its container, the cctc-tooltip element width css property default value of fit-content may need to be overridden")
    };

    var usageCode = new List<(string title, string description, string code, RenderFragment fragment)>
    {
        ("TooltipPlacement: Right, TooltipBehaviour: Enabled", "",
@"<Tooltip
    Id=""usage1""
    Content=""Tooltip message""
    TooltipPlacement=""@TooltipPlacement.Right""
    TooltipBehaviour=""@(new TooltipBehaviour.Enabled())"">
    Hover for tooltip
</Tooltip>

@code {

}",
        @<Tooltip
            Id="usage1"
            Content="Tooltip message"
            TooltipPlacement="@TooltipPlacement.Right"
            TooltipBehaviour="@(new TooltipBehaviour.Enabled())">
            Hover for tooltip
        </Tooltip>),
        ("TooltipPlacement: Top, TooltipBehaviour: EnabledOnOverflow", "",
@"<Tooltip
    Id=""usage2""
    Content=""Tooltip message - Hover for tooltip which is enabled on overflow""
    TooltipPlacement=""@TooltipPlacement.Top""
    TooltipBehaviour=""@(new TooltipBehaviour.EnabledOnOverflow(""#child-element-id""))"">
    <div id=""child-element-id"" class=""nowrap"">Hover for tooltip which is enabled on overflow</div>
</Tooltip>

@code {

}",
        @<Tooltip
            Id="usage2"
            Content="Tooltip message - Hover for tooltip which is enabled on overflow"
            TooltipPlacement="@TooltipPlacement.Top"
            TooltipBehaviour="@(new TooltipBehaviour.EnabledOnOverflow("#child-element-id"))">
            <div id="child-element-id" class="nowrap">Hover for tooltip which is enabled on overflow</div>
        </Tooltip>),
        ("TooltipPlacement: Right, TooltipBehaviour: Disabled", "",
@"<Tooltip
    Id=""usage3""
    Content=""Tooltip message""
    TooltipPlacement=""@TooltipPlacement.Right""
    TooltipBehaviour=""@(new TooltipBehaviour.Disabled())"">
    Disabled tooltip
</Tooltip>

@code {

}",
        @<Tooltip
            Id="usage3"
            Content="Tooltip message"
            TooltipPlacement="@TooltipPlacement.Right"
            TooltipBehaviour="@(new TooltipBehaviour.Disabled())">
            Disabled tooltip
        </Tooltip>)
    };
}

@* The wrapper div is necessary here for ::deep isolated css rules to be applied to the Tooltip component *@
<div>
    <Sampler
        ComponentName="Tooltip"
        ComponentCssName="tooltip"
        Description="@description"
        Features="@features"
        UsageText="Typical usages of the <code>Tooltip</code> component are shown below"
        UsageCodeList="@usageCode"
        Gotchas="@gotchas"
        Tips="@tips"
        ContentHeightPixels="275">
        <ExampleTemplate>
            <Tooltip
                Id="Example1"
                Content="Tooltip message"
                TooltipPlacement="@TooltipPlacement.Right">
                Hover for tooltip
            </Tooltip>
        </ExampleTemplate>
    </Sampler>
</div>
