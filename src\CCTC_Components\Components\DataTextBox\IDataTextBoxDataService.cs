﻿using CCTC_Lib.Contracts.Data;

namespace CCTC_Components.Components.DataTextBox;

/// <summary>
/// A data service used with the DataTextBox to provide the functionality to query the components data
/// </summary>
/// <typeparam name="TData">The type of data the component holds</typeparam>
/// <typeparam name="TK<PERSON>">The child types type key that can be used to provide a grouping by type</typeparam>
public interface IDataTextBoxDataService<TData, TKey> where TData : IUniqueIdentity<string>
{
    /// <summary>
    /// Gets the TKey from a TData object
    /// </summary>
    /// <param name="item">The item of type TData</param>
    /// <returns>The TKey</returns>
    TKey GetTypeKey(TData item);

    /// <summary>
    /// Gets the distinct 'types' of children from the target parent
    /// </summary>
    /// <param name="parent">The parent of type TData for which the child types are required</param>
    /// <returns>A distinct list of child type keys</returns>
    /// <remarks>The list should be distinct when using the typeKey</remarks>
    Task<List<TKey>> GetDistinctChildTypes(TData parent);

    /// <summary>
    /// Gets the children for the given parent
    /// </summary>
    /// <param name="parent">The parent of type TData to retrieve the children for</param>
    /// <returns>A list of the parents children</returns>
    Task<List<TData>> GetChildren(TData parent);

    /// <summary>
    /// Gets the children for the given parent with the given type
    /// </summary>
    /// <param name="parent">The parent of type TData to retrieve the children for</param>
    /// <param name="childType">The type of child to retrieve</param>
    /// <returns>A list of the parents children</returns>
    Task<List<TData>> GetChildren(TData parent, TKey? childType);

    /// <summary>
    /// Filters the loaded child types using a function
    /// </summary>
    /// <param name="filter">The function that filters the child types</param>
    /// <returns>A filtered list of TKey</returns>
    Task<List<TKey>> FilterChildTypes(Predicate<TKey> filter);

    /// <summary>
    /// Filters the loaded children using a function
    /// </summary>
    /// <param name="filter">The function that filters the children</param>
    /// <returns>A filtered list of TData</returns>
    Task<List<TData>> FilterChildren(Predicate<TKey> filter);
}