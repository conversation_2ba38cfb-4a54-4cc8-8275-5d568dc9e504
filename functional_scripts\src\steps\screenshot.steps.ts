import { ICustomWorld } from '../support/custom-world';
import * as Helpers from '../support/helper-functions';
import { compareToBaseImage } from '../utils/compareImages';
import { Then } from '@cucumber/cucumber';

Then(
  'the screen matches the base image {string}',
  async function (this: ICustomWorld, name: string) {
    const page = this.page!;
    const screenshot = await Helpers.tryGetStablePageScreenshot(page, {
      mask: [page.locator('.header'), page.locator('.menu')]
    });
    await compareToBaseImage(this, name, screenshot);
  }
);

Then(
  'the sampler tabs selector content matches the base image {string}',
  async function (this: ICustomWorld, name: string) {
    const screenshot = await Helpers.tryGetStableScreenshot(
      Helpers.getSamplerTabsSelectedContent(this)
    );
    await compareToBaseImage(this, name, screenshot);
  }
);
