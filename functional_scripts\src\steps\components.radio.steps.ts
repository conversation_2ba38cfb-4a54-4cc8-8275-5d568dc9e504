import { ICustomWorld } from '../support/custom-world';
import { compareToBaseImage } from '../utils/compareImages';
import * as Helpers from '../support/helper-functions';
import { When, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';

When(
  'the user clicks on the radio option button with the associated label text {string}',
  async function (this: ICustomWorld, optionText: string) {
    await Helpers.getSamplerTabsSelectedContent(this)
      .getByLabel(optionText, { exact: true })
      .click();
  }
);

When(
  'the user clicks on the radio option label with the text {string}',
  async function (this: ICustomWorld, optionText: string) {
    await Helpers.getSamplerTabsSelectedContent(this)
      .getByText(optionText, { exact: true })
      .click();
  }
);

Then(
  'the radio option button with the associated label text {string} is disabled',
  async function (this: ICustomWorld, optionText: string) {
    await expect(
      Helpers.getSamplerTabsSelectedContent(this).getByLabel(optionText, { exact: true })
    ).toBeDisabled();
  }
);

Then('the radio option buttons are all disabled', async function (this: ICustomWorld) {
  const radioButtons = Helpers.getSamplerTabsSelectedContent(this).getByRole('radio');
  for (const radioButton of await radioButtons.all()) {
    await expect(radioButton).toBeDisabled();
  }
});

Then(
  'the current selected radio option has the text {string}',
  async function (this: ICustomWorld, optionText: string) {
    await expect(
      Helpers.getSamplerTabsSelectedContent(this).getByLabel(optionText, { exact: true })
    ).toBeChecked();
  }
);

Then(
  'the Radio component image matches the base image {string}',
  async function (this: ICustomWorld, name: string) {
    const radio = Helpers.getSamplerTabsSelectedContent(this).locator(
      'cctc-input[data-cctc-input-type="radio"]'
    );
    const radioGroup = radio.getByRole('radiogroup');
    await radioGroup.evaluate((element) => {
      element.scrollTo(0, 0);
    });
    const screenshot = await Helpers.tryGetStableScreenshot(radio);
    await compareToBaseImage(this, name, screenshot);
  }
);

When('the user clicks on the radio clear icon', async function (this: ICustomWorld) {
  await Helpers.getSamplerTabsSelectedContent(this).locator('.show-clear-icon').click();
});

Then(
  'the Radio component does not have a selected radio option',
  async function (this: ICustomWorld) {
    expect(
      await Helpers.any(Helpers.getSamplerTabsSelectedContent(this).getByRole('radio'), x =>
        x.isChecked()
      )
    ).toBeFalsy();
  }
);

Then('the radio clear icon is no longer in view', async function (this: ICustomWorld) {
  await expect(
    Helpers.getSamplerTabsSelectedContent(this).locator('.hide-clear-icon')
  ).toBeHidden();
});

Then('the radio options can scroll', async function (this: ICustomWorld) {
  const radioLabels = Helpers.getSamplerTabsSelectedContent(this).locator(
    'cctc-input[data-cctc-input-type="radio"] label'
  );
  for (const label of await radioLabels.all()) {
    await expect(label).toHaveCSS('white-space', 'nowrap');
    await expect(label).toHaveCSS('overflow-x', 'auto');
  }
});

Then(
  'the radio options have tooltips enabled containing the full option text',
  async function (this: ICustomWorld) {
    const tooltips = Helpers.getSamplerTabsSelectedContent(this).locator('cctc-tooltip');
    for (const tooltip of await tooltips.all()) {
      const currentOptionLabelText = await tooltip.locator('label').innerText();
      await expect(tooltip).toHaveAttribute('data-bs-original-title', currentOptionLabelText);
    }
  }
);

Then('the radio options do not have tooltips enabled', async function (this: ICustomWorld) {
  await expect(Helpers.getSamplerTabsSelectedContent(this).locator('cctc-tooltip')).toHaveCount(0);
});
