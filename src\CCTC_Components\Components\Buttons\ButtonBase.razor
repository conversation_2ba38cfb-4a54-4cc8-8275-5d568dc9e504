﻿@using CCTC_Lib.Enums.UI
@inherits CCTC_Components.Components.__CCTC.CCTCBase

@namespace CCTC_Components.Components.Buttons

@code {

    /// <summary>
    /// The button icon
    /// </summary>
    [Parameter]
    public string? ButtonIcon { get; set; }

    /// <summary>
    /// The button text
    /// </summary>
    [Parameter]
    public string? ButtonText { get; set; }

    /// <summary>
    /// The icon position
    /// </summary>
    [Parameter]
    public PositionX IconPosition { get; set; } = PositionX.Left;

    /// <summary>
    /// A callback which fires when the button is clicked
    /// </summary>
    [Parameter, EditorRequired]
    public EventCallback OnClick { get; set; }

    /// <summary>
    /// Disabled when true
    /// </summary>
    [Parameter]
    public bool Disabled { get; set; }

    /// <summary>
    /// Includes a border when true
    /// </summary>
    [Parameter]
    public bool StyledWithBorder { get; set; }

    protected string? _buttonWrapperClass;

    virtual protected async Task OnButtonClick()
    {
        if (!Disabled)
        {
            await OnClick.InvokeAsync();
        }
    }

    ///<inheritdoc />
    protected override void OnParametersSet()
    {
        _buttonWrapperClass =
            new CssBuilder()
                .AddClass("button-wrapper")
                .AddClass("icon-left", IconPosition == PositionX.Left)
                .AddClass("icon-right", IconPosition == PositionX.Right)
                .AddClass("disabled", Disabled)
                .AddClass("button-border", StyledWithBorder)
                .Build();
    }
 }

