﻿using CCTC_Components.Components;

namespace CCTC_Components.bUnit.test;

public class TimeOutTests : TestContext
{
    [Fact]
    public void SpaceIsReserved()
    {
        var cut = RenderComponent<TimeOut>(parameters => parameters
            .Add(p => p.ChildContent, $"<div id='content-id'>Some text</div>")
            .Add(p => p.ReserveSpace, true)
        );

        TestHelpers.AssertHasClass(cut.Find("cctc-timeout > div"), "reserve");
        TestHelpers.AssertDoesNotHaveClass(cut.Find("cctc-timeout > div"), "no-reserve");
    }

    [Fact]
    public void SpaceIsNotReserved()
    {
        var cut = RenderComponent<TimeOut>(parameters => parameters
            .Add(p => p.ChildContent, $"<div id='content-id'>Some text</div>")
            .Add(p => p.ReserveSpace, false)
        );

        TestHelpers.AssertHasClass(cut.Find("cctc-timeout > div"), "no-reserve");
        TestHelpers.AssertDoesNotHaveClass(cut.Find("cctc-timeout > div"), "reserve");
    }

    [Fact]
    public async Task SpaceIsNotReservedAndCollapsesAtCompletion()
    {
        var hasCompleted = false;

        var cut = RenderComponent<TimeOut>(parameters => parameters
            .Add(p => p.ChildContent, $"<div id='content-id'>Some text</div>")
            .Add(p => p.ReserveSpace, false)
            .Add(p => p.CollapseOnFadeComplete, true)
            .Add(p => p.ShowContentFor, TimeSpan.FromMilliseconds(1))
            .Add(p => p.FadeFor, TimeSpan.FromMilliseconds(1))

            .Add(p => p.AfterFadeFinished, () => hasCompleted = true)
        );

        TestHelpers.AssertHasClass(cut.Find("cctc-timeout > div"), "no-reserve");
        TestHelpers.AssertDoesNotHaveClass(cut.Find("cctc-timeout > div"), "reserve");

        cut.Instance.Initiate();

        await Task.Delay(TimeSpan.FromMilliseconds(100));

        //check has completed and that the ui is in the correct state
        Assert.True(hasCompleted);
        TestHelpers.AssertHasClass(cut.Find("cctc-timeout > div"), "no-reserve");
        TestHelpers.AssertDoesNotHaveClass(cut.Find("cctc-timeout > div"), "reserve");
    }

    [Fact]
    public async Task SpaceIsNotReservedAndDoesNotCollapseAtCompletion()
    {
        var hasCompleted = false;

        var cut = RenderComponent<TimeOut>(parameters => parameters
            .Add(p => p.ChildContent, $"<div id='content-id'>Some text</div>")
            .Add(p => p.ReserveSpace, false)
            .Add(p => p.CollapseOnFadeComplete, false)
            .Add(p => p.ShowContentFor, TimeSpan.FromMilliseconds(1))
            .Add(p => p.FadeFor, TimeSpan.FromMilliseconds(1))

            .Add(p => p.AfterFadeFinished, () => hasCompleted = true)
        );

        TestHelpers.AssertHasClass(cut.Find("cctc-timeout > div"), "no-reserve");
        TestHelpers.AssertDoesNotHaveClass(cut.Find("cctc-timeout > div"), "reserve");

        cut.Instance.Initiate();

        await Task.Delay(TimeSpan.FromMilliseconds(100));

        //check has completed and that the ui is in the correct state
        Assert.True(hasCompleted);
        TestHelpers.AssertHasClass(cut.Find("cctc-timeout > div"), "reserve");
        TestHelpers.AssertDoesNotHaveClass(cut.Find("cctc-timeout > div"), "no-reserve");
    }

    [Fact]
    public async Task SpaceIsReservedAndCollapsesAtCompletion()
    {
        var hasCompleted = false;

        var cut = RenderComponent<TimeOut>(parameters => parameters
            .Add(p => p.ChildContent, $"<div id='content-id'>Some text</div>")
            .Add(p => p.ReserveSpace, true)
            .Add(p => p.CollapseOnFadeComplete, true)
            .Add(p => p.ShowContentFor, TimeSpan.FromMilliseconds(1))
            .Add(p => p.FadeFor, TimeSpan.FromMilliseconds(1))

            .Add(p => p.AfterFadeFinished, () => hasCompleted = true)
        );

        TestHelpers.AssertHasClass(cut.Find("cctc-timeout > div"), "reserve");
        TestHelpers.AssertDoesNotHaveClass(cut.Find("cctc-timeout > div"), "no-reserve");

        cut.Instance.Initiate();

        await Task.Delay(TimeSpan.FromMilliseconds(100));

        //check has completed and that the ui is in the correct state
        Assert.True(hasCompleted);
        TestHelpers.AssertHasClass(cut.Find("cctc-timeout > div"), "no-reserve");
        TestHelpers.AssertDoesNotHaveClass(cut.Find("cctc-timeout > div"), "reserve");
    }

    [Fact]
    public async Task SpaceIsReservedDoesNotCollapsesAtCompletion()
    {
        var hasCompleted = false;

        var cut = RenderComponent<TimeOut>(parameters => parameters
            .Add(p => p.ChildContent, $"<div id='content-id'>Some text</div>")
            .Add(p => p.ReserveSpace, true)
            .Add(p => p.CollapseOnFadeComplete, false)
            .Add(p => p.ShowContentFor, TimeSpan.FromMilliseconds(1))
            .Add(p => p.FadeFor, TimeSpan.FromMilliseconds(1))

            .Add(p => p.AfterFadeFinished, () => hasCompleted = true)
        );

        TestHelpers.AssertHasClass(cut.Find("cctc-timeout > div"), "reserve");
        TestHelpers.AssertDoesNotHaveClass(cut.Find("cctc-timeout > div"), "no-reserve");

        cut.Instance.Initiate();

        await Task.Delay(TimeSpan.FromMilliseconds(100));

        //check has completed and that the ui is in the correct state
        Assert.True(hasCompleted);
        TestHelpers.AssertHasClass(cut.Find("cctc-timeout > div"), "reserve");
        TestHelpers.AssertDoesNotHaveClass(cut.Find("cctc-timeout > div"), "no-reserve");
    }

    [Fact]
    public async Task SpaceIsReservedDoesNotCollapsesAtCompletionAutoRestart()
    {
        var hasCompleted = false;
        var wasStopped = false;
        var wasAutoStopped = false;
        var cut = RenderComponent<TimeOut>(parameters => parameters
            .Add(p => p.ChildContent, $"<div id='content-id'>Some text</div>")
            .Add(p => p.ReserveSpace, true)
            .Add(p => p.CollapseOnFadeComplete, false)
            .Add(p => p.ShowContentFor, TimeSpan.FromMilliseconds(10))
            .Add(p => p.FadeFor, TimeSpan.FromMilliseconds(10))
            .Add(p => p.AutoRestart, true)
            .Add(p => p.WhenStopped, autostop =>
            {
                wasStopped = true;
                wasAutoStopped = autostop;
            })
            .Add(p => p.AfterFadeFinished, () => hasCompleted = true)
        );
        TestHelpers.AssertHasClass(cut.Find("cctc-timeout > div"), "reserve");
        TestHelpers.AssertDoesNotHaveClass(cut.Find("cctc-timeout > div"), "no-reserve");
        cut.Instance.Initiate();
        await Task.Delay(TimeSpan.FromMilliseconds(2));
        cut.Instance.Initiate();
        await Task.Delay(TimeSpan.FromMilliseconds(100));
        //should complete eventually but should also record that was auto stopped
        Assert.True(hasCompleted);
        Assert.True(wasStopped);
        Assert.True(wasAutoStopped);
        TestHelpers.AssertHasClass(cut.Find("cctc-timeout > div"), "reserve");
        TestHelpers.AssertDoesNotHaveClass(cut.Find("cctc-timeout > div"), "no-reserve");
    }

    [Fact]
    public async Task SpaceIsReservedDoesNotCollapsesAtCompletionStoppedButNotAutoRestart()
    {
        var hasCompleted = false;
        var wasStopped = false;
        var wasAutoStopped = false;
        var cut = RenderComponent<TimeOut>(parameters => parameters
            .Add(p => p.ChildContent, $"<div id='content-id'>Some text</div>")
            .Add(p => p.ReserveSpace, true)
            .Add(p => p.CollapseOnFadeComplete, false)
            .Add(p => p.ShowContentFor, TimeSpan.FromMilliseconds(10))
            .Add(p => p.FadeFor, TimeSpan.FromMilliseconds(10))
            .Add(p => p.AutoRestart, false)
            .Add(p => p.WhenStopped, autostop =>
            {
                wasStopped = true;
                wasAutoStopped = autostop;
            })
            .Add(p => p.AfterFadeFinished, () => hasCompleted = true)
        );
        TestHelpers.AssertHasClass(cut.Find("cctc-timeout > div"), "reserve");
        TestHelpers.AssertDoesNotHaveClass(cut.Find("cctc-timeout > div"), "no-reserve");
        cut.Instance.Initiate();
        await Task.Delay(TimeSpan.FromMilliseconds(2));
        cut.Instance.Stop();

        await Task.Delay(TimeSpan.FromMilliseconds(100));
        //doesn't complete as stopped, wasn't auto stopped but was stopped
        Assert.False(hasCompleted);
        Assert.True(wasStopped);
        Assert.False(wasAutoStopped);
        TestHelpers.AssertHasClass(cut.Find("cctc-timeout > div"), "reserve");
        TestHelpers.AssertDoesNotHaveClass(cut.Find("cctc-timeout > div"), "no-reserve");
    }

    [Fact]
    public async Task RestartFunctionalityWorksCorrectly()
    {
        var completionCount = 0;
        var stopCount = 0;
        var autoRestartCount = 0;

        var cut = RenderComponent<TimeOut>(parameters => parameters
            .Add(p => p.ChildContent, $"<div id='content-id'>Restart test content</div>")
            .Add(p => p.ReserveSpace, true)
            .Add(p => p.CollapseOnFadeComplete, false)
            .Add(p => p.ShowContentFor, TimeSpan.FromMilliseconds(20))
            .Add(p => p.FadeFor, TimeSpan.FromMilliseconds(20))
            .Add(p => p.AutoRestart, true)
            .Add(p => p.WhenStopped, isAutoRestart =>
            {
                stopCount++;
                if (isAutoRestart) autoRestartCount++;
            })
            .Add(p => p.AfterFadeFinished, () => completionCount++)
        );

        // Verify initial state
        Assert.False(cut.Instance.IsRunning);

        // Start first timeout
        cut.Instance.Initiate();
        Assert.True(cut.Instance.IsRunning);

        // Wait a bit then restart before completion
        await Task.Delay(TimeSpan.FromMilliseconds(10));
        cut.Instance.Initiate(); // This should trigger AutoRestart

        // Wait for completion
        await Task.Delay(TimeSpan.FromMilliseconds(60));

        // Verify restart behavior occurred
        Assert.True(stopCount >= 1);
        Assert.True(autoRestartCount >= 1);
        Assert.True(completionCount >= 1);
        Assert.False(cut.Instance.IsRunning);
    }

    [Fact]
    public async Task StopFunctionalityWorksCorrectly()
    {
        var hasCompleted = false;
        var stopCount = 0;
        var wasManualStop = false;

        var cut = RenderComponent<TimeOut>(parameters => parameters
            .Add(p => p.ChildContent, $"<div id='content-id'>Stop test content</div>")
            .Add(p => p.ReserveSpace, true)
            .Add(p => p.CollapseOnFadeComplete, false)
            .Add(p => p.ShowContentFor, TimeSpan.FromMilliseconds(50))
            .Add(p => p.FadeFor, TimeSpan.FromMilliseconds(50))
            .Add(p => p.AutoRestart, false)
            .Add(p => p.WhenStopped, isAutoRestart =>
            {
                stopCount++;
                wasManualStop = !isAutoRestart;
            })
            .Add(p => p.AfterFadeFinished, () => hasCompleted = true)
        );

        // Verify initial state
        Assert.False(cut.Instance.IsRunning);

        // Start timeout
        cut.Instance.Initiate();
        Assert.True(cut.Instance.IsRunning);

        // Wait a bit then manually stop before completion
        await Task.Delay(TimeSpan.FromMilliseconds(10));
        cut.Instance.Stop();

        // Wait to ensure it doesn't complete naturally
        await Task.Delay(TimeSpan.FromMilliseconds(150));

        // Verify stop behavior
        Assert.False(hasCompleted);
        Assert.Equal(1, stopCount);
        Assert.True(wasManualStop);
        Assert.False(cut.Instance.IsRunning);
    }
}