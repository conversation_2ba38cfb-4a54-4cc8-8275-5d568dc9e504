﻿namespace CCTC_Components.Components.Radio
{
    /// <summary>
    /// Radio label tooltip behaviour
    /// </summary>
    public enum RadioLabelTooltipBehaviour
    {
        /// <summary>
        /// Enable radio label tooltip when the label overflows its container
        /// </summary>
        EnabledOnLabelOverflow,
        /// <summary>
        /// Enable radio label tooltip
        /// </summary>
        Enabled,
        /// <summary>
        /// Disable radio label tooltip
        /// </summary>
        Disabled
    }
}
