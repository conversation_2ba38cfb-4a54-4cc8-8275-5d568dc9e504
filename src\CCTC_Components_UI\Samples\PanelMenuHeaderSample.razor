﻿@page "/panelmenuheadersample"

@{
    var description = new List<string>
    {
        "A PanelMenuHeader component represents a container for PanelMenuItems. " +
        "The PanelMenuHeader doesn't support navigation but can be collapsed and expanded to help manage large PanelMenus.",
    };

    var relatedComponents = new List<(Relation relation, string display, string url, string description)>
    {
        (Relation.Parent, "PanelMenu", "panelmenusample", "A PanelMenuHeader has a single PanelMenu parent"),
        (Relation.Child, "PanelMenuItem", "panelmenuitemsample", "A PanelMenuHeader can contain one or more PanelMenuItems")
    };

}

<Sampler
    ComponentName="PanelMenuHeader"
    ComponentCssName="panelmenuheader"
    Description="@description"
    RelatedComponents="@relatedComponents">
</Sampler>

@code {

}