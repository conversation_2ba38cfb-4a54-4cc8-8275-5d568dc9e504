﻿@page "/checkboxsample"

@{
    var description = new List<string>
    {
        "A component for checkbox input"
    };

    var features = new List<(string, string)>
    {
        ("Interaction", "Can be made read-only and / or disabled. The read-only icon is optional"),
        ("Orientation", "The orientation of the checkbox relative to the label. Can be set to Left (default) or Right"),
        ("Label", "Label overflow can be set to Wrap (default), NoWrap, Scroll or None. The label is optional"),
        ("Tooltip", "Tooltip behaviour can be set to EnabledOnLabelOverflow (default), Enabled or Disabled. Tooltip placement is configurable")
    };

    var gotchas = new List<(string, string)>
    {
        ("Sizing", "the css variable --cctc-input-height sets the minimum height of the component. The component height can be greater than this depending on the length of the provided label. The input checkbox scales according to the value of --cctc-input-height")
    };

    var tips = new List<(string, string)>
    {
        ("Label wrapping", "When the label is set to wrap, the --cctc-input-webkit-line-clamp css variable sets the maximum number of lines before truncation is applied"),
        ("Target the cctc-input component(s) on a page ignoring any child cctc-input components. For example, setting a uniform input component width adjusting according to screen size",
@"
<code>
    <pre>

    ::deep cctc-input:not(cctc-input cctc-input) {
        width: 100%;
    }

    @media (min-width: 1200px) {
        ::deep cctc-input:not(cctc-input cctc-input) {
            width: 35%;
        }
    }
    </pre>
</code>")
    };

    var usageCode = new List<(string title, string description, string code, RenderFragment fragment)>
    {
        ("CheckBoxOrientation: Left, CheckBoxLabelOverflow: Wrap, CheckBoxLabelTooltipPlacement: Right, CheckBoxLabelTooltipBehaviour: EnabledOnLabelOverflow, with callback", "",
@"<CheckBox
    Id=""usage1""
    @bind-Value=""CheckBoxValue1""
    Label=""@CheckBoxLabel1""
    CheckBoxOrientation=""CheckBoxOrientation.Left""
    CheckBoxLabelOverflow=""CheckBoxLabelOverflow.Wrap""
    CheckBoxLabelTooltipPlacement=""TooltipPlacement.Right""
    CheckBoxLabelTooltipBehaviour=""CheckBoxLabelTooltipBehaviour.EnabledOnLabelOverflow"">
</CheckBox>

@code {

    bool CheckBoxValue1 { get; set; } = true;

    string? CheckBoxLabel1 { get; set; } = ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur."";
}",
        @<CheckBox
            Id="usage1"
            @bind-Value="CheckBoxValue1"
            Label="@CheckBoxLabel1"
            CheckBoxOrientation="CheckBoxOrientation.Left"
            CheckBoxLabelOverflow="CheckBoxLabelOverflow.Wrap"
            CheckBoxLabelTooltipPlacement="TooltipPlacement.Right"
            CheckBoxLabelTooltipBehaviour="CheckBoxLabelTooltipBehaviour.EnabledOnLabelOverflow"
            CheckBoxChanged=@(args => Console.WriteLine($"Checkbox changed to {args.Value!.ToString()}"))>
        </CheckBox>),
        ("CheckBoxOrientation: Right, CheckBoxLabelOverflow: Wrap, CheckBoxLabelTooltipPlacement: Left, CheckBoxLabelTooltipBehaviour: EnabledOnLabelOverflow, with callback", "",
@"<CheckBox
    TValue=""bool""
    Id=""usage2""
    Value=""CheckBoxValue2""
    ValueChanged=""OnCheckBoxValue2Changed""
    Label=""@CheckBoxLabel1""
    CheckBoxOrientation=""CheckBoxOrientation.Right""
    CheckBoxLabelOverflow=""CheckBoxLabelOverflow.Wrap""
    CheckBoxLabelTooltipPlacement=""TooltipPlacement.Left""
    CheckBoxLabelTooltipBehaviour=""CheckBoxLabelTooltipBehaviour.EnabledOnLabelOverflow"">
</CheckBox>

@code {

    bool CheckBoxValue2 { get; set; } = false;

    string? CheckBoxLabel1 { get; set; } = ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur."";

    void OnCheckBoxValue2Changed(bool newValue)
    {
        CheckBoxValue2 = newValue;
        Console.WriteLine($""Checkbox value changed with new value {newValue}"");
    }
}",
        @<CheckBox
            TValue="bool"
            Id="usage2"
            Value="CheckBoxValue2"
            ValueChanged="OnCheckBoxValue2Changed"
            Label="@CheckBoxLabel1"
            CheckBoxOrientation="CheckBoxOrientation.Right"
            CheckBoxLabelOverflow="CheckBoxLabelOverflow.Wrap"
            CheckBoxLabelTooltipPlacement="TooltipPlacement.Left"
            CheckBoxLabelTooltipBehaviour="CheckBoxLabelTooltipBehaviour.EnabledOnLabelOverflow">
        </CheckBox>),
        ("CheckBoxOrientation: Left, CheckBoxLabelOverflow: NoWrap, CheckBoxLabelTooltipPlacement: Top, CheckBoxLabelTooltipBehaviour: Enabled", "",
@"<CheckBox
    Id=""usage3""
    @bind-Value=""CheckBoxValue1""
    Label=""@CheckBoxLabel2""
    CheckBoxOrientation=""CheckBoxOrientation.Left""
    CheckBoxLabelOverflow=""CheckBoxLabelOverflow.NoWrap""
    CheckBoxLabelTooltipPlacement=""TooltipPlacement.Top""
    CheckBoxLabelTooltipBehaviour=""CheckBoxLabelTooltipBehaviour.Enabled"">
</CheckBox>

@code {

    bool CheckBoxValue1 { get; set; } = true;

    string? CheckBoxLabel2 { get; set; } = ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."";
}",
        @<CheckBox
            Id="usage3"
            @bind-Value="CheckBoxValue1"
            Label="@CheckBoxLabel2"
            CheckBoxOrientation="CheckBoxOrientation.Left"
            CheckBoxLabelOverflow="CheckBoxLabelOverflow.NoWrap"
            CheckBoxLabelTooltipPlacement="TooltipPlacement.Top"
            CheckBoxLabelTooltipBehaviour="CheckBoxLabelTooltipBehaviour.Enabled">
        </CheckBox>),
        ("CheckBoxOrientation: Left, CheckBoxLabelOverflow: NoWrap, CheckBoxLabelTooltipBehaviour: Disabled, CssClass applied", "",
@"<CheckBox
    Id=""usage4""
    @bind-Value=""CheckBoxValue1""
    CssClass=""red-border""
    Label=""@CheckBoxLabel2""
    CheckBoxOrientation=""CheckBoxOrientation.Left""
    CheckBoxLabelOverflow=""CheckBoxLabelOverflow.NoWrap""
    CheckBoxLabelTooltipBehaviour=""CheckBoxLabelTooltipBehaviour.Disabled"">
</CheckBox>

@code {

    bool CheckBoxValue1 { get; set; } = true;

    string? CheckBoxLabel2 { get; set; } = ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."";
}",
        @<CheckBox
            Id="usage4"
            @bind-Value="CheckBoxValue1"
            CssClass="red-border"
            Label="@CheckBoxLabel2"
            CheckBoxOrientation="CheckBoxOrientation.Left"
            CheckBoxLabelOverflow="CheckBoxLabelOverflow.NoWrap"
            CheckBoxLabelTooltipBehaviour="CheckBoxLabelTooltipBehaviour.Disabled">
        </CheckBox>),
        ("CheckBoxOrientation: Left, CheckBoxLabelOverflow: Scroll, CheckBoxLabelTooltipBehaviour: Disabled", "",
@"<CheckBox
    Id=""usage5""
    @bind-Value=""CheckBoxValue1""
    Label=""@CheckBoxLabel2""
    CheckBoxOrientation=""CheckBoxOrientation.Left""
    CheckBoxLabelOverflow=""CheckBoxLabelOverflow.Scroll""
    CheckBoxLabelTooltipBehaviour=""CheckBoxLabelTooltipBehaviour.Disabled"">
</CheckBox>

@code {

    bool CheckBoxValue1 { get; set; } = true;

    string? CheckBoxLabel2 { get; set; } = ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."";
}",
        @<CheckBox
            Id="usage5"
            @bind-Value="CheckBoxValue1"
            Label="@CheckBoxLabel2"
            CheckBoxOrientation="CheckBoxOrientation.Left"
            CheckBoxLabelOverflow="CheckBoxLabelOverflow.Scroll"
            CheckBoxLabelTooltipBehaviour="CheckBoxLabelTooltipBehaviour.Disabled">
        </CheckBox>),
        ("CheckBoxOrientation: Left, CheckBoxLabelOverflow: None, CheckBoxLabelTooltipBehaviour: Disabled", "",
@"<CheckBox
    Id=""usage6""
    @bind-Value=""CheckBoxValue1""
    Label=""@CheckBoxLabel1""
    CheckBoxOrientation=""CheckBoxOrientation.Left""
    CheckBoxLabelOverflow=""CheckBoxLabelOverflow.None""
    CheckBoxLabelTooltipBehaviour=""CheckBoxLabelTooltipBehaviour.Disabled"">
</CheckBox>

@code {

    bool CheckBoxValue1 { get; set; } = true;

    string? CheckBoxLabel1 { get; set; } = ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur."";
}",
        @<CheckBox
            Id="usage6"
            @bind-Value="CheckBoxValue1"
            Label="@CheckBoxLabel1"
            CheckBoxOrientation="CheckBoxOrientation.Left"
            CheckBoxLabelOverflow="CheckBoxLabelOverflow.None"
            CheckBoxLabelTooltipBehaviour="CheckBoxLabelTooltipBehaviour.Disabled">
        </CheckBox>),
        ("CheckBoxOrientation: Left, CheckBoxLabelOverflow: Wrap, CheckBoxLabelTooltipBehaviour: EnabledOnLabelOverflow, read-only", "",
@"<CheckBox
    Id=""usage7""
    @bind-Value=""CheckBoxValue1""
    Label=""@CheckBoxLabel1""
    CheckBoxOrientation=""CheckBoxOrientation.Left""
    CheckBoxLabelOverflow=""CheckBoxLabelOverflow.Wrap""
    CheckBoxLabelTooltipBehaviour=""CheckBoxLabelTooltipBehaviour.EnabledOnLabelOverflow""
    ReadOnly=""true"">
</CheckBox>

@code {

    bool CheckBoxValue1 { get; set; } = true;

    string? CheckBoxLabel1 { get; set; } = ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur."";
}",
        @<CheckBox
            Id="usage7"
            @bind-Value="CheckBoxValue1"
            Label="@CheckBoxLabel1"
            CheckBoxOrientation="CheckBoxOrientation.Left"
            CheckBoxLabelOverflow="CheckBoxLabelOverflow.Wrap"
            CheckBoxLabelTooltipBehaviour="CheckBoxLabelTooltipBehaviour.EnabledOnLabelOverflow"
            ReadOnly="true">
        </CheckBox>),
        ("CheckBoxOrientation: Left, CheckBoxLabelOverflow: Wrap, CheckBoxLabelTooltipBehaviour: EnabledOnLabelOverflow, read-only, hide read-only icon", "",
@"<CheckBox
    Id=""usage8""
    @bind-Value=""CheckBoxValue1""
    Label=""@CheckBoxLabel1""
    CheckBoxOrientation=""CheckBoxOrientation.Left""
    CheckBoxLabelOverflow=""CheckBoxLabelOverflow.Wrap""
    CheckBoxLabelTooltipBehaviour=""CheckBoxLabelTooltipBehaviour.EnabledOnLabelOverflow""
    ReadOnly=""true""
    HideReadOnlyIcon=""true"">
</CheckBox>

@code {

    bool CheckBoxValue1 { get; set; } = true;

    string? CheckBoxLabel1 { get; set; } = ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur."";
}",
        @<CheckBox
            Id="usage8"
            @bind-Value="CheckBoxValue1"
            Label="@CheckBoxLabel1"
            CheckBoxOrientation="CheckBoxOrientation.Left"
            CheckBoxLabelOverflow="CheckBoxLabelOverflow.Wrap"
            CheckBoxLabelTooltipBehaviour="CheckBoxLabelTooltipBehaviour.EnabledOnLabelOverflow"
            ReadOnly="true"
            HideReadOnlyIcon="true">
        </CheckBox>),
        ("CheckBoxOrientation: Left, CheckBoxLabelOverflow: Wrap, CheckBoxLabelTooltipBehaviour: EnabledOnLabelOverflow, disabled", "",
@"<CheckBox
    Id=""usage9""
    @bind-Value=""CheckBoxValue1""
    Label=""@CheckBoxLabel1""
    CheckBoxOrientation=""CheckBoxOrientation.Left""
    CheckBoxLabelOverflow=""CheckBoxLabelOverflow.Wrap""
    CheckBoxLabelTooltipBehaviour=""CheckBoxLabelTooltipBehaviour.EnabledOnLabelOverflow""
    Disabled=""true"">
</CheckBox>

@code {

    bool CheckBoxValue1 { get; set; } = true;

    string? CheckBoxLabel1 { get; set; } = ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur."";
}",
        @<CheckBox
            Id="usage9"
            @bind-Value="CheckBoxValue1"
            Label="@CheckBoxLabel1"
            CheckBoxOrientation="CheckBoxOrientation.Left"
            CheckBoxLabelOverflow="CheckBoxLabelOverflow.Wrap"
            CheckBoxLabelTooltipBehaviour="CheckBoxLabelTooltipBehaviour.EnabledOnLabelOverflow"
            Disabled="true">
        </CheckBox>),
        ("CheckBoxOrientation: Right, CheckBoxLabelOverflow: Wrap, CheckBoxLabelTooltipBehaviour: EnabledOnLabelOverflow, disabled and read-only", "",
@"<CheckBox
    Id=""usage10""
    @bind-Value=""CheckBoxValue1""
    Label=""@CheckBoxLabel1""
    CheckBoxOrientation=""CheckBoxOrientation.Right""
    CheckBoxLabelOverflow=""CheckBoxLabelOverflow.Wrap""
    CheckBoxLabelTooltipBehaviour=""CheckBoxLabelTooltipBehaviour.EnabledOnLabelOverflow""
    Disabled=""true""
    ReadOnly=""true"">
</CheckBox>

@code {

    bool CheckBoxValue1 { get; set; } = true;

    string? CheckBoxLabel1 { get; set; } = ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur."";
}",
        @<CheckBox
            Id="usage10"
            @bind-Value="CheckBoxValue1"
            Label="@CheckBoxLabel1"
            CheckBoxOrientation="CheckBoxOrientation.Right"
            CheckBoxLabelOverflow="CheckBoxLabelOverflow.Wrap"
            CheckBoxLabelTooltipBehaviour="CheckBoxLabelTooltipBehaviour.EnabledOnLabelOverflow"
            Disabled="true"
            ReadOnly="true">
        </CheckBox>),
        ("CheckBoxOrientation: Left, CheckBoxLabelOverflow: Wrap, CheckBoxLabelTooltipPlacement: Right, CheckBoxLabelTooltipBehaviour: EnabledOnLabelOverflow, null initial value, line clamp modified via Style parameter", "",
@"<CheckBox
    Id=""usage11""
    Style=""--cctc-input-webkit-line-clamp: 2;""
    @bind-Value=""CheckBoxValue3""
    Label=""@CheckBoxLabel1""
    CheckBoxOrientation=""CheckBoxOrientation.Left""
    CheckBoxLabelOverflow=""CheckBoxLabelOverflow.Wrap""
    CheckBoxLabelTooltipPlacement=""TooltipPlacement.Right""
    CheckBoxLabelTooltipBehaviour=""CheckBoxLabelTooltipBehaviour.EnabledOnLabelOverflow"">
</CheckBox>

@code {

    bool? CheckBoxValue3 { get; set; } = null;

    string? CheckBoxLabel1 { get; set; } = ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur."";
}",
        @<CheckBox
            Id="usage11"
            Style="--cctc-input-webkit-line-clamp: 2;"
            @bind-Value="CheckBoxValue3"
            Label="@CheckBoxLabel1"
            CheckBoxOrientation="CheckBoxOrientation.Left"
            CheckBoxLabelOverflow="CheckBoxLabelOverflow.Wrap"
            CheckBoxLabelTooltipPlacement="TooltipPlacement.Right"
            CheckBoxLabelTooltipBehaviour="CheckBoxLabelTooltipBehaviour.EnabledOnLabelOverflow">
        </CheckBox>),
        ("CheckBoxOrientation: Left, CheckBoxLabelOverflow: Wrap, CheckBoxLabelTooltipPlacement: Right, CheckBoxLabelTooltipBehaviour: EnabledOnLabelOverflow, input height modified via Style parameter", "",
@"<CheckBox
    Id=""usage12""
    Style=""--cctc-input-height: 3rem;""
    @bind-Value=""CheckBoxValue1""
    Label=""@CheckBoxLabel1""
    CheckBoxOrientation=""CheckBoxOrientation.Left""
    CheckBoxLabelOverflow=""CheckBoxLabelOverflow.Wrap""
    CheckBoxLabelTooltipPlacement=""TooltipPlacement.Right""
    CheckBoxLabelTooltipBehaviour=""CheckBoxLabelTooltipBehaviour.EnabledOnLabelOverflow"">
</CheckBox>

@code {

    bool CheckBoxValue1 { get; set; } = true;

    string? CheckBoxLabel1 { get; set; } = ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur."";
}",
        @<CheckBox
            Id="usage12"
            Style="--cctc-input-height: 3rem;"
            @bind-Value="CheckBoxValue1"
            Label="@CheckBoxLabel1"
            CheckBoxOrientation="CheckBoxOrientation.Left"
            CheckBoxLabelOverflow="CheckBoxLabelOverflow.Wrap"
            CheckBoxLabelTooltipPlacement="TooltipPlacement.Right"
            CheckBoxLabelTooltipBehaviour="CheckBoxLabelTooltipBehaviour.EnabledOnLabelOverflow">
        </CheckBox>),
        ("No label with style applied and default configuration", "",
@"<CheckBox
    Id=""usage13""
    Style=""width: fit-content;""
    @bind-Value=""CheckBoxValue1"">
</CheckBox>

@code {

    bool CheckBoxValue1 { get; set; } = true;
}",
        @<CheckBox
            Id="usage13"
            Style="width: fit-content;"
            @bind-Value="CheckBoxValue1">
        </CheckBox>)
    };
}

@* The wrapper div is necessary here for ::deep isolated css rules to be applied to the CheckBox component *@
<div>
    <Sampler
        ComponentName="CheckBox"
        ComponentCssName="input"
        ComponentTypeName="checkbox"
        Description="@description"
        Features="@features"
        UsageText="Typical usages of the <code>CheckBox</code> component are shown below"
        UsageCodeList="@usageCode"
        Gotchas="@gotchas"
        Tips="@tips"
        ContentHeightPixels="450">
        <ExampleTemplate>
            <CheckBox
                Id="Example1"
                @bind-Value="CheckBoxValue1"
                Label="@CheckBoxLabel1"
                CheckBoxOrientation="CheckBoxOrientation.Left"
                CheckBoxLabelOverflow="CheckBoxLabelOverflow.Wrap"
                CheckBoxLabelTooltipPlacement="TooltipPlacement.Right"
                CheckBoxLabelTooltipBehaviour="CheckBoxLabelTooltipBehaviour.EnabledOnLabelOverflow"
                CheckBoxChanged=@(args => Console.WriteLine($"Checkbox changed to {args.Value!.ToString()}"))>
            </CheckBox>
        </ExampleTemplate>
    </Sampler>
</div>

@code {

    bool CheckBoxValue1 { get; set; } = true;

    bool CheckBoxValue2 { get; set; } = false;

    bool? CheckBoxValue3 { get; set; } = null;

    string? CheckBoxLabel1 { get; set; } = "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.";

    string? CheckBoxLabel2 { get; set; } = "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.";

    void OnCheckBoxValue2Changed(bool newValue)
    {
        CheckBoxValue2 = newValue;
        Console.WriteLine($"Checkbox value changed with new value {newValue}");
    }
}
