@component @temporal @time @time_1
Feature: the time component can receive time input

    <PERSON>enario: the time component sample page is available
        Given the user is at the home page
        When the user selects the "Time" component in the container "Input"
        Then the url ending is "timesample"

    Scenario: the time component can receive time input
        Given the user is at the home page
        And the user selects the "Time" component in the container "Input"
        When the user enters "141618" into the Time component
        Then the Time component has the value "14:16:18"