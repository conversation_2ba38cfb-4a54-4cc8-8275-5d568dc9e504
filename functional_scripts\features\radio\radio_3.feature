@component @radio @radio_3
Feature: the radio component can allow an empty value and an entered value can be optionally cleared
    Scenario: the current selected radio option can be empty when show clear is set to true
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "RadioOrientation: Horizontal, null initial value with show clear"
        Then the Radio component does not have a selected radio option
        And the Radio component image matches the base image "radio-cleared-hide-clear-icon"

    Scenario: the current selected radio option can be cleared when show clear is set to true
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "RadioOrientation: VerticalLeft, nullable tuple data with show clear"
        And the current selected radio option has the text "Yes"
        And the Radio component image matches the base image "radio-show-clear-icon"
        When the user clicks on the radio clear icon
        Then the Radio component does not have a selected radio option
        And the radio clear icon is no longer in view

    <PERSON>ena<PERSON>: the current selected radio option can be empty when show clear is set to false
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "RadioOrientation: Horizontal, null initial value, CssClass applied"
        Then the Radio component does not have a selected radio option