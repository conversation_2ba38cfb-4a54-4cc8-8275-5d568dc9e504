﻿using CCTC_Components.Components;

namespace CCTC_Components.bUnit.test
{
    public class AnimatedPlaceholderTests : CCTCComponentsTestContext
    {
        [Fact]
        public void AnimatedPlaceholderInvalidMarginThrowsArgumentException()
        {
            var cut = () => RenderComponent<AnimatedPlaceholder>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Height, "100px")
                .Add(p => p.Width, "200px")
                .Add(p => p.<PERSON><PERSON>, "3rem 5% 5px 3rem 4rem")
            );

            var expectedMessage = "The Margin parameter value (3rem 5% 5px 3rem 4rem) is not valid";
            var actual = Assert.Throws<ArgumentException>(cut);
            Assert.Equal(expectedMessage, actual.Message);
        }

        [Fact]
        public void AnimatedPlaceholderValidMarginDoesNotThrowArgumentException()
        {
            RenderComponent<AnimatedPlaceholder>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Height, "100px")
                .Add(p => p.Width, "200px")
                .Add(p => p.Margin, "1rem")
            );
        }

        [Fact]
        public void AnimatedPlaceholderInvalidHeightThrowsArgumentException()
        {
            var cut = () => RenderComponent<AnimatedPlaceholder>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Height, "15p")
                .Add(p => p.Width, "200px")
            );

            var expectedMessage = "The Height parameter value (15p) is not valid";
            var actual = Assert.Throws<ArgumentException>(cut);
            Assert.Equal(expectedMessage, actual.Message);
        }

        [Fact]
        public void AnimatedPlaceholderValidHeightDoesNotThrowArgumentException()
        {
            RenderComponent<AnimatedPlaceholder>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Height, "auto")
                .Add(p => p.Width, "200px")
            );
        }

        [Fact]
        public void AnimatedPlaceholderInvalidWidthThrowsArgumentException()
        {
            var cut = () => RenderComponent<AnimatedPlaceholder>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Height, "100px")
                .Add(p => p.Width, "1..2rem")
            );

            var expectedMessage = "The Width parameter value (1..2rem) is not valid";
            var actual = Assert.Throws<ArgumentException>(cut);
            Assert.Equal(expectedMessage, actual.Message);
        }

        [Fact]
        public void AnimatedPlaceholderValidWidthDoesNotThrowArgumentException()
        {
            RenderComponent<AnimatedPlaceholder>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Height, "100px")
                .Add(p => p.Width, "1px")
            );
        }
    }
}
