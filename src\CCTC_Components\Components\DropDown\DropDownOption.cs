﻿namespace CCTC_Components.Components.DropDown
{
    /// <summary>
    /// Represents an option within a <see cref="DropDown"/>
    /// </summary>
    /// <typeparam name="TData">The data type for the DropDown</typeparam>
    /// <remarks>Used for wrapping each option with information required for DropDown</remarks>
    public class DropDownOption<TData>
    {
        /// <summary>
        /// Index of the item
        /// </summary>
        public int Index { get; set; }

        /// <summary>
        /// The option data
        /// </summary>
        public required TData OptionData { get; set; }

        /// <summary>
        /// The option value
        /// </summary>
        public required string OptionValue { get; set; }

        /// <summary>
        /// The option display
        /// </summary>
        public required string OptionDisplay { get; set; }

        /// <summary>
        /// True when the option is selected
        /// </summary>
        public required bool IsOptionSelected { get; set; }

        /// <summary>
        /// True when the option is visible
        /// </summary>
        public required bool IsOptionVisible { get; set; }

        /// <summary>
        /// True when the option is disabled
        /// </summary>
        public required bool IsOptionDisabled { get; set; }
    }
}
