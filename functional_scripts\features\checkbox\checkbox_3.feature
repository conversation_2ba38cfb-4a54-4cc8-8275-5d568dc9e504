@component @checkbox @checkbox_3

Feature: the checkbox component handles a label that has a large amount of text and can display a tooltip when required

    Scenario: the checkbox label can be truncated on a single line
        Given the user is at the home page
        And the user selects the "Checkbox" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "CheckBoxOrientation: Left, CheckBoxLabelOverflow: NoWrap, CheckBoxLabelTooltipBehaviour: Disabled, CssClass applied"
        Then the Checkbox component image matches the base image "checkbox-truncated-one-line"

    Scenario: the checkbox label can be truncated on a two lines
        Given the user is at the home page
        And the user selects the "Checkbox" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "CheckBoxOrientation: Left, CheckBoxLabelOverflow: Wrap, CheckBoxLabelTooltipPlacement: Right, CheckBoxLabelTooltipBehaviour: EnabledOnLabelOverflow, null initial value, line clamp modified via Style parameter"
        Then the Checkbox component image matches the base image "checkbox-truncated-two-lines"

    Scenario: the checkbox label can be truncated on three lines
        Given the user is at the home page
        And the user selects the "Checkbox" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "CheckBoxOrientation: Left, CheckBoxLabelOverflow: Wrap, CheckBoxLabelTooltipPlacement: Right, CheckBoxLabelTooltipBehaviour: EnabledOnLabelOverflow, with callback"
        Then the Checkbox component image matches the base image "checkbox-truncated-three-lines"

    Scenario: the checkbox label can scroll
        Given the user is at the home page
        And the user selects the "Checkbox" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "CheckBoxOrientation: Left, CheckBoxLabelOverflow: Scroll, CheckBoxLabelTooltipBehaviour: Disabled"
        Then the checkbox label can scroll
        And the Checkbox component image matches the base image "checkbox-scroll-label"

    Scenario: the checkbox can display a tooltip
        Given the user is at the home page
        And the user selects the "Checkbox" component in the container "Input"
        Then the checkbox has a tooltip enabled containing the full label text

    Scenario: the checkbox can not have a tooltip
        Given the user is at the home page
        And the user selects the "Checkbox" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "CheckBoxOrientation: Left, CheckBoxLabelOverflow: NoWrap, CheckBoxLabelTooltipBehaviour: Disabled, CssClass applied"
        Then the checkbox does not have a tooltip enabled containing the full label text

     Scenario: the checkbox label can display a large amount of text
        Given the user is at the home page
        And the user selects the "Checkbox" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "CheckBoxOrientation: Left, CheckBoxLabelOverflow: None, CheckBoxLabelTooltipBehaviour: Disabled"
        Then the Checkbox component image matches the base image "checkbox-full-text"

