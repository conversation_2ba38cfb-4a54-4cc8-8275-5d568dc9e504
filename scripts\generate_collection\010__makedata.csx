#load "000__env.csx"

var init = NamedNodeIdentifier.Create("_Base", "Theme", Guid.NewGuid(), new[] { "CCTU_CT"});
var tree = new TreeNode<NamedNodeIdentifier>(init);

//teans
var aero = tree.AddChild(NamedNodeIdentifier.Create("_Base", "Team", Guid.NewGuid(), new[] { "AeroDigestive" }));
var womens = tree.AddChild(NamedNodeIdentifier.Create("_Base", "Team", Guid.NewGuid(), new[] { "Womens" }));

//trials
//aero
var trial1 = aero.AddChild(NamedNodeIdentifier.Create("_Base", "Trial", Guid.NewGuid(), new[] { "Aero", "Trial 1" }));
var trial2 = aero.AddChild(NamedNodeIdentifier.Create("_Base", "Trial", Guid.NewGuid(), new[] { "Aero", "Trial 2" }));
var trial3 = aero.AddChild(NamedNodeIdentifier.Create("_Base", "Trial", Guid.NewGuid(), new[] { "Aero", "Trial 3" }));
var trial4 = aero.AddChild(NamedNodeIdentifier.Create("_Base", "Trial", Guid.NewGuid(), new[] { "Aero", "Trial 4" }));

//womens
var trial11 = womens.AddChild(NamedNodeIdentifier.Create("_Base", "Trial", Guid.NewGuid(), new[] { "womens", "Trial 1" }));
var trial12 = womens.AddChild(NamedNodeIdentifier.Create("_Base", "Trial", Guid.NewGuid(), new[] { "womens", "Trial 2" }));
var trial13 = womens.AddChild(NamedNodeIdentifier.Create("_Base", "Trial", Guid.NewGuid(), new[] { "womens", "Trial 3" }));
var trial14 = womens.AddChild(NamedNodeIdentifier.Create("_Base", "Trial", Guid.NewGuid(), new[] { "womens", "Trial 4" }));



var patient1_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 1" }));
var patient2_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 2" }));
var patient3_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 3" }));
var patient4_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 4" }));
var patient5_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 5" }));
var patient6_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 6" }));
var patient7_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 7" }));
var patient8_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 8" }));
var patient9_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 9" }));
var patient10_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 10" }));
var patient11_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 11" }));
var patient12_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 12" }));
var patient13_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 13" }));
var patient14_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 14" }));
var patient15_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 15" }));
var patient16_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 16" }));
var patient17_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 17" }));
var patient18_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 18" }));
var patient19_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 19" }));
var patient20_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 20" }));
var patient21_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 21" }));
var patient22_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 22" }));
var patient23_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 23" }));
var patient24_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 24" }));
var patient25_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 25" }));
var patient26_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 26" }));
var patient27_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 27" }));
var patient28_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 28" }));
var patient29_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 29" }));
var patient30_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 30" }));
var patient31_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 31" }));
var patient32_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 32" }));
var patient33_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 33" }));
var patient34_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 34" }));
var patient35_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 35" }));
var patient36_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 36" }));
var patient37_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 37" }));
var patient38_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 38" }));
var patient39_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 39" }));
var patient40_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 40" }));
var patient41_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 41" }));
var patient42_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 42" }));
var patient43_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 43" }));
var patient44_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 44" }));
var patient45_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 45" }));
var patient46_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 46" }));
var patient47_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 47" }));
var patient48_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 48" }));
var patient49_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 49" }));
var patient50_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 1", "Patient 50" }));

var patient1_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 2", "Patient 1" }));
var patient2_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 2", "Patient 2" }));
var patient3_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 2", "Patient 3" }));
var patient4_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 2", "Patient 4" }));
var patient5_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 2", "Patient 5" }));
var patient6_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 2", "Patient 6" }));
var patient7_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 2", "Patient 7" }));
var patient8_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 2", "Patient 8" }));
var patient9_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 2", "Patient 9" }));
var patient10_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 2", "Patient 10" }));

var patient1_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 3", "Patient 1" }));
var patient2_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 3", "Patient 2" }));
var patient3_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 3", "Patient 3" }));
var patient4_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 3", "Patient 4" }));
var patient5_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 3", "Patient 5" }));
var patient6_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 3", "Patient 6" }));
var patient7_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 3", "Patient 7" }));
var patient8_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 3", "Patient 8" }));
var patient9_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 3", "Patient 9" }));
var patient10_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 3", "Patient 10" }));
var patient11_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 3", "Patient 11" }));
var patient12_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 3", "Patient 12" }));
var patient13_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 3", "Patient 13" }));
var patient14_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 3", "Patient 14" }));
var patient15_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 3", "Patient 15" }));
var patient16_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 3", "Patient 16" }));
var patient17_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 3", "Patient 17" }));
var patient18_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 3", "Patient 18" }));
var patient19_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 3", "Patient 19" }));
var patient20_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 3", "Patient 20" }));


var patient1_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 1" }));
var patient2_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 2" }));
var patient3_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 3" }));
var patient4_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 4" }));
var patient5_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 5" }));
var patient6_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 6" }));
var patient7_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 7" }));
var patient8_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 8" }));
var patient9_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 9" }));
var patient10_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 10" }));
var patient11_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 11" }));
var patient12_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 12" }));
var patient13_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 13" }));
var patient14_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 14" }));
var patient15_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 15" }));
var patient16_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 16" }));
var patient17_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 17" }));
var patient18_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 18" }));
var patient19_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 19" }));
var patient20_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 20" }));
var patient21_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 21" }));
var patient22_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 22" }));
var patient23_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 23" }));
var patient24_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 24" }));
var patient25_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 25" }));
var patient26_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 26" }));
var patient27_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 27" }));
var patient28_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 28" }));
var patient29_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 29" }));
var patient30_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 30" }));
var patient31_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 31" }));
var patient32_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 32" }));
var patient33_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 33" }));
var patient34_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 34" }));
var patient35_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 35" }));
var patient36_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 36" }));
var patient37_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 37" }));
var patient38_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 38" }));
var patient39_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 39" }));
var patient40_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 40" }));
var patient41_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 41" }));
var patient42_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 42" }));
var patient43_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 43" }));
var patient44_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 44" }));
var patient45_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 45" }));
var patient46_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 46" }));
var patient47_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 47" }));
var patient48_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 48" }));
var patient49_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 49" }));
var patient50_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 11", "Patient 50" }));

var patient1_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 1" }));
var patient2_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 2" }));
var patient3_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 3" }));
var patient4_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 4" }));
var patient5_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 5" }));
var patient6_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 6" }));
var patient7_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 7" }));
var patient8_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 8" }));
var patient9_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 9" }));
var patient10_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 10" }));
var patient11_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 11" }));
var patient12_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 12" }));
var patient13_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 13" }));
var patient14_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 14" }));
var patient15_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 15" }));
var patient16_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 16" }));
var patient17_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 17" }));
var patient18_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 18" }));
var patient19_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 19" }));
var patient20_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 20" }));
var patient21_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 21" }));
var patient22_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 22" }));
var patient23_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 23" }));
var patient24_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 24" }));
var patient25_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 25" }));
var patient26_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 26" }));
var patient27_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 27" }));
var patient28_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 28" }));
var patient29_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 29" }));
var patient30_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 30" }));
var patient31_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 31" }));
var patient32_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 32" }));
var patient33_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 33" }));
var patient34_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 34" }));
var patient35_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 12", "Patient 35" }));

var patient1_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 1" }));
var patient2_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 2" }));
var patient3_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 3" }));
var patient4_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 4" }));
var patient5_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 5" }));
var patient6_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 6" }));
var patient7_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 7" }));
var patient8_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 8" }));
var patient9_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 9" }));
var patient10_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 10" }));
var patient11_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 11" }));
var patient12_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 12" }));
var patient13_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 13" }));
var patient14_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 14" }));
var patient15_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 15" }));
var patient16_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 16" }));
var patient17_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 17" }));
var patient18_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 18" }));
var patient19_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 19" }));
var patient20_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 20" }));
var patient21_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 21" }));
var patient22_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 22" }));
var patient23_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 23" }));
var patient24_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 24" }));
var patient25_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 25" }));
var patient26_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 26" }));
var patient27_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 27" }));
var patient28_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 28" }));
var patient29_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 29" }));
var patient30_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 30" }));
var patient31_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 31" }));
var patient32_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 32" }));
var patient33_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 33" }));
var patient34_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 34" }));
var patient35_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 35" }));
var patient36_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 36" }));
var patient37_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 37" }));
var patient38_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 38" }));
var patient39_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 39" }));
var patient40_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 40" }));
var patient41_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 41" }));
var patient42_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 42" }));
var patient43_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 43" }));
var patient44_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 44" }));
var patient45_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 45" }));
var patient46_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 46" }));
var patient47_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 47" }));
var patient48_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 48" }));
var patient49_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 49" }));
var patient50_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] {"Trial 13", "Patient 50" }));

var site1_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 1" }));
var site2_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 2" }));
var site3_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 3" }));
var site4_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 4" }));
var site5_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 5" }));
var site6_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 6" }));
var site7_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 7" }));
var site8_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 8" }));
var site9_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 9" }));
var site10_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 10" }));
var site11_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 11" }));
var site12_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 12" }));
var site13_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 13" }));
var site14_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 14" }));
var site15_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 15" }));
var site16_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 16" }));
var site17_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 17" }));
var site18_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 18" }));
var site19_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 19" }));
var site20_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 20" }));
var site21_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 21" }));
var site22_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 22" }));
var site23_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 23" }));
var site24_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 24" }));
var site25_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 25" }));
var site26_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 26" }));
var site27_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 27" }));
var site28_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 28" }));
var site29_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 29" }));
var site30_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 30" }));
var site31_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 31" }));
var site32_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 32" }));
var site33_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 33" }));
var site34_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 34" }));
var site35_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 35" }));
var site36_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 36" }));
var site37_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 37" }));
var site38_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 38" }));
var site39_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 39" }));
var site40_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 40" }));
var site41_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 41" }));
var site42_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 42" }));
var site43_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 43" }));
var site44_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 44" }));
var site45_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 45" }));
var site46_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 46" }));
var site47_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 47" }));
var site48_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 48" }));
var site49_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 49" }));
var site50_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 1", "Site 50" }));

var site1_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 1" }));
var site2_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 2" }));
var site3_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 3" }));
var site4_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 4" }));
var site5_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 5" }));
var site6_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 6" }));
var site7_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 7" }));
var site8_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 8" }));
var site9_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 9" }));
var site10_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 10" }));
var site11_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 11" }));
var site12_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 12" }));
var site13_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 13" }));
var site14_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 14" }));
var site15_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 15" }));
var site16_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 16" }));
var site17_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 17" }));
var site18_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 18" }));
var site19_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 19" }));
var site20_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 20" }));
var site21_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 21" }));
var site22_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 22" }));
var site23_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 23" }));
var site24_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 24" }));
var site25_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 25" }));
var site26_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 26" }));
var site27_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 27" }));
var site28_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 28" }));
var site29_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 29" }));
var site30_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 30" }));
var site31_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 31" }));
var site32_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 32" }));
var site33_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 33" }));
var site34_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 34" }));
var site35_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 35" }));
var site36_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 36" }));
var site37_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 37" }));
var site38_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 38" }));
var site39_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 39" }));
var site40_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 40" }));
var site41_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 41" }));
var site42_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 42" }));
var site43_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 43" }));
var site44_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 44" }));
var site45_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 45" }));
var site46_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 46" }));
var site47_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 47" }));
var site48_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 48" }));
var site49_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 49" }));
var site50_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 2", "Site 50" }));

var site1_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 1" }));
var site2_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 2" }));
var site3_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 3" }));
var site4_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 4" }));
var site5_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 5" }));
var site6_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 6" }));
var site7_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 7" }));
var site8_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 8" }));
var site9_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 9" }));
var site10_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 10" }));
var site11_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 11" }));
var site12_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 12" }));
var site13_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 13" }));
var site14_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 14" }));
var site15_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 15" }));
var site16_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 16" }));
var site17_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 17" }));
var site18_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 18" }));
var site19_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 19" }));
var site20_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 20" }));
var site21_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 21" }));
var site22_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 22" }));
var site23_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 23" }));
var site24_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 24" }));
var site25_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 25" }));
var site26_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 26" }));
var site27_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 27" }));
var site28_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 28" }));
var site29_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 29" }));
var site30_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 30" }));
var site31_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 31" }));
var site32_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 32" }));
var site33_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 33" }));
var site34_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 34" }));
var site35_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 35" }));
var site36_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 36" }));
var site37_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 37" }));
var site38_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 38" }));
var site39_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 39" }));
var site40_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 40" }));
var site41_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 41" }));
var site42_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 42" }));
var site43_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 43" }));
var site44_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 44" }));
var site45_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 45" }));
var site46_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 46" }));
var site47_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 47" }));
var site48_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 48" }));
var site49_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 49" }));
var site50_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 3", "Site 50" }));

var site1_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 1" }));
var site2_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 2" }));
var site3_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 3" }));
var site4_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 4" }));
var site5_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 5" }));
var site6_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 6" }));
var site7_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 7" }));
var site8_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 8" }));
var site9_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 9" }));
var site10_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 10" }));
var site11_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 11" }));
var site12_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 12" }));
var site13_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 13" }));
var site14_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 14" }));
var site15_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 15" }));
var site16_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 16" }));
var site17_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 17" }));
var site18_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 18" }));
var site19_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 19" }));
var site20_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 20" }));
var site21_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 21" }));
var site22_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 22" }));
var site23_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 23" }));
var site24_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 24" }));
var site25_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 25" }));
var site26_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 26" }));
var site27_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 27" }));
var site28_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 28" }));
var site29_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 29" }));
var site30_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 30" }));
var site31_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 31" }));
var site32_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 32" }));
var site33_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 33" }));
var site34_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 34" }));
var site35_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 35" }));
var site36_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 36" }));
var site37_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 37" }));
var site38_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 38" }));
var site39_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 39" }));
var site40_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 40" }));
var site41_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 41" }));
var site42_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 42" }));
var site43_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 43" }));
var site44_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 44" }));
var site45_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 45" }));
var site46_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 46" }));
var site47_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 47" }));
var site48_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 48" }));
var site49_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 49" }));
var site50_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 11", "Site 50" }));

var site1_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 1" }));
var site2_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 2" }));
var site3_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 3" }));
var site4_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 4" }));
var site5_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 5" }));
var site6_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 6" }));
var site7_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 7" }));
var site8_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 8" }));
var site9_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 9" }));
var site10_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 10" }));
var site11_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 11" }));
var site12_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 12" }));
var site13_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 13" }));
var site14_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 14" }));
var site15_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 15" }));
var site16_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 16" }));
var site17_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 17" }));
var site18_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 18" }));
var site19_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 19" }));
var site20_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 20" }));
var site21_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 21" }));
var site22_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 22" }));
var site23_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 23" }));
var site24_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 24" }));
var site25_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 25" }));
var site26_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 26" }));
var site27_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 27" }));
var site28_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 28" }));
var site29_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 29" }));
var site30_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 30" }));
var site31_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 31" }));
var site32_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 32" }));
var site33_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 33" }));
var site34_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 34" }));
var site35_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 35" }));
var site36_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 36" }));
var site37_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 37" }));
var site38_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 38" }));
var site39_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 39" }));
var site40_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 40" }));
var site41_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 41" }));
var site42_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 42" }));
var site43_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 43" }));
var site44_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 44" }));
var site45_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 45" }));
var site46_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 46" }));
var site47_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 47" }));
var site48_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 48" }));
var site49_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 49" }));
var site50_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 12", "Site 50" }));

var site1_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 1" }));
var site2_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 2" }));
var site3_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 3" }));
var site4_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 4" }));
var site5_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 5" }));
var site6_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 6" }));
var site7_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 7" }));
var site8_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 8" }));
var site9_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 9" }));
var site10_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 10" }));
var site11_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 11" }));
var site12_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 12" }));
var site13_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 13" }));
var site14_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 14" }));
var site15_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 15" }));
var site16_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 16" }));
var site17_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 17" }));
var site18_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 18" }));
var site19_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 19" }));
var site20_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 20" }));
var site21_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 21" }));
var site22_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 22" }));
var site23_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 23" }));
var site24_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 24" }));
var site25_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 25" }));
var site26_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 26" }));
var site27_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 27" }));
var site28_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 28" }));
var site29_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 29" }));
var site30_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 30" }));
var site31_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 31" }));
var site32_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 32" }));
var site33_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 33" }));
var site34_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 34" }));
var site35_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 35" }));
var site36_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 36" }));
var site37_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 37" }));
var site38_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 38" }));
var site39_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 39" }));
var site40_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 40" }));
var site41_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 41" }));
var site42_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 42" }));
var site43_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 43" }));
var site44_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 44" }));
var site45_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 45" }));
var site46_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 46" }));
var site47_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 47" }));
var site48_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 48" }));
var site49_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 49" }));
var site50_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] {"Trial 13", "Site 50" }));


var crf1_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 1" }));
var crf2_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 2" }));
var crf3_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 3" }));
var crf4_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 4" }));
var crf5_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 5" }));
var crf6_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 6" }));
var crf7_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 7" }));
var crf8_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 8" }));
var crf9_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 9" }));
var crf10_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 10" }));
var crf11_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 11" }));
var crf12_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 12" }));
var crf13_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 13" }));
var crf14_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 14" }));
var crf15_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 15" }));
var crf16_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 16" }));
var crf17_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 17" }));
var crf18_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 18" }));
var crf19_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 19" }));
var crf20_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 20" }));
var crf21_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 21" }));
var crf22_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 22" }));
var crf23_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 23" }));
var crf24_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 24" }));
var crf25_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 25" }));
var crf26_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 26" }));
var crf27_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 27" }));
var crf28_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 28" }));
var crf29_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 29" }));
var crf30_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 30" }));
var crf31_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 31" }));
var crf32_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 32" }));
var crf33_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 33" }));
var crf34_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 34" }));
var crf35_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 35" }));
var crf36_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 36" }));
var crf37_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 37" }));
var crf38_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 38" }));
var crf39_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 39" }));
var crf40_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 40" }));
var crf41_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 41" }));
var crf42_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 42" }));
var crf43_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 43" }));
var crf44_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 44" }));
var crf45_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 45" }));
var crf46_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 46" }));
var crf47_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 47" }));
var crf48_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 48" }));
var crf49_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 49" }));
var crf50_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient1_Trial1", "Crf 50" }));

var crf1_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 1" }));
var crf2_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 2" }));
var crf3_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 3" }));
var crf4_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 4" }));
var crf5_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 5" }));
var crf6_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 6" }));
var crf7_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 7" }));
var crf8_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 8" }));
var crf9_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 9" }));
var crf10_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 10" }));
var crf11_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 11" }));
var crf12_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 12" }));
var crf13_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 13" }));
var crf14_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 14" }));
var crf15_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 15" }));
var crf16_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 16" }));
var crf17_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 17" }));
var crf18_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 18" }));
var crf19_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 19" }));
var crf20_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 20" }));
var crf21_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 21" }));
var crf22_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 22" }));
var crf23_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 23" }));
var crf24_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 24" }));
var crf25_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 25" }));
var crf26_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 26" }));
var crf27_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 27" }));
var crf28_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 28" }));
var crf29_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 29" }));
var crf30_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 30" }));
var crf31_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 31" }));
var crf32_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 32" }));
var crf33_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 33" }));
var crf34_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 34" }));
var crf35_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 35" }));
var crf36_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 36" }));
var crf37_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 37" }));
var crf38_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 38" }));
var crf39_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 39" }));
var crf40_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 40" }));
var crf41_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 41" }));
var crf42_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 42" }));
var crf43_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 43" }));
var crf44_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 44" }));
var crf45_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 45" }));
var crf46_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 46" }));
var crf47_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 47" }));
var crf48_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 48" }));
var crf49_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 49" }));
var crf50_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient2_Trial1", "Crf 50" }));

var crf1_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 1" }));
var crf2_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 2" }));
var crf3_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 3" }));
var crf4_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 4" }));
var crf5_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 5" }));
var crf6_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 6" }));
var crf7_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 7" }));
var crf8_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 8" }));
var crf9_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 9" }));
var crf10_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 10" }));
var crf11_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 11" }));
var crf12_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 12" }));
var crf13_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 13" }));
var crf14_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 14" }));
var crf15_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 15" }));
var crf16_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 16" }));
var crf17_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 17" }));
var crf18_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 18" }));
var crf19_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 19" }));
var crf20_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 20" }));
var crf21_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 21" }));
var crf22_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 22" }));
var crf23_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 23" }));
var crf24_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 24" }));
var crf25_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 25" }));
var crf26_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 26" }));
var crf27_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 27" }));
var crf28_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 28" }));
var crf29_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 29" }));
var crf30_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 30" }));
var crf31_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 31" }));
var crf32_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 32" }));
var crf33_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 33" }));
var crf34_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 34" }));
var crf35_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 35" }));
var crf36_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 36" }));
var crf37_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 37" }));
var crf38_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 38" }));
var crf39_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 39" }));
var crf40_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 40" }));
var crf41_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 41" }));
var crf42_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 42" }));
var crf43_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 43" }));
var crf44_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 44" }));
var crf45_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 45" }));
var crf46_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 46" }));
var crf47_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 47" }));
var crf48_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 48" }));
var crf49_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 49" }));
var crf50_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] {"Patient3_Trial1", "Crf 50" }));


var x = trial13.Children.Count;
Console.WriteLine(x);

// var enumerator = tree.GetEnumerator();
// while (enumerator.MoveNext())
// {
//     var curr = enumerator.Current;
//     Console.WriteLine(curr.ToString());
//     //Console.WriteLine(enumerator.Current.Schema);
// }

var y = tree.Children.First();
var k = y.Data.Key();
Console.WriteLine(k);



//create a function that can dynamically run when the parent changes




