
//reads and updates the name id helpful for testing

#r "nuget: Lib"

open System
open Common.Helpers

let dataFile = __SOURCE_DIRECTORY__ + @"\..\src\CCTC_Components_UI\wwwroot\data\circa_15k_crf_namedNodes.json"
let data = IO.File.ReadAllText dataFile

type Node =
    {
        Schema : string
        NodeResource : string
    }

type NodeIdentifier =
    {
        Node : Node
        Id : Guid
    }

type NamedNodeIdentifier =
    {
        NodeIdentifier : NodeIdentifier
        Name : string array
    }


let deser = Json.deserialize<NamedNodeIdentifier array> data
let updateIndex (nn : NamedNodeIdentifier) =
    let part = nn.Name[0]
    let index = (part |> String.left 6 |> int) - 1
    let newPart = sprintf "%06i%s" index (part.Substring(6, part.Length - 6))
    let newName = newPart :: (nn.Name |> Array.toList)[1..] |> List.toArray
    { nn with Name = newName }

let newFileText =
    deser
    |> Array.map updateIndex
    |> Json.serialize

IO.File.WriteAllText(dataFile, newFileText)