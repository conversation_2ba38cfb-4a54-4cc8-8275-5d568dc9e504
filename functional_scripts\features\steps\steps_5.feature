@component @steps @steps_5
Feature: the header size and button positions are configurable
    Scenario: The header size can be large with shorter superscript text
        Given the user is at the home page
        And the user selects the "Steps" component in the container "Steps"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "Required data"
        Then the steps headers image matches the base image "larger header sizes"
        And the header font size is "19px"
        And the selected header superscript is "opt"
        When the user clicks on the next button
        Then the selected header superscript is "req"

    <PERSON><PERSON><PERSON>: the header size can be smaller with longer superscript text
        Given the user is at the home page
        And the user selects the "Steps" component in the container "Steps"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "UI configurable"
        Then the steps headers image matches the base image "smaller header sizes"
        And the header font size is "14px"
        And the selected header superscript is "optional"
        When the user clicks on the next button
        Then the selected header superscript is "required"

    Scenario: The step buttons can be at the top
        Given the user is at the home page
        And the user selects the "Steps" component in the container "Steps"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "UI configurable"
        Then the steps buttons are at the top
        And the steps component image matches the base image "step buttons at top"

    Scenario: The step buttons can be at the bottom
        Given the user is at the home page
        And the user selects the "Steps" component in the container "Steps"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "Required data"
        Then the steps buttons are at the bottom
        And the steps component image matches the base image "step buttons at bottom"
