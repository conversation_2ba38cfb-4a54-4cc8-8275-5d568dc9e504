﻿@page "/panelmenuitemsample"

@{
    var description = new List<string>
    {
        "A PanelMenuItem component is the navigable component within a PanelMenu. " +
        "It can be a direct descendant of the PanelMenu, or a descendant of a PanelMenuHeader.",
    };

    var relatedComponents = new List<(Relation relation, string display, string url, string description)>
    {
        (Relation.Parent, "PanelMenu", "panelmenusample", "The containing PanelMenu to which the PanelMenuItem belongs either directly or indirectly via a PanelMenuHeader"),
        (Relation.Parent, "PanelMenuHeader", "panelmenuheadersample", "A PanelMenuHeader has a single PanelMenu parent"),
    };

}

<Sampler
    ComponentName="PanelMenuItem"
    ComponentCssName="panelmenuitem"
    Description="@description"
    RelatedComponents="@relatedComponents">
</Sampler>

@code {

}