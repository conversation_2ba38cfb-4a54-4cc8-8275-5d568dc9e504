﻿using CCTC_Lib.Contracts.Interaction;

namespace CCTC_Components.bUnit.test.Helpers;

/// <summary>
/// Used to ignore any delays in the code
/// </summary>
public class NoDelayService : IDelayService
{
    public Task Delay(TimeSpan timeSpan)
    {
        return Task.CompletedTask;
    }

    public Task Delay(TimeSpan timeSpan, CancellationToken token)
    {
        return Task.CompletedTask;
    }
}