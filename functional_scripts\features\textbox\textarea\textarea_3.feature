@component @textbox @textarea @textarea_3
Feature: the text area component input can be constrained by applying an input mask, preventing whitespace (with a configurable response delay) or setting a max length
    Scenario: the text area component input can be constrained by applying an input mask
        Given the user is at the home page
        And the user selects the "Text area" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "With mask and style applied"
        And the Text area component has the placeholder "(000)000/000/00"
        When the user enters "4857463545867" into the Text area component
        Then the Text area component has the value "(485)746/354/58"

    Scenario: the text area component can prevent whitespace
        Given the user is at the home page
        And the user selects the "Text area" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Reactive with prevent whitespace"
        When the user focusses on the Text area component
        And the user presses the backspace key 13 times
        Then the Text area component has the value "Demo TextArea"

    Scenario: the text area component can set a max length
        Given the user is at the home page
        And the user selects the "Text area" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "With callback and max length of 20"
        When the user enters "Some demo Text greater than max length" into the Text area component
        Then the Text area component has the value "Some demo Text great"