import { ICustomWorld } from '../support/custom-world';
import { compareToBaseImage } from '../utils/compareImages';
import * as Helpers from '../support/helper-functions';
import { When, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';

When(
  'the user clicks the Checkbox within the Checkbox component',
  async function (this: ICustomWorld) {
    await Helpers.getSamplerTabsSelectedContent(this).getByRole('checkbox').last().click();
  }
);

Then('the Checkbox component is checked', async function (this: ICustomWorld) {
  await expect(
    Helpers.getSamplerTabsSelectedContent(this).getByRole('checkbox').last()
  ).toBeChecked();
});

Then('the Checkbox component is unchecked', async function (this: ICustomWorld) {
  await expect(
    Helpers.getSamplerTabsSelectedContent(this).getByRole('checkbox').last()
  ).not.toBeChecked();
});

Then(
  'the Checkbox component image matches the base image {string}',
  async function (this: ICustomWorld, name: string) {
    const screenshot = await Helpers.tryGetStableScreenshot(
      Helpers.getSamplerTabsSelectedContent(this)
        .locator('cctc-input[data-cctc-input-type="checkbox"]')
        .last()
    );
    await compareToBaseImage(this, name, screenshot);
  }
);

When(
  'the user clicks the Label within the Checkbox component',
  async function (this: ICustomWorld) {
    await Helpers.getSamplerTabsSelectedContent(this)
      .locator('cctc-input[data-cctc-input-type="checkbox"] label')
      .last()
      .click();
  }
);

Then(
  'the checkbox has a tooltip enabled containing the full label text',
  async function (this: ICustomWorld) {
    const checkboxLabelText = await Helpers.getSamplerTabsSelectedContent(this)
      .locator('cctc-input[data-cctc-input-type="checkbox"]')
      .innerText();
    await expect(
      Helpers.getSamplerTabsSelectedContent(this).locator('cctc-tooltip')
    ).toHaveAttribute('data-bs-original-title', checkboxLabelText);
  }
);

When('the checkbox label can scroll', async function (this: ICustomWorld) {
  const checkboxLabel = Helpers.getSamplerTabsSelectedContent(this)
    .locator('cctc-input[data-cctc-input-type="checkbox"] label')
    .last();
  await expect(checkboxLabel).toHaveCSS('white-space', 'nowrap');
  await expect(checkboxLabel).toHaveCSS('overflow-x', 'auto');
});

When('the Checkbox component is disabled', async function (this: ICustomWorld) {
  await expect(Helpers.getSamplerTabsSelectedContent(this).getByRole('checkbox').last())
    .toBeDisabled();
});

Then(
  'the checkbox does not have a tooltip enabled containing the full label text',
  async function (this: ICustomWorld) {
    await expect(
      Helpers.getSamplerTabsSelectedContent(this).getByRole('checkbox').locator('cctc-tooltip')
    ).toHaveCount(0);
  }
);
