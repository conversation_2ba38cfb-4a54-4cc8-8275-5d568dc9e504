﻿namespace CCTC_Components.Helpers
{
    /// <summary>
    /// Constants
    /// </summary>
    public static class Constants
    {
        /// <summary>
        /// Pattern for determining a valid number format
        /// </summary>
        public static string NumberFormatPattern => @"^-?(([0#]+)|([eE][1-9]*))((?<![eE])\.?[0#]+[eE][+-]?|())(\.?[0#]{1,15}|[0#]{0,15})$";

        /// <summary>
        /// Pattern for determining a valid css width or height value
        /// </summary>
        public static string CssWidthOrHeightPattern => @"^\s*(\d+\.?\d*(%|px|rem|em)|auto|0)\s*$";

        /// <summary>
        /// Pattern for determining a valid css margin value
        /// </summary>
        public static string CssMarginPattern => @"^\s*(\d+\.?\d*(%|px|rem|em)|auto|0)( (\d+\.?\d*(%|px|rem|em)|auto)){0,3}\s*$";

        /// <summary>
        /// The Playwright custom test id
        /// </summary>
        /// <remarks>https://playwright.dev/docs/locators#locate-by-test-id</remarks>
        public static string PlaywrightCustomTestId => "data-pw";
    }
}
