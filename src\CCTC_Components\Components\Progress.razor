﻿@inherits CCTC_Components.Components.__CCTC.CCTCBase

<cctc-progress id="@Id" class="@CssClass" style="@Style" data-author="cctc">
    <div class="@_componentWrapperClass">
@{
    string progressId = $"{Id}-progress";
    if (Label is not null)
    {
        <label for="@progressId">@Label</label>
    }
        <progress id="@progressId" max="@Max" value="@(IsIndeterminate ? null : Value)"></progress>
    if (ShowValue && !IsIndeterminate)
    {
        <span>@_percentageProgress %</span>
    }
}
    </div>
</cctc-progress>

@code {

    /// <summary>
    /// The maximum expected value
    /// </summary>
    [Parameter]
    public int Max { get; set; } = 100;

    /// <summary>
    /// Show progress when true
    /// </summary>
    [Parameter]
    public bool Show { get; set; } = true;

    /// <summary>
    /// Show value as a percentage of the maximum
    /// </summary>
    [Parameter]
    public bool ShowValue { get; set; } = true;

    /// <summary>
    /// The current value
    /// </summary>
    [Parameter]
    public int Value { get; set; }

    /// <summary>
    /// Show an indeterminate progress bar
    /// </summary>
    [Parameter]
    public bool IsIndeterminate { get; set; }

    /// <summary>
    /// Show a progress label
    /// </summary>
    [Parameter]
    public string? Label { get; set; }

    string? _componentWrapperClass;
    int _percentageProgress;

    int CalculatePercentageProgress(int max, int value)
    {
        return value < max ? Convert.ToInt32(Math.Floor(((decimal)value / max) * 100)) : 100;
    }

    /// <inheritdoc />
    protected override void OnInitialized()
    {
        if (Value > 0 && IsIndeterminate)
        {
            throw new ArgumentException("A value should not be provided when progress is indeterminate");
        }
    }

    /// <inheritdoc />
    protected override void OnParametersSet()
    {
        _componentWrapperClass =
            new CssBuilder("component-wrapper")
                .AddClass("show", Show)
                .Build();

        _percentageProgress = CalculatePercentageProgress(Max, Value);
    }
}
