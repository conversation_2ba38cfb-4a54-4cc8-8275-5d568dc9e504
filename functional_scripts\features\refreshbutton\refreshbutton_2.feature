@component @refreshbutton @refreshbutton_2
Feature: the refresh button text and be short, long, or not exist
    Scenario: The refresh button text is short
        Given the user is at the home page
        And the user selects the "Refresh Button" component in the container "Buttons"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "ButtonIcon: refresh, IconPosition: Left, styled with border"
        Then the button text is "some text"

    Scenario: The refresh button text is long 
        Given the user is at the home page
        And the user selects the "Refresh Button" component in the container "Buttons"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "ButtonIcon: refresh, IconPosition: Left, ButtonText: long text to demonstrate wrapping, CssClass: restricted-width"
        Then the button text is "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat."
        And the button component image matches the base image "refresh button with wrapped text"

    Scenario: The refresh button has no text
        Given the user is at the home page
        And the user selects the "Refresh Button" component in the container "Buttons"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "ButtonIcon: refresh, ButtonText: no text"
        Then the button component image matches the base image "no button text"