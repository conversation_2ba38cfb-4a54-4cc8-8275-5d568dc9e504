﻿namespace CCTC_Components.Components.Lister;

/// <summary>
/// Determines what counts are displayed in a Lister
/// </summary>
/// <remarks>If FilterAndTotal is selected but filtering is turned off will show TotalOnly</remarks>
public enum ListerCountDisplay
{
    /// <summary>
    /// Do not show any display
    /// </summary>
    None,
    /// <summary>
    /// Show total only when CanFilter = true i.e. using internal filter
    /// </summary>
    TotalOnly,
    /// <summary>
    /// Show filtered count and total when CanFilter = true i.e. using internal filter
    /// </summary>
    FilterAndTotal,
    /// <summary>
    /// Show selected count and total count
    /// </summary>
    SelectedAndTotal,
    /// <summary>
    /// Show filtered count, total count and selected count
    /// </summary>
    FilteredAndTotalAndSelected
}