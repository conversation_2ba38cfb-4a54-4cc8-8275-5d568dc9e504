@component @timeout @timeout_5
Feature: the timeout component can start without reserving space and collapse after displaying content

    Scenario: the timeout component does not reserve space initially and collapses space after displaying content
        Given the user is at the home page
        And the user selects the "Time out" component
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Not reserved and collapses"
        And the timeout component does not reserve space
        And the timeout component content is not visible
        When the user clicks the timeout component initiate button
        Then the timeout component displays content
        And the timeout component content is visible
        And the timeout component reserves space
        And the timeout component image matches the base image "timeout-not-reserved-collapses-active"
        When the user waits for 1200 milliseconds
        Then the timeout component is fading
        When the user waits for 2500 milliseconds
        Then the timeout component is not fading
        And the timeout component content is not visible
        And the timeout component does not reserve space
        And the timeout component image matches the base image "timeout-not-reserved-collapses-completed"