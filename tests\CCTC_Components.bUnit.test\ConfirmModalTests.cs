﻿using Blazored.Modal;
using Blazored.Modal.Services;
using CCTC_Components.Components.Modals.Confirm;

namespace CCTC_Components.bUnit.test
{
    public class ConfirmModalTests : CCTCComponentsTestContext
    {
        public ConfirmModalTests()
        {
            JSInterop.Mode = JSRuntimeMode.Loose;
        }

        [Fact]
        public async Task ConfirmModalPositiveClickReturnsCorrectResult()
        {
            IModalService? modalService;
            IRenderedComponent<BlazoredModal>? cut;
            BlazoredModalHelpers.ArrangeBlazoredModal(this, out cut, out modalService);

            var modalParameters = new ModalParameters
            {
                { "Id", "test1" },
                { "Heading", "Submit changes" },
                { "QuestionText", "Are you sure you want to submit your changes?" }
            };

            var modalReference = modalService?.Show<ConfirmModal>("create", modalParameters);
            var positive = cut?.Find("cctc-confirmmodal-responses .ok-response");
            positive?.Click();

            var result = await modalReference!.Result;
            Assert.Equal("True", result.Data?.ToString());
        }

        [Fact]
        public async Task ConfirmModalNegativeClickReturnsCorrectResult()
        {
            IModalService? modalService;
            IRenderedComponent<BlazoredModal>? cut;
            BlazoredModalHelpers.ArrangeBlazoredModal(this, out cut, out modalService);

            var modalParameters = new ModalParameters
            {
                { "Id", "test1" },
                { "Heading", "Submit changes" },
                { "QuestionText", "Are you sure you want to submit your changes?" }
            };

            var modalReference = modalService?.Show<ConfirmModal>("create", modalParameters);
            var negative = cut?.Find("cctc-confirmmodal-responses .cancel-response");
            negative?.Click();

            var result = await modalReference!.Result;
            Assert.True(result.Cancelled);
        }
    }
}
