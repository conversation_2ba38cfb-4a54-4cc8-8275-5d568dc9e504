import { ICustomWorld } from '../support/custom-world';
import { compareToBaseImage } from '../utils/compareImages';
import * as Helpers from '../support/helper-functions';
import { When, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';

Then('the header is {string}', async function (this: ICustomWorld, headerContent: string) {
  const header = Helpers.getSamplerTabsSelectedContent(this)
    .locator(
      'cctc-concertina cctc-concertina-header, cctc-concertina cctc-concertinaitem cctc-concertina cctc-concertina-header'
    )
    .last()
    .getByText(headerContent, { exact: true });
  await expect(header).toHaveText(headerContent);
});

Then(
  'concertina content {string} is visible',
  async function (this: ICustomWorld, contentString: string) {
    const content = Helpers.getSamplerTabsSelectedContent(this)
      .locator('cctc-concertina, cctc-concertina cctc-concertinaitem cctc-concertina')
      .last()
      .getByText(contentString, { exact: true });
    await expect(content).toHaveText(contentString);
  }
);

When('the collapse or expand all button is clicked', async function (this: ICustomWorld) {
  await Helpers.getSamplerTabsSelectedContent(this)
    .locator('cctc-concertina, cctc-concertina cctc-concertinaitem cctc-concertina')
    .last()
    .locator('.config-button')
    .click();
});

Then(
  'concertina content {string} is hidden',
  async function (this: ICustomWorld, contentString: string) {
    const page = this.page!;
    const content = page.getByText(contentString, { exact: true });
    await expect(content).not.toBeAttached();
  }
);

When(
  'the user expands the concertina by clicking on the header with the text {string}',
  async function (this: ICustomWorld, headerText: string) {
    await this.page!.locator('cctc-concertinaitem-header')
      .getByText(headerText, { exact: true })
      .click();
  }
);

Then(
  'the Concertina component image matches the base image {string}',
  async function (this: ICustomWorld, name: string) {
    const screenshot = await Helpers.tryGetStableScreenshot(
      Helpers.getSamplerTabsSelectedContent(this)
        .locator('cctc-concertina, cctc-concertina cctc-concertinaitem cctc-concertina')
        .last()
    );
    await compareToBaseImage(this, name, screenshot);
  }
);

Then(
  'the concertina has {int} concertina headers',
  async function (this: ICustomWorld, headersNumber: number) {
    const headers = Helpers.getSamplerTabsSelectedContent(this)
      .locator('cctc-concertina, cctc-concertina cctc-concertinaitem cctc-concertina')
      .last()
      .locator('cctc-concertina-header:has(> div:nth-child(2))');
    await expect(headers).toHaveCount(headersNumber);
  }
);

Then(
  'the concertina has {int} item headers',
  async function (this: ICustomWorld, itemHeadersNumber: number) {
    const itemHeaders = Helpers.getSamplerTabsSelectedContent(this)
      .locator('cctc-concertina, cctc-concertina cctc-concertinaitem cctc-concertina')
      .last()
      .locator('cctc-concertinaitem-header');
    await expect(itemHeaders).toHaveCount(itemHeadersNumber);
  }
);

Then(
  'the concertina has {int} sub-headers',
  async function (this: ICustomWorld, subHeadersNumber: number) {
    const subHeaders = Helpers.getSamplerTabsSelectedContent(this)
      .locator('cctc-concertina, cctc-concertina cctc-concertinaitem cctc-concertina')
      .last()
      .locator('cctc-concertina-subheader');
    await expect(subHeaders).toHaveCount(subHeadersNumber);
  }
);

Then(
  'the concertina has {int} item sub-headers',
  async function (this: ICustomWorld, itemSubHeadersNumber: number) {
    const itemSubHeaders = Helpers.getSamplerTabsSelectedContent(this)
      .locator('cctc-concertina, cctc-concertina cctc-concertinaitem cctc-concertina')
      .last()
      .locator('cctc-concertinaitem-subheader');
    await expect(itemSubHeaders).toHaveCount(itemSubHeadersNumber);
  }
);

Then(
  'the concertina has a unique header containg the text {string}',
  async function (this: ICustomWorld, name: string) {
    await expect(
      Helpers.getSamplerTabsSelectedContent(this).getByRole('heading').getByText(name)
    ).toHaveCount(1);
  }
);

Then(
  'the concertina has a unique subheader containg the text {string}',
  async function (this: ICustomWorld, name: string) {
    await expect(
      Helpers.getSamplerTabsSelectedContent(this).getByRole('heading').getByText(name)
    ).toHaveCount(1);
  }
);

Then(
  'the concertina collapse or expand all button is clicked',
  async function (this: ICustomWorld) {
    const concertina = Helpers.getSamplerTabsSelectedContent(this)
      .locator('cctc-concertina, cctc-concertina cctc-concertinaitem cctc-concertina')
      .last();
    await concertina.locator('.config-button').click();
  }
);

When(
  'the user presses the collapse or expand button of header {string}',
  async function (this: ICustomWorld, concertinaHeaderName: string) {
    const concertina = Helpers.getSamplerTabsSelectedContent(this)
      .locator('cctc-concertina, cctc-concertina cctc-concertinaitem cctc-concertina')
      .last();
    await concertina
      .locator(`cctc-concertinaitem-header:has-text("${concertinaHeaderName}")`)
      .locator('.collapse-expand-button')
      .click();
  }
);
