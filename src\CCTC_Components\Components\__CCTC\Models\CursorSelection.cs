﻿namespace CCTC_Components.Components.__CCTC.Models;

/// <summary>
/// The positions of an elements selection
/// </summary>
/// <param name="Start">The start position of the selection</param>
/// <param name="End">The end position of the selection</param>
/// <remarks>The End position can be used to get the current cursor position within the element</remarks>
public record CursorSelection(int Start, int End);