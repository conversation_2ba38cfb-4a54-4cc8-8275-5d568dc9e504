﻿@page "/concertinaitemsample"

@{
    var description = new List<string>
    {
        "A ConcertinaItem component provides the header, subheader and content to each section of the Concertina component. " +
        "It can be collapsed or expanded by the user to manage the available screen space more effectively.",
    };

    var relatedComponents = new List<(Relation relation, string display, string url, string description)>
    {
        (Relation.Parent, "Concertina", "concertinasample", "The containing Concertina to which the ConcertinaItem belongs"),
    };

}

<Sampler
    ComponentName="ConcertinaItem"
    ComponentCssName="concertinaitem"
    Description="@description"
    RelatedComponents="@relatedComponents"
    ContentHeightPixels="250">
</Sampler>

@code {

}