1 - the dropdown component option can be changed using the mouse or the keyboard
2 - the dropdown component can allow an empty value and an entered value can be optionally cleared
3 - the dropdown component handles options that have a large amount of text and can display tooltips when required
4 - the dropdown component can be made read-only and individual options can be disabled or set as visible. The read-only icon is optional