@component @temporal @date @date_1
Feature: the date component date can be typed in as text or can be entered via a date picker
    Scenario: the date component sample page is available
        Given the user is at the home page
        When the user selects the "Date" component in the container "Input"
        Then the url ending is "datesample"

    Scenario: the date component date can be typed in as text
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        When the user enters "20210105" into the Date component
        Then the Date component has the value "2021-01-05"

    Scenario: the date component date can be entered via a date picker
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        When the user enters "2021-01-05" into the Date component via the date picker
        Then the Date component has the value "2021-01-05"
