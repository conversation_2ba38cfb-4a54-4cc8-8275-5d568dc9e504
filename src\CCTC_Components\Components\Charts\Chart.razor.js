﻿/*
    Implemented on version 4.4.3

    - Note the use of blazor and chart objects to store the Chart and Blazor (helper) objects
    - The objects store the Chart and helper using the guid string chartId
    - This is required as the blazor and chart variables are global in scope and get overwritten
        with each new instance of the chart. This is fine on pages where there is only one chart
        object but will cause odd behaviour when more than one chart is used as the later charts
        will overwrite the chart object if not an array

*/

//holds a collection of blazor (helper) objects - one for each chart in use
let blazor = [];
//holds a collection of chart objects - one for each chart in use
let chart = [];

//creates the initial chart
//id = the element id which contains the chart
//helper = the blazor object
//chartId = the internal guid id that identifies the specific chart instance
//config = the config to set
//color = the initial color of the chart
export function setUpChart(id, helper, chartId, config, color) {
    blazor[chartId] = helper;

    chart[chartId] = new Chart(
        document.getElementById(id),
        config
    );

    if (color) {
        Chart.defaults.color = color;
    }
}

function checkReady(chartId) {
    if (!chart[chartId]) {
        throw new Error('Chart must be setup first - the first function called should be setUpChart - error for chart with id ' + chartId);
    }
}


//gets the current chart type in use
export function getCurrentChartType(chartId) {
    checkReady(chartId);

    return chart[chartId].config.type;
}


//turns on or off the maintenance of the aspect ratio
export function setMaintainAspectRatio(chartId, newValue) {
    checkReady(chartId);

    chart[chartId].options.maintainAspectRatio = newValue;

    update(chartId);
}

//sets the options - note: this overwrites the current options
export function setOptions(chartId, options) {
    checkReady(chartId);
    chart[chartId].options = options;
    update(chartId);
}

//sets the title
export function setTitle(chartId, display, text) {
    checkReady(chartId);

    if (display !== null) {
        chart[chartId].options.plugins.title.display = display;
    }
    if (text) {
        chart[chartId].options.plugins.title.text = text;
    }

    update(chartId);
}

//sets the subtitle
export function setSubtitle(chartId, display, text) {
    checkReady(chartId);

    if (display !== null) {
        chart[chartId].options.plugins.subtitle.display = display;
    }
    if (text) {
        chart[chartId].options.plugins.subtitle.text = text;
    }

    update(chartId);
}

//sets the border color of the dataset with the given index or all datasets if index not specified
//if index is invalid, applies to all datasets
export function setBorderColor(chartId, color, index) {
    if (index && chart[chartId].data.datasets.at(index) !== undefined) {
        chart[chartId].data.datasets[index].borderColor = color;
    } else {
        chart[chartId].data.datasets.forEach((d) => d.borderColor = color);
    }

    update(chartId);
}

//sets the default color
export function setColor(chartId, color) {
    Chart.defaults.color = color;

    update(chartId);
}

//set the legend
export function setLegend(chartId, legend) {
    chart[chartId].options.plugins.legend = legend;
    update(chartId);
}

//set the scales
export function setScales(chartId, scales) {
    chart[chartId].options.scales = scales;
    update(chartId);
}

//adds a dataset by index or just adds to end if index is not specified
//if index is invalid, adds to the end
export function addDataset(chartId, dataset, index) {
    if (index && chart[chartId].data.datasets.at(index) !== undefined) {
        chart[chartId].data.datasets.splice(index, 1, dataset);
    } else {
        chart[chartId].data.datasets.push(dataset);
    }

    update(chartId);
}

//removes the dataset by index or the last one if not specified
//if index is invalid, applies to all datasets
export function removeDataset(chartId, index) {
    if (index && chart[chartId].data.datasets.at(index) !== undefined) {
        chart[chartId].data.datasets.splice(index, 1);
    } else {
        chart[chartId].data.datasets.splice(-1);
    }

    update(chartId);
}

//sets the background colors for the dataset with given index or all datasets if index not specified
//if index is invalid, applies to all datasets
export function setBackgroundColors(chartId, colors, index) {

    if (index && chart[chartId].data.datasets.at(index) !== undefined) {
        chart[chartId].data.datasets[index].backgroundColor = colors;
    } else {
        chart[chartId].data.datasets.forEach((d) => d.backgroundColor = colors);
    }

    update(chartId);
}

//sets a specific option using the given property and value
export function setOption(chartId, opt, value) {
    //see here for example of using this notation
    //https://stackoverflow.com/questions/4255472/javascript-object-access-variable-property-by-name-as-string
    //e.g. given 'fill' and true equates to chart.options.fill = true

    chart[chartId].options[opt] = value;

    update(chartId);
}

//sets the option of the option using the given value
export function setOptionOption(chartId, opt1, opt2, value) {
    //e.g. given 'animation', 'animateRotate' and true equates to chart.options.animation.animateRotate = true

    chart[chartId].options[opt1][opt2] = value;

    update(chartId);
}

//sets the property value for the dataset with the given index when given, or
//sets the property to the value for all datasets when omitted
export function setDatasetProperty(chartId, index, opt, value) {
    if (index !== null) {
        if(chart[chartId].data.datasets.at(index) !== undefined) {
            chart[chartId].data.datasets[index][opt] = value;
        }
    } else {
        chart[chartId].data.datasets.forEach((d) => d[opt] = value);
    }

    update(chartId);
}

//sets the data value at the given dataPointIndex for the given dataset index
export function setDatasetDataValue(chartId, index, dataPointIndex, value) {
    if(chart[chartId].data.datasets.at(index) !== undefined) {
        chart[chartId].data.datasets[index].data[dataPointIndex] = value;
    }

    update(chartId);
}

//just prints the chart object to the console for debugging purposes
export function print(chartId) {
    console.log(chart[chartId]);
}

//triggers an update which will update scales, legends and then re-render
export function update(chartId) {
    if(chart[chartId]) {
        chart[chartId].update();
    }
}

//resizes the chart based on its container
export function resize(chartId) {
    if (chart[chartId]) {
        chart[chartId].resize();
    }
}

//trigger a redraw of all chart elements - note: does not add new data, use update for that
export function render(chartId) {
    if (chart[chartId]) {
        chart[chartId].render();
    }
}

//resets the chart to its state before animation then can retrigger using update
export function reset(chartId) {
    if (chart[chartId]) {
        chart[chartId].reset();
    }
}

//complete clean up including blazor reference
export function destroy(chartId) {
    //clean up
    blazor[chartId] = null;
    destroyChart();
}

//just destroys the chart - required for when updating the chart config
export function destroyChart(chartId) {
    if (chart[chartId]) {
        chart[chartId].destroy();
    }

    chart[chartId] = null;
}