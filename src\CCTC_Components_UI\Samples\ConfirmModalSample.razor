﻿@page "/confirmmodalsample"
@using CCTC_Components.Components.Modals.Confirm;

@{
    var description = new List<string>
    {
        "The ConfirmModal component"
    };

    var features = new List<(string, string)>
    {
        ("Configuration", "Customise the heading, question text, positive and negative text and icons"),
        ("Interactivity", "The user can click either a positive or negative response")
    };

    var gotchas = new List<(string, string)>
    {
        ("Theming", "The blazored modal is placed higher in the DOM tree than the MainLayout component. Therefore the component theme classes need to be applied via the modal options class")
    };

    var usageCode = new List<(string title, string description, string code, RenderFragment fragment)>
    {
        ("Customised positive and negative responses, Modal options classes: UI theme plus custom class defining a max width", "",
@"<style>
    .confirm-modal {
        max-width: 350px;
    }
</style>

<div>
    <button @onclick=""ShowUsage1ConfirmModal"">Confirm modal</button>
    <div class=""mt-2"">confirm result: @_usage1ConfirmRes</div>
</div>

@code {

    [CascadingParameter]
    public IModalService Modal { get; set; } = default!;

    [CascadingParameter]
    public MainLayout? MainLayout { get; set; }

    string? _usage1ConfirmRes;

    ModalOptions MakeModalOptions()
    {
        return new ModalOptions
        {
            HideHeader = true,
            HideCloseButton = true,
            Class = $""confirmmodal-{MainLayout?.GetTheme() ?? ""default""} confirm-modal"",
            UseCustomLayout = false,
            Position = ModalPosition.Middle
        };
    }

    async Task ShowUsage1ConfirmModal()
    {
        var parameters = new ModalParameters();
        parameters.Add(""Id"", ""usage1"");
        parameters.Add(""Heading"", ""Heading which is quite long so wraps"");
        parameters.Add(""QuestionText"", ""Are you sure you want to make this change? This question is quite long too to see what it looks like"");
        parameters.Add(""PositiveText"", ""Confirm"");
        parameters.Add(""NegativeText"", ""Go back"");
        parameters.Add(""PositiveIcon"", ""check_circle"");
        parameters.Add(""NegativeIcon"", ""cancel"");

        var confirm = Modal.Show<ConfirmModal>(""confirm"", parameters, MakeModalOptions());
        var result = await confirm.Result;

        if (result is null)
        {
            _usage1ConfirmRes = ""result is null"";
        }
        else
        {
            _usage1ConfirmRes = result.Cancelled ? ""cancelled"" : result.Data?.ToString();
        }
    }
}",
        @<div>
            <button @onclick="ShowUsage1ConfirmModal">Confirm modal</button>
            <div class="mt-2">confirm result: @_usage1ConfirmRes</div>
        </div>),
        ("Default positive and negative responses, Modal options classes: UI theme plus custom class defining a max width", "",
@"<style>
    .confirm-modal {
        max-width: 350px;
    }
</style>

<div>
    <button @onclick=""ShowUsage2ConfirmModal"">Confirm modal</button>
    <div class=""mt-2"">confirm result: @_usage2ConfirmRes</div>
</div>

@code {

    [CascadingParameter]
    public IModalService Modal { get; set; } = default!;

    [CascadingParameter]
    public MainLayout? MainLayout { get; set; }

    string? _usage2ConfirmRes;

    ModalOptions MakeModalOptions()
    {
        return new ModalOptions
        {
            HideHeader = true,
            HideCloseButton = true,
            Class = $""confirmmodal-{MainLayout?.GetTheme() ?? ""default""} confirm-modal"",
            UseCustomLayout = false,
            Position = ModalPosition.Middle
        };
    }

    async Task ShowUsage1ConfirmModal()
    {
        var parameters = new ModalParameters();
        parameters.Add(""Id"", ""usage2"");
        parameters.Add(""Heading"", ""Submit changes"");
        parameters.Add(""QuestionText"", ""Are you sure you want to submit your changes?"");

        var confirm = Modal.Show<ConfirmModal>(""confirm"", parameters, MakeModalOptions());
        var result = await confirm.Result;

        if (result is null)
        {
            _usage2ConfirmRes = ""result is null"";
        }
        else
        {
            _usage2ConfirmRes = result.Cancelled ? ""cancelled"" : result.Data?.ToString();
        }
    }
}",
        @<div>
            <button @onclick="ShowUsage2ConfirmModal">Confirm modal</button>
            <div class="mt-2">confirm result: @_usage2ConfirmRes</div>
        </div>)
    };
}

<style>
    .confirm-modal {
        max-width: 350px;
    }
</style>

<Sampler
    ComponentName="ConfirmModal"
    ComponentCssName="confirmmodal"
    Description="@description"
    Features="@features"
    UsageText="Typical usages of the <code>ConfirmModal</code> component are shown below"
    Gotchas="@gotchas"
    UsageCodeList="@usageCode"
    ContentHeightPixels="450">
    <ExampleTemplate>
        <div>
            <button @onclick="ShowExample1ConfirmModal">Confirm modal</button>
            <div class="mt-2">confirm result: @_example1ConfirmRes</div>
        </div>
    </ExampleTemplate>
</Sampler>

@code {

    [CascadingParameter]
    public IModalService Modal { get; set; } = default!;

    [CascadingParameter]
    public MainLayout? MainLayout { get; set; }

    string? _example1ConfirmRes;
    string? _usage1ConfirmRes;
    string? _usage2ConfirmRes;

    ModalOptions MakeModalOptions()
    {
        return new ModalOptions
        {
            HideHeader = true,
            HideCloseButton = true,
            Class = $"confirmmodal-{MainLayout?.GetTheme() ?? "default"} confirm-modal",
            UseCustomLayout = false,
            Position = ModalPosition.Middle
        };
    }

    async Task ShowExample1ConfirmModal()
    {
        var parameters = new ModalParameters();
        parameters.Add("Id", "example1");
        parameters.Add("Heading", "Heading which is quite long so wraps");
        parameters.Add("QuestionText", "Are you sure you want to make this change? This question is quite long too to see what it looks like");
        parameters.Add("PositiveText", "Confirm");
        parameters.Add("NegativeText", "Go back");

        var confirm = Modal.Show<ConfirmModal>("confirm", parameters, MakeModalOptions());
        var result = await confirm.Result;

        if (result is null)
        {
            _example1ConfirmRes = "result is null";
        }
        else
        {
            _example1ConfirmRes = result.Cancelled ? "cancelled" : result.Data?.ToString();
        }
    }

    async Task ShowUsage1ConfirmModal()
    {
        var parameters = new ModalParameters();
        parameters.Add("Id", "usage1");
        parameters.Add("Heading", "Heading which is quite long so wraps");
        parameters.Add("QuestionText", "Are you sure you want to make this change? This question is quite long too to see what it looks like");
        parameters.Add("PositiveText", "Confirm");
        parameters.Add("NegativeText", "Go back");
        parameters.Add("PositiveIcon", "check_circle");
        parameters.Add("NegativeIcon", "cancel");

        var confirm = Modal.Show<ConfirmModal>("confirm", parameters, MakeModalOptions());
        var result = await confirm.Result;

        if (result is null)
        {
            _usage1ConfirmRes = "result is null";
        }
        else
        {
            _usage1ConfirmRes = result.Cancelled ? "cancelled" : result.Data?.ToString();
        }
    }

    async Task ShowUsage2ConfirmModal()
    {
        var parameters = new ModalParameters();
        parameters.Add("Id", "usage2");
        parameters.Add("Heading", "Submit changes");
        parameters.Add("QuestionText", "Are you sure you want to submit your changes?");

        var confirm = Modal.Show<ConfirmModal>("confirm", parameters, MakeModalOptions());
        var result = await confirm.Result;

        if (result is null)
        {
            _usage2ConfirmRes = "result is null";
        }
        else
        {
            _usage2ConfirmRes = result.Cancelled ? "cancelled" : result.Data?.ToString();
        }
    }
}