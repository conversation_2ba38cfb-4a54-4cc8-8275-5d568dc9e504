1 - the datepicker has the ability to handle partial dates. The user can select a year, a month and year or a week of the year
2 - the datepicker includes the option of choosing a date before or after a date or partial date
3 - the datepicker has the facility to optionally allow the date to be cleared
4 - the datepicker highlights the selected date. This could be a single date or a range of dates if a before or after partial date has been selected
5 - the datepicker can selectively disable chosen dates
6 - the datepicker can set a minimum date
7 - the datepicker can set a maximum date
8 - the datepicker can optionally highlight bank holidays
9 - the datepicker can be set to read-only
10 - the datepicker includes keyboard navigation of day of the week
11 - the datepicker can move between months by clicking before or after arrows