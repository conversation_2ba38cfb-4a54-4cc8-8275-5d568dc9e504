﻿@using CCTC_Lib.Enums.UI
@using Microsoft.JSInterop
@inject IJSRuntime JSRuntime
@inherits CCTC_Components.Components.__CCTC.CCTCBase
@implements IAsyncDisposable

<cctc-infoicon id="@Id" class="@CssClass" style="@Style" data-author="cctc">
@if (InfoType == InfoType.Popover)
{
    <a id="@_popoverId" tabindex="0" role="button" data-bs-toggle="popover" data-bs-placement="@_infoPlacement"
        data-bs-trigger="focus" data-bs-container="@($"#{_popoverId}")" title="@Title"
           data-bs-content="@Content" data-bs-custom-class="custom-popover">
        <cctc-infoicon-image-container style="@_imageContainerStyle" @onclick="IconClicked">
        @if (ImageSource is not null)
        {
            <img src="@ImageSource" alt="info"/>
        }
        else
        {
            <div class="material-icons" style="@_imageIconStyle">
                @ImageIcon
            </div>
        }
        </cctc-infoicon-image-container>
    </a>
}
else
{
    <div id="@_tooltipId" data-bs-toggle="tooltip" data-bs-placement="@_infoPlacement"
        data-bs-container="@($"#{_tooltipId}")">
        <cctc-infoicon-image-container style="@_imageContainerStyle" @onclick="IconClicked">
        @if (ImageSource is not null)
        {
            <img src="@ImageSource" alt="info"/>
        }
        else
        {
            <div class="material-icons" style="@_imageIconStyle">
                @ImageIcon
            </div>
        }
        </cctc-infoicon-image-container>
    </div>
}
</cctc-infoicon>

@code {

    /// <summary>
    /// The content to display in the info icon popover or tooltip
    /// </summary>
    [Parameter, EditorRequired]
    public required string Content { get; set; }

    /// <summary>
    /// The info icon popover title
    /// </summary>
    [Parameter]
    public string? Title { get; set; }

    /// <summary>
    /// The image source
    /// </summary>
    [Parameter]
    public string? ImageSource { get; set; }

    /// <summary>
    /// The image icon
    /// </summary>
    [Parameter]
    public string? ImageIcon { get; set; }

    /// <summary>
    /// The icon size
    /// </summary>
    [Parameter]
    public Size Size { get; set; } = Size.XSmall;

    /// <summary>
    /// The info icon type
    /// </summary>
    [Parameter]
    public InfoType InfoType { get; set; }

    /// <summary>
    /// Configure the info icon popover or tooltip placement
    /// </summary>
    [Parameter]
    public InfoPlacement InfoPlacement { get; set; }

    /// <summary>
    /// A callback which fires when the info icon is clicked
    /// </summary>
    [Parameter]
    public EventCallback OnClick { get; set; }

    (string iconHeight, string iconWidth) _iconSize;
    string? _infoPlacement;
    IJSObjectReference? _jsModulePopover;
    IJSObjectReference? _jsModuleTooltip;
    string? _popoverId;
    string? _tooltipId;
    string? _imageContainerStyle;
    string? _imageIconStyle;

    Task IconClicked()
    {
        return OnClick.InvokeAsync();
    }

    (string iconHeight, string iconWidth) GetIconSize()
    {
        return Size switch
        {
            Size.XXSmall => ("0.8rem", "0.8rem"),
            Size.XSmall => ("1rem", "1rem"),
            Size.Small => ("1.2rem", "1.2rem"),
            Size.Medium => ("1.5rem", "1.5rem"),
            Size.Large => ("2rem", "2rem"),
            Size.XLarge => ("2.5rem", "2.5rem"),
            Size.XXLarge => ("3rem", "3rem"),
            _ => throw new ArgumentOutOfRangeException()
        };
    }

    /// <inheritdoc />
    protected override void OnInitialized()
    {
        _iconSize = GetIconSize();
        _infoPlacement = InfoPlacement.ToString().ToLower();
        if (Title is not null && InfoType == InfoType.Tooltip)
        {
            throw new ArgumentException($"The title '{Title}' cannot be used with a Tooltip. Consider using a Popover instead");
        }

        if (ImageSource is not null && ImageIcon is not null)
        {
            throw new ArgumentException($"Provide an ImageSource or an ImageIcon");
        }

        if (ImageSource is null && ImageIcon is null)
        {
            //set default image
            ImageSource = "./_content/CCTC_Components/images/info_bsinfo_24dp.svg";
        }

        _popoverId = $"{Id}-popover";
        _tooltipId = $"{Id}-tooltip";
    }

    ///<inheritdoc />
    protected override void OnParametersSet()
    {
        _imageContainerStyle =
            new StyleBuilder()
                .AddStyle("height", _iconSize.iconHeight)
                .AddStyle("width", _iconSize.iconWidth)
                .AddStyle("cursor", InfoType == InfoType.Popover || OnClick.HasDelegate ? "pointer" : "default")
                .Build();

        _imageIconStyle =
            new StyleBuilder()
                .AddStyle("font-size", _iconSize.iconHeight)
                .Build();
    }

    /// <inheritdoc />
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            if (InfoType == InfoType.Popover)
            {
                _jsModulePopover = await JSRuntime.InvokeAsync<IJSObjectReference>("import", "./_content/CCTC_Components/scripts/bootstrap_popover.js");
                await _jsModulePopover.InvokeVoidAsync("addPopover", _popoverId);
            }
            else
            {
                _jsModuleTooltip = await JSRuntime.InvokeAsync<IJSObjectReference>("import", "./_content/CCTC_Components/scripts/bootstrap_tooltip.js");
                await _jsModuleTooltip.InvokeVoidAsync("addTooltip", _tooltipId);
            }
        }

        // Tooltip title set and updated here with a small delay required to prevent tooltip freeze when changing the content
        if (InfoType == InfoType.Tooltip && _jsModuleTooltip is not null)
        {
            await _jsModuleTooltip.InvokeVoidAsync("updateTooltipTitle", _tooltipId, Content, 100);
        }
    }

    /// <inheritdoc />
    public override async ValueTask DisposeAsync()
    {
        await base.DisposeAsync();

        if (_jsModulePopover is not null)
        {
            await _jsModulePopover.InvokeVoidAsync("disposePopover", _popoverId);
            await _jsModulePopover.DisposeAsync();
        }

        if (_jsModuleTooltip is not null)
        {
            await _jsModuleTooltip.InvokeVoidAsync("disposeTooltip", _tooltipId);
            await _jsModuleTooltip.DisposeAsync();
        }
    }
}
