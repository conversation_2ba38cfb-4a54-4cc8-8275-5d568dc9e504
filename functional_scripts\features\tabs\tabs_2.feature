@component @tabs @tabs_2
Feature: tabs can be preselected so that it is opened by default when Tabs is rendered. If no selection, the first tab is selected by default. 
    Scenario: the second tab can be preselected
        Given the user is at the home page
        When the user selects the "Tabs" component in the container "Tabs"
        Then the tab content contains "Tab 3"
        And the Tabs component image matches the base image "tabs-3"
        When the user selects "tabs-example-tab2" from the dropdown showing "tabs-example-tab3"
        Then the tab content contains "This is some content in tab 2 styled with a dim grey background"
        And the Tabs component image matches the base image "tabs-2"
        When the user clicks the "Usage" tab
        And the user clicks the "Example" tab
        Then the tab content contains "This is some content in tab 2 styled with a dim grey background"
        And the Tabs component image matches the base image "tabs-2"
        
