﻿h3 {
    margin-top: 1.5rem;
}

.button-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1.5rem;
    padding-top: 1.5rem;
}

.main-section {
    text-align: center;
    padding-top: 5rem;
}

.github-logo {
    height: 1.5rem;
}

/*tablet*/
@media (min-width: 576px) {
    .button-container {
        padding-top: 2rem;
    }
  
    .main-section {
        padding-top: 7rem;
    }

    h3 {
        margin-top: 2rem;
    }
}

/*desktop-small*/
@media (min-width: 1200px) {
    .button-container {
        padding-top: 2rem;
    }
    
    .main-section {
        padding-top: 7rem;
    }

    h3 {
        margin-top: 2rem;
    }
}

/*desktop-standard*/
@media (min-width: 1920px) {
    .button-container {
        padding-top: 2rem;
    }
    
    .main-section {
        padding-top: 10rem;
    }

    h3 {
        margin-top: 3rem;
    }
}