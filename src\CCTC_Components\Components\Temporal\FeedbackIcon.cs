﻿namespace CCTC_Components.Components.Temporal
{
    /// <summary>
    /// Provides feedback to the user as the relevant icon
    /// </summary>
    public enum FeedbackIcon
    {
        /// <summary>
        /// Error
        /// </summary>
        Error,
        /// <summary>
        /// Valid
        /// </summary>
        Valid,
        /// <summary>
        /// Both
        /// </summary>
        /// <remarks>Will display an icon for both the Error and Valid state</remarks>
        Both,
        /// <summary>
        /// None
        /// </summary>
        /// <remarks>Will not display a feedback icon</remarks>
        None
    }
}
