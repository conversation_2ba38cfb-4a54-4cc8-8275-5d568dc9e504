@component @numeric @numeric_4
Feature: the numeric component input can be constrained by applying an optional number format and the mathematical rounding method can be specified
    Scenario: the numeric component input can have no format
        Given the user is at the home page
        Given the user selects the "Numeric" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "No format with a null initial value, redact text, allows whitespace, max length"
        When the user enters "1.2" into the Numeric component
        Then the Numeric component has the stable value "1.2"

    Scenario: the numeric component input can apply a number format
        Given the user is at the home page
        Given the user selects the "Numeric" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Exponential Format: E4"
        When the user enters "15678" into the Numeric component
        Then the Numeric component has the value "1.5678E+004"

    Scenario: the numeric component input can apply a mathematical rounding method
        Given the user is at the home page
        Given the user selects the "Numeric" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: -#000.0, rounding to even, CssClass applied"
        When the user enters "296.45" into the Numeric component
        Then the Numeric component has the value "296.4"
