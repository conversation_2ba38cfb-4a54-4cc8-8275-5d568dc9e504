﻿using System.Text.Json;
using CCTC_Components_UI.Helpers.Models;
using CCTC_Lib.Contracts.Data;
using CCTC_Lib.Contracts.Interaction;
using CCTC_Lib.Enums.Data;
using Microsoft.AspNetCore.Components;

namespace CCTC_Components_UI.Helpers.Services;

/// <summary>
/// An implementation of IItemsService for demonstration purposes
/// </summary>
public class NamedNodeItemsService : IItemsService<NamedNodeIdentifier>
{
    readonly IDelayService _delayService;
    readonly NavigationManager _navigationManager;

    public NamedNodeItemsService(IDelayService delayService, NavigationManager navigationManager)
    {
        _delayService = delayService;
        _navigationManager = navigationManager;
    }

    public async Task<(int totalItems, IEnumerable<NamedNodeIdentifier> items)> GetItemsAsync(int startIndex, int numItems,
        CancellationToken token)
    {
        await PopDataAndTotal(token);
        return (_total, _data.Skip(startIndex).Take(numItems));
    }

    public Task<(int totalItems, IEnumerable<NamedNodeIdentifier> items)> GetItemsAsync(int startIndex, int numItems)
    {
        return GetItemsAsync(startIndex, numItems, CancellationToken.None);
    }

    public async Task<(int totalItems, IEnumerable<NamedNodeIdentifier> items)> GetItemsAsync(int startIndex, int numItems, string sortBy, SortDir sortDirection,
        CancellationToken token)
    {
        //just sort by name ascending

        await PopDataAndTotal(token);
        return (_total, _data.OrderBy(x => x.AsName()).Skip(startIndex).Take(numItems));
    }

    public Task<(int totalItems, IEnumerable<NamedNodeIdentifier> items)> GetItemsAsync(int startIndex, int numItems, string sortBy, SortDir sortDirection)
    {
        return GetItemsAsync(startIndex, numItems, sortBy, sortDirection, CancellationToken.None);
    }

    public async Task<(int totalItems, int filteredItems, IEnumerable<NamedNodeIdentifier> items)> GetItemsAsync(string filter, FilterComparisonType filterComparisonType,
        int startIndex, int numItems, string sortBy, SortDir sortDirection,
        CancellationToken token)
    {
        //sort by name ascending and filter using name with default comparison type

        await PopDataAndTotal(token);
        var filteredQuery = _data.Where(x => x.AsName().Contains(filter));
        var totalFiltered = filteredQuery.Count();  // Get total filtered count before pagination

        var filtered = filteredQuery
            .OrderBy(x => x.AsName())
            .Skip(startIndex)
            .Take(numItems)
            .ToList();

        return (_total, totalFiltered, filtered);
    }

    public Task<(int totalItems, int filteredItems, IEnumerable<NamedNodeIdentifier> items)> GetItemsAsync(string filter, FilterComparisonType filterComparisonType,
        int startIndex, int numItems, string sortBy, SortDir sortDirection)
    {
        return GetItemsAsync(filter, filterComparisonType, startIndex, numItems, sortBy, sortDirection, CancellationToken.None);
    }

    public int ObservableThrottleMs { get; set; } = 500;

    List<NamedNodeIdentifier> _data = new();
    int _total = 0;

    async Task PopDataAndTotal(CancellationToken token)
    {
        _data.Clear();
        await LoadingFunc(token);
        _total = _data.Count;
    }

    public async Task LoadingFunc(CancellationToken token)
    {
        if (token.IsCancellationRequested)
        {
            return;
        }

        var client = new HttpClient();
        client.BaseAddress = new Uri(_navigationManager.BaseUri);

        try
        {
            await _delayService.Delay(TimeSpan.FromMilliseconds(100), token);
            _data.Clear();

            await using var data = await client.GetStreamAsync("./data/circa_15k_crf_namedNodes.json", token)
                .ConfigureAwait(false);
            var deserializeOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };

            await foreach (var item in JsonSerializer.DeserializeAsyncEnumerable<NamedNodeIdentifier>(
                data, deserializeOptions, token))
            {
                if (item != null)
                {
                    _data.Add(item);
                }

                // This helps keep the UI responsive during deserialization
                if (_data.Count % 1000 == 0)
                {
                    await _delayService.Delay(TimeSpan.FromMilliseconds(1), token);
                }
            }
        }
        catch (OperationCanceledException)
        {
            // Catching cancellation exception - simply return, no need to do anything
            return;
        }
        catch (Exception)
        {
            // For other exceptions, propagate them up
            throw;
        }
    }
}