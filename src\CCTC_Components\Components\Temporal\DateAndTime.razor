﻿@using static Common.Services.Clock
@inherits CCTC_Components.Components.__CCTC.CCTCBase
@typeparam TValue

<cctc-input data-cctc-input-type="dateandtime" id="@Id" class="@CssClass" style="@Style" data-author="cctc">
    <div class="component-wrapper">
        <div class="date-wrapper">
            <Date
                Id="@($"{Id}-date")"
                TValue="DateOnly?"
                Value="@_dateOnlyValue"
                ValueChanged="OnDateValueChanged"
                DateFormat="@(DateFormat is null ? null : fullDateFormatToDateOnly(DateFormat))"
                MinDate="@_minDateOnlyValue"
                MaxDate="@_maxDateOnlyValue"
                ThrottleMs="@ThrottleMs"
                Disabled="@Disabled"
                ReadOnly="@ReadOnly"
                HideReadOnlyIcon="true"
                FeedbackIcon="@_feedbackIcon"
                AllowClear="@AllowClear">
            </Date>
        </div>
        <div class="time-wrapper">
            <Time
                Id="@($"{Id}-time")"
                TValue="TimeOnly?"
                Value="@_timeOnlyValue"
                ValueChanged="OnTimeValueChanged"
                DateFormat="@(DateFormat is null ? null : fullDateFormatToTimeOnly(DateFormat))"
                MinTime="@_minTimeOnlyValue"
                MaxTime="@_maxTimeOnlyValue"
                ThrottleMs="@ThrottleMs"
                Disabled="@Disabled"
                ReadOnly="@ReadOnly"
                HideReadOnlyIcon="@HideReadOnlyIcon"
                FeedbackIcon="@_feedbackIcon"
                AllowClear="@AllowClear">
            </Time>
        </div>
    </div>
</cctc-input>

@code {

    /// <summary>
    /// The input value
    /// </summary>
    [Parameter]
    public TValue? Value { get; set; }

    /// <summary>
    /// A callback which fires when the input value changes
    /// </summary>
    [Parameter]
    public EventCallback<TValue> ValueChanged { get; set; }

    /// <summary>
    /// A second callback which fires when the input value changes. Useful when consuming using @bind-Value
    /// </summary>
    [Parameter]
    public EventCallback<ChangeEventArgs> DateTimeChanged { get; set; }

    /// <summary>
    /// An Action callback which fires when either the date or time changes
    /// </summary>
    [Parameter]
    public Action<DateOnly?, TimeOnly?>? ActionOnDateOrTimeChanged { get; set; }

    /// <summary>
    /// The date format. See <see cref="availableDateFormats"/> for the supported formats
    /// </summary>
    [Parameter]
    public string? DateFormat { get; set; }

    /// <summary>
    /// Provide a minimum datetime
    /// </summary>
    [Parameter]
    public DateTime? MinDateTime { get; set; }

    /// <summary>
    /// Provide a minimum datetime
    /// </summary>
    [Parameter]
    public DateTime? MaxDateTime { get; set; }

    /// <summary>
    /// Disabled if true
    /// </summary>
    [Parameter]
    public bool Disabled { get; set; }

    /// <summary>
    /// Read-only if true
    /// </summary>
    [Parameter]
    public bool ReadOnly { get; set; }

    /// <summary>
    /// Hides the read-only icon when <see cref="ReadOnly"/> is true
    /// </summary>
    [Parameter]
    public bool HideReadOnlyIcon { get; set; }

    /// <summary>
    /// The Throttle speed
    /// </summary>
    [Parameter]
    public int ThrottleMs { get; set; }

    /// <summary>
    /// Control the feedback icon display
    /// </summary>
    [Parameter]
    public FeedbackIcon? FeedbackIcon { get; set; }

    /// <summary>
    /// Allows the current value to be cleared from the UI
    /// </summary>
    [Parameter]
    public bool AllowClear { get; set; }

    const string _defaultDateFormat = "yyyy-MM-dd HH:mm:ss";
    DateOnly? _dateOnlyValue;
    DateOnly? _minDateOnlyValue;
    DateOnly? _maxDateOnlyValue;
    TimeOnly? _timeOnlyValue;
    TimeOnly? _minTimeOnlyValue;
    TimeOnly? _maxTimeOnlyValue;
    FeedbackIcon _feedbackIcon;
    bool _typeParamAllowsNull;
    bool _canClear;

    async Task OnDateValueChanged(DateOnly? newValue)
    {
        _dateOnlyValue = newValue;
        await OnValueChanged();
    }

    async Task OnTimeValueChanged(TimeOnly? newValue)
    {
        _timeOnlyValue = newValue;
        await OnValueChanged();
    }

    async Task OnValueChanged()
    {
        var dateTimeValue = GetDateTimeValue(_dateOnlyValue, _timeOnlyValue);
        bool dateTimeCleared = dateTimeValue is null;

        ActionOnDateOrTimeChanged?.Invoke(_dateOnlyValue, _timeOnlyValue);

        if (dateTimeCleared && _canClear)
        {
            await OnDateOrTimeChanged(null);
            return;
        }

        if (_dateOnlyValue is not null && _timeOnlyValue is not null && IsDateTimeWithinLimits(dateTimeValue))
        {
            await OnDateOrTimeChanged(dateTimeValue);
        }
    }

    async Task OnDateOrTimeChanged(DateTime? dateTimeValue)
    {
        await ValueChanged.InvokeAsync((TValue?)(object?)dateTimeValue);
        await DateTimeChanged.InvokeAsync(new ChangeEventArgs() { Value = dateTimeValue is null ? null : GetFormattedDateTime(dateTimeValue.Value, DateFormat) });
    }

    DateTime? GetDateTimeValue(DateOnly? dateOnly, TimeOnly? timeOnly)
    {
        return (dateOnly, timeOnly) switch
        {
            (DateOnly d, TimeOnly t) => d.ToDateTime(t),
            (DateOnly d, null) => d.ToDateTime(TimeOnly.MinValue),
            (null, TimeOnly t) => DateOnly.MinValue.ToDateTime(t),
            _ => null
        };
    }

    string GetFormattedDateTime(DateTime dateTime, string? format)
    {
        string dateFormat = format is null ? _defaultDateFormat : format;
        return dateTime.ToString(dateFormat);
    }

    bool IsDateTimeWithinLimits(DateTime? dateTimeValue)
    {
        if (dateTimeValue is null)
        {
            return true;
        }

        bool minResult = true;
        bool maxResult = true;

        if (MinDateTime is not null)
        {
            minResult = dateTimeValue >= MinDateTime;
        }

        if (MaxDateTime is not null)
        {
            maxResult = dateTimeValue <= MaxDateTime;
        }

        return minResult && maxResult;
    }

    void CheckConfig(bool typeParamAllowsNull)
    {
        if (typeof(TValue) != typeof(DateTime) && !typeof(DateTime).IsAssignableFrom(Nullable.GetUnderlyingType(typeof(TValue))))
        {
            throw new ArgumentException("Expected a datetime or nullable datetime type", nameof(TValue));
        }

        if (AllowClear && !typeParamAllowsNull)
        {
            throw new ArgumentException($"{nameof(AllowClear)} should only be set to true when {nameof(TValue)} is of type nullable datetime", nameof(AllowClear));
        }
    }

    ///<inheritdoc />
    protected override void OnInitialized()
    {
        _typeParamAllowsNull = default(TValue) == null;
        CheckConfig(_typeParamAllowsNull);
    }

    /// <inheritdoc/>
    protected override void OnParametersSet()
    {
        _canClear = _typeParamAllowsNull && AllowClear && !ReadOnly && !Disabled;
        _feedbackIcon = FeedbackIcon ?? Temporal.FeedbackIcon.Error;

        var dateTimeValue = Value as DateTime?;
        if (dateTimeValue is null && _dateOnlyValue is not null && _timeOnlyValue is not null)
        {
            _dateOnlyValue = null;
            _timeOnlyValue = null;
        }
        else if (dateTimeValue is not null)
        {
            _dateOnlyValue = DateOnly.FromDateTime(dateTimeValue.Value);
            _timeOnlyValue = TimeOnly.FromDateTime(dateTimeValue.Value);
        }

        if (MinDateTime is not null)
        {
            _minDateOnlyValue = DateOnly.FromDateTime(MinDateTime.Value);
            _minTimeOnlyValue = TimeOnly.FromDateTime(MinDateTime.Value);
        }

        if (MaxDateTime is not null)
        {
            _maxDateOnlyValue = DateOnly.FromDateTime(MaxDateTime.Value);
            _maxTimeOnlyValue = TimeOnly.FromDateTime(MaxDateTime.Value);
        }
    }
}
