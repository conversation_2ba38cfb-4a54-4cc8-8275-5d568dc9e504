NUGET
  remote: https://api.nuget.org/v3/index.json
    BlackFox.VsWhere (1.1) - restriction: >= netstandard2.0
      FSharp.Core (>= 4.0.0.1) - restriction: >= net45
      FSharp.Core (>= 4.2.3) - restriction: && (< net45) (>= netstandard2.0)
      Microsoft.Win32.Registry (>= 4.7) - restriction: && (< net45) (>= netstandard2.0)
    BouncyCastle.Cryptography (2.4) - restriction: >= net8.0
    Expecto (10.2.1) - restriction: >= net8.0
      FSharp.Core (>= 7.0.200) - restriction: >= net6.0
      Mono.Cecil (>= 0.11.4 < 1.0) - restriction: >= net6.0
    Fake.Api.GitHub (6.0)
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
      Octokit (>= 0.50) - restriction: >= netstandard2.0
    Fake.BuildServer.GitHubActions (6.0)
      Fake.Core.Environment (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Trace (>= 6.0) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
    Fake.Core.CommandLineParsing (6.0) - restriction: >= netstandard2.0
      FParsec (>= 1.1.1) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
    Fake.Core.Context (6.0) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
    Fake.Core.Environment (6.0)
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
    Fake.Core.FakeVar (6.0) - restriction: >= netstandard2.0
      Fake.Core.Context (>= 6.0) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
    Fake.Core.Process (6.0)
      Fake.Core.Environment (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.FakeVar (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.String (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Trace (>= 6.0) - restriction: >= netstandard2.0
      Fake.IO.FileSystem (>= 6.0) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
      System.Collections.Immutable (>= 6.0) - restriction: >= netstandard2.0
    Fake.Core.ReleaseNotes (6.0)
      Fake.Core.SemVer (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.String (>= 6.0) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
    Fake.Core.SemVer (6.0) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
    Fake.Core.String (6.0) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
    Fake.Core.Target (6.0)
      Fake.Core.CommandLineParsing (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Context (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Environment (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.FakeVar (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Process (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.String (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Trace (>= 6.0) - restriction: >= netstandard2.0
      FSharp.Control.Reactive (>= 5.0.2) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
    Fake.Core.Tasks (6.0) - restriction: >= netstandard2.0
      Fake.Core.Trace (>= 6.0) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
    Fake.Core.Trace (6.0) - restriction: >= netstandard2.0
      Fake.Core.Environment (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.FakeVar (>= 6.0) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
    Fake.Core.UserInput (6.0)
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
    Fake.Core.Xml (6.0) - restriction: >= netstandard2.0
      Fake.Core.String (>= 6.0) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
    Fake.Documentation.DocFx (6.0)
      Fake.Core.Process (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.String (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Trace (>= 6.0) - restriction: >= netstandard2.0
      Fake.IO.FileSystem (>= 6.0) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
    Fake.DotNet.AssemblyInfoFile (6.0)
      Fake.Core.Environment (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.String (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Trace (>= 6.0) - restriction: >= netstandard2.0
      Fake.IO.FileSystem (>= 6.0) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
    Fake.DotNet.Cli (6.0)
      Fake.Core.Environment (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Process (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.String (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Trace (>= 6.0) - restriction: >= netstandard2.0
      Fake.DotNet.MSBuild (>= 6.0) - restriction: >= netstandard2.0
      Fake.DotNet.NuGet (>= 6.0) - restriction: >= netstandard2.0
      Fake.IO.FileSystem (>= 6.0) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
      Mono.Posix.NETStandard (>= 1.0) - restriction: >= netstandard2.0
      Newtonsoft.Json (>= 13.0.1) - restriction: >= netstandard2.0
    Fake.DotNet.MSBuild (6.0)
      BlackFox.VsWhere (>= 1.1) - restriction: >= netstandard2.0
      Fake.Core.Environment (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Process (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.String (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Trace (>= 6.0) - restriction: >= netstandard2.0
      Fake.IO.FileSystem (>= 6.0) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
      MSBuild.StructuredLogger (>= 2.1.545) - restriction: >= netstandard2.0
    Fake.DotNet.NuGet (6.0)
      Fake.Core.Environment (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Process (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.SemVer (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.String (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Tasks (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Trace (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Xml (>= 6.0) - restriction: >= netstandard2.0
      Fake.IO.FileSystem (>= 6.0) - restriction: >= netstandard2.0
      Fake.Net.Http (>= 6.0) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
      Newtonsoft.Json (>= 13.0.1) - restriction: >= netstandard2.0
      NuGet.Protocol (>= 6.0) - restriction: >= netstandard2.0
    Fake.DotNet.Paket (6.0)
      Fake.Core.Process (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.String (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Trace (>= 6.0) - restriction: >= netstandard2.0
      Fake.DotNet.Cli (>= 6.0) - restriction: >= netstandard2.0
      Fake.IO.FileSystem (>= 6.0) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
    Fake.IO.FileSystem (6.0)
      Fake.Core.String (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Trace (>= 6.0) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
    Fake.Net.Http (6.0) - restriction: >= netstandard2.0
      Fake.Core.Trace (>= 6.0) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
    Fake.Tools.Git (6.0)
      Fake.Core.Environment (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Process (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.SemVer (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.String (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Trace (>= 6.0) - restriction: >= netstandard2.0
      Fake.IO.FileSystem (>= 6.0) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
    FParsec (1.1.1) - restriction: >= netstandard2.0
      FSharp.Core (>= 4.3.4) - restriction: || (>= net45) (>= netstandard2.0)
      System.ValueTuple (>= 4.4) - restriction: >= net45
    FSharp.Control.Reactive (5.0.5) - restriction: >= netstandard2.0
      FSharp.Core (>= 4.7.2) - restriction: >= netstandard2.0
      System.Reactive (>= 5.0 < 6.0) - restriction: >= netstandard2.0
    FSharp.Core (8.0.301)
    FSharp.Data (6.4)
      FSharp.Core (>= 6.0.1) - restriction: >= netstandard2.0
      FSharp.Data.Csv.Core (>= 6.4) - restriction: >= netstandard2.0
      FSharp.Data.Html.Core (>= 6.4) - restriction: >= netstandard2.0
      FSharp.Data.Http (>= 6.4) - restriction: >= netstandard2.0
      FSharp.Data.Json.Core (>= 6.4) - restriction: >= netstandard2.0
      FSharp.Data.Runtime.Utilities (>= 6.4) - restriction: >= netstandard2.0
      FSharp.Data.WorldBank.Core (>= 6.4) - restriction: >= netstandard2.0
      FSharp.Data.Xml.Core (>= 6.4) - restriction: >= netstandard2.0
    FSharp.Data.Csv.Core (6.4) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.1) - restriction: >= netstandard2.0
      FSharp.Data.Runtime.Utilities (>= 6.4) - restriction: >= netstandard2.0
    FSharp.Data.Html.Core (6.4) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.1) - restriction: >= netstandard2.0
      FSharp.Data.Csv.Core (>= 6.4) - restriction: >= netstandard2.0
      FSharp.Data.Runtime.Utilities (>= 6.4) - restriction: >= netstandard2.0
    FSharp.Data.Http (6.4) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.1) - restriction: >= netstandard2.0
    FSharp.Data.Json.Core (6.4) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.1) - restriction: >= netstandard2.0
      FSharp.Data.Http (>= 6.4) - restriction: >= netstandard2.0
      FSharp.Data.Runtime.Utilities (>= 6.4) - restriction: >= netstandard2.0
    FSharp.Data.Runtime.Utilities (6.4) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.1) - restriction: >= netstandard2.0
      FSharp.Data.Http (>= 6.4) - restriction: >= netstandard2.0
    FSharp.Data.WorldBank.Core (6.4) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.1) - restriction: >= netstandard2.0
      FSharp.Data.Http (>= 6.4) - restriction: >= netstandard2.0
      FSharp.Data.Json.Core (>= 6.4) - restriction: >= netstandard2.0
      FSharp.Data.Runtime.Utilities (>= 6.4) - restriction: >= netstandard2.0
    FSharp.Data.Xml.Core (6.4) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.1) - restriction: >= netstandard2.0
      FSharp.Data.Http (>= 6.4) - restriction: >= netstandard2.0
      FSharp.Data.Json.Core (>= 6.4) - restriction: >= netstandard2.0
      FSharp.Data.Runtime.Utilities (>= 6.4) - restriction: >= netstandard2.0
    FSharp.SystemTextJson (1.3.13) - restriction: >= net8.0
      FSharp.Core (>= 4.7) - restriction: >= netstandard2.0
      System.Text.Json (>= 6.0) - restriction: >= netstandard2.0
    FsToolkit.ErrorHandling (4.15.3) - restriction: >= net8.0
      FSharp.Core (>= 7.0.300) - restriction: >= netstandard2.1
    FsToolkit.ErrorHandling.TaskResult (4.15.3) - restriction: >= net8.0
      FsToolkit.ErrorHandling (>= 4.15.3) - restriction: >= netstandard2.0
    MailKit (4.7.1) - restriction: >= net8.0
      MimeKit (>= 4.7.1) - restriction: || (&& (>= net462) (< netstandard2.0)) (&& (< net462) (>= netstandard2.0)) (>= net47) (>= netstandard2.1)
    Microsoft.Bcl.AsyncInterfaces (8.0) - restriction: || (&& (>= net462) (>= netstandard2.0)) (>= net472) (&& (< net5.0) (>= netstandard2.0)) (>= net8.0)
    Microsoft.Build.Framework (17.9.5) - restriction: >= netstandard2.0
      Microsoft.Win32.Registry (>= 5.0) - restriction: && (< net472) (< net8.0) (>= netstandard2.0)
      System.Memory (>= 4.5.5) - restriction: && (< net472) (< net8.0) (>= netstandard2.0)
      System.Runtime.CompilerServices.Unsafe (>= 6.0) - restriction: || (>= net472) (&& (< net8.0) (>= netstandard2.0))
      System.Security.Principal.Windows (>= 5.0) - restriction: && (< net472) (< net8.0) (>= netstandard2.0)
    Microsoft.Build.Utilities.Core (17.9.5) - restriction: >= netstandard2.0
      Microsoft.Build.Framework (>= 17.9.5) - restriction: >= netstandard2.0
      Microsoft.IO.Redist (>= 6.0) - restriction: >= net472
      Microsoft.NET.StringTools (>= 17.9.5) - restriction: >= netstandard2.0
      Microsoft.Win32.Registry (>= 5.0) - restriction: && (< net472) (< net8.0) (>= netstandard2.0)
      System.Collections.Immutable (>= 8.0) - restriction: >= netstandard2.0
      System.Configuration.ConfigurationManager (>= 8.0) - restriction: >= netstandard2.0
      System.Memory (>= 4.5.5) - restriction: || (>= net472) (&& (< net8.0) (>= netstandard2.0))
      System.Runtime.CompilerServices.Unsafe (>= 6.0) - restriction: || (>= net472) (&& (< net8.0) (>= netstandard2.0))
      System.Security.Principal.Windows (>= 5.0) - restriction: && (< net472) (< net8.0) (>= netstandard2.0)
      System.Text.Encoding.CodePages (>= 7.0) - restriction: && (< net472) (< net8.0) (>= netstandard2.0)
    Microsoft.IO.Redist (6.0) - restriction: >= net472
      System.Buffers (>= 4.5.1) - restriction: >= net472
      System.Memory (>= 4.5.4) - restriction: >= net472
    Microsoft.NET.StringTools (17.9.5) - restriction: >= netstandard2.0
      System.Memory (>= 4.5.5) - restriction: || (>= net472) (&& (< net8.0) (>= netstandard2.0))
      System.Runtime.CompilerServices.Unsafe (>= 6.0) - restriction: || (>= net472) (&& (< net8.0) (>= netstandard2.0))
    Microsoft.NETCore.Platforms (7.0.4) - restriction: || (&& (< monoandroid) (< net45) (< netcoreapp3.1) (>= netstandard2.0) (< win8) (< wpa81) (< xamarinios) (< xamarinmac) (< xamarintvos) (< xamarinwatchos)) (&& (< monoandroid) (< net45) (< netstandard1.2) (>= netstandard2.0) (< win8)) (&& (< monoandroid) (< net45) (< netstandard1.3) (>= netstandard2.0) (< win8) (< wpa81)) (&& (< monoandroid) (< net45) (< netstandard1.5) (>= netstandard2.0) (< win8) (< wpa81)) (&& (>= netcoreapp2.0) (< netcoreapp2.1)) (&& (>= netcoreapp2.1) (< netcoreapp3.0))
    Microsoft.NETCore.Targets (5.0) - restriction: || (&& (< monoandroid) (< net45) (< netcoreapp3.1) (>= netstandard2.0) (< win8) (< wpa81) (< xamarinios) (< xamarinmac) (< xamarintvos) (< xamarinwatchos)) (&& (< monoandroid) (< net45) (< netstandard1.2) (>= netstandard2.0) (< win8)) (&& (< monoandroid) (< net45) (< netstandard1.3) (>= netstandard2.0) (< win8) (< wpa81)) (&& (< monoandroid) (< net45) (< netstandard1.5) (>= netstandard2.0) (< win8) (< wpa81))
    Microsoft.Win32.Registry (5.0) - restriction: || (&& (< net45) (>= netstandard2.0)) (&& (< net472) (< net8.0) (>= netstandard2.0))
      System.Buffers (>= 4.5.1) - restriction: || (&& (>= monoandroid) (< netstandard1.3)) (>= monotouch) (&& (< net46) (< netcoreapp2.0) (>= netstandard2.0)) (>= xamarinios) (>= xamarinmac) (>= xamarintvos) (>= xamarinwatchos)
      System.Memory (>= 4.5.4) - restriction: || (&& (< monoandroid) (>= netcoreapp2.0) (< netcoreapp2.1) (< xamarinios) (< xamarinmac) (< xamarintvos) (< xamarinwatchos)) (&& (< net46) (< netcoreapp2.0) (>= netstandard2.0) (< xamarinios) (< xamarinmac) (< xamarintvos) (< xamarinwatchos)) (>= uap10.1)
      System.Security.AccessControl (>= 5.0) - restriction: || (&& (>= monoandroid) (< netstandard1.3)) (&& (< monoandroid) (>= netcoreapp2.0)) (>= monotouch) (&& (< net46) (< netcoreapp2.0) (>= netstandard2.0)) (>= net461) (>= netcoreapp2.1) (>= uap10.1) (>= xamarinios) (>= xamarinmac) (>= xamarintvos) (>= xamarinwatchos)
      System.Security.Principal.Windows (>= 5.0) - restriction: || (&& (>= monoandroid) (< netstandard1.3)) (&& (< monoandroid) (>= netcoreapp2.0)) (>= monotouch) (&& (< net46) (< netcoreapp2.0) (>= netstandard2.0)) (>= net461) (>= netcoreapp2.1) (>= uap10.1) (>= xamarinios) (>= xamarinmac) (>= xamarintvos) (>= xamarinwatchos)
    MimeKit (4.7.1) - restriction: >= net8.0
      BouncyCastle.Cryptography (>= 2.4) - restriction: || (&& (>= net462) (< netstandard2.0)) (&& (< net462) (>= netstandard2.0)) (>= net47) (>= netstandard2.1)
      System.Formats.Asn1 (>= 8.0.1) - restriction: || (&& (< net462) (>= netstandard2.0)) (>= netstandard2.1)
      System.Security.Cryptography.Pkcs (>= 8.0) - restriction: || (&& (< net462) (>= netstandard2.0)) (>= netstandard2.1)
    Mono.Cecil (0.11.5) - restriction: >= net8.0
    Mono.Posix.NETStandard (1.0) - restriction: >= netstandard2.0
    MSBuild.StructuredLogger (2.2.206) - restriction: >= netstandard2.0
      Microsoft.Build.Framework (>= 17.5) - restriction: >= netstandard2.0
      Microsoft.Build.Utilities.Core (>= 17.5) - restriction: >= netstandard2.0
    Neo4j.Driver (5.22) - restriction: >= net8.0
      Microsoft.Bcl.AsyncInterfaces (>= 5.0) - restriction: >= netstandard2.0
      System.IO.Pipelines (>= 7.0) - restriction: >= netstandard2.0
      System.ValueTuple (>= 4.5) - restriction: >= netstandard2.0
    Newtonsoft.Json (13.0.3) - restriction: >= netstandard2.0
    NodaTime (3.1.11) - restriction: >= net8.0
      System.Runtime.CompilerServices.Unsafe (>= 4.7.1) - restriction: >= netstandard2.0
    NodaTime.Serialization.SystemTextJson (1.2) - restriction: >= net8.0
      NodaTime (>= 3.0 < 4.0) - restriction: >= netstandard2.0
    NuGet.Common (6.9.1) - restriction: >= netstandard2.0
      NuGet.Frameworks (>= 6.9.1) - restriction: >= netstandard2.0
    NuGet.Configuration (6.9.1) - restriction: >= netstandard2.0
      NuGet.Common (>= 6.9.1) - restriction: >= netstandard2.0
      System.Security.Cryptography.ProtectedData (>= 4.4) - restriction: && (< net472) (>= netstandard2.0)
    NuGet.Frameworks (6.9.1) - restriction: >= netstandard2.0
    NuGet.Packaging (6.9.1) - restriction: >= netstandard2.0
      Newtonsoft.Json (>= 13.0.3) - restriction: >= netstandard2.0
      NuGet.Configuration (>= 6.9.1) - restriction: >= netstandard2.0
      NuGet.Versioning (>= 6.9.1) - restriction: >= netstandard2.0
      System.Security.Cryptography.Pkcs (>= 6.0.4) - restriction: || (&& (< net472) (>= netstandard2.0)) (>= net5.0)
    NuGet.Protocol (6.9.1) - restriction: >= netstandard2.0
      NuGet.Packaging (>= 6.9.1) - restriction: >= netstandard2.0
      System.Text.Json (>= 7.0.3) - restriction: || (>= net472) (&& (< net5.0) (>= netstandard2.0))
    NuGet.Versioning (6.9.1) - restriction: >= netstandard2.0
    Octokit (10.0)
    System.Buffers (4.5.1) - restriction: || (&& (>= monoandroid) (< netstandard1.1) (>= netstandard2.0)) (&& (>= monoandroid) (< netstandard1.3) (>= netstandard2.0)) (&& (< monoandroid) (< netstandard1.1) (>= netstandard2.0) (< win8)) (&& (>= monotouch) (>= netstandard2.0)) (&& (< net45) (< netcoreapp2.0) (>= netstandard2.0)) (&& (>= net461) (>= netstandard2.0)) (&& (>= net462) (>= netstandard2.0)) (>= net472) (&& (< net5.0) (>= netstandard2.0)) (&& (< netstandard1.1) (>= netstandard2.0) (>= win8)) (&& (>= netstandard2.0) (>= xamarintvos)) (&& (>= netstandard2.0) (>= xamarinwatchos)) (>= xamarinios) (>= xamarinmac)
    System.Collections.Immutable (8.0) - restriction: >= netstandard2.0
      System.Memory (>= 4.5.5) - restriction: || (>= net462) (&& (< net6.0) (>= netstandard2.0))
      System.Runtime.CompilerServices.Unsafe (>= 6.0) - restriction: || (>= net462) (&& (>= net6.0) (< net7.0)) (&& (< net6.0) (>= netstandard2.0))
    System.Configuration.ConfigurationManager (8.0) - restriction: >= netstandard2.0
      System.Diagnostics.EventLog (>= 8.0) - restriction: >= net7.0
      System.Security.Cryptography.ProtectedData (>= 8.0) - restriction: || (&& (< net462) (>= netstandard2.0)) (>= net6.0)
    System.Diagnostics.EventLog (8.0) - restriction: >= net7.0
    System.Formats.Asn1 (8.0.1) - restriction: >= net8.0
    System.IO.Pipelines (8.0) - restriction: >= net8.0
    System.Memory (4.5.5) - restriction: || (&& (< monoandroid) (>= netcoreapp2.0) (< netcoreapp2.1) (< xamarinios) (< xamarinmac) (< xamarintvos) (< xamarinwatchos)) (&& (< net45) (< netcoreapp2.0) (>= netstandard2.0) (< xamarinios) (< xamarinmac) (< xamarintvos) (< xamarinwatchos)) (&& (>= net462) (>= netstandard2.0)) (>= net472) (&& (< net5.0) (>= netstandard2.0)) (&& (< net6.0) (>= netstandard2.0)) (&& (< net8.0) (>= netstandard2.0)) (&& (>= netstandard2.0) (>= uap10.1))
      System.Buffers (>= 4.5.1) - restriction: || (&& (>= monoandroid) (< netstandard1.1)) (&& (< monoandroid) (< net45) (>= netstandard1.1) (< netstandard2.0) (< win8) (< wpa81)) (&& (< monoandroid) (< netstandard1.1) (>= portable-net45+win8+wpa81) (< win8)) (>= monotouch) (&& (>= net45) (< netstandard2.0)) (&& (< net45) (< netcoreapp2.0) (>= netstandard2.0)) (>= net461) (&& (< netstandard1.1) (>= win8)) (&& (< netstandard2.0) (< uap10.1) (>= wpa81)) (>= xamarinios) (>= xamarinmac) (>= xamarintvos) (>= xamarinwatchos)
      System.Numerics.Vectors (>= 4.4) - restriction: && (< net45) (< netcoreapp2.0) (>= netstandard2.0) (< xamarinios) (< xamarinmac) (< xamarintvos) (< xamarinwatchos)
      System.Numerics.Vectors (>= 4.5) - restriction: >= net461
      System.Runtime.CompilerServices.Unsafe (>= 4.5.3) - restriction: || (&& (>= monoandroid) (< netstandard1.1)) (&& (< monoandroid) (< net45) (>= netstandard1.1) (< netstandard2.0) (< win8) (< wpa81)) (&& (< monoandroid) (>= netcoreapp2.0) (< netcoreapp2.1)) (&& (< monoandroid) (< netstandard1.1) (>= portable-net45+win8+wpa81) (< win8)) (>= monotouch) (&& (>= net45) (< netstandard2.0)) (&& (< net45) (< netcoreapp2.0) (>= netstandard2.0)) (>= net461) (&& (< netstandard1.1) (>= win8)) (&& (< netstandard2.0) (>= wpa81)) (>= uap10.1) (>= xamarinios) (>= xamarinmac) (>= xamarintvos) (>= xamarinwatchos)
    System.Numerics.Vectors (4.5) - restriction: || (&& (< net45) (< netcoreapp2.0) (>= netstandard2.0) (< xamarinios) (< xamarinmac) (< xamarintvos) (< xamarinwatchos)) (&& (>= net461) (>= netstandard2.0)) (&& (>= net462) (>= netstandard2.0))
    System.Reactive (5.0) - restriction: >= netstandard2.0
      System.Runtime.InteropServices.WindowsRuntime (>= 4.3) - restriction: && (< net472) (< netcoreapp3.1) (>= netstandard2.0)
      System.Threading.Tasks.Extensions (>= 4.5.4) - restriction: || (>= net472) (&& (< netcoreapp3.1) (>= netstandard2.0)) (>= uap10.1)
    System.Runtime (4.3.1) - restriction: && (< monoandroid) (< net45) (< netcoreapp3.1) (>= netstandard2.0) (< win8) (< wpa81) (< xamarinios) (< xamarinmac) (< xamarintvos) (< xamarinwatchos)
      Microsoft.NETCore.Platforms (>= 1.1.1) - restriction: || (&& (< monoandroid) (< net45) (>= netstandard1.0) (< netstandard1.2) (< win8) (< wp8)) (&& (< monoandroid) (< net45) (>= netstandard1.2) (< netstandard1.3) (< win8) (< wpa81)) (&& (< monoandroid) (< net45) (>= netstandard1.3) (< netstandard1.5) (< win8) (< wpa81)) (&& (< monotouch) (< net45) (>= netstandard1.5) (< win8) (< wpa81) (< xamarinios) (< xamarinmac) (< xamarintvos) (< xamarinwatchos))
      Microsoft.NETCore.Targets (>= 1.1.3) - restriction: || (&& (< monoandroid) (< net45) (>= netstandard1.0) (< netstandard1.2) (< win8) (< wp8)) (&& (< monoandroid) (< net45) (>= netstandard1.2) (< netstandard1.3) (< win8) (< wpa81)) (&& (< monoandroid) (< net45) (>= netstandard1.3) (< netstandard1.5) (< win8) (< wpa81)) (&& (< monotouch) (< net45) (>= netstandard1.5) (< win8) (< wpa81) (< xamarinios) (< xamarinmac) (< xamarintvos) (< xamarinwatchos))
    System.Runtime.CompilerServices.Unsafe (6.0) - restriction: || (&& (< monoandroid) (>= netcoreapp2.0) (< netcoreapp2.1)) (&& (>= net462) (>= netcoreapp2.0)) (&& (>= net462) (>= xamarinios)) (&& (>= net462) (>= xamarinmac)) (&& (< net5.0) (>= net6.0)) (&& (>= net6.0) (< net7.0)) (&& (< net6.0) (>= xamarinios)) (&& (< net6.0) (>= xamarinmac)) (>= netstandard2.0)
    System.Runtime.InteropServices.WindowsRuntime (4.3) - restriction: && (< net472) (< netcoreapp3.1) (>= netstandard2.0)
      System.Runtime (>= 4.3) - restriction: && (< monoandroid) (< monotouch) (< net45) (>= netstandard1.0) (< win8) (< wp8) (< wpa81) (< xamarinios) (< xamarinmac) (< xamarintvos) (< xamarinwatchos)
    System.Security.AccessControl (6.0.1) - restriction: || (&& (>= monoandroid) (< netstandard1.3) (>= netstandard2.0)) (&& (< monoandroid) (>= netcoreapp2.0)) (&& (>= monotouch) (>= netstandard2.0)) (&& (< net45) (>= net461) (>= netstandard2.0)) (&& (< net45) (< netcoreapp2.0) (>= netstandard2.0)) (>= netcoreapp2.1) (&& (>= netstandard2.0) (>= uap10.1)) (&& (>= netstandard2.0) (>= xamarintvos)) (&& (>= netstandard2.0) (>= xamarinwatchos)) (>= xamarinios) (>= xamarinmac)
      System.Security.Principal.Windows (>= 5.0) - restriction: || (>= net461) (&& (< net6.0) (>= netstandard2.0))
    System.Security.Cryptography.Pkcs (8.0) - restriction: || (&& (< net472) (>= netstandard2.0)) (>= net5.0)
      System.Formats.Asn1 (>= 8.0) - restriction: || (&& (< net462) (>= netstandard2.0)) (>= netstandard2.1)
    System.Security.Cryptography.ProtectedData (8.0) - restriction: || (&& (< net462) (>= netstandard2.0)) (&& (< net472) (>= netstandard2.0)) (>= net6.0)
      System.Memory (>= 4.5.5) - restriction: && (< net462) (< net6.0) (>= netstandard2.0)
    System.Security.Principal.Windows (5.0) - restriction: || (&& (>= monoandroid) (< netstandard1.3) (>= netstandard2.0)) (&& (< monoandroid) (>= netcoreapp2.0)) (&& (>= monotouch) (>= netstandard2.0)) (&& (< net45) (>= net461) (>= netstandard2.0)) (&& (< net45) (< netcoreapp2.0) (>= netstandard2.0)) (&& (>= net461) (>= netcoreapp2.0)) (&& (< net472) (< net8.0) (>= netstandard2.0)) (>= netcoreapp2.1) (&& (>= netstandard2.0) (>= uap10.1)) (&& (>= netstandard2.0) (>= xamarintvos)) (&& (>= netstandard2.0) (>= xamarinwatchos)) (>= xamarinios) (>= xamarinmac)
      Microsoft.NETCore.Platforms (>= 5.0) - restriction: || (&& (>= netcoreapp2.0) (< netcoreapp2.1)) (&& (>= netcoreapp2.1) (< netcoreapp3.0))
    System.Text.Encoding.CodePages (8.0) - restriction: && (< net472) (< net8.0) (>= netstandard2.0)
      System.Memory (>= 4.5.5) - restriction: || (>= net462) (&& (< net6.0) (>= netstandard2.0))
      System.Runtime.CompilerServices.Unsafe (>= 6.0) - restriction: || (>= net462) (&& (>= net6.0) (< net7.0)) (&& (< net6.0) (>= netstandard2.0))
    System.Text.Encodings.Web (8.0) - restriction: || (&& (>= net462) (>= netstandard2.0)) (>= net472) (&& (< net5.0) (>= netstandard2.0))
      System.Buffers (>= 4.5.1) - restriction: || (>= net462) (&& (< net6.0) (>= netstandard2.0))
      System.Memory (>= 4.5.5) - restriction: || (>= net462) (&& (< net6.0) (>= netstandard2.0))
      System.Runtime.CompilerServices.Unsafe (>= 6.0) - restriction: || (>= net462) (&& (>= net6.0) (< net7.0)) (&& (< net6.0) (>= netstandard2.0))
    System.Text.Json (8.0.3) - restriction: || (>= net472) (&& (< net5.0) (>= netstandard2.0)) (>= net8.0)
      Microsoft.Bcl.AsyncInterfaces (>= 8.0) - restriction: || (>= net462) (&& (< net6.0) (>= netstandard2.0))
      System.Buffers (>= 4.5.1) - restriction: || (>= net462) (&& (< net6.0) (>= netstandard2.0))
      System.Memory (>= 4.5.5) - restriction: || (>= net462) (&& (< net6.0) (>= netstandard2.0))
      System.Runtime.CompilerServices.Unsafe (>= 6.0) - restriction: || (>= net462) (&& (>= net6.0) (< net7.0)) (&& (< net6.0) (>= netstandard2.0))
      System.Text.Encodings.Web (>= 8.0) - restriction: || (>= net462) (>= netstandard2.0)
      System.Threading.Tasks.Extensions (>= 4.5.4) - restriction: || (>= net462) (&& (< net6.0) (>= netstandard2.0))
      System.ValueTuple (>= 4.5) - restriction: >= net462
    System.Threading.Tasks.Extensions (4.5.4) - restriction: || (&& (>= net462) (>= netstandard2.0)) (>= net472) (&& (< net5.0) (>= netstandard2.0)) (&& (< netcoreapp3.1) (>= netstandard2.0)) (&& (>= netstandard2.0) (>= uap10.1))
      System.Runtime.CompilerServices.Unsafe (>= 4.5.3) - restriction: || (&& (< monoandroid) (< monotouch) (< net45) (>= netstandard1.0) (< netstandard2.0) (< win8) (< wpa81) (< xamarintvos) (< xamarinwatchos)) (&& (< monoandroid) (< netstandard1.0) (>= portable-net45+win8+wp8+wpa81) (< win8)) (&& (>= net45) (< netstandard2.0)) (&& (< net45) (< netcoreapp2.1) (>= netstandard2.0) (< xamarinios) (< xamarinmac) (< xamarintvos) (< xamarinwatchos)) (>= net461) (&& (< netstandard1.0) (>= win8)) (&& (< netstandard2.0) (>= wpa81)) (>= wp8)
    System.ValueTuple (4.5) - restriction: || (&& (>= net462) (>= netstandard2.0)) (>= net472) (>= net8.0)
  remote: https://nuget.pkg.github.com/CCTC-team/index.json
    Lib (1.0.32)
      Expecto (>= 10.2.1) - restriction: >= net8.0
      FSharp.Core (>= 8.0.300) - restriction: >= net8.0
      FSharp.Data (>= 6.4) - restriction: >= net8.0
      FSharp.SystemTextJson (>= 1.3.13) - restriction: >= net8.0
      FsToolkit.ErrorHandling (>= 4.15.2) - restriction: >= net8.0
      FsToolkit.ErrorHandling.TaskResult (>= 4.15.2) - restriction: >= net8.0
      MailKit (>= 4.7.1) - restriction: >= net8.0
      Neo4j.Driver (>= 5.20) - restriction: >= net8.0
      NodaTime (>= 3.1.11) - restriction: >= net8.0
      NodaTime.Serialization.SystemTextJson (>= 1.2) - restriction: >= net8.0
