﻿@using CCTC_Lib.Contracts.Interaction
@using CCTC_Lib.Contracts.Data
@using CCTC_Lib.Enums.Data
@using CCTC_Lib.Enums.UI

@typeparam TData where TData : IUniqueIdentity<string>, ISortable, ISearchable

@Template(Content.ItemData)

@code {

    /// <summary>
    /// The <see cref="Action"/> invoked when the last item has loaded
    /// </summary>
    /// <remarks>Requires an Action rather than an EventCallback</remarks>
    [Parameter, EditorRequired]
    public required Action HasLoaded { get; set; }

    /// <summary>
    /// The template that is displayed
    /// </summary>
    [Parameter, EditorRequired]
    public required RenderFragment<TData> Template { get; set; }

    /// <summary>
    /// The content from the Item in Lister/>
    /// </summary>
    [Parameter, EditorRequired]
    public required ListerItem<TData> Content { get; set; }

    /// <summary>
    /// Simply invokes the action after the first render
    /// </summary>
    /// <param name="firstRender">True if the first render</param>
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await InvokeAsync(HasLoaded.Invoke);
        }
    }
}