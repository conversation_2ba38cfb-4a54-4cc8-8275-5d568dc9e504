﻿using CCTC_Lib.Contracts.Interaction;

namespace CCTC_Components.Services;

/// <summary>
/// Provides a delay that can be mocked in unit tests
/// </summary>
public class DelayService : IDelayService
{
    public async Task Delay(TimeSpan timeSpan)
    {
        await Task.Delay(timeSpan);
    }

    public async Task Delay(TimeSpan timeSpan, CancellationToken token)
    {
        await Task.Delay(timeSpan, token);
    }
}