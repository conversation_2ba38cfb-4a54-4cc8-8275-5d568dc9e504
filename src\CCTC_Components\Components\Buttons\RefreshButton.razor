﻿@inherits CCTC_Components.Components.Buttons.ButtonBase

<cctc-button data-cctc-button-type="refreshbutton" id="@Id" class="@CssClass" style="@Style" data-author="cctc">
    <div class="@_buttonWrapperClass" @onclick="OnButtonClick">
@{
    if (!string.IsNullOrEmpty(_buttonIcon))
    {
        <div class="button-icon material-icons">@_buttonIcon</div>
    }
    if (!string.IsNullOrEmpty(ButtonText))
    {
        <div class="button-text">@ButtonText</div>
    }
    if (_showConfirmation)
    {
        <TimeOut
            Id="@($"{Id}-timeout")"
            @ref="@_refreshTimeOut"
            FadeFor="TimeSpan.FromSeconds(1)"
            ShowContentFor="TimeSpan.FromSeconds(2)"
            ReserveSpace="true"
            CollapseOnFadeComplete="false">
            <div class="confirmation-icon-wrapper">
                <div class="confirmation-icon material-icons">done</div>
            </div>
        </TimeOut>
    }
}
    </div>
</cctc-button>

@code {

    [Parameter]
    public bool? ShowConfirmation { get; set; }

    TimeOut? _refreshTimeOut;
    string? _buttonIcon;
    bool _showConfirmation;

    protected override Task OnButtonClick()
    {
        if (_showConfirmation && !Disabled)
        {
            _refreshTimeOut?.Initiate();
        }

        return base.OnButtonClick();
    }

    ///<inheritdoc />
    protected override void OnParametersSet()
    {
        _buttonIcon = ButtonIcon ?? "refresh";
        _showConfirmation = ShowConfirmation ?? true;
        base.OnParametersSet();
    }
}