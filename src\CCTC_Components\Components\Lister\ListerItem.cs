﻿using CCTC_Lib.Contracts.Data;

namespace CCTC_Components.Components.Lister;

/// <summary>
/// Represents an item within a <see cref="Lister"/>
/// </summary>
/// <typeparam name="TData">The data type for the lister</typeparam>
/// <remarks>Used for wrapping each data item with information required for lister</remarks>
public class ListerItem<TData> where TData : IUniqueIdentity<string>, ISortable, ISearchable
{
    /// <summary>
    /// Index of the item
    /// </summary>
    public int Index { get; set; }

    /// <summary>
    /// True when selected
    /// </summary>
    public bool IsSelected { get; set; }

    /// <summary>
    /// The data
    /// </summary>
    public required TData ItemData { get; init; }

    /// <summary>
    /// True when the item is included
    /// </summary>
    public bool Included { get; set; }

    /// <inheritdoc />
    public override string ToString()
    {
        return $"i: {Index}, sel: {IsSelected}, inc: {Included}, item: {ItemData}";
    }
}