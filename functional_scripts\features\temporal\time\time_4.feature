@component @temporal @time @time_4
Feature: the time component can be made read-only and / or disabled. The read-only icon is optional
    Scenario: the time component can be made read-only
        Given the user is at the home page
        And the user selects the "Time" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: h:mm tt, FeedbackIcon.Valid, read-only"
        Then the Time component is not editable
        And the Time component image matches the base image "time-readonly"

    Scenario: the time component can be disabled
        Given the user is at the home page
        And the user selects the "Time" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: h:mm tt, FeedbackIcon.Valid, disabled"
        Then the Time component is disabled
        And the Time component image matches the base image "time-disabled"

    Scenario: the time component can be made read-only and disabled
        Given the user is at the home page
        And the user selects the "Time" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: h:mm tt, FeedbackIcon.Valid, read-only and disabled"
        Then the Time component is not editable
        And the Time component is disabled
        And the Time component image matches the base image "time-readonly-disabled"

    Scenario: the time component can be made read-only without a read-only icon
        Given the user is at the home page
        And the user selects the "Time" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: h:mm tt, FeedbackIcon.Valid, read-only and read-only icon hidden"
        Then the Time component is not editable
        And the Time component image matches the base image "time-readonly-no-icon"

    Scenario: the time component can be made read-only and disabled without a read-only icon
        Given the user is at the home page
        And the user selects the "Time" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: h:mm tt, FeedbackIcon.Valid, read-only, disabled and read-only icon hidden"
        Then the Time component is not editable
        And the Time component is disabled
        And the Time component image matches the base image "time-readonly-disabled-no-icon"
