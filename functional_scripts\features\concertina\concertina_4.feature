@component @concertina @concertina_4
Feature: the concertina has a subheader

Sc<PERSON>rio: the concertina has a subheader
    Given the user is at the home page
    And the user selects the "Concertina" component in the container "Concertina"
    And the user clicks the "Usage" tab
    When the user expands the concertina by clicking on the header with the text "Includes collapse/expand all"
    And concertina content "Sub header" is visible
    Then the concertina has 1 sub-headers
    And the Concertina component image matches the base image "Concertina with subheader"
    




