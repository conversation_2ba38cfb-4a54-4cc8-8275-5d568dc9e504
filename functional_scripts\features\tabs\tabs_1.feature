@component @tabs @tabs_1
Feature: the tabs each display separate content
    Scenario: the tabs component sample page is available
        Given the user is at the home page
        When the user selects the "Tabs" component in the container "Tabs"
        Then the url ending is "tabssample"

    Scenario: the tabs can display different words
        Given the user is at the home page
        When the user selects the "Tabs" component in the container "Tabs"
        And the user clicks the "1 Tab" tab
        Then the tab content contains "Lorem ipsum dolor sit amet consectetur adipisicing elit. Praesentium, officia! Numquam ex saepe mollitia ut nam doloribus dolor ea amet magni voluptates. Placeat laudantium voluptatum sint vel nisi dolores mollitia!"
        And the Tabs component image matches the base image "tabs-1"
        When the user clicks the "2 Tab" tab
        Then the tab content contains "This is some content in tab 2 styled with a dim grey background"
        And the Tabs component image matches the base image "tabs-2"

    Scenario: the tabs can display different colours
        Given the user is at the home page
        When the user selects the "Tabs" component in the container "Tabs"
        And the user clicks the "1 Tab" tab
        Then the tab background colour should be "rgba(0, 0, 0, 0)"
        And the Tabs component image matches the base image "tabs-1"
        When the user clicks the "2 Tab" tab
        Then the tab background colour should be "rgb(105, 105, 105)"
        And the Tabs component image matches the base image "tabs-2"

    Scenario: the tabs can display different font sizes
        Given the user is at the home page
        When the user selects the "Tabs" component in the container "Tabs"
        And the user clicks the "1 Tab" tab
        Then the tab content font size should be "35px"
        And the Tabs component image matches the base image "tabs-1"
        When the user clicks the "2 Tab" tab
        Then the tab content font size should be "14px"
        And the Tabs component image matches the base image "tabs-2"

    Scenario: the content box can be different sizes in each tab
        Given the user is at the home page
        When the user selects the "Tabs" component in the container "Tabs"
        And the user clicks the "1 Tab" tab
        Then the tab content box height should be "200px"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "Typical usage"
        Then the tab content box height should be "150px"

        