import { ICustomWorld } from '../../support/custom-world';
import { compareToBaseImage } from '../../utils/compareImages';
import * as Helpers from '../../support/helper-functions';
import { When, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';

When(
  /^the user enters "([^"]*)" into the (Date|Time) (?:part of the Date and Time )?component$/,
  async function (this: ICustomWorld, value: string, componentType: string) {
    await Helpers.getSamplerTabsSelectedContent(this)
      .locator(`cctc-input[data-cctc-input-type="${componentType.toLowerCase()}"]`)
      .getByRole('textbox')
      .first()
      .fill(value);
  }
);

When(
  'the user enters {string} into the Date (part of the Date and Time )component via the date picker',
  async function (this: ICustomWorld, date: string) {
    const dateComponent = Helpers.getSamplerTabsSelectedContent(this).locator(
      'cctc-input[data-cctc-input-type="date"]'
    );
    await dateComponent.getByRole('textbox').last().fill(date);
  }
);

Then(
  /^the (Date|Time) (?:part of the Date and Time )?component has the value "([^"]*)"$/,
  async function (this: ICustomWorld, componentType: string, expectedValue: string) {
    await expect(
      Helpers.getSamplerTabsSelectedContent(this)
        .locator(`cctc-input[data-cctc-input-type="${componentType.toLowerCase()}"]`)
        .getByRole('textbox')
        .first()
    ).toHaveValue(expectedValue);
  }
);

Then(
  /^the (Date|Time) (?:part of the Date and Time )?component displays a (green tick|red exclamation mark) feedback icon$/,
  async function (this: ICustomWorld, componentType: string, validationIcon: string) {
    const icon = Helpers.getSamplerTabsSelectedContent(this)
      .locator(`cctc-input[data-cctc-input-type="${componentType.toLowerCase()}"]`)
      .locator('.val-icon-wrapper > i');
    if (validationIcon === 'green tick') {
      await expect(icon).toHaveClass(/check/);
      await expect(icon).toHaveCSS('color', 'rgb(0, 128, 0)');
    } else {
      await expect(icon).toHaveClass(/priority_high/);
      await expect(icon).toHaveCSS('color', 'rgb(255, 0, 0)');
    }
  }
);

Then(
  /^the (Date|Time) (?:part of the Date and Time )?component has the placeholder "([^"]*)"$/,
  async function (this: ICustomWorld, componentType: string, placeholderText: string) {
    await expect(
      Helpers.getSamplerTabsSelectedContent(this)
        .locator(`cctc-input[data-cctc-input-type="${componentType.toLowerCase()}"]`)
        .getByRole('textbox')
        .first()
    ).toHaveAttribute('placeholder', placeholderText);
  }
);

Then(
  /the (Date|Time) (?:part of the Date and Time )?component is not editable/,
  async function (this: ICustomWorld, componentType: string) {
    await expect(
      Helpers.getSamplerTabsSelectedContent(this)
        .locator(`cctc-input[data-cctc-input-type="${componentType.toLowerCase()}"]`)
        .getByRole('textbox')
        .first()
    ).toBeEditable({ editable: false });
  }
);

Then(
  /the (Date|Time) (?:part of the Date and Time )?component is disabled/,
  async function (this: ICustomWorld, componentType: string) {
    await expect(
      Helpers.getSamplerTabsSelectedContent(this)
        .locator(`cctc-input[data-cctc-input-type="${componentType.toLowerCase()}"]`)
        .getByRole('textbox')
        .first()
    ).toBeDisabled();
  }
);

Then(
  /^the (Date|Time|Date and Time) component image matches the base image "([^"]+)"$/,
  async function (this: ICustomWorld, componentType: string, name: string) {
    const inputType = componentType.toLowerCase().replaceAll(' ', '');
    const screenshot = await Helpers.tryGetStableScreenshot(
      Helpers.getSamplerTabsSelectedContent(this).locator(
        `cctc-input[data-cctc-input-type="${inputType}"]`
      )
    );
    await compareToBaseImage(this, name, screenshot);
  }
);
