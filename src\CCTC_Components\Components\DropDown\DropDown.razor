﻿@using TextCopy
@inject IClipboard Clipboard
@inherits CCTC_Components.Components.__CCTC.CCTCBase
@typeparam TData

<cctc-input data-cctc-input-type="dropdown" id="@Id" class="@CssClass" style="@Style" data-author="cctc">
    <cctc-input-dropdown-selected role="combobox" class="@_inputSelectedClass" aria-expanded="@IsDropDownVisible"
        @onfocusout="HideDropDown" @onclick="ManageDropDown" tabindex="0" @onkeydown="@(x => OnKeyDown(x))" @onkeydown:preventDefault="true">      
@{
    var selectedInputId = $"{Id}-selected-input";
    string selectedInputText = _selectedOption?.OptionDisplay ?? string.Empty;
    RenderFragment selectedInput = @<p id="@selectedInputId">@selectedInputText</p>;
    if (DropDownTooltipBehaviour != DropDownTooltipBehaviour.Disabled)
    {
        <Tooltip
            Id="@($"{Id}-selected-input-tooltip")"
            Content="@selectedInputText"
            TooltipPlacement="DropDownTooltipPlacement"
            TooltipBehaviour="@(GetTooltipBehaviour(DropDownTooltipBehaviour, $"#{selectedInputId}"))">
            @selectedInput
        </Tooltip>
    }
    else
    {
        @selectedInput;
    }     
}
        <div class="icon-wrapper">
            <span class="material-icons expand">
                expand_more
            </span>
            @if (ReadOnly && !HideReadOnlyIcon)
            {
                <span class="material-icons lock">
                    lock
                </span>
            }
            else if (_canClear)
            {
                <span class="@_clearIconClass" @onclick="@(_ => OnValueChanged(null))" @onclick:stopPropagation="true">
                    backspace
                </span>
            }
        </div>
    </cctc-input-dropdown-selected>
    <cctc-input-dropdown class="@_dropDownVisibilityClass">
        <cctc-input-dropdown-options class="@_optionOverflowClass" role="listbox">
            @{
                foreach (var item in _data)
                {
                    if (!item.IsOptionVisible)
                    {
                        continue;
                    }

                    var optionId = $"{Id}-{item.Index}";
                    RenderFragment dropDownOption =
                        @<cctc-input-dropdown-option id="@optionId" @key="item.Index" role="option" data-option-value="@item.OptionValue"
                            data-option-selected="@item.IsOptionSelected"
                            data-option-disabled="@item.IsOptionDisabled"
                            @onmousedown="@(_ => OnMouseDown(item.Index))">
                            @item.OptionDisplay
                        </cctc-input-dropdown-option>;

                    if (DropDownTooltipBehaviour != DropDownTooltipBehaviour.Disabled)
                    {
                        <Tooltip
                            Id="@($"{optionId}-dropdown-option-tooltip")"
                            Content="@item.OptionDisplay"
                            TooltipPlacement="DropDownTooltipPlacement"
                            TooltipBehaviour="@(GetTooltipBehaviour(DropDownTooltipBehaviour, $"#{optionId}"))">
                            @dropDownOption
                        </Tooltip>
                    }
                    else
                    {
                        @dropDownOption
                    }
                }
            }
        </cctc-input-dropdown-options>
    </cctc-input-dropdown>
</cctc-input>

@code {

    /// <summary>
    /// The input data
    /// </summary>
    [EditorRequired]
    [Parameter]
    public required IEnumerable<TData> Data { get; set; }

    /// <summary>
    /// The input value
    /// </summary>
    [Parameter]
    public TData? Value { get; set; }

    /// <summary>
    /// A callback which fires when the input value changes
    /// </summary>
    [Parameter]
    public EventCallback<TData> ValueChanged { get; set; }

    /// <summary>
    /// A second callback which fires when the input value changes. Useful when consuming using @bind-Value
    /// </summary>
    [Parameter]
    public EventCallback<ChangeEventArgs> SelectionChanged { get; set; }

    /// <summary>
    /// A function to specify the option value text
    /// </summary>
    [Parameter]
    public Func<TData, string>? ValueSelector { get; set; }

    /// <summary>
    /// A function to specify the option display text
    /// </summary>
    [Parameter]
    public Func<TData, string>? DisplaySelector { get; set; }

    /// <summary>
    /// A function to specify which options are disabled
    /// </summary>
    [Parameter]
    public Func<TData, bool>? Disabled { get; set; }

    /// <summary>
    /// Read-only if true
    /// </summary>
    [Parameter]
    public bool ReadOnly { get; set; }

    /// <summary>
    /// A function to specify which options are visible
    /// </summary>
    [Parameter]
    public Func<TData, bool>? Visible { get; set; }

    /// <summary>
    /// Hides the read-only icon when <see cref="ReadOnly"/> is true
    /// </summary>
    [Parameter]
    public bool HideReadOnlyIcon { get; set; }

    /// <summary>
    /// Allows the current selection to be cleared from the UI
    /// </summary>
    [Parameter]
    public bool ShowClear { get; set; }

    /// <summary>
    /// Configure the dropdown option overflow
    /// </summary>
    [Parameter]
    public DropDownOptionOverflow DropDownOptionOverflow { get; set; }

    /// <summary>
    /// Configure the dropdown tooltip placement
    /// </summary>
    [Parameter]
    public TooltipPlacement DropDownTooltipPlacement { get; set; }

    /// <summary>
    /// Configure the dropdown tooltip behavior
    /// </summary>
    [Parameter]
    public DropDownTooltipBehaviour DropDownTooltipBehaviour { get; set; }

    List<DropDownOption<TData>> _data = new();
    DropDownOption<TData>? _selectedOption;
    bool _disableDropdown;
    string? _inputSelectedClass;
    string? _optionOverflowClass;
    string? _clearIconClass;
    bool _typeParamAllowsNull;
    bool _canClear;
    bool _showClear;
    string _dropDownVisibilityClass = DropdownHiddenClass;
    const string DropdownHiddenClass = "dropdown-hidden";
    const string DropdownVisibleClass = "dropdown-visible";
    bool IsDropDownVisible => _dropDownVisibilityClass == DropdownVisibleClass;

    void ShowDropDown()
    {
        _dropDownVisibilityClass = DropdownVisibleClass;
    }

    void HideDropDown()
    {
        _dropDownVisibilityClass = DropdownHiddenClass;
    }

    void ManageDropDown()
    {
        if (IsDropDownVisible)
        {
            HideDropDown();
        }
        else if (!_disableDropdown)
        {
            ShowDropDown();
        }
    }

    async Task OnValueChanged(DropDownOption<TData>? newSelection)
    {
        if (!_disableDropdown)
        {
            _data.ForEach(x => x.IsOptionSelected = false);
            _selectedOption = newSelection;

            if (_selectedOption is not null)
            {
                _selectedOption.IsOptionSelected = true;
            }

            await ValueChanged.InvokeAsync(_selectedOption is null ? default(TData) : _selectedOption.OptionData);
            await SelectionChanged.InvokeAsync(new ChangeEventArgs { Value = _selectedOption?.OptionValue });
        }
    }

    /// <summary>
    /// Change to the first available option
    /// </summary>
    /// <param name="indexPredicate">An optional predicate for selecting on option index</param>
    /// <returns></returns>
    async Task ChangeToFirstAvailableOption(Func<DropDownOption<TData>, bool>? indexPredicate = null)
    {
        var newSelection = _data.FirstOrDefault(x => x.IsOptionVisible && !x.IsOptionDisabled && (indexPredicate?.Invoke(x) ?? true));
        if (newSelection is not null)
        {
            await OnValueChanged(newSelection);
        }
    }

    /// <summary>
    /// Change to the last available option
    /// </summary>
    /// <param name="indexPredicate">An optional predicate for selecting on option index</param>
    /// <returns></returns>
    async Task ChangeToLastAvailableOption(Func<DropDownOption<TData>, bool>? indexPredicate = null)
    {
        var newSelection = _data.LastOrDefault(x => x.IsOptionVisible && !x.IsOptionDisabled && (indexPredicate?.Invoke(x) ?? true));
        if (newSelection is not null)
        {
            await OnValueChanged(newSelection);
        }
    }

    async Task IncrementSelectedOption()
    {
        if (_selectedOption is not null && _selectedOption.Index < _data.Count - 1)
        {
            await ChangeToFirstAvailableOption(x => x.Index > _selectedOption.Index);
        }
        else if (_selectedOption is null)
        {
            await ChangeToFirstAvailableOption();
        }
    }

    async Task DecrementSelectedOption()
    {
        if (_selectedOption is not null && _selectedOption.Index > 0)
        {
            await ChangeToLastAvailableOption(x => x.Index < _selectedOption.Index);
        }
        else if (_selectedOption is null)
        {
            await ChangeToFirstAvailableOption();
        }
    }

    async Task OnMouseDown(int selectedIndex)
    {
        await ChangeToFirstAvailableOption(x => x.Index == selectedIndex);
    }

    async Task CopyText()
    {
        await Clipboard.SetTextAsync(await GetSelectedText());
    }

    async Task OnKeyDown(KeyboardEventArgs args)
    {
        switch (args.Key)
        {
            case "ArrowDown" when IsDropDownVisible:
                await IncrementSelectedOption();
                break;
            case "ArrowUp" when IsDropDownVisible:
                await DecrementSelectedOption();
                break;
            case "Enter":
                HideDropDown();
                break;
            case "Escape":
                HideDropDown();
                break;
            case "c":
                if (args.CtrlKey)
                {
                    await CopyText();
                }
                break;
        }
    }

    void CheckConfig(bool typeParamAllowsNull)
    {
        if (ShowClear && !typeParamAllowsNull)
        {
            throw new ArgumentException($"{nameof(ShowClear)} should only be set to true when {nameof(TData)} is a reference type or nullable value type", nameof(ShowClear));
        }
    }

    bool ValuesAreEqual(TData? firstValue, TData? secondValue)
    {
        bool valuesAreEqual = false;
        if (firstValue is null && secondValue is null)
        {
            valuesAreEqual = true;
        }
        else if (firstValue is null || secondValue is null)
        {
            valuesAreEqual = false;
        }
        else if (ValueSelector is not null)
        {
            valuesAreEqual = ValueSelector(firstValue) == ValueSelector(secondValue);
        }
        else if (DisplaySelector is not null)
        {
            valuesAreEqual = DisplaySelector(firstValue) == DisplaySelector(secondValue);
        }
        else
        {
            valuesAreEqual = firstValue.Equals(secondValue);
        }

        return valuesAreEqual;
    }

    List<DropDownOption<TData>> WrapData(IEnumerable<TData> data, TData? value)
    {
        List<DropDownOption<TData>> wrappedData = new();
        var arrayData = data as TData[] ?? data.ToArray();

        for (int i = 0; i < arrayData.Length; i++)
        {
            var optionData = arrayData[i];
            wrappedData.Add(new DropDownOption<TData>()
                {
                    Index = i,
                    OptionData = optionData,
                    OptionValue = optionData is null ? string.Empty : ValueSelector?.Invoke(optionData) ?? (optionData.ToString() ?? string.Empty),
                    OptionDisplay = optionData is null ? string.Empty : DisplaySelector?.Invoke(optionData) ?? (optionData.ToString() ?? string.Empty),
                    IsOptionSelected = ValuesAreEqual(value, optionData),
                    IsOptionVisible = optionData is null ? true : Visible?.Invoke(optionData) ?? true,
                    IsOptionDisabled = optionData is null ? false : Disabled?.Invoke(optionData) ?? false
                });
        }

        return wrappedData;
    }

    TooltipBehaviour GetTooltipBehaviour(DropDownTooltipBehaviour dropDownTooltipBehaviour, string optionSelector)
    {
        return dropDownTooltipBehaviour switch
        {
            DropDownTooltipBehaviour.EnabledOnOptionOverflow => new TooltipBehaviour.EnabledOnOverflow(optionSelector),
            DropDownTooltipBehaviour.Enabled => new TooltipBehaviour.Enabled(),
            DropDownTooltipBehaviour.Disabled => new TooltipBehaviour.Disabled(),
            _ => throw new ArgumentException("case not handled", nameof(dropDownTooltipBehaviour))
        };
    }

    ///<inheritdoc />
    protected override void OnInitialized()
    {
        _typeParamAllowsNull = default(TData) == null;
        CheckConfig(_typeParamAllowsNull);
    }

    ///<inheritdoc />
    protected override void OnParametersSet()
    {
        _data = WrapData(Data, Value);
        _selectedOption = Value is null ? null :_data.Single(x => x.IsOptionSelected);

        bool allOptionsDisabled = _data.All(x => x.IsOptionDisabled);
        _disableDropdown = ReadOnly || allOptionsDisabled;
        _canClear = _typeParamAllowsNull && ShowClear && !ReadOnly && !allOptionsDisabled;
        _showClear = _canClear && _selectedOption is not null;

        _inputSelectedClass =
            new CssBuilder()
                .AddClass("read-only", ReadOnly && !allOptionsDisabled)
                .AddClass("disabled", allOptionsDisabled)
                .Build();

        _optionOverflowClass =
            new CssBuilder()
                .AddClass("option-wrap", DropDownOptionOverflow == DropDownOptionOverflow.Wrap)
                .AddClass("option-nowrap", DropDownOptionOverflow == DropDownOptionOverflow.NoWrap)
                .AddClass("option-scroll", DropDownOptionOverflow == DropDownOptionOverflow.Scroll)
                .Build();

        _clearIconClass =
            new CssBuilder()
                .AddClass("material-icons backspace")
                .AddClass("show-clear-icon", _showClear)
                .AddClass("hide-clear-icon", !_showClear)
                .Build();
    }

    ///<inheritdoc />
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (IsDropDownVisible && _selectedOption is not null)
        {
            await ScrollIntoViewFromSelector($"#{Id}-{_selectedOption.Index}");
        }
    }
}
