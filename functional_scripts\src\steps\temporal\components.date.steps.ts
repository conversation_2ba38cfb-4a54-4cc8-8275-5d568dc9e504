import { ICustomWorld } from '../../support/custom-world';
import * as Helpers from '../../support/helper-functions';
import { Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';

Then(
  'on clearing the Date component value via the date picker an alert is displayed with the text {string}',
  async function (this: ICustomWorld, alertText: string) {
    this.page!.on('dialog', (dialog) => {
      dialog.accept();
      expect(dialog.message()).toBe(alertText);
    });
    await Helpers.getSamplerTabsSelectedContent(this)
      .locator('cctc-input[data-cctc-input-type="date"]')
      .getByRole('textbox')
      .last()
      .fill('');
  }
);
