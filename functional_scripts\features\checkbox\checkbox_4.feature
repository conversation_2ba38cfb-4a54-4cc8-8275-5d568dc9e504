@component @checkbox @checkbox_4

Feature: the checkbox component can be made read-only and / or disabled. The read-only icon is optional

    Scenario: the checkbox component can be made read-only with a read-only icon
        Given the user is at the home page
        And the user selects the "Checkbox" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "CheckBoxOrientation: Left, CheckBoxLabelOverflow: Wrap, CheckBoxLabelTooltipBehaviour: EnabledOnLabelOverflow, read-only"
        And the Checkbox component is checked
        When the user clicks the Checkbox within the Checkbox component
        Then the Checkbox component is checked
        And the Checkbox component image matches the base image "checkbox-readonly-icon"

    Scenario: the checkbox component can be made read-only without a read-only icon
        Given the user is at the home page
        And the user selects the "Checkbox" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "CheckBoxOrientation: Left, CheckBoxLabelOverflow: Wrap, CheckBoxLabelTooltipBehaviour: EnabledOnLabelOverflow, read-only, hide read-only icon"
        And the Checkbox component is checked
        When the user clicks the Checkbox within the Checkbox component
        Then the Checkbox component is checked
        And the Checkbox component image matches the base image "checkbox-readonly-no-icon"

    Scenario: the checkbox component can be made disabled only
        Given the user is at the home page
        And the user selects the "Checkbox" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "CheckBoxOrientation: Left, CheckBoxLabelOverflow: Wrap, CheckBoxLabelTooltipBehaviour: EnabledOnLabelOverflow, disabled"
        Then the Checkbox component is disabled
        And the Checkbox component image matches the base image "checkbox-disabled-only"

    Scenario: the checkbox component can be made read-only and disabled
        Given the user is at the home page
        And the user selects the "Checkbox" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "CheckBoxOrientation: Right, CheckBoxLabelOverflow: Wrap, CheckBoxLabelTooltipBehaviour: EnabledOnLabelOverflow, disabled and read-only"
        Then the Checkbox component is disabled
        And the Checkbox component image matches the base image "checkbox-disabled-readonly-icon"
