@component @temporal @time @time_3
Feature: the time component time is validated (time format, min and / or max time) and there is feedback provided to the user via an icon
    Scenario: an incomplete time is shown as an error
        Given the user is at the home page
        And the user selects the "Time" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: HH:mm:ss, FeedbackIcon.Error"
        When the user enters "14151" into the Time component
        Then the Time component has the value "14:15:1"
        And the Time component displays a red exclamation mark feedback icon
        And the Time component image matches the base image "time-error"

    Scenario: a complete time is shown as valid
        Given the user is at the home page
        And the user selects the "Time" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: h:mm tt, null initial value, FeedbackIcon.Valid"
        When the user enters "1213pm" into the Time component
        Then the Time component has the value "12:13 pm"
        And the Time component displays a green tick feedback icon
        And the Time component image matches the base image "time-valid"

    Scenario: a time earlier than the minimum time is shown as an error
        Given the user is at the home page
        And the user selects the "Time" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: h:mm tt, Min (11:05 am) and max (1:10 pm), FeedbackIcon.Both, CssClass applied"
        When the user enters "1104am" into the Time component
        Then the Time component has the value "11:04 am"
        And the Time component displays a red exclamation mark feedback icon

    Scenario: a time later than the maximum time is shown as an error
        Given the user is at the home page
        And the user selects the "Time" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: h:mm tt, Min (11:05 am) and max (1:10 pm), FeedbackIcon.Both, CssClass applied"
        When the user enters "1:11pm" into the Time component
        Then the Time component has the value "1:11 pm"
        And the Time component displays a red exclamation mark feedback icon