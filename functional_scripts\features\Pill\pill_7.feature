@component @pill @pill_7

Feature: a contextual variation can optionally be provided in the pill
    Scenario: the pill can be an inform context pill
        Given the user is at the home page
        And the user selects the "Pill" component
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "PillContext: Info, ColorStyle: Fill, Size: Small, Icon: info, with OnClick callback"
        Then the pill context is "pill-info"
        And the pill component image matches the base image "pill info"

    Scenario: the pill can be a danger context pill
        Given the user is at the home page
        And the user selects the "Pill" component
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "PillContext: Danger, ColorStyle: Fill, Size: Small, Icon: dangerous"
        Then the pill context is "pill-danger"
        And the pill component image matches the base image "pill danger"

    Scenario: the pill can be a success context pill
        Given the user is at the home page
        And the user selects the "Pill" component
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "PillContext: Success, ColorStyle: Outline, Size: Small, Icon: done_outline"
        Then the pill context is "pill-success pill-outline"
        And the pill component image matches the base image "pill success and outline"


