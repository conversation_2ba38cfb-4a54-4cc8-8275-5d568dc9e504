@component @numeric @numeric_1
Feature: the numeric component can receive numeric input
    Scenario: the numeric component sample page is available
        Given the user is at the home page
        When the user selects the "Numeric" component in the container "Input"
        Then the url ending is "numericsample"

    Scenario: the numeric component can receive numeric input
        Given the user is at the home page
        Given the user selects the "Numeric" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: -##00"
        When the user enters "123456" into the Numeric component
        Then the Numeric component has the value "1234"