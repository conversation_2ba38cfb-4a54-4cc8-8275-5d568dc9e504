﻿@using System.Reactive.Subjects
@using System.Reactive.Linq
@using System.Runtime.CompilerServices
@using System.Runtime.InteropServices
@using CCTC_Components.Components.__CCTC.Models
@using static Common.Helpers.StringMask
@using Microsoft.JSInterop;
@using SortDir = CCTC_Lib.Enums.Data.SortDir
@using CCTC_Lib.Contracts.Data
@using CCTC_Lib.Contracts.Reactive
@using CCTC_Lib.Enums.Data
@using CCTC_Lib.Enums.UI
@using CCTC_Components.Components.Lister
@using CCTC_Components.Components.TextBox.TextInteraction
@using CCTC_Lib.Models.UI
@using Neo4j.Driver
@inject ISchedulerProvider SchedulerProvider
@inject IJSRuntime Js
@inherits CCTC_Components.Components.__CCTC.CCTCBase
@implements IDisposable
@typeparam TData where TData : class, IUniqueIdentity<string>, ISortable, ISearchable
    @typeparam TKey where TKey : class, IUniqueIdentity<string>, ISortable, ISearchable

<cctc-datatextbox id="@Id" @ref="@_cctcDatatextbox" class="@CssClass" style="@Style" data-author="cctc"
                  @onfocusin="@RaiseFocusEvent"
                  @onfocusout="@RaiseFocusEvent"
                  >
    <div class="d-flex flex-column">
        <cctc-datatextbox-input tabindex="0">
            <cctc-datatextbox-selected>
                @{
                    var i = _selected.Count + TextInputTabIndex;
                    foreach (var item in _selected)
                    {
                        var selIndex = _selected.IndexOf(item);
                        item.Index = selIndex;

                        <div id="@($"{Id}-selected-item-{selIndex}")"
                             class="selected-item"
                             @ref="@item.ElementReference"
                             tabindex="@(i)"
                             @onkeydown="@(args => SelectedItemKeyDown(args, item))"
                             @onkeydown:preventDefault="true"
                             @onclick="@(args => SelectedItemClicked(args, item))"
                             @onfocus="@(args => SelectedItemGetFocus(args, item))">
                            <span style="margin-left: 0.3rem">
                                <sup>@item.Index</sup>
                            </span>
                            <span>@(DisplayFunc(item.Content))</span>
                            <span style="padding-right: 0.5rem">@item.Separator</span>
                        </div>

                        i--;
                    }
                }
            </cctc-datatextbox-selected>

            <Text
                Id="@($"{Id}-text-input")"
                @ref="_inputElementRef"
                Value="@_filterValue"
                ValueChanged="@OnTextValueChanged"
                BindEvent="BindEvent.OnInput"
                CssClass="flex-fill"
                InputAttributes="InputAttributes"
                Interaction="@TextInteractionCallback">
            </Text>

            <div id="@Id-datatextbox-clear"
                 class="material-icons icon @(GetResetClass())"
                 tabindex="0"
                 @onclick="Reset"
                 @onkeydown="@(args => Reset(args))">
                backspace
            </div>
        </cctc-datatextbox-input>


        <cctc-datatextbox-dropdown id="@Id-datatextbox-dropdown" class="@_dropDownVisibilityClass">
            @if (LastRunQueryIsChildType)
            {
                <Lister
                    TData="TKey"
                    @ref="@_listerChildType"
                    Id="@($"{Id}-dropdown-childtypes-lister")"
                    TabIndex="0"
                    Data="@(_childTypeData ?? new List<TKey>())"
                    FocusItemOption="FocusItemOption.Allow"
                    KeyDownOnItem="@(args => OnItemSelection(args.keyboardEventArgs, null, args.item))"
                    OnClickedItem="@(args => OnItemSelection(null, null, args.item))"
                    SelectionType="ListerSelectionType.None"
                    CanFilter="false"
                    CanClick="@(_ => true)"
                    CountDisplay="ListerCountDisplay.FilterAndTotal"
                    SetExternalFilter="@_filterValue"
                    AfterFocusOnItemsContainer="@SetFocusOnInput"
                    ShowLoadingDisplay="false"
                    FilterComparisonType="FilterComparisonType.Cleaned"
                    LoadingTemplate="@LoadingListTemplate"
                    ItemSizePixels="@ListerItemHeightPixels"
                    ItemsContainerHeightPixels="@_listerItemsContainerHeight">
                    <Template Context="item">
                        @DisplayChildTypesFunc(item)
                    </Template>
                    <EmptyListTemplate>
                        <div>No matching items for child types</div>
                    </EmptyListTemplate>
                </Lister>
            }

            @if (LastRunQueryIsChildren)
            {
                <Lister
                    TData="TData"
                    @ref="@_lister"
                    Id="@($"{Id}-dropdown-children-lister")"
                    TabIndex="0"
                    Data="@(_childData ?? new List<TData>())"
                    FocusItemOption="FocusItemOption.Allow"
                    KeyDownOnItem="@(args => OnItemSelection(args.keyboardEventArgs, args.item))"
                    OnClickedItem="@(args => OnItemSelection(null, args.item))"
                    SelectionType="ListerSelectionType.None"
                    CanFilter="false"
                    CanClick="@(_ => true)"
                    CountDisplay="ListerCountDisplay.FilterAndTotal"
                    SetExternalFilter="@_filterValue"
                    AfterFocusOnItemsContainer="@SetFocusOnInput"
                    ShowLoadingDisplay="false"
                    FilterComparisonType="FilterComparisonType.Cleaned"
                    LoadingTemplate="@LoadingListTemplate"
                    ItemSizePixels="@ListerItemHeightPixels"
                    ItemsContainerHeightPixels="@_listerItemsContainerHeight">
                    <Template Context="item">
                        @DisplayFunc(item)
                    </Template>
                    <EmptyListTemplate>
                        <div>No matching items for children</div>
                    </EmptyListTemplate>
                </Lister>
            }

        </cctc-datatextbox-dropdown>

    </div>

</cctc-datatextbox>

@* ReSharper disable once StaticMemberInGenericType *@

@code {

    /* DataTextBox

        - allows a user to tunnel into a collection by selecting items and presenting a further selection based on the previous one
        - the new version will encapsulate the logic of the component more completely. The original implementation required a lot
            of code on the containing page or component as this needs to be removed. The change will use a IDataTextBoxDataService
            for querying and returning data

        - Type is set by generics
        - an initial selection is required
        - when selecting, needs to allow some form of grouping when needed. This should be an option. For instance, if using NamedNodeIdentifier, it needs to be able to
            present a grouping of child types from which a list of those types can be chosen
            e.g.
                starting with an initial theme

                1 - team, trial (grouping by type)
                select trial
                2 - list of trials (no grouping, just displays objects of type selected)
                select a trial
                3 - patient, site (grouping by type)
                select patient
                4 - list of patients (no grouping, just displays patients)
                etc.

            this requires a current 'selection type status' to be maintained
            - ? does the loading need to be run differently for each type? would be more efficient -> graphQL for this?-> not ready for this yet
                - yes -> more efficient that loading the instances
                - i.e.
                    1. get the types
                    2. get the objects of a type

            - to encapsulate this type of behaviour in datatextbox means making dynamic queries to a data source
            - the datatextbox would need to be able to build these requests based on previous responses
            - it would need to receive an initial function that could determine how the queries should be constructed
                based on the user selection. So in the example above,

                initial func
                Func<TData, List<TData>>

                further funcs would have to take account of the selection from
                1. previous selection
                2. any filter value given in the text box
                    (different filter styles? e.g. contains, starts with, regex?)

            - this therefore needs a data service to provide this

        - user actions;
            - if the focus is on the lister, scrolling can

     */

    Dictionary<string, object> InputAttributes => new()
    {
        { "spellcheck", "false" },
        { "tabindex", $"{TextInputTabIndex}" },
    };

    const string ResetAvailableCss = "icon-available";
    const string ResetNotAvailableCss = "icon-not-available";
    const string DropdownHiddenClass = "dropdown-hidden";
    const string DropdownVisibleClass = "dropdown-visible";
    const string Sep = " ➔ ";
    const string EndSelection = " ↩";
    const int TextInputTabIndex = 1000;

    ElementReference _cctcDatatextbox;
    Text? _inputElementRef;
    Lister<TData>? _lister;
    Lister<TKey>? _listerChildType;
    List<DataTextBoxSelected<TData>> _selected = new();
    List<(TData sourceData, TypeOfQuery queryType, TKey? childParams)> _queryHistory = new();
    int _listerItemsContainerHeight;
    BoundingRect? _dropDownBoundingRect;

    RenderFragment LoadingListTemplate => @<Flow Size="25px" Color="Orange"/>;

    //flags the last run query type
    TypeOfQuery _lastRunQuery;

    #region parameters

    /// <summary>
    /// The initial item to load
    /// </summary>
    [Parameter, EditorRequired]
    public required TData InitialValue { get; set; }

    /// <summary>
    /// The implementation of the <see cref="IDataTextBoxDataService{T,TKey}"/> that provides the data as
    /// the user navigates into the data collection
    /// </summary>
    [Parameter, EditorRequired]
    public required IDataTextBoxDataService<TData, TKey> DataTextBoxDataService { get; set; }

    /// <summary>
    /// When true (default), a grouping step will be included. This will perform a look up of the distinct
    /// child types based on the definition of TKey
    /// </summary>
    [Parameter]
    public bool IncludeGroupingStep { get; set; } = true;

    /// <summary>
    /// A callback invoked with the final selected item
    /// </summary>
    [Parameter, EditorRequired]
    public EventCallback<TData?> OnSelectionComplete { get; set; }

    /// <summary>
    /// A callback invoked with full list of selected items
    /// </summary>
    [Parameter]
    public EventCallback<List<TData>?> SelectedPath { get; set; }

    /// <summary>
    /// A history of the queries ran during this interaction
    /// </summary>
    [Parameter]
    public EventCallback<List<(TData sourceData, TypeOfQuery queryType, TKey? childParams)>?> QueryHistory { get; set; }

    /// <summary>
    /// A function that provides the display format for items of TData
    /// </summary>
    [Parameter, EditorRequired]
    public required Func<TData, string> DisplayFunc { get; set; }

    /// <summary>
    /// A function that provides the display format for items of TKey
    /// </summary>
    [Parameter, EditorRequired]
    public required Func<TKey, string> DisplayChildTypesFunc { get; set; }

    /// <summary>
    /// The throttle used for observables. Defaults to 200ms
    /// </summary>
    [Parameter]
    public int ComponentDefaultThrottleMs { get; set; } = 200;

    #region Lister config

    /// <summary>
    /// The height in pixels of the items used in the contained Lister component
    /// </summary>
    /// <remarks>This is requirement of virtualization used within Lister</remarks>
    [Parameter, EditorRequired]
    public required float ListerItemHeightPixels { get; set; }

    #endregion Lister config

    #endregion parameters

    #region loading

    //assumes the lister loading style uses a preloaded collection
    //this could be embellished to use a loading func perhaps?

    List<TKey>? _childTypeData = null;

    TData LastSelectedContent => _selected.Last().Content;

    async Task LoadListerWithChildTypes()
    {
        _childTypeData = await DataTextBoxDataService.GetDistinctChildTypes(LastSelectedContent);

        _lastRunQuery = TypeOfQuery.ChildType;
    }

    List<TData>? _childData = null;

    async Task LoadListerWithChildren(TKey lastChildType)
    {
        _childData =
            await DataTextBoxDataService
                .GetChildren(LastSelectedContent, lastChildType);

        _lastRunQuery = TypeOfQuery.Children;
    }

    async Task LoadListerWithChildren()
    {
        _childData =
            await DataTextBoxDataService
                .GetChildren(LastSelectedContent);

        _lastRunQuery = TypeOfQuery.Children;
    }

    #endregion loading

    #region querying

    //flags whether the lister should have its data refreshed. this is required only when the
    //type of query is the same but the data is changed
    bool _forceRefreshLister;

    //usually the query type is not given as it will be determined by the last one run
    //however on initialisation and when a user reruns a previous query this
    //value is required
    //takes into account what the last run query is, and whether a grouping step is included
    async Task RunQuery(TypeOfQuery? queryTypeToRun, TKey? childType = null, bool logHistory = true)
    {
        switch (queryTypeToRun, childType, _lastRunQuery, IncludeGroupingStep)
        {
            case (TypeOfQuery.ChildType, null, _, _):
                await LoadListerWithChildTypes();
                _forceRefreshLister = true;
                break;
            case (TypeOfQuery.Children, null, _, _):
                await LoadListerWithChildren();
                _forceRefreshLister = true;
                break;
            case (TypeOfQuery.Children, not null, _, _):
                await LoadListerWithChildren(childType);
                _forceRefreshLister = true;
                break;
            case (null, null, _, false):
                await LoadListerWithChildren();
                _forceRefreshLister = true;
                break;
            case (null, null, TypeOfQuery.Children, true):
                await LoadListerWithChildTypes();
                break;
            case (null, not null, TypeOfQuery.ChildType, true):
                await LoadListerWithChildren(childType);
                break;
            case (null, null, TypeOfQuery.ChildType, true):
                await LoadListerWithChildren();
                break;
            default:
                var args = $"queryTypeToRun: {queryTypeToRun}, childType: {childType}, lastRunQuery: {_lastRunQuery}, IncludeGroupStep: {IncludeGroupingStep}";
                throw new InvalidOperationException($"unexpected parameters in RunQuery. This can be caused by not refreshing the lister when " +
                                                    $"changing the grouping type. Unhandled args were \n {args}");
        }

        //the last selected item should always be the source of the query
        if (logHistory)
        {
            _queryHistory.Add((_selected.Last().Content, _lastRunQuery, childType));
        }

        StateHasChanged();
        await InvokeSelectedPath();
        await InvokeQueryHistory();
    }

    async Task InvokeSelectedPath()
    {
        await SelectedPath.InvokeAsync(_selected.Select(x => x.Content).ToList());
    }

    async Task InvokeQueryHistory()
    {
        await QueryHistory.InvokeAsync(_queryHistory);
    }

    async Task RunAppropriateQuery()
    {
        if (IncludeGroupingStep)
        {
            await RunQuery(TypeOfQuery.ChildType);
        }
        else
        {
            await RunQuery(TypeOfQuery.Children);
        }
    }

    #endregion querying

    #region ui

    string GetResetClass()
    {
        return
            _selected.Count > 1 || !string.IsNullOrEmpty(_filterValue)
                ? ResetAvailableCss
                : ResetNotAvailableCss;
    }

    string _dropDownVisibilityClass = DropdownHiddenClass;

    async Task ShowDropDown()
    {
        _dropDownVisibilityClass = DropdownVisibleClass;
        await InvokeAsync(StateHasChanged);
    }

    async Task HideDropDown()
    {
        _dropDownVisibilityClass = DropdownHiddenClass;
        await InvokeAsync(StateHasChanged);
    }

    IDisposable? _obs;

    event EventHandler<FocusEventArgs>? FocusEvent;

    //raises the focusevent when either focusin or focusout are raised on the component
    void RaiseFocusEvent(FocusEventArgs args)
    {
        FocusEvent?.Invoke(this, args);
    }

    void SetFocusObservable()
    {
        /* this observable does the following to prevent the dropdown hiding then showing with each selection;
            1. receives the FocusEvent when the component receives or loses focus via focusin or focusout
            2. throttles the requests and uses switch - stops focus out event firing when immediately followed by a focusin
            3. uses Observable.FromAsync to correctly handle an async call
         */

        _obs =
            Observable
                .FromEventPattern<EventHandler<FocusEventArgs>, FocusEventArgs>(
                    h => FocusEvent += h,
                    h => FocusEvent -= h)
                .Throttle(TimeSpan.FromMilliseconds(ComponentDefaultThrottleMs), SchedulerProvider.Default)
                .Select(args => Observable.Return(args.EventArgs.Type))
                .Switch()
                .Select(eventType => Observable.FromAsync(() => ManageDropdown(eventType!)))
                .Concat()
                .Subscribe();
    }

    async Task ManageDropdown(string eventType)
    {
        switch (eventType)
        {
            case "focusin":
                if (!IsDropDownVisible && CurrentHasChildren)
                {
                    await ShowDropDown();
                    _selected.Last().Separator = Sep;
                    _lastCursorPosition = 0;
                    _inputElementRef?.ElementRef.FocusAsync();
                }
                break;
            case "focusout":
                if (IsDropDownVisible)
                {
                    await HideDropDown();
                }
                break;
            default:
                throw new InvalidOperationException($"arg type not expected: {eventType}");
        }
    }

    #endregion ui

    #region interactions

    #region selectedItems

    void AddToSelection(DataTextBoxSelected<TData> item)
    {
        _selected.Add(item);
        _shouldScrollSelected = true;
        StateHasChanged();
    }

    //drops selected items from the collection
    void DropFromSelection(int? toIndex = null)
    {
        if (_selected.Count > 1)
        {
            if (toIndex is null)
            {
                //just drop the last
                _selected.Remove(_selected[^1]);
            }
            else
            {
                //drop the range
                _selected.RemoveRange(toIndex.Value + 1, _selected.Count - 1 - toIndex.Value);
            }

            StateHasChanged();
        }
    }

    async Task SelectedItemGetFocus(FocusEventArgs args, DataTextBoxSelected<TData> sel)
    {
        var scrollArgs = new { behavior = "instant", block = "nearest", inline = "nearest", alignToTop = false };
        await ScrollIntoViewFromSelector($"#{Id}-selected-item-{_selected.IndexOf(sel)}", scrollArgs);
    }

    //one of the previous selections got a key down
    async Task SelectedItemKeyDown(KeyboardEventArgs args, DataTextBoxSelected<TData> sel)
    {
        var shiftOn = args.ShiftKey;

        switch (args.Key, shiftOn)
        {
            case ("ArrowLeft", _) or ("Tab", false):
            {
                //move to the next item
                var next = _selected.SingleOrDefault(x => x.Index == sel.Index - 1);
                next?.ElementReference.FocusAsync();
                break;
            }
            case ("ArrowRight", _) or ("Tab", true) when sel.Index == _selected.Count - 1:
                //select the text input item
                await _inputElementRef!.ElementRef.FocusAsync();
                break;
            case ("ArrowRight", _) or ("Tab", true):
            {
                var next = _selected.SingleOrDefault(x => x.Index == sel.Index + 1);
                next?.ElementReference.FocusAsync();
                break;
            }
            case ("ArrowDown" or "Enter", _):
                //if the selected item is not the current (i.e. last) then revert to selected
                if (_selected.Last() != sel)
                {
                    await SelectToItem(sel);
                }
                break;
            case ("Backspace" or "Delete", _):
                //if the selected item is not the first one, then delete everything from this point
                //forward including the current
                if (_selected.First() != sel)
                {
                    var selIndex = _selected.IndexOf(sel);
                    await SelectToItem(_selected[selIndex - 1]);
                }
                break;
        }
    }

    async Task SelectedItemClicked(MouseEventArgs args, DataTextBoxSelected<TData> sel)
    {
        await SelectToItem(sel);
    }

    //fires when a previous item is selected
    async Task SelectToItem(DataTextBoxSelected<TData> sel)
    {
        DropFromSelection(_selected.IndexOf(sel));
        await RunAppropriateQuery();

        _lastCursorPosition = 0;
        _filterValue = "";
        _setFocusOnInput = true;
        StateHasChanged();
    }

    #endregion selectedItems

    // maintains the previous cursor position to prevent input losing focus immediately
    // when the cursor is at the start of input
    int _lastCursorPosition;

    /// <summary>
    /// The call back invoked when the text input receives user interaction
    /// </summary>
    /// <param name="interactionArgs">The <see cref="InteractionArgs"/> resulting from user input</param>
    async Task TextInteractionCallback(InteractionArgs interactionArgs)
    {
        if (_obs is null)
        {
            SetFocusObservable();
            FocusEvent?.Invoke(this, new FocusEventArgs { Type = "focusin" });
        }

        if (interactionArgs is KeyboardInteractionArgs args)
        {
            switch (args.KeyboardEventArgs.Key, args.CursorStart, _lastCursorPosition,
                args.KeyboardEventArgs.CtrlKey, args.KeyboardEventArgs.AltKey)
            {

                case ("ArrowLeft", 0, 0, _, _):
                    //move focus to first selector
                    await _selected.Last().ElementReference.FocusAsync();
                    break;
                case ("Backspace", 0, 0, _, _):
                    //move focus back to previous selector
                    if (_selected.Count > 1)
                    {
                        await SelectToItem(_selected[^2]);
                    }
                    break;
                case ("ArrowDown", _, _, _, _):
                    //focus on the current item
                    SetFocusOnListerItem(0, true);
                    break;
                case ("Enter", _, _, true, _) or("Enter", _, _, _, true):
                    //make the current selection the completed
                    await OnSelectionComplete.InvokeAsync(_selected.Last().Content);
                    _filterValue = null;
                    _selected.Last().Separator = EndSelection;
                    await HideDropDown();
                    await _inputElementRef!.ElementRef.FocusAsync();
                    break;

                case ("0" or "1" or "2" or "3" or "4" or "5" or "6" or "7" or "8" or "9", _, _, _,true):
                    //move focus back to item by index
                    var selected = int.Parse(args.KeyboardEventArgs.Key);
                    if (selected < _selected.Count)
                    {
                        await SelectToItem(_selected[selected]);
                    }
                    break;
            }

            _lastCursorPosition = args.CursorStart;
        }
    }

    bool LastRunQueryIsChildType => _lastRunQuery == TypeOfQuery.ChildType;
    bool LastRunQueryIsChildren => _lastRunQuery == TypeOfQuery.Children;
    bool CurrentHasChildren =>
        LastRunQueryIsChildren
            ? _childData!.Any()
            : _childTypeData!.Any();

    void SetFocusOnListerItem(int index, bool setFocusToPermitKeystrokes)
    {
        if (LastRunQueryIsChildren && _childData is not null && _childData.Any())
        {
            _lister!.SetCurrentFocusIndex(index, setFocusToPermitKeystrokes);
            return;
        }

        if (LastRunQueryIsChildType && _childTypeData is not null && _childTypeData.Any())
        {
            _listerChildType!.SetCurrentFocusIndex(index, setFocusToPermitKeystrokes);
            return;
        }
    }

    //maintains the previous lister index so that if the user tries to move up again after
    //focus on index 0, the focus moves to the input and off the lister
    static int? _lastListerIndex = -1;

    //a flag that determines whether the action on lister should be invoked after stealing the
    //focus after rendering
    bool _setFocusOnInput;

    void SetFocusOnInput()
    {
        if (_setFocusOnInput)
        {
            _setFocusOnInput = false;
            _inputElementRef?.ElementRef.FocusAsync();
        }
    }

    //an item in the lister was selected using the keyboard or mouse
    async Task OnItemSelection(KeyboardEventArgs? keyboardEventArgs, TData? item = null, TKey? childType = null)
    {
        var usedMouse = keyboardEventArgs is null;
        var keyUsed =
            usedMouse
                ? null
                : keyboardEventArgs!.Key;

        var currIndex =
            usedMouse
                ? null
                : LastRunQueryIsChildType
                    ? (int?)_listerChildType!.GetCurrentFocusIndex
                    : _lister!.GetCurrentFocusIndex;

        switch (usedMouse, keyUsed, currIndex, _lastListerIndex)
        {
            case (true, _, _, _) or (false, "Tab" or "Enter", _, _):
                //item was selected with mouse or by tab or enter key

                if (item is not null)
                {
                    AddToSelection(DataTextBoxSelected<TData>.Create(item, Sep));
                }

                //this should really run first so the separator doesn't need updating, though
                //that causes issues with updating the lister so left it like this for now
                await RunQuery(null, childType);

                //update sep if needed
                if (!CurrentHasChildren)
                {
                    _selected.Last().Separator = EndSelection;
                    await HideDropDown();
                    await _inputElementRef!.ElementRef.FocusAsync();
                }

                _setFocusOnInput = true;
                _lastListerIndex = -1;
                break;
            case (false, "ArrowUp", 0, 0):
                //arrow up from top of list should select text input

                SetFocusOnListerItem(-1, false);
                _setFocusOnInput = false;
                _inputElementRef?.ElementRef.FocusAsync();
                _lastListerIndex = currIndex;
                break;
            case (false, _, _, _):
                //handle state only

                _setFocusOnInput = false;
                _lastListerIndex = currIndex;
                break;
        }
    }

    #endregion interactions

    #region filtering

    string? _filterValue;

    void OnTextValueChanged(string? newValue)
    {
        _filterValue = newValue;

        _setFocusOnInput = true;
        StateHasChanged();
    }

    #endregion filtering

    bool IsAllowedKey(string key) => key is "Enter" or "Space";

    async Task Reset()
    {
        //reset to initial status
        await SelectToItem(_selected.First());

        _filterValue = "";
        _lastCursorPosition = 0;

        StateHasChanged();
    }

    async Task Reset(KeyboardEventArgs args)
    {
        if (IsAllowedKey(args.Key))
        {
            await Reset();
        }
    }


    //required to force a refresh of the lister data
    async Task ForceRefreshListerRequested()
    {
        if (_forceRefreshLister)
        {
            _forceRefreshLister = false;

            if (_lister is not null && _lastRunQuery == TypeOfQuery.Children)
            {
                await _lister.Load(true);
            }

            if (_listerChildType is not null && _lastRunQuery == TypeOfQuery.ChildType)
            {
                await _listerChildType.Load(true);
            }
        }
    }

    //when true, the selected list will scroll to the last item
    bool _shouldScrollSelected;

    //mechanism to scroll to the selected item
    async Task ScrollToSelectedRequested()
    {
        if (_shouldScrollSelected)
        {
            _shouldScrollSelected = false;
            await SelectedItemGetFocus(new FocusEventArgs(), _selected[^1]);
        }
    }

    bool IsDropDownVisible => _dropDownVisibilityClass == DropdownVisibleClass;

    /// <inheritdoc />
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            if (_dropDownBoundingRect is null)
            {
                _dropDownBoundingRect = await GetElementBoundRectFromQuery("cctc-datatextbox-dropdown");
                _listerItemsContainerHeight = _dropDownBoundingRect!.Height - RemToPixels(3.5);
            }

            await _cctcDatatextbox.FocusAsync();
        }

        await ForceRefreshListerRequested();
        await ScrollToSelectedRequested();
    }

    /// <summary>
    /// Initialise the datatextbox
    /// </summary>
    public async Task Initialise(bool includeGroupingStep)
    {
        //Console.WriteLine($"init: {includeGroupingStep}");

        _selected.Clear();
        _filterValue = "";
        _lastCursorPosition = 0;

        _selected.Add(
            new DataTextBoxSelected<TData>
            {
                Content = InitialValue,
                Separator = Sep
            });

        _lastRunQuery = includeGroupingStep ? TypeOfQuery.ChildType : TypeOfQuery.Children;

        await RunQuery(includeGroupingStep ? TypeOfQuery.ChildType : TypeOfQuery.Children, logHistory: false);
    }

    /// <inheritdoc />
    protected override async Task OnInitializedAsync()
    {
        await Initialise(IncludeGroupingStep);
    }

    /// <inheritdoc/>
    public void Dispose()
    {
        _obs?.Dispose();
    }
}