﻿@page "/infotextsample"

@{
    var description = new List<string>
    {
        "A component for displaying and copying info text"
    };

    var features = new List<(string, string)>
    {
        ("Info text", "Can be set to Wrap (default), NoWrap, Scroll or None"),
        ("Tooltip", "Tooltip behaviour can be set to EnabledOnTextOverflow (default), Enabled or Disabled. Tooltip placement is configurable"),
        ("Copy icon", "Can be set to permanently show or show on hover (default). Confirmatory copy popover text and placement is configurable")
    };

    var tips = new List<(string, string)>
    {
        ("Text wrapping", "When the text is set to wrap, the --cctc-infotext-webkit-line-clamp css variable sets the maximum number of lines before truncation is applied")
    };

    var usageCode = new List<(string title, string description, string code, RenderFragment fragment)>
    {
        ("InfoTextOverflow: Wrap, InfoTextTooltipPlacement: Bottom, InfoTextPopoverPlacement: Bottom, InfoTextTooltipBehaviour: EnabledOnTextOverflow", "",
@"<InfoText
    Id=""usage1""
    Content=""@LongContent""
    InfoTextOverflow=""InfoTextOverflow.Wrap""
    InfoTextTooltipPlacement=""TooltipPlacement.Bottom""
    InfoTextPopoverPlacement=""InfoTextPopoverPlacement.Bottom""
    InfoTextTooltipBehaviour=""InfoTextTooltipBehaviour.EnabledOnTextOverflow"">
</InfoText>
        
@code {

    string? LongContent { get; set; } = ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat."";
",
        @<InfoText
            Id="usage1"
            Content="@LongContent"
            InfoTextOverflow="InfoTextOverflow.Wrap"
            InfoTextTooltipPlacement="TooltipPlacement.Bottom"
            InfoTextPopoverPlacement="InfoTextPopoverPlacement.Bottom"
            InfoTextTooltipBehaviour="InfoTextTooltipBehaviour.EnabledOnTextOverflow">
        </InfoText>),
        ("InfoTextOverflow: NoWrap, InfoTextTooltipPlacement: Bottom, InfoTextPopoverPlacement: Right, InfoTextTooltipBehaviour: EnabledOnTextOverflow", "",
@"<InfoText
    Id=""usage2""
    Content=""@LongContent""
    InfoTextOverflow=""InfoTextOverflow.NoWrap""
    InfoTextTooltipPlacement=""TooltipPlacement.Bottom""
    InfoTextPopoverPlacement=""InfoTextPopoverPlacement.Right""
    InfoTextTooltipBehaviour=""InfoTextTooltipBehaviour.EnabledOnTextOverflow"">
</InfoText>

@code {

    string? LongContent { get; set; } = ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat."";
}",
        @<InfoText
            Id="usage2"
            Content="@LongContent"
            InfoTextOverflow="InfoTextOverflow.NoWrap"
            InfoTextTooltipPlacement="TooltipPlacement.Bottom"
            InfoTextPopoverPlacement="InfoTextPopoverPlacement.Right"
            InfoTextTooltipBehaviour="InfoTextTooltipBehaviour.EnabledOnTextOverflow">
        </InfoText>),
        ("InfoTextOverflow: Scroll, InfoTextPopoverPlacement: Right, InfoTextTooltipBehaviour: Disabled", "",
@"<InfoText
    Id=""usage3""
    Content=""@LongContent""
    InfoTextOverflow=""InfoTextOverflow.Scroll""
    InfoTextPopoverPlacement=""InfoTextPopoverPlacement.Right""
    InfoTextTooltipBehaviour=""InfoTextTooltipBehaviour.Disabled"">
</InfoText>

@code {

    string? LongContent { get; set; } = ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat."";
}",
        @<InfoText
            Id="usage3"
            Content="@LongContent"
            InfoTextOverflow="InfoTextOverflow.Scroll"
            InfoTextPopoverPlacement="InfoTextPopoverPlacement.Right"
            InfoTextTooltipBehaviour="InfoTextTooltipBehaviour.Disabled">
        </InfoText>),
        ("InfoTextOverflow: None, InfoTextPopoverPlacement: Top, InfoTextTooltipBehaviour: Disabled, TextCopyMessage: Copied, OnlyShowCopyIconOnHover: false", "",
@"<InfoText
    Id=""usage4""
    Content=""@LongContent""
    InfoTextOverflow=""InfoTextOverflow.None""
    InfoTextPopoverPlacement=""InfoTextPopoverPlacement.Top""
    InfoTextTooltipBehaviour=""InfoTextTooltipBehaviour.Disabled""
    TextCopyMessage=""Copied""
    OnlyShowCopyIconOnHover=""false"">
</InfoText>

@code {

    string? LongContent { get; set; } = ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat."";
}",
        @<InfoText
            Id="usage4"
            Content="@LongContent"
            InfoTextOverflow="InfoTextOverflow.None"
            InfoTextPopoverPlacement="InfoTextPopoverPlacement.Top"
            InfoTextTooltipBehaviour="InfoTextTooltipBehaviour.Disabled"
            TextCopyMessage="Copied"
            OnlyShowCopyIconOnHover="false">
        </InfoText>),
        ("InfoTextOverflow: NoWrap, InfoTextTooltipPlacement: Top, InfoTextPopoverPlacement: Top, InfoTextTooltipBehaviour: Enabled", "",
@"<div class=""d-flex gap-2"">
    <span>Before InfoText</span>
    <InfoText Id=""usage5""
        Content=""@LongContent""
        InfoTextOverflow=""InfoTextOverflow.NoWrap""
        InfoTextTooltipPlacement=""TooltipPlacement.Top""
        InfoTextPopoverPlacement=""InfoTextPopoverPlacement.Top""
        InfoTextTooltipBehaviour=""InfoTextTooltipBehaviour.Enabled"">
    </InfoText>
    <span>After InfoText</span>
</div>

@code {

    string? LongContent { get; set; } = ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat."";
}",
        @<div class="d-flex gap-2">
            <span>Before InfoText</span>
            <InfoText Id="usage5"
                Content="@LongContent"
                InfoTextOverflow="InfoTextOverflow.NoWrap"
                InfoTextTooltipPlacement="TooltipPlacement.Top"
                InfoTextPopoverPlacement="InfoTextPopoverPlacement.Top"
                InfoTextTooltipBehaviour="InfoTextTooltipBehaviour.Enabled">
            </InfoText>
            <span>After InfoText</span>
        </div>)
    };
}

@* The wrapper div is necessary here for ::deep isolated css rules to be applied to the InfoText component *@
<div>
    <Sampler
        ComponentName="InfoText"
        ComponentCssName="infotext"
        Description="@description"
        Features="@features"
        UsageText="Typical usages of the <code>InfoText</code> component are shown below"
        UsageCodeList="@usageCode"
        Tips="@tips"
        ContentHeightPixels="375">
        <ExampleTemplate>
            <InfoText
                Id="example1"
                Style="width: 5rem;"
                Content="@Guid.NewGuid().ToString()"
                InfoTextOverflow="InfoTextOverflow.NoWrap"
                InfoTextTooltipPlacement="TooltipPlacement.Bottom"
                InfoTextPopoverPlacement="InfoTextPopoverPlacement.Bottom"
                InfoTextTooltipBehaviour="InfoTextTooltipBehaviour.EnabledOnTextOverflow">
            </InfoText>
        </ExampleTemplate>
    </Sampler>
</div>

@code {

    string? LongContent { get; set; } = "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.";
}