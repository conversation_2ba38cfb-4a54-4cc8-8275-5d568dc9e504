﻿using CCTC_Components.Components.DropDown;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;

namespace CCTC_Components.bUnit.test
{
    public class DropDownTests : CCTCComponentsTestContext
    {
        [Fact]
        public void MouseInteractionInvokesEventCallbacksCorrectly()
        {
            AddAddTooltip();
            AddUpdateTooltipTitle();

            var mockDummyService = new Mock<IDummyService>();
            string? valueChangedNewValue = null;
            ChangeEventArgs? selectionChangedNewValue = null;
            string? expectedValue = "SecondOption";

            var cut = RenderComponent<DropDown<string>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Data, new List<string> { "FirstOption", "SecondOption", "ThirdOption" })
                .Add(p => p.ValueChanged, async args => { valueChangedNewValue = args; await mockDummyService.Object.MethodOneAsync(); })
                .Add(p => p.SelectionChanged, async args => { selectionChangedNewValue = args; await mockDummyService.Object.MethodTwoAsync(); })
                .Add(p => p.Value, "ThirdOption")
                .Add(p => p.ShowClear, true)
            );

            var dropdownOptions= cut.FindAll("cctc-input-dropdown-option");
            dropdownOptions[1].MouseDown();
            Assert.Equal(expectedValue, valueChangedNewValue);
            Assert.Equal(expectedValue, selectionChangedNewValue?.Value?.ToString());

            var clearIcon = cut.Find(".icon-wrapper .material-icons.backspace");
            clearIcon.Click();
            expectedValue = null;
            Assert.Equal(expectedValue, valueChangedNewValue);
            Assert.Equal(expectedValue, selectionChangedNewValue?.Value?.ToString());

            mockDummyService.Verify(m => m.MethodOneAsync(), Times.Exactly(2));
            mockDummyService.Verify(m => m.MethodTwoAsync(), Times.Exactly(2));
        }

        [Fact]
        public void KeyboardInteractionInvokesEventCallbacksCorrectly()
        {
            AddAddTooltip();
            AddUpdateTooltipTitle();
            AddScrollIntoViewFromQuery();

            var mockDummyService = new Mock<IDummyService>();
            string? valueChangedNewValue = null;
            ChangeEventArgs? selectionChangedNewValue = null;
            string? expectedValue = "SecondOption";

            var cut = RenderComponent<DropDown<string>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Data, new List<string> { "FirstOption", "SecondOption", "ThirdOption" })
                .Add(p => p.ValueChanged, async args => { valueChangedNewValue = args; await mockDummyService.Object.MethodOneAsync(); })
                .Add(p => p.SelectionChanged, async args => { selectionChangedNewValue = args; await mockDummyService.Object.MethodTwoAsync(); })
                .Add(p => p.Value, "ThirdOption")
                .Add(p => p.ShowClear, true)
            );

            var inputSelected = cut.Find("cctc-input-dropdown-selected");
            inputSelected.Click();
            inputSelected.KeyDown(new KeyboardEventArgs { Key = "ArrowUp" });

            Assert.Equal(expectedValue, valueChangedNewValue);
            Assert.Equal(expectedValue, selectionChangedNewValue?.Value?.ToString());

            inputSelected.KeyDown(new KeyboardEventArgs { Key = "ArrowDown" });
            expectedValue = "ThirdOption";

            Assert.Equal(expectedValue, valueChangedNewValue);
            Assert.Equal(expectedValue, selectionChangedNewValue?.Value?.ToString());

            //already on last option so no change expected
            inputSelected.KeyDown(new KeyboardEventArgs { Key = "ArrowDown" });

            Assert.Equal(expectedValue, valueChangedNewValue);
            Assert.Equal(expectedValue, selectionChangedNewValue?.Value?.ToString());

            //move back to first option
            inputSelected.KeyDown(new KeyboardEventArgs { Key = "ArrowUp" });
            inputSelected.KeyDown(new KeyboardEventArgs { Key = "ArrowUp" });
            expectedValue = "FirstOption";

            Assert.Equal(expectedValue, valueChangedNewValue);
            Assert.Equal(expectedValue, selectionChangedNewValue?.Value?.ToString());

            //already on first option so no change expected
            inputSelected.KeyDown(new KeyboardEventArgs { Key = "ArrowUp" });

            Assert.Equal(expectedValue, valueChangedNewValue);
            Assert.Equal(expectedValue, selectionChangedNewValue?.Value?.ToString());
        }

        [Fact]
        public void SelectedOptionFixedWhenDropDownClosed()
        {
            AddAddTooltip();
            AddUpdateTooltipTitle();

            var mockDummyService = new Mock<IDummyService>();
            string? valueChangedNewValue = null;
            ChangeEventArgs? selectionChangedNewValue = null;
            string? expectedValue = null;

            var cut = RenderComponent<DropDown<string>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Data, new List<string> { "FirstOption", "SecondOption", "ThirdOption" })
                .Add(p => p.ValueChanged, async args => { valueChangedNewValue = args; await mockDummyService.Object.MethodOneAsync(); })
                .Add(p => p.SelectionChanged, async args => { selectionChangedNewValue = args; await mockDummyService.Object.MethodTwoAsync(); })
                .Add(p => p.Value, "SecondOption")
                .Add(p => p.ShowClear, true)
            );

            var inputSelected = cut.Find("cctc-input-dropdown-selected");
            inputSelected.KeyDown(new KeyboardEventArgs { Key = "ArrowUp" });

            Assert.Equal(expectedValue, valueChangedNewValue);
            Assert.Equal(expectedValue, selectionChangedNewValue?.Value?.ToString());

            inputSelected.KeyDown(new KeyboardEventArgs { Key = "ArrowDown" });

            Assert.Equal(expectedValue, valueChangedNewValue);
            Assert.Equal(expectedValue, selectionChangedNewValue?.Value?.ToString());
        }

        [Fact]
        public void SelectedTextCopiedViaKeyboard()
        {
            TestHelpers.ClearClipboardText();
            string expectedText = "some text";
            AddAddTooltip();
            AddUpdateTooltipTitle();
            AddScrollIntoViewFromQuery();
            AddGetSelectedText(expectedText);

            var cut = RenderComponent<DropDown<string>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Data, new List<string> { "FirstOption", "SecondOption", "ThirdOption" })
                .Add(p => p.Value, "ThirdOption")
            );

            var inputSelected = cut.Find("cctc-input-dropdown-selected");
            inputSelected.KeyDown(new KeyboardEventArgs { CtrlKey = true, Key = "c" });

            JSInterop.VerifyInvoke("getSelectedText");
            Assert.Equal(expectedText, TestHelpers.GetClipboardText());
        }

        [Fact]
        public void SelectedOptionScrollsIntoView()
        {
            AddAddTooltip();
            AddUpdateTooltipTitle();
            AddScrollIntoViewFromQuery();

            var cut = RenderComponent<DropDown<string>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Data, new List<string> { "FirstOption", "SecondOption", "ThirdOption" })
                .Add(p => p.Value, "ThirdOption")
            );

            var inputSelected = cut.Find("cctc-input-dropdown-selected");
            inputSelected.Click();
            inputSelected.KeyDown(new KeyboardEventArgs { Key = "ArrowUp" });

            var invocations = JSInterop.Invocations["scrollIntoViewFromQuery"];
            //first scrollIntoViewFromQuery invocation - opening dropdown causes third option to scroll into view
            Assert.Equal("#test-id-2", invocations[0].Arguments[0]);
            //second scrollIntoViewFromQuery invocation - up arrow changes selection to second option and scrolls into view
            Assert.Equal("#test-id-1", invocations[1].Arguments[0]);
        }

        [Fact]
        public void MouseInteractionExpandsAndClosesDropDown()
        {
            AddAddTooltip();
            AddUpdateTooltipTitle();
            AddScrollIntoViewFromQuery();

            var cut = RenderComponent<DropDown<string>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Data, new List<string> { "FirstOption", "SecondOption", "ThirdOption" })
                .Add(p => p.Value, "ThirdOption")
            );

            var inputSelected = cut.Find("cctc-input-dropdown-selected");
            var inputDropdown = cut.Find("cctc-input-dropdown");
            Assert.False(inputSelected.HasAttribute("aria-expanded"));
            Assert.Equal("dropdown-hidden", inputDropdown.ClassName);

            inputSelected.Click();
            Assert.True(inputSelected.HasAttribute("aria-expanded"));
            Assert.Equal("dropdown-visible", inputDropdown.ClassName);

            inputSelected.Click();
            Assert.Equal("dropdown-hidden", inputDropdown.ClassName);

            inputSelected.Click();
            Assert.Equal("dropdown-visible", inputDropdown.ClassName);

            inputSelected.FocusOut();
            Assert.Equal("dropdown-hidden", inputDropdown.ClassName);
        }

        [Fact]
        public void EnterAndEscapeCloseDropDown()
        {
            AddAddTooltip();
            AddUpdateTooltipTitle();
            AddScrollIntoViewFromQuery();

            var cut = RenderComponent<DropDown<string>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Data, new List<string> { "FirstOption", "SecondOption", "ThirdOption" })
                .Add(p => p.Value, "ThirdOption")
            );

            var inputSelected = cut.Find("cctc-input-dropdown-selected");
            var inputDropdown = cut.Find("cctc-input-dropdown");

            inputSelected.Click();
            Assert.Equal("dropdown-visible", inputDropdown.ClassName);

            inputSelected.KeyDown(new KeyboardEventArgs { Key = "Enter" });
            Assert.Equal("dropdown-hidden", inputDropdown.ClassName);

            inputSelected.Click();
            Assert.Equal("dropdown-visible", inputDropdown.ClassName);

            inputSelected.KeyDown(new KeyboardEventArgs { Key = "Escape" });
            Assert.Equal("dropdown-hidden", inputDropdown.ClassName);
        }

        [Fact]
        public void OptionSelectedChangesCorrectly()
        {
            AddAddTooltip();
            AddUpdateTooltipTitle();

            var cut = RenderComponent<DropDown<string>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Data, new List<string> { "FirstOption", "SecondOption", "ThirdOption" })
                .Add(p => p.Value, "ThirdOption")
                .Add(p => p.ShowClear, true)
            );

            string selectedAttributeName = "data-option-selected";
            var dropdownOptions = cut.FindAll("cctc-input-dropdown-option");

            Assert.False(dropdownOptions[0].HasAttribute(selectedAttributeName));
            Assert.False(dropdownOptions[1].HasAttribute(selectedAttributeName));
            Assert.True(dropdownOptions[2].HasAttribute(selectedAttributeName));

            dropdownOptions[1].MouseDown();
            dropdownOptions = cut.FindAll("cctc-input-dropdown-option");

            Assert.False(dropdownOptions[0].HasAttribute(selectedAttributeName));
            Assert.True(dropdownOptions[1].HasAttribute(selectedAttributeName));
            Assert.False(dropdownOptions[2].HasAttribute(selectedAttributeName));

            dropdownOptions[0].MouseDown();
            dropdownOptions = cut.FindAll("cctc-input-dropdown-option");

            Assert.True(dropdownOptions[0].HasAttribute(selectedAttributeName));
            Assert.False(dropdownOptions[1].HasAttribute(selectedAttributeName));
            Assert.False(dropdownOptions[2].HasAttribute(selectedAttributeName));
        }

        [Fact]
        public void DropDownOptionsConfiguredCorrectly()
        {
            AddAddTooltip();

            var cut = RenderComponent<DropDown<(string value, string display)>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Data, new List<(string value, string display)> { ("1", "One"), ("2", "Two"), ("3", "Three") })
                .Add(p => p.Value, ("1", "One"))
                .Add(p => p.ValueSelector, item => item.value)
                .Add(p => p.DisplaySelector, item => item.display)
                .Add(p => p.Visible, item => item.value != "3")
            );

            var dropdownOptions = cut.FindAll("cctc-input-dropdown-option");
            int expectedOptionElementCount = 2;
            string valueAttributeName = "data-option-value";

            Assert.Equal(expectedOptionElementCount, dropdownOptions.Count);
            Assert.Equal("1", dropdownOptions[0].GetAttribute(valueAttributeName));
            Assert.Equal("2", dropdownOptions[1].GetAttribute(valueAttributeName));
            Assert.Equal("One", dropdownOptions[0].InnerHtml);
            Assert.Equal("Two", dropdownOptions[1].InnerHtml);
        }

        [Fact]
        public void DropDownAttributesConfiguredCorrectly()
        {
            AddAddTooltip();

            var data = new List<string> { "FirstOption", "SecondOption" };
            var cut = RenderComponent<DropDown<string>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Data, data)
                .Add(p => p.Value, "SecondOption")
                .Add(p => p.CssClass, "w-50")
                .Add(p => p.Style, "color: blue;")
            );

            var dropDownWrapperElement = cut.Find("cctc-input[data-cctc-input-type=\"dropdown\"]");

            var expectedDropDownWrapperAttributes = new Dictionary<string, string?>()
            {
                { "id", "test-id" },
                { "class", "w-50" },
                { "style", "color: blue;" },
                { "data-author", "cctc" }
            };

            var actualDropDownWrapperAttributes = new Dictionary<string, string?>()
            {
                { "id", dropDownWrapperElement.Id },
                { "class", dropDownWrapperElement.ClassName },
                { "style", dropDownWrapperElement.GetAttribute("style") },
                { "data-author", dropDownWrapperElement.GetAttribute("data-author") }
            };

            var inputSelected = cut.Find("cctc-input-dropdown-selected");

            var expectedInputSelectedAttributes = new Dictionary<string, string?>()
            {
                { "role", "combobox" }
            };

            var actualInputSelectedAttributes = new Dictionary<string, string?>()
            {
                { "role", inputSelected.GetAttribute("role") }
            };

            var inputDropdownList = cut.Find("cctc-input-dropdown-options");

            var expectedInputDropdownListAttributes = new Dictionary<string, string?>()
            {
                { "role", "listbox" }
            };

            var actualInputDropdownListAttributes = new Dictionary<string, string?>()
            {
                { "role", inputDropdownList.GetAttribute("role") }
            };

            var dropdownOptions = cut.FindAll("cctc-input-dropdown-option");
            var firstDropdownOption = dropdownOptions[0];
            var secondDropdownOption = dropdownOptions[1];

            var expectedOptionAttributes = new List<(string attribute, string? value)>
            {
                ( "id", "test-id-0" ),
                ( "id", "test-id-1" ),
                ( "role", "option" ),
                ( "role", "option" )
            };

            var actualOptionAttributes = new List<(string attribute, string? value)>
            {
                ( "id", firstDropdownOption.Id),
                ( "id", secondDropdownOption.Id),
                ( "role", firstDropdownOption.GetAttribute("role") ),
                ( "role", secondDropdownOption.GetAttribute("role") )
            };

            Assert.Equal(expectedDropDownWrapperAttributes, actualDropDownWrapperAttributes);
            Assert.Equal(expectedInputSelectedAttributes, actualInputSelectedAttributes);
            Assert.Equal(expectedInputDropdownListAttributes, actualInputDropdownListAttributes);
            Assert.Equal(expectedOptionAttributes, actualOptionAttributes);
        }

        [Fact]
        public void DropDownOptionOverflowConfiguredCorrectly()
        {
            AddAddTooltip();
            AddUpdateTooltipTitle();

            var cut = RenderComponent<DropDown<string>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Data, new List<string> { "FirstOption", "SecondOption", "ThirdOption" })
                .Add(p => p.Value, "ThirdOption")
            );

            var inputDropdownList = cut.Find("cctc-input-dropdown-options");

            inputDropdownList.ClassName?.MarkupMatches("option-wrap");

            cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.DropDownOptionOverflow, DropDownOptionOverflow.NoWrap)
            );

            inputDropdownList.ClassName?.MarkupMatches("option-nowrap");

            cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.DropDownOptionOverflow, DropDownOptionOverflow.Scroll)
            );

            inputDropdownList.ClassName?.MarkupMatches("option-scroll");

            cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.DropDownOptionOverflow, DropDownOptionOverflow.None)
            );

            inputDropdownList.ClassName?.MarkupMatches(string.Empty);
        }

        [Fact]
        public void DropDownDisabledWhenAllOptionsDisabled()
        {
            AddAddTooltip();

            var cut = RenderComponent<DropDown<string>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Data, new List<string> { "FirstOption", "SecondOption" })
                .Add(p => p.Value, "SecondOption")
                .Add(p => p.Disabled, _ => true)
            );

            var inputSelected = cut.Find("cctc-input-dropdown-selected");
            var dropdownOptions = cut.FindAll("cctc-input-dropdown-option");

            Assert.Equal("disabled", inputSelected.ClassName);
            Assert.True(dropdownOptions[0].HasAttribute("data-option-disabled"));
            Assert.True(dropdownOptions[1].HasAttribute("data-option-disabled"));
        }

        [Fact]
        public void DropDownNotDisabledWhenAllOptionsNotDisabled()
        {
            AddAddTooltip();

            var cut = RenderComponent<DropDown<string>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Data, new List<string> { "FirstOption", "SecondOption" })
                .Add(p => p.Value, "SecondOption")
                .Add(p => p.Disabled, item => item == "SecondOption")
            );

            var inputSelected = cut.Find("cctc-input-dropdown-selected");
            var dropdownOptions = cut.FindAll("cctc-input-dropdown-option");

            Assert.Equal(string.Empty, inputSelected.ClassName);
            Assert.False(dropdownOptions[0].HasAttribute("data-option-disabled"));
            Assert.True(dropdownOptions[1].HasAttribute("data-option-disabled"));
        }

        [Theory]
        [InlineData(true, true, 0)]
        [InlineData(true, false, 1)]
        [InlineData(false, false, 0)]
        public void DropDownReadOnlyIconConfiguredCorrectly(bool readOnly, bool hideReadOnlyIcon, int expectedIconCount)
        {
            AddAddTooltip();

            var cut = RenderComponent<DropDown<string>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Data, new List<string> { "FirstOption", "SecondOption" })
                .Add(p => p.Value, "SecondOption")
                .Add(p => p.ReadOnly, readOnly)
                .Add(p => p.HideReadOnlyIcon, hideReadOnlyIcon)
            );

            var readOnlyIcon = cut.FindAll(".icon-wrapper .material-icons.lock");
            Assert.Equal(expectedIconCount, readOnlyIcon.Count);
        }

        [Fact]
        public void ShowClearIconClassesCorrectlyConfigured()
        {
            AddAddTooltip();
            AddUpdateTooltipTitle();

            var mockDummyService = new Mock<IDummyService>();
            int? value = null;
            var cut = RenderComponent<DropDown<int?>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Data, new List<int?> { 1, 2 })
                .Add(p => p.Value, value)
                .Add(p => p.ValueChanged, args => value = args)
                .Add(p => p.ShowClear, true)
            );

            var clearIcon = cut.Find(".material-icons.backspace");
            clearIcon.ClassName?.MarkupMatches("material-icons backspace hide-clear-icon");

            value = 2;
            cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.Value, value)
            );

            clearIcon.ClassName?.MarkupMatches("material-icons backspace show-clear-icon");

            clearIcon.Click();
            cut.SetParametersAndRender(parameters => parameters
               .Add(p => p.Value, value)
           );

            clearIcon.ClassName?.MarkupMatches("material-icons backspace hide-clear-icon");
        }

        [Fact]
        public void ShowClearWithNonNullableValueTypeThrowsArgumentException()
        {
            var cut = () => RenderComponent<DropDown<int>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Data, new List<int> { 1, 2 })
                .Add(p => p.Value, 2)
                .Add(p => p.ShowClear, true)
            );

            var actual = Assert.Throws<ArgumentException>(cut);
            string expectedParamName = "ShowClear";
            string expectedMessage = $"ShowClear should only be set to true when TData is a reference type or nullable value type (Parameter 'ShowClear')";
            Assert.Equal(expectedParamName, actual.ParamName);
            Assert.Equal(expectedMessage, actual.Message);
        }

        [Fact]
        public void ShowClearWithNullableValueTypeDoesNotThrowArgumentException()
        {
            AddAddTooltip();

            var cut = RenderComponent<DropDown<int?>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Data, new List<int?> { 1, 2 })
                .Add(p => p.Value, 2)
                .Add(p => p.ShowClear, true)
            );
        }

        [Fact]
        public void DuplicateOptionSelectionThrowsInvalidOperationException()
        {
            AddAddTooltip();

            int value = 2;
            var cut = RenderComponent<DropDown<int>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Data, new List<int> { 1, 2, 1 })
                .Add(p => p.Value, value)
            );

            value = 1;
            var updateCut = () => cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.Value, value)
            );

            var actual = Assert.Throws<InvalidOperationException>(updateCut);
            Assert.Equal("Sequence contains more than one matching element", actual.Message);
        }
    }
}
