@component @concertina @concertina_2
Feature: the concertina allows the user to select whether they should collapse other on item selection

Scenario: Items are collapsed when a new one is opened when the 'collapse others?' checkbox is checked. 
    Given the user is at the home page
    And the user selects the "Concertina" component in the container "Concertina"
    And the user clicks the "Usage" tab
    And the user expands the concertina by clicking on the header with the text "User chooses collapse others"
    And the user clicks the Checkbox within the Checkbox component
    And the Checkbox component is checked
    And the collapse or expand all button is clicked
    And the user presses the collapse or expand button of header "header 1"
    And concertina content "content 1" is visible
    When the user presses the collapse or expand button of header "header 2"
    Then concertina content "content 1" is hidden
    And concertina content "content 2" is visible
    And the Concertina component image matches the base image "Collapse others checked new content visible old content hidden"

Scenario: Items are not collapsed when a new one is opened when the 'collapse others?' checkbox is unchecked. 
    Given the user is at the home page
    And the user selects the "Concertina" component in the container "Concertina"
    And the user clicks the "Usage" tab
    When the user expands the concertina by clicking on the header with the text "User chooses collapse others"
    And the Checkbox component is unchecked
    And the collapse or expand all button is clicked
    And the user presses the collapse or expand button of header "header 1"
    And concertina content "content 1" is visible
    When the user presses the collapse or expand button of header "header 2"
    Then concertina content "content 1" is visible
    And concertina content "content 2" is visible
    And the Concertina component image matches the base image "Collapse others unchecked new content visible old content visible"



