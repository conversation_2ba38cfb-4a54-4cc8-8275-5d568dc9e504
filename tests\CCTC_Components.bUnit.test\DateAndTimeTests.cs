﻿using CCTC_Components.Components.Temporal;
using CCTC_Components.Components.TextBox;
using Microsoft.AspNetCore.Components;
using System.Linq.Expressions;

namespace CCTC_Components.bUnit.test
{
    public class DateAndTimeTests : TestContext
    {
        [Theory]
        [MemberData(nameof(GetDateAndTimeCallbackTestData))]
        public async Task DateAndTime_Raises_Correct_Callbacks(bool allowClear, string inputDateValue, string inputTimeValue, ExpectedInvocation expectedInvocation)
        {
            var testSchedulers = new TestSchedulers();
            Services.AddTestSchedulers(sp => testSchedulers);

            int componentDefaultThrottleMs = 500;
            string dateFormat = "yyyy-MM-dd HH:mm:ss";
            var mockDummyService = new Mock<IDummyService>();

            var cut = RenderComponent<DateAndTime<DateTime?>>(parameters => parameters
                .Add(p => p.DateFormat, dateFormat)
                .Add(p => p.ValueChanged, async args => await mockDummyService.Object.MethodSixAsync(args))
                .Add(p => p.DateTimeChanged, async args => await mockDummyService.Object.MethodSevenAsync(args))
                .Add(p => p.ActionOnDateOrTimeChanged, async (args1, args2) => await mockDummyService.Object.MethodEightAsync(args1, args2))
                .Add(p => p.MinDateTime, new DateTime(2022, 5, 1, 13, 15, 22))
                .Add(p => p.MaxDateTime, new DateTime(2022, 6, 1, 14, 20, 30))
                .Add(p => p.ThrottleMs, componentDefaultThrottleMs)
                .Add(p => p.AllowClear, allowClear)
            );

            var textComponents = cut.FindComponents<Text>();
            var dateTextComponent = textComponents.First();
            var timeTextComponent = textComponents.Last();

            await dateTextComponent.InvokeAsync(() =>
            {
                dateTextComponent.Find("input").Input(inputDateValue);
                testSchedulers.Default.AdvanceBy(TimeSpan.FromMilliseconds(componentDefaultThrottleMs).Ticks);
            });

            await timeTextComponent.InvokeAsync(() =>
            {
                timeTextComponent.Find("input").Input(inputTimeValue);
                testSchedulers.Default.AdvanceBy(TimeSpan.FromMilliseconds(componentDefaultThrottleMs).Ticks);
            });

            mockDummyService.Verify(expectedInvocation.Expression, expectedInvocation.Times);
        }

        [Fact]
        public void InvalidTypeParameterThrowsArgumentException()
        {
            Services.AddTestSchedulers();

            var cut = () => RenderComponent<DateAndTime<int>>(parameters => parameters
                .Add(p => p.Id, "test-id")
            );

            var actual = Assert.Throws<ArgumentException>(cut);
            string expectedParamName = "TValue";
            string expectedMessage = $"Expected a datetime or nullable datetime type (Parameter 'TValue')";
            Assert.Equal(expectedParamName, actual.ParamName);
            Assert.Equal(expectedMessage, actual.Message);
        }

        [Fact]
        public void AllowClearWithNonNullableValueTypeThrowsArgumentException()
        {
            Services.AddTestSchedulers();

            var cut = () => RenderComponent<DateAndTime<DateTime>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.AllowClear, true)
            );

            var actual = Assert.Throws<ArgumentException>(cut);
            string expectedParamName = "AllowClear";
            string expectedMessage = $"AllowClear should only be set to true when TValue is of type nullable datetime (Parameter 'AllowClear')";
            Assert.Equal(expectedParamName, actual.ParamName);
            Assert.Equal(expectedMessage, actual.Message);
        }

        [Fact]
         void AllowClearWithNullableValueTypeDoesNotThrowArgumentException()
        {
            Services.AddTestSchedulers();

            var cut = RenderComponent<DateAndTime<DateTime?>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.AllowClear, true)
            );
        }

        /// <summary>
        /// Generates test data for callbacks triggered by changing date and / or time input
        /// </summary>
        /// <returns>An IEnumerable of object arrays, one array for each test, comprising of allowClear, inputDateValue, inputTimeValue, expectedInvocation
        /// </returns>
        public static IEnumerable<object?[]> GetDateAndTimeCallbackTestData()
        {
            //supply a valid date and time
            yield return new object?[] { false, "2022-05-01", "13:15:22",
                new ExpectedInvocation(m => m.MethodSixAsync(new DateTime(new DateOnly(2022, 5, 1), new TimeOnly(13, 15, 22))), Times.Once()) };
            yield return new object?[] { false, "2022-05-01", "13:15:22",
                new ExpectedInvocation(m => m.MethodSevenAsync(It.Is<ChangeEventArgs>(args => (string?)args.Value == "2022-05-01 13:15:22")), Times.Once()) };
            yield return new object?[] { false, "2022-05-01", "13:15:22",
                new ExpectedInvocation(m => m.MethodEightAsync(new DateOnly(2022, 5, 1), null), Times.Once()) };
            yield return new object?[] { false, "2022-05-01", "13:15:22",
                new ExpectedInvocation(m => m.MethodEightAsync(new DateOnly(2022, 5, 1), new TimeOnly(13, 15, 22)), Times.Once()) };

            //below minimum datetime
            yield return new object?[] { false, "2022-05-01", "13:15:21",
                new ExpectedInvocation(m => m.MethodSixAsync(It.IsAny<DateTime>()), Times.Never()) };
            yield return new object?[] { false, "2022-05-01", "13:15:21",
                new ExpectedInvocation(m => m.MethodSevenAsync(It.IsAny<ChangeEventArgs>()), Times.Never()) };
            yield return new object?[] { false, "2022-05-01", "13:15:21",
                new ExpectedInvocation(m => m.MethodEightAsync(new DateOnly(2022, 5, 1), null), Times.Once()) };

            //above maximum datetime
            yield return new object?[] { false, "2022-06-01", "14:20:31",
                new ExpectedInvocation(m => m.MethodSixAsync(It.IsAny<DateTime>()), Times.Never()) };
            yield return new object?[] { false, "2022-06-01", "14:20:31",
                new ExpectedInvocation(m => m.MethodSevenAsync(It.IsAny<ChangeEventArgs>()), Times.Never()) };
            yield return new object?[] { false, "2022-06-01", "14:20:31",
                new ExpectedInvocation(m => m.MethodEightAsync(new DateOnly(2022, 6, 1), null), Times.Once()) };

            //time not valid
            yield return new object?[] { false, "2022-05-01", "not a time",
                new ExpectedInvocation(m => m.MethodSixAsync(It.IsAny<DateTime>()), Times.Never()) };
            yield return new object?[] { false, "2022-05-01", "not a time",
                new ExpectedInvocation(m => m.MethodSevenAsync(It.IsAny<ChangeEventArgs>()), Times.Never()) };
            yield return new object?[] { false, "2022-05-01", "not a time",
                new ExpectedInvocation(m => m.MethodEightAsync(new DateOnly(2022, 5, 1), null), Times.Once()) };

            //date not valid
            yield return new object?[] { false, "not a date", "13:15:22",
                new ExpectedInvocation(m => m.MethodSixAsync(It.IsAny<DateTime>()), Times.Never()) };
            yield return new object?[] { false, "not a date", "13:15:22",
                new ExpectedInvocation(m => m.MethodSevenAsync(It.IsAny<ChangeEventArgs>()), Times.Never()) };
            yield return new object?[] { false, "not a date", "13:15:22",
                new ExpectedInvocation(m => m.MethodEightAsync(null, new TimeOnly(13, 15, 22)), Times.Once()) };

            //date and time not valid
            yield return new object?[] { false, "not a date", "not a time",
                new ExpectedInvocation(m => m.MethodSixAsync(It.IsAny<DateTime>()), Times.Never()) };
            yield return new object?[] { false, "not a date", "not a time",
                new ExpectedInvocation(m => m.MethodSevenAsync(It.IsAny<ChangeEventArgs>()), Times.Never()) };
            yield return new object?[] { false, "not a date", "not a time",
                new ExpectedInvocation(m => m.MethodEightAsync(null, null), Times.Never()) };

            //clear date and time when allowclear is false
            yield return new object?[] { false, "", "",
                new ExpectedInvocation(m => m.MethodSixAsync(It.IsAny<DateTime>()), Times.Never()) };
            yield return new object?[] { false, "", "",
                new ExpectedInvocation(m => m.MethodSevenAsync(It.IsAny<ChangeEventArgs>()), Times.Never()) };
            yield return new object?[] { false, "", "",
                new ExpectedInvocation(m => m.MethodEightAsync(null, null), Times.Never()) };

            //clear date and time when allowclear is true
            yield return new object?[] { true, "", "",
                new ExpectedInvocation(m => m.MethodSixAsync(null), Times.Exactly(2)) };
            yield return new object?[] { true, "", "",
                new ExpectedInvocation(m => m.MethodSevenAsync(It.Is<ChangeEventArgs>(args => args.Value == null)), Times.Exactly(2)) };
            yield return new object?[] { true, "", "",
                new ExpectedInvocation(m => m.MethodEightAsync(null, null), Times.Exactly(2)) };
        }
    }

    /// <summary>
    /// Defines the expression for the mock invocation match and the number of invocations allowed
    /// </summary>
    /// <param name="Expression"></param>
    /// <param name="Times"></param>
    public record ExpectedInvocation(Expression<Func<IDummyService, Task>> Expression, Times Times);
}
