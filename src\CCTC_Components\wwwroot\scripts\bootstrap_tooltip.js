export function addTooltip(containerId) {
    return new bootstrap.Tooltip(document.getElementById(containerId));
}

export function disposeTooltip(containerId) {
    $(`#${containerId}`).tooltip('dispose');
}

export function enableTooltip(containerId) {
    $(`#${containerId}`).tooltip('enable');
}

export function disableTooltip(containerId) {
    $(`#${containerId}`).tooltip('disable');
}

export function updateTooltipTitle(containerId, newTitle, delay = 0, enable = true) {
    setTimeout(function () {
        $(`#${containerId}`).attr('data-bs-original-title', newTitle);
        if (enable) {
            enableTooltip(containerId);
        }
        else {
            disableTooltip(containerId);
        }
    }, delay);
}
