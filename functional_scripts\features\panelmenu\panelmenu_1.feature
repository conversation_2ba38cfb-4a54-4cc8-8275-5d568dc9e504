@component @panelmenu @panelmenu_1
Feature: the panel menu can contain items and headers
    <PERSON><PERSON><PERSON>: the panel menu sample page is available
        Given the user is at the home page
        When the user selects the "Panel menu" component in the container "Panel menu"
        Then the url ending is "panelmenusample"

    Scenario: the panel menu contains individual items and headers containing items
        Given the user is at the home page
        And the user selects the "Panel menu" component in the container "Panel menu"
        And there is a panel menu item called "menuitem without header"
        And there is a panel menu header called "header 1"
        And the panel menu header "header 1" is "closed"
        When the user selects the "header 1" Panel menu header
        And the panel menu header "header 1" is "open"
        Then there is a panel menu item called "panel item 1A"
        And the panel menu matches the base image "items inside and outside of headers"