1 - the date and time component date can be typed in as text or can be entered via a date picker. Time can be typed in as text
2 - the date and time component can allow an empty value and an entered value can be optionally cleared
3 - the date and time component date and time are validated (date / time format, min and / or max date / time) and there is feedback provided to the user via an icon
4 - the date and time component can be made read-only and / or disabled. The read-only icon is optional
5 - the date and time component has placeholders which match the date and time formats