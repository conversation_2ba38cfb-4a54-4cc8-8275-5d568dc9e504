﻿using AngleSharp.Html.Dom;
using CCTC_Components.Components;
using Microsoft.AspNetCore.Components;

namespace CCTC_Components.bUnit.test
{
    public class NumericTests : TestContext
    {
        [Theory]
        [InlineData("12.24", "0.0##e+00", false, "1.224e+01", true, true)]
        [InlineData("not a number", "0.00", false, "1.00", true, true)]
        [InlineData("", "0.00", false, null, true, true)]
        [InlineData("", "0.00", true, "1.00", true, true)]
        [InlineData("1.2", null, false, "1.2", true, true )]
        public void Numeric_Raises_Correct_Callbacks(string value, string? format, bool preventWhitespace,
            string? expectedValue, bool valueChangedRaisedExpected,
            bool numberChangedRaisedExpected)
        {
            var testSchedulers = new TestSchedulers();
            Services.AddTestSchedulers(sp => testSchedulers);

            bool valueChangedRaised = false;
            bool numberChangedRaised = false;
            string? valueChangedNewValue = null;
            ChangeEventArgs? numberChangedNewValue = null;
            int componentDefaultThrottleMs = Constants.DefaultThrottleMs;

            var cut = RenderComponent<Numeric>(parameters => parameters
                .Add(p => p.Value, "1.0")
                .Add(p => p.Format, format)
                .Add(p => p.PreventWhitespace, preventWhitespace)
                .Add(p => p.ValueChanged, args => { valueChangedRaised = true; valueChangedNewValue = args; })
                .Add(p => p.NumberChanged, args => { numberChangedRaised = true; numberChangedNewValue = args; })
            );

            cut.Find("input").Input(value);
            testSchedulers.Default.AdvanceBy(TimeSpan.FromMilliseconds(componentDefaultThrottleMs).Ticks);

            Assert.Equal(valueChangedRaisedExpected, valueChangedRaised);
            Assert.Equal(numberChangedRaisedExpected, numberChangedRaised);
            Assert.Equal(expectedValue, valueChangedNewValue);
            Assert.Equal(expectedValue, numberChangedNewValue?.Value?.ToString());
        }

        [Fact]
        public void Numeric_Can_Force_Complete()
        {
            Services.AddTestSchedulers();

            bool valueChangedRaised = false;
            string expectedValue = "5.2";
            string? valueChangedNewValue = null;

            var cut = RenderComponent<Numeric>(parameters => parameters
                .Add(p => p.ValueChanged, args => { valueChangedRaised = true; valueChangedNewValue = args; })
            );

            cut.Find("input").Input(expectedValue);
            cut.Instance.ForceComplete();

            Assert.True(valueChangedRaised);
        }

        [Fact]
        public void Numeric_Throws_ArgumentException_Bad_Format()
        {
            Services.AddTestSchedulers();

            var cut = () => RenderComponent<Numeric>(parameters => parameters
                .Add(p => p.Format, "bad format")
            );

            var actual = Assert.Throws<ArgumentException>(cut);
            string expectedParamName = "Format";
            string expectedMessage = $"The format should match the following regular expression: {CCTC_Components.Helpers.Constants.NumberFormatPattern} (Parameter 'Format')";
            Assert.Equal(expectedParamName, actual.ParamName);
            Assert.Equal(expectedMessage, actual.Message);
        }

        [Theory]
        [InlineData(false, false, false)]
        [InlineData(true, true, true)]
        [InlineData(true, false, false)]
        [InlineData(false, true, false)]
        public void NumericAttributesConfiguredCorrectly(bool readOnly, bool disabled, bool redactText)
        {
            Services.AddTestSchedulers();

            var cut = RenderComponent<Numeric>(parameters => parameters
                .Add(p => p.CssClass, "w-50")
                .Add(p => p.Style, "color: green;")
                .Add(p => p.Id, "test-id")
                .Add(p => p.ReadOnly, readOnly)
                .Add(p => p.Disabled, disabled)
                .Add(p => p.MaxLength, 10)
                .Add(p => p.Placeholder, "0000")
                .Add(p => p.RedactText, redactText)
            );

            var numericWrapperElement = cut.Find("cctc-input[data-cctc-input-type=\"numeric\"]");
            var inputElement = (IHtmlInputElement)cut.FindAll("input")[0];


            var expectedNumericWrapperAttributes = new Dictionary<string, string?>()
            {
                { "id", "test-id" },
                { "class", "w-50" },
                { "style", "color: green;" },
                { "data-author", "cctc" }
            };

            var actualNumericWrapperAttributes = new Dictionary<string, string?>()
            {
                { "id", numericWrapperElement.Id },
                { "class", numericWrapperElement.ClassName },
                { "style", numericWrapperElement.GetAttribute("style") },
                { "data-author", numericWrapperElement.GetAttribute("data-author") }
            };

            var expectedInputAttributes = new Dictionary<string, object?>()
            {
                { "maxlength", 10 },
                { "type", $"{(redactText ? "password" : "text")}" },
                { "placeholder", "0000" },
                { "autocomplete", "off" }
            };

            var actualInputAttributes = new Dictionary<string, object?>()
            {
                { "maxlength", inputElement.MaxLength },
                { "type", inputElement.Type },
                { "placeholder", inputElement.Placeholder },
                { "autocomplete", inputElement.Autocomplete }
            };

            Assert.Equal(expectedNumericWrapperAttributes, actualNumericWrapperAttributes);
            Assert.Equal(expectedInputAttributes, actualInputAttributes);
            Assert.Equal(readOnly, inputElement.IsReadOnly);
            Assert.Equal(disabled, inputElement.IsDisabled);
        }

        [Theory]
        [InlineData(true, true, 0)]
        [InlineData(true, false, 1)]
        [InlineData(false, false, 0)]
        public void NumericReadOnlyIconConfiguredCorrectly(bool readOnly, bool hideReadOnlyIcon, int expectedIconCount)
        {
            Services.AddTestSchedulers();

            var cut = RenderComponent<Numeric>(parameters => parameters
                .Add(p => p.ReadOnly, readOnly)
                .Add(p => p.HideReadOnlyIcon, hideReadOnlyIcon)
            );

            var readOnlyIcon = cut.FindAll(".icon-wrapper .material-icons");
            Assert.Equal(expectedIconCount, readOnlyIcon.Count);
        }
    }
}
