﻿namespace CCTC_Components.bUnit.test.Helpers.Models;

using CCTC_Lib.Contracts.Data;
using CCTC_Lib.Enums.Data;

public sealed class NamedNodeIdentifier : IEquatable<NamedNodeIdentifier>, ISearchable, IUniqueIdentity<string>, ISortable
{

    string DisplayJoinString => " | ";

    string KeyValueDelimiter => "¦";

    string DisplayKeyValueDelimiter => $" {KeyValueDelimiter} ";


    public NamedNodeIdentifier(NodeIdentifier nodeIdentifier, string[] name)
    {
        NodeIdentifier = nodeIdentifier;
        Name = name;
    }

    public static NamedNodeIdentifier Create(string schema, string nodeResource, Guid id, string[] name)
    {
        return new NamedNodeIdentifier(new NodeIdentifier(new Node(schema, nodeResource), id), name);
    }

    public static NamedNodeIdentifier Create(string schema, string nodeResource, Guid id, string name)
    {
        return Create(schema, nodeResource, id, new[] { name });
    }

    public NodeIdentifier NodeIdentifier { get; }

    public Node Node => NodeIdentifier.Node;

    public string[] Name { get; }

    public string Schema => NodeIdentifier.Schema;
    public string NodeResource => NodeIdentifier.NodeResource;
    public Guid Id => NodeIdentifier.Id;
    public string AsLabel => NodeIdentifier.AsLabel();
    public string AsId => NodeIdentifier.AsId();

    public bool ShowIdAndNameParts { get; set; }

    public string AsName()
    {
        return string.Join(DisplayJoinString, Name);
    }

    public string NameAsString => AsName();

    public string AsIdAndName()
    {
        return $"{NodeIdentifier.AsShortId()}{DisplayKeyValueDelimiter}{AsName()}";
    }

    public string AsName(int truncateNameChars)
    {
        return
            Common.Helpers.String.truncate(string.Join(DisplayJoinString, Name), truncateNameChars, true);
    }

    public string AsLabelAndName()
    {
        return $"{NodeIdentifier.AsLabel()}{DisplayKeyValueDelimiter}{AsName()}";
    }

    public string AsIdAndName(int truncateNameChars)
    {
        return $"{NodeIdentifier.AsShortId()}{DisplayKeyValueDelimiter}{AsName(truncateNameChars)}";
    }

    public string AsStringForDisplay()
    {
        return
            $"{NodeIdentifier.AsLabel()}{DisplayKeyValueDelimiter}{AsIdAndName()}";
    }

    public string AsStringForDisplay(int truncateNameChars)
    {
        return
            $"{NodeIdentifier.AsLabel()}{DisplayKeyValueDelimiter}{AsIdAndName(truncateNameChars)}";
    }

    public string AsShortId()
    {
        return Common.Helpers.String.truncate(Id.ToString(), 8, false);
    }

    public string AsIdAndNameParts()
    {
        return $"{AsShortId()}{DisplayKeyValueDelimiter}{AsName()}";
    }

    public string DisplayAs()
    {
        return
            ShowIdAndNameParts
                ? AsIdAndNameParts()
                : AsLabel;
    }

    public string SearchText()
    {
        return $"{NodeIdentifier.SearchText()} {AsName()}";
    }

    public NamedNodeIdentifier Clone() =>
        new(NodeIdentifier.Copy(), Name.Clone() as string[] ?? Array.Empty<string>());

    public static NamedNodeIdentifier Empty() => new(NodeIdentifier.Empty(), new[] { "" });

    public override string ToString()
    {
        return $"{NodeIdentifier}, {AsName()}";
    }

    public bool Equals(NamedNodeIdentifier? other)
    {
        if (ReferenceEquals(null, other)) return false;
        if (ReferenceEquals(this, other)) return true;

        return
            NodeIdentifier.Equals(other.NodeIdentifier) && AsName() == other.AsName();
    }

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(null, obj)) return false;
        if (ReferenceEquals(this, obj)) return true;
        if (obj.GetType() != this.GetType()) return false;
        return Equals((NamedNodeIdentifier)obj);
    }

    public override int GetHashCode()
    {
        return HashCode.Combine(NodeIdentifier, AsName());
    }

    public static string SortBy { get; set; } = "NameAsString";

    public static SortDir SortDir { get; set; } = SortDir.Asc;
    public string Key => Id.ToString();
}