@component @temporal @dateandtime @dateandtime_5
Feature: the date and time component has placeholders which match the date and time formats
    Scenario: the date and time component has placeholders which match the date and time formats
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: default, allow clear, null initial value, FeedbackIcon.Error"
        Then the Date part of the Date and Time component has the placeholder "yyyy-MM-dd"
        And the Time part of the Date and Time component has the placeholder "HH:mm:ss"
        And the Date and Time component image matches the base image "dateandtime-placeholder"
