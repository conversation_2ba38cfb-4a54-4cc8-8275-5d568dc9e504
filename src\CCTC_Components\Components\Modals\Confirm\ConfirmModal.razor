﻿@inherits CCTC_Components.Components.__CCTC.CCTCBase

<cctc-confirmmodal id="@Id" class="@CssClass" style="@Style" data-author="cctc">
    <cctc-confirmmodal-container>
        <cctc-confirmmodal-header>
            <h4>@Heading</h4>
            <div class="material-icons">
                question_mark
            </div>
        </cctc-confirmmodal-header>
        <hr/>
        <h5>@QuestionText</h5>
        <cctc-confirmmodal-responses>
            <div class="response ok-response" @onclick="@(_ => Close())">
                <div class="material-icons">
                    @PositiveIcon
                </div>
                <div>@PositiveText</div>
            </div>
            <div class="response cancel-response" @onclick="@(_ => Cancel())">
                <div class="material-icons">
                    @NegativeIcon
                </div>
                <div>@NegativeText</div>
            </div>
        </cctc-confirmmodal-responses>
    </cctc-confirmmodal-container>
</cctc-confirmmodal>

@code {

    /// <summary>
    /// The BlazoredModel instance
    /// </summary>
    [CascadingParameter]
    BlazoredModalInstance BlazoredModal { get; set; } = default!;

    /// <summary>
    /// The modal heading
    /// </summary>
    [Parameter, EditorRequired]
    public required string Heading { get; set; }

    /// <summary>
    /// The modal question text
    /// </summary>
    [Parameter, EditorRequired]
    public required string QuestionText { get; set; }

    /// <summary>
    /// The text to display for the positive response
    /// </summary>
    [Parameter] public string PositiveText { get; set; } = "Ok";

    /// <summary>
    /// The icon for the positive response
    /// </summary>
    [Parameter] public string PositiveIcon { get; set; } = "done";

    /// <summary>
    /// The icon for the negative response
    /// </summary>
    [Parameter] public string NegativeIcon { get; set; } = "close";

    /// <summary>
    /// The text to display for the negative response
    /// </summary>
    [Parameter] public string NegativeText { get; set; } = "Cancel";

    async Task Close() => await BlazoredModal.CloseAsync(ModalResult.Ok(true));
    async Task Cancel() => await BlazoredModal.CancelAsync();
}