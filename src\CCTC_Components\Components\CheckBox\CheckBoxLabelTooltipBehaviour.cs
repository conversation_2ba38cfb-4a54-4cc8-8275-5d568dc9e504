﻿namespace CCTC_Components.Components.CheckBox
{
    /// <summary>
    /// Checkbox label tooltip behaviour
    /// </summary>
    public enum CheckBoxLabelTooltipBehaviour
    {
        /// <summary>
        /// Enable checkbox label tooltip when the label overflows its container
        /// </summary>
        EnabledOnLabelOverflow,
        /// <summary>
        /// Enable checkbox label tooltip
        /// </summary>
        Enabled,
        /// <summary>
        /// Disable checkbox label tooltip
        /// </summary>
        Disabled
    }
}
