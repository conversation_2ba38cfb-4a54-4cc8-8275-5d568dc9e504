@component @refreshbutton @refreshbutton_4
Feature: the button icon is customisable 
    Scenario: the refresh button has an icon
        Given the user is at the home page
        And the user selects the "Refresh Button" component in the container "Buttons"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "ButtonIcon: refresh, IconPosition: Left, styled with border"
        Then the button component has an icon

    Scenario: the button icon style is refresh
        Given the user is at the home page
        And the user selects the "Refresh Button" component in the container "Buttons"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "ButtonIcon: refresh, IconPosition: Left, styled with border"
        Then the button icon is "refresh"
        And the button component image matches the base image "refresh button with a refresh icon"

    Scenario: the refresh button icon style is in light mode
        Given the user is at the home page
        And the user selects the "Refresh Button" component in the container "Buttons"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "ButtonIcon: light_mode, IconPosition: Right, overridden icon color via Style"
        Then the button icon is "light_mode" 
        And the button component image matches the base image "refresh button with a light icon"


