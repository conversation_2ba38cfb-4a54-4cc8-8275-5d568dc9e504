@component @textbox @textarea @textarea_2
Feature: the text area component has the facility to add a placeholder and / or set an initial number of display rows
    Scenario: the text area component can add a placeholder
        Given the user is at the home page
        And the user selects the "Text area" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "With mask and style applied"
        Then the Text area component has the placeholder "(000)000/000/00"
        And the Text area component image matches the base image "textarea-placeholder"

    Scenario: the text area component can set an initial number of display rows
        Given the user is at the home page
        And the user selects the "Text area" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "With four given rows and CssClass applied"
        Then the Text area component has 4 rows