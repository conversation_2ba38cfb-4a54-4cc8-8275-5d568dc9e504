﻿@page "/concertinasample"

@{
    var description = new List<string>
    {
        "The Concertina component can display large amounts of information in a compact manner. " +
        "It enables a user to collapse and expand one or all of its constituent ConcertinaItems."
    };

    var features = new List<(string, string)>
    {
        ("collapse/expand all", "setting <code>CanCollapseOrExpandAll</code> enables the ability to collapse or expand every ConcertinaItem"),
        ("collapse others", "<code>CollapseOthersOption</code> allows other items to collapse (or not) when an item is selected, or gives the user the option to configure this per ConcertinaItem"),
        ("scroll into view", "an item will automatically scroll into view when expanded"),
    };

    var gotchas = new List<(string, string)>
    {
        ("width", "by default the Concertina will take all available space")
    };

    var usageCode = new List<(string title, string description, string code, RenderFragment fragment)>
    {
        ("Includes collapse/expand all", "expand on creation - allow collapse or expand or - shows item without content",
            @"<Concertina Expanded=""true"" CanCollapseOrExpandAll=""true""
                Id=""concertina-sample"">
        <HeaderContent>
            <h4>Concertina header</h4>
        </HeaderContent>
        <SubHeaderContent>
            <h6 class=""text-info"">Sub header</h6>
        </SubHeaderContent>
        <ChildContent>
            <ConcertinaItem Id=""concertina-sample-item1"">
                <HeaderContent>
                    <h5>this is header 1</h5>
                </HeaderContent>
                <ContentTemplate>
                    this is the content 1
                </ContentTemplate>
            </ConcertinaItem>
            <ConcertinaItem Id=""concertina-sample-item2"">
                <HeaderContent>
                    <h5>Header without content shows no collapse icon</h5>
                </HeaderContent>
            </ConcertinaItem>
            <ConcertinaItem Id=""concertina-sample-item3"">
                <HeaderContent>
                    <h5>this is header 3</h5>
                </HeaderContent>
                <ContentTemplate>
                    this is the content 3
                </ContentTemplate>
            </ConcertinaItem>
        </ChildContent>
    </Concertina>",
            @<Concertina Expanded="true" CanCollapseOrExpandAll="true"
                         Id="concertina-sample">
                <HeaderContent>
                    <h4>Concertina header</h4>
                </HeaderContent>
                <SubHeaderContent>
                    <h6 class="text-info">Sub header</h6>
                </SubHeaderContent>
                <ChildContent>
                    <ConcertinaItem Id="concertina-sample-item1">
                        <HeaderContent>
                            <h5>this is header 1</h5>
                        </HeaderContent>
                        <ContentTemplate>
                            this is the content 1
                        </ContentTemplate>
                    </ConcertinaItem>
                    <ConcertinaItem Id="concertina-sample-item2">
                        <HeaderContent>
                            <h5>Header without content shows no collapse icon</h5>
                        </HeaderContent>
                    </ConcertinaItem>
                    <ConcertinaItem Id="concertina-sample-item3">
                        <HeaderContent>
                            <h5>this is header 3</h5>
                        </HeaderContent>
                        <ContentTemplate>
                            this is the content 3
                        </ContentTemplate>
                    </ConcertinaItem>
                </ChildContent>
            </Concertina>),

        ("No collapse/expand all option", "collapse on creation - no header - no collapse/expand all",
            @"<Concertina Expanded=""false"" CanCollapseOrExpandAll=""false""
                Id=""concertina-sample-1"">
        <ChildContent>
            <ConcertinaItem Id=""concertina-sample-1-item1"">
                <HeaderContent>
                    <h5>this is header 1</h5>
                </HeaderContent>
                <ContentTemplate>
                    this is the content 1
                </ContentTemplate>
            </ConcertinaItem>
            <ConcertinaItem Id=""concertina-sample-1-item2"">
                <HeaderContent>
                    <h5>Header without content shows no collapse icon</h5>
                </HeaderContent>
            </ConcertinaItem>
            <ConcertinaItem Id=""concertina-sample-1-item3"">
                <HeaderContent>
                    <h5>this is header 3</h5>
                </HeaderContent>
                <ContentTemplate>
                    this is the content 3
                </ContentTemplate>
            </ConcertinaItem>
        </ChildContent>
    </Concertina>",
            @<Concertina Expanded="false" CanCollapseOrExpandAll="false"
                         Id="concertina-sample-1">
                <ChildContent>
                    <ConcertinaItem Id="concertina-sample-1-item1">
                        <HeaderContent>
                            <h5>this is header 1</h5>
                        </HeaderContent>
                        <ContentTemplate>
                            this is the content 1
                        </ContentTemplate>
                    </ConcertinaItem>
                    <ConcertinaItem Id="concertina-sample-1-item2">
                        <HeaderContent>
                            <h5>Header without content shows no collapse icon</h5>
                        </HeaderContent>
                    </ConcertinaItem>
                    <ConcertinaItem Id="concertina-sample-1-item3">
                        <HeaderContent>
                            <h5>this is header 3</h5>
                        </HeaderContent>
                        <ContentTemplate>
                            this is the content 3
                        </ContentTemplate>
                    </ConcertinaItem>
                </ChildContent>
            </Concertina>),
        ("Collapse others", "collapse on creation - no header - no collapse/expand all - collapse others on selected",
            @"<Concertina Expanded=""true"" CanCollapseOrExpandAll=""false"" CollapseOthersOption=""CollapseOthersOption.CollapseOthers""
                Id=""concertina-sample-2"">
        <ChildContent>
            <ConcertinaItem Id=""concertina-sample-2-item1"">
                <HeaderContent>
                    <h5>this is header 1</h5>
                </HeaderContent>
                <ContentTemplate>
                    this is the content 1
                </ContentTemplate>
            </ConcertinaItem>
            <ConcertinaItem Id=""concertina-sample-2-item2"">
                <HeaderContent>
                    <h5>header 2</h5>
                </HeaderContent>
                <ContentTemplate>
                    <div>some content 2</div>
                </ContentTemplate>
            </ConcertinaItem>
            <ConcertinaItem Id=""concertina-sample-2-item3"">
                <HeaderContent>
                    <h5>this is header 3</h5>
                </HeaderContent>
                <ContentTemplate>
                    this is the content 3
                </ContentTemplate>
            </ConcertinaItem>
        </ChildContent>
    </Concertina>",
            @<Concertina Expanded="true" CanCollapseOrExpandAll="false" CollapseOthersOption="CollapseOthersOption.CollapseOthers"
                         Id="concertina-sample-2">
                <ChildContent>
                    <ConcertinaItem Id="concertina-sample-2-item1">
                        <HeaderContent>
                            <h5>this is header 1</h5>
                        </HeaderContent>
                        <ContentTemplate>
                            this is the content 1
                        </ContentTemplate>
                    </ConcertinaItem>
                    <ConcertinaItem Id="concertina-sample-2-item2">
                        <HeaderContent>
                            <h5>header 2</h5>
                        </HeaderContent>
                        <ContentTemplate>
                            <div>some content 2</div>
                        </ContentTemplate>
                    </ConcertinaItem>
                    <ConcertinaItem Id="concertina-sample-2-item3">
                        <HeaderContent>
                            <h5>this is header 3</h5>
                        </HeaderContent>
                        <ContentTemplate>
                            this is the content 3
                        </ContentTemplate>
                    </ConcertinaItem>
                </ChildContent>
            </Concertina>),
        ("User chooses collapse others", "allow configuration of collapse others on selected",
            @"<Concertina Expanded=""true"" CanCollapseOrExpandAll=""true"" CollapseOthersOption=""CollapseOthersOption.UserOption""
                Id=""concertina-sample-3"">
        <HeaderContent>
            this is some header content
        </HeaderContent>
        <ChildContent>
            @foreach (var item in _concertinaItemCollection)
            {
                var itemId = $""concertina-sample-3-{item.Id}"";

                <ConcertinaItem Id=""@itemId"">
                    <HeaderContent>
                        <h5>@item.Header</h5>
                    </HeaderContent>
                    <ContentTemplate>
                        @item.Content
                    </ContentTemplate>
                </ConcertinaItem>
            }
        </ChildContent>
    </Concertina>",
            @<Concertina Expanded="true" CanCollapseOrExpandAll="true" CollapseOthersOption="CollapseOthersOption.UserOption"
                         Id="concertina-sample-3">
                <HeaderContent>
                    this is some header content
                </HeaderContent>
                <ChildContent>
                    @foreach (var item in _concertinaItemCollection)
                    {
                        var itemId = $"concertina-sample-3-{item.Id}";

                        <ConcertinaItem Id="@itemId">
                            <HeaderContent>
                                <h5>@item.Header</h5>
                            </HeaderContent>
                            <ContentTemplate>
                                @item.Content
                            </ContentTemplate>
                        </ConcertinaItem>
                    }
                </ChildContent>
            </Concertina>)
    };

    var relatedComponents = new List<(Relation relation, string display, string url, string description)>
    {
        (Relation.Child, "ConcertinaItem", "concertinaitemsample", "A Concertina contains one or more ConcertinaItems"),
    };
}

<Sampler
    ComponentName="Concertina"
    ComponentCssName="concertina"
    Description="@description"
    RelatedComponents="@relatedComponents"
    UsageText="Typical usages of the <code>PanelMenu</code> component are shown below"
    UsageCodeList="@usageCode"
    ContentHeightPixels="400"
    Features="@features"
    Gotchas="@gotchas">
    <ExampleTemplate>
        <div class="mx-3">
            <Concertina Id="concertina-example"
                Expanded="true" CanCollapseOrExpandAll="true" CollapseOthersOption="CollapseOthersOption.UserOption">
                <HeaderContent>
                    <div>This is the header</div>
                </HeaderContent>
                <SubHeaderContent>
                    <div>This is the sub header</div>
                </SubHeaderContent>
                <ChildContent>
                    @foreach (var item in _concertinaItemCollection)
                    {
                        var itemId = $"concertina-example-{item.Id}";

                        <ConcertinaItem Id="@itemId">
                            <HeaderContent>
                                @item.Header
                            </HeaderContent>
                            <SubHeaderContent>
                                sub @item.Header
                            </SubHeaderContent>
                            <ContentTemplate>
                                @item.Content
                            </ContentTemplate>
                        </ConcertinaItem>
                    }

                    <ConcertinaItem Id="concertina-example-penultimate-item">
                        <HeaderContent>
                            header for the penultimate item
                        </HeaderContent>
                        <SubHeaderContent>
                            sub header for penultimate item
                        </SubHeaderContent>
                        <ContentTemplate>
                            <div style="padding: 1rem; height: 600px; background-color: darkgoldenrod; font-size: xx-large">
                                Concertina item will scroll into view
                            </div>
                        </ContentTemplate>
                    </ConcertinaItem>

                    <ConcertinaItem Id="concertina-example-last-item">
                        <HeaderContent>
                            header for the last item
                        </HeaderContent>
                        <SubHeaderContent>
                            sub header
                        </SubHeaderContent>
                        <ContentTemplate>
                            <div style="padding: 1rem; height: 100px; background-color: grey; font-size: xx-large">
                                Concertina item will scroll into view
                            </div>
                        </ContentTemplate>
                    </ConcertinaItem>
                </ChildContent>
            </Concertina>
        </div>
    </ExampleTemplate>
</Sampler>

@code {

    List<ConcertinaItemText> _concertinaItemCollection = default!;

    protected override void OnInitialized()
    {
        _concertinaItemCollection = new();
        for (var i = 0; i < 3; i++)
        {
            var num = i + 1;
            _concertinaItemCollection.Add(new ConcertinaItemText($"item{num}", $"header {num}", $"content {num}"));
        }
    }

    internal class ConcertinaItemText
    {
        public ConcertinaItemText(string id, string header, string content)
        {
            Id = id;
            Header = header;
            Content = content;
        }

        public string Id { get; set; }

        public string Header { get; }

        public string Content { get; }
    }

}