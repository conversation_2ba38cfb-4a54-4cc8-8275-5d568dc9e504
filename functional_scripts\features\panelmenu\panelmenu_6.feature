@component @panelmenu @panelmenu_6
Feature: text associated with an item can be hidden
    Scenario: an panel menu item or header can have no visible text
        Given the user is at the home page
        And the user selects the "Panel menu" component in the container "Panel menu"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "Typical usage (icons only)"
        # Then the text "menuitem without header" is not visible in the panel menu
        And the text "header 1" is not visible in the panel menu
        And the panel menu matches the base image "panel menu icons only"

        #i think my test works but line 8 fails (9 passes) and i think i need to
        # change the actual usage because it is technically there but small 
        # update; <PERSON><PERSON> is changing it at some point, so have kept my tests the same. 
