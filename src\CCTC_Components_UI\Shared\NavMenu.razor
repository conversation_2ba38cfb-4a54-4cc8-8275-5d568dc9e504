﻿@using BlazorComponentUtilities
@inject IJSRuntime JSRuntime

<nav class="nav-container">
    <PanelMenu Id="primary-nav-panel-menu" CssClass="@_css" MatchPathPred="@PanelMenuMatches.StartsWithMatchPred" PanelMenuExpand="PanelMenuExpand.Multi"
               IconsOnly="@PanelMenuIconsOnly"
               CanUserCollapse="@CanUserCollapse"
               PanelMenuWasCollapsed="@HandlePanelMenuCollapse">
        <PanelMenuItem Id="home" Title="Home" Icon="home" Path="/"></PanelMenuItem>
        <PanelMenuItem Id="get-started" Title="Get Started" Icon="start" Path="get-started"></PanelMenuItem>
        <PanelMenuHeader Id="input-header" Title="Input" Icon="input">
            <PanelMenuItem Id="text" Title="Text" Icon="short_text" Path="textsample"></PanelMenuItem>
            <PanelMenuItem Id="text-area" Title="Text area" Icon="notes" Path="textareasample"></PanelMenuItem>
            <PanelMenuItem Id="date" Title="Date" Icon="insert_invitation" Path="datesample"></PanelMenuItem>
            <PanelMenuItem Id="time" Title="Time" Icon="schedule" Path="timesample"></PanelMenuItem>
            <PanelMenuItem Id="date-and-time" Title="Date and time" Icon="schedule" Path="dateandtimesample"></PanelMenuItem>
            <PanelMenuItem Id="numeric" Title="Numeric" Icon="numbers" Path="numericsample"></PanelMenuItem>
            <PanelMenuItem Id="dropdown" Title="Dropdown" Icon="list_alt" Path="dropdownsample"></PanelMenuItem>
            <PanelMenuItem Id="radio" Title="Radio" Icon="radio_button_checked" Path="radiosample"></PanelMenuItem>
            <PanelMenuItem Id="checkbox" Title="Checkbox" Icon="check_box" Path="checkboxsample"></PanelMenuItem>
        </PanelMenuHeader>
        <PanelMenuItem Id="switch" Title="Switch" Icon="toggle_on" Path="switchsample"></PanelMenuItem>
        <PanelMenuItem Id="animated-placeholder" Title="Animated placeholder" Icon="crop_3_2" Path="animatedplaceholdersample"></PanelMenuItem>
        <PanelMenuItem Id="tooltip" Title="Tooltip" Icon="launch" Path="tooltipsample"></PanelMenuItem>
        <PanelMenuItem Id="info-text" Title="Info text" Icon="content_copy" Path="infotextsample"></PanelMenuItem>
        <PanelMenuItem Id="info-icon" Title="Info icon" Icon="info" Path="infoiconsample"></PanelMenuItem>
        <PanelMenuItem Id="lister" Title="Lister" Icon="list" Path="listersample"></PanelMenuItem>
        <PanelMenuHeader Id="tabs-header" Title="Tabs" Icon="folder">
            <PanelMenuItem Id="tabs" Title="Tabs" Icon="folder" Path="tabssample"></PanelMenuItem>
            <PanelMenuItem Id="tabitem" Title="TabItem" Icon="folder_open" Path="tabitemsample"></PanelMenuItem>
        </PanelMenuHeader>
        <PanelMenuHeader Id="panelmenu-header" Title="Panel menu" Icon="density_medium">
            <PanelMenuItem Id="panelmenu" Title="Panel menu" Icon="density_medium" Path="panelmenusample"></PanelMenuItem>
            <PanelMenuItem Id="panelmenuheader" Title="Panel menu header" Icon="view_stream" Path="panelmenuheadersample"></PanelMenuItem>
            <PanelMenuItem Id="panelmenuitem" Title="Panel menu item" Icon="maximize" Path="panelmenuitemsample"></PanelMenuItem>
        </PanelMenuHeader>
        <PanelMenuHeader Id="concertina-header" Title="Concertina" Icon="unfold_more_double">
            <PanelMenuItem Id="concertina" Title="Concertina" Icon="unfold_more_double" Path="concertinasample"></PanelMenuItem>
            <PanelMenuItem Id="concertinaitem" Title="Concertina item" Icon="unfold_more" Path="concertinaitemsample"></PanelMenuItem>
        </PanelMenuHeader>
        <PanelMenuHeader Id="steps-header" Title="Steps" Icon="linear_scale">
            <PanelMenuItem Id="steps" Title="Steps" Icon="linear_scale" Path="stepssample"></PanelMenuItem>
            <PanelMenuItem Id="progressstep" Title="Progress step" Icon="fiber_manual_record" Path="progressstepsample"></PanelMenuItem>
        </PanelMenuHeader>
        <PanelMenuItem Id="data-textbox" Title="Data textbox" Icon="list_alt" Path="datatextboxsample"></PanelMenuItem>
        <PanelMenuHeader Id="buttons-header" Title="Buttons" Icon="dialpad">
            <PanelMenuItem Id="button" Title="Button" Icon="smart_button" Path="buttonsample"></PanelMenuItem>
            <PanelMenuItem Id="refreshbutton" Title="Refresh Button" Icon="refresh" Path="refreshbuttonsample"></PanelMenuItem>
        </PanelMenuHeader>
        <PanelMenuItem Id="time-out" Title="Time out" Icon="hourglass_empty" Path="timeoutsample"></PanelMenuItem>
        <PanelMenuHeader Id="modals-header" Title="Modals" Icon="chat_bubble">
            <PanelMenuItem Id="confirmmodal" Title="Confirm modal" Icon="check_circle" Path="confirmmodalsample"></PanelMenuItem>
        </PanelMenuHeader>
        <PanelMenuItem Id="progress" Title="Progress" Icon="speed" Path="progresssample"></PanelMenuItem>
        <PanelMenuItem Id="pill" Title="Pill" Icon="article" Path="pillsample"></PanelMenuItem>

        <PanelMenuItem Id="chart" Title="Chart" Icon="pie_chart" Path="chartsample"></PanelMenuItem>


    </PanelMenu>
</nav>

@code {

    [CascadingParameter]
    public MainLayout? MainLayout { get; set; }

    [Parameter]
    public bool PanelMenuIconsOnly { get; set; }

    [Parameter]
    public EventCallback<bool> NavMenuWasCollapsed { get; set; }

    [Parameter]
    public bool CanUserCollapse { get; set; }

    void HandlePanelMenuCollapse(bool wasCollapsed)
    {
        if (NavMenuWasCollapsed.HasDelegate)
        {
            NavMenuWasCollapsed.InvokeAsync(wasCollapsed);
        }
    }

    string? _css;

    protected override void OnParametersSet()
    {
        //always include tabs and concertina styles as these are used in the sampler itself
        var theme = MainLayout!.GetTheme();

        _css =
            new CssBuilder(theme)
                .AddClass($"panelmenu-{theme}")
                .Build();
    }
}