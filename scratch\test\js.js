let sideBarVisible = true;

function toggleSideBar() {
    let inner = document.querySelector("#inner");

    if (sideBarVisible) {
        inner.classList.remove("inner-grid-with-sidebar");
        inner.classList.add("inner-grid");
        document.querySelector(".right-sidebar").remove();
        sideBarVisible = false;
    } else {
        inner.classList.remove("inner-grid");
        inner.classList.add("inner-grid-with-sidebar");
        let submain = document.querySelector("#inner > div:nth-child(1)");
        submain.insertAdjacentHTML('afterend', '<div class="right-sidebar">Right sidebar</div>')
        sideBarVisible = true;
    }



}