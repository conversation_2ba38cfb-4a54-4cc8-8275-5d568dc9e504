@component @textbox @text @text_3
Feature: the text component input can be constrained by applying an input mask, preventing whitespace (with a configurable response delay) or setting a max length
    Scenario: the text component input can be constrained by applying an input mask
        Given the user is at the home page
        And the user selects the "Text" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "With alternative mask"
        And the Text component has the placeholder ">L<??-00-0"
        When the user enters "lp12456" into the Text component
        Then the Text component has the value "Lp-12-4"

    Scenario: the text component can prevent whitespace
        Given the user is at the home page
        And the user selects the "Text" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Reactive with prevent whitespace and redacted text"
        When the user focusses on the Text component
        And the user presses the backspace key 9 times
        Then the Text component has the value "Demo Text"

    Scenario: the text component can set a max length
        Given the user is at the home page
        And the user selects the "Text" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "With callback and max length of 20"
        When the user enters "Some demo Text greater than max length" into the Text component
        Then the Text component has the value "Some demo Text great"