using Blazored.Modal;
using CCTC_Components_UI;
using CCTC_Components_UI.Services;
using CCTC_Components_UI.StateContainers.ReadOnlyStateContainers;
using CCTC_Components.Helpers;
using CCTC_Lib.Contracts.UI;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using TextCopy;

var builder = WebAssemblyHostBuilder.CreateDefault(args);
builder.RootComponents.Add<App>("#app");
builder.RootComponents.Add<HeadOutlet>("head::after");

builder.Services.AddBlazoredModal();
builder.Services.AddCCTCComponents();
builder.Services.InjectClipboard();
builder.Services.AddSingleton<ClientReadOnlyStateContainer>();
builder.Services.AddSingleton<IBrowserService, BrowserService>();
builder.Services.AddSingleton<IBrowserNameProvider>(sp => sp.GetRequiredService<ClientReadOnlyStateContainer>());
builder.Services.AddScoped<IAppReadinessService, AppReadinessService>();

var host = builder.Build();
//initialize ClientReadOnlyStateContainer at startup
host.Services.GetRequiredService<ClientReadOnlyStateContainer>();

await host.RunAsync();
