@component @pill @pill_5

Feature: the pill text content is configurable
    <PERSON>enario: the pill can contain short text
        Given the user is at the home page
        And the user selects the "Pill" component
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "PillContext: None, ColorStyle: Fill, Size: Medium, Icon: circle, with OnClick callback"
        Then the pill text is "Pill content"
        And the pill component image matches the base image "pill with pill content text"

    Scenario: the pill can contain long text that is truncated
        Given the user is at the home page
        And the user selects the "Pill" component
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "PillContext: None, ColorStyle: Fill, Size: Medium, Icon: circle, MaxWidth: 25rem, PillTooltipBehaviour: EnabledOnTextOverflow, PillTooltipPlacement: Right"
        Then the pill text is "Pill content longer - Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."
        And the pill component image matches the base image "pill with long text"


