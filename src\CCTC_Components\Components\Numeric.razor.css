﻿cctc-input {
    width: 100%;
}

.component-wrapper {
    display: flex;
    align-items: center;
}

.material-icons {
    color: var(--cctc-input-readonly-icon-color);
}

input {
    width: 100%;
    height: var(--cctc-input-height);
    padding-left: var(--cctc-input-padding-left);
    padding-right: max(var(--cctc-input-padding-right), 1.5rem);
    color: var(--cctc-input-color);
    background-color: var(--cctc-input-background-color);
    border-radius: var(--cctc-input-border-radius);
    border-color: var(--cctc-input-border-color);
    border-width: var(--cctc-input-border-width);
    border-style: var(--cctc-input-border-style);
    outline: none;
}

input:disabled {
    color: var(--cctc-input-disabled-color);
    border-color: var(--cctc-input-disabled-background-color);
    background-color: var(--cctc-input-disabled-background-color);
}

.icon-wrapper {
    display: flex;
    align-items: center;
    margin-left: -1.35rem;
}