cctc-confirmmodal-container {
    padding: 1.5rem;
    color: var(--cctc-confirmmodal-color);
    background-color: var(--cctc-confirmmodal-background-color);
    border-radius: var(--cctc-confirmmodal-border-radius);
    border: 1px solid var(--cctc-confirmmodal-border-color);
    box-shadow: 0 2px 2px rgba(0,0,0,.75);
}

cctc-confirmmodal-header {
    display: flex;
    align-items: center;
    color: var(--cctc-confirmmodal-header-color);
}

cctc-confirmmodal-header .material-icons {
    font-size: 3rem;
    margin-right: 0.25rem;
}

cctc-confirmmodal-responses {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-top: 2.5rem;
    margin-bottom: 0.25rem;
}

cctc-confirmmodal-responses .material-icons {
    margin-right: 0.25rem;
}

.response {
    display: flex;
    align-items: center;
    font-size: 1.2rem;
    color: var(--cctc-confirmmodal-response-color);
    border-width: 1px;
    border-style: solid;
    border-color: var(--cctc-confirmmodal-response-border-color);
    border-radius: 2px;
    padding: 0.3rem 0.8rem 0.3rem 0.8rem;
}

.response:hover {
    color: var(--cctc-confirmmodal-response-hover-color);
    border-color: var(--cctc-confirmmodal-response-hover-border-color);
    cursor: pointer;
}
