﻿@inherits CCTC_Components.Components.__CCTC.CCTCBase
@typeparam TData

<cctc-input data-cctc-input-type="radio" id="@Id" class="@CssClass" style="@Style" data-author="cctc">
    <div class="component-wrapper">
        <div role="radiogroup" class="@_radioGroupWrapperClass" @onclick:preventDefault="@ReadOnly">
    @{
        var i = 0;
        foreach (var item in Data)
        {
            if (!Visible?.Invoke(item) ?? false)
            {
                 continue;       
            }

            var radioValue = GetRadioValue(item);
            var radioLabelText = DisplaySelector is null ? item?.ToString() : DisplaySelector(item);
            var radioId = $"{Id}-{i}";
            var radioLabelId = $"{Id}-{i}-label";
            var radioSelected = radioValue == _radioValue;
            var radioDisabled = Disabled?.Invoke(item) ?? false;
            RenderFragment radioLabel = @<label id="@radioLabelId" for=@radioId>@radioLabelText</label>;

            <div class="radio-parent">
                <input
                    type="radio"
                    id="@radioId"
                    name="@Id-radio-group"
                    value="@radioValue"
                    @key="radioValue"
                    @onchange="OnValueChanged"
                    checked=@radioSelected
                    disabled=@radioDisabled/>

            @if (RadioLabelTooltipBehaviour != RadioLabelTooltipBehaviour.Disabled)
            {
                <Tooltip
                    Id="@($"{radioId}-radio-label-tooltip")"
                    Content="@radioLabelText"
                    TooltipPlacement="@RadioLabelTooltipPlacement"
                    TooltipBehaviour="@(GetTooltipBehaviour(RadioLabelTooltipBehaviour, $"#{radioLabelId}"))">
                    @radioLabel
                </Tooltip>
            }
            else
            {
                @radioLabel
            }
            </div>

            i++;
        }
    }
        </div>
    @if (ReadOnly && !HideReadOnlyIcon)
    {
        <div class="icon-wrapper">
            <span class="@_readOnlyIconClass">
                lock
            </span>
        </div>
    }
    else if (_canClear)
    {
        <div class="icon-wrapper">
            <span class="@_clearIconClass" @onclick="ClearSelection">
                backspace
            </span>
        </div>
    }
    </div>
</cctc-input>

@code {

    /// <summary>
    /// The input data
    /// </summary>
    [EditorRequired]
    [Parameter]
    public required IEnumerable<TData> Data { get; set; }

    /// <summary>
    /// The input value
    /// </summary>
    [Parameter]
    public TData? Value { get; set; }

    /// <summary>
    /// A callback which fires when the input value changes
    /// </summary>
    [Parameter]
    public EventCallback<TData> ValueChanged { get; set; }

    /// <summary>
    /// A second callback which fires when the input value changes. Useful when consuming using @bind-Value
    /// </summary>
    [Parameter]
    public EventCallback<ChangeEventArgs> SelectionChanged { get; set; }

    /// <summary>
    /// A function to specify the radio value text
    /// </summary>
    [Parameter]
    public Func<TData, string>? ValueSelector { get; set; }

    /// <summary>
    /// A function to specify the radio label display text
    /// </summary>
    [Parameter]
    public Func<TData, string>? DisplaySelector { get; set; }

    /// <summary>
    /// The orientation of the radio collection
    /// </summary>
    [Parameter]
    public RadioOrientation RadioOrientation { get; set; }

    /// <summary>
    /// A function to specify which radio values are disabled
    /// </summary>
    [Parameter]
    public Func<TData, bool>? Disabled { get; set; }

    /// <summary>
    /// Read-only if true
    /// </summary>
    [Parameter]
    public bool ReadOnly { get; set; }

    /// <summary>
    /// A function to specify which radio values are visible
    /// </summary>
    [Parameter]
    public Func<TData, bool>? Visible { get; set; }

    /// <summary>
    /// Hides the read-only icon when <see cref="ReadOnly"/> is true
    /// </summary>
    [Parameter]
    public bool HideReadOnlyIcon { get; set; }

    /// <summary>
    /// Allows the current selection to be cleared from the UI
    /// </summary>
    [Parameter]
    public bool ShowClear { get; set; }

    /// <summary>
    /// Configure the radio label overflow
    /// </summary>
    [Parameter]
    public RadioLabelOverflow RadioLabelOverflow { get; set; }

    /// <summary>
    /// Configure the radio label tooltip placement
    /// </summary>
    [Parameter]
    public TooltipPlacement RadioLabelTooltipPlacement { get; set; }

    /// <summary>
    /// Configure the radio label tooltip behavior
    /// </summary>
    [Parameter]
    public RadioLabelTooltipBehaviour RadioLabelTooltipBehaviour { get; set; }

    string _radioValue = string.Empty;
    string? _radioGroupWrapperClass;
    string? _readOnlyIconClass;
    string? _clearIconClass;
    bool _typeParamAllowsNull;
    bool _canClear;
    bool _showClear;

    async Task OnValueChanged(ChangeEventArgs args)
    {
        _radioValue =
            args.Value is null ?
                string.Empty :
                args.Value.ToString()!;

        await ValueChanged.InvokeAsync(Data.Single(x => GetRadioValue(x) == _radioValue));
        await SelectionChanged.InvokeAsync(args);
    }

    TooltipBehaviour GetTooltipBehaviour(RadioLabelTooltipBehaviour radioLabelTooltipBehaviour, string labelSelector)
    {
        return radioLabelTooltipBehaviour switch
        {
            RadioLabelTooltipBehaviour.EnabledOnLabelOverflow => new TooltipBehaviour.EnabledOnOverflow(labelSelector),
            RadioLabelTooltipBehaviour.Enabled => new TooltipBehaviour.Enabled(),
            RadioLabelTooltipBehaviour.Disabled => new TooltipBehaviour.Disabled(),
            _ => throw new ArgumentException("case not handled", nameof(radioLabelTooltipBehaviour))
        };
    }

    string GetRadioValue(TData? value)
    {
        if (value is null)
        {
            return string.Empty;
        }
        else
        {
            return ValueSelector is not null ? ValueSelector!(value!) : value.ToString()!;
        }
    }

    async Task ClearSelection()
    {
        if (_canClear)
        {
            _radioValue = string.Empty;
            await ValueChanged.InvokeAsync(default(TData));
            await SelectionChanged.InvokeAsync(new ChangeEventArgs { Value = null });
        }
    }

    void CheckConfig(bool typeParamAllowsNull)
    {
        if (ShowClear && !typeParamAllowsNull)
        {
            throw new ArgumentException($"{nameof(ShowClear)} should only be set to true when TData is a reference type or nullable value type", nameof(ShowClear));
        }
    }

    ///<inheritdoc />
    protected override void OnInitialized()
    {
        _typeParamAllowsNull = default(TData) == null;
        CheckConfig(_typeParamAllowsNull);
    }

    ///<inheritdoc />
    protected override void OnParametersSet()
    {
        bool allOptionsDisabled = Data.All(x => Disabled?.Invoke(x) ?? false);
        _radioValue = GetRadioValue(Value);
        _canClear = _typeParamAllowsNull && ShowClear && !ReadOnly && !allOptionsDisabled;
        _showClear = _canClear && _radioValue != string.Empty;

        _radioGroupWrapperClass =
            new CssBuilder("radio-group-wrapper")
                .AddClass("radio-group-column left-justify", RadioOrientation == RadioOrientation.VerticalLeft)
                .AddClass("radio-group-column right-justify", RadioOrientation == RadioOrientation.VerticalRight)
                .AddClass("radio-group-row", RadioOrientation == RadioOrientation.Horizontal)
                .AddClass("label-wrap", RadioLabelOverflow == RadioLabelOverflow.Wrap)
                .AddClass("label-nowrap", RadioLabelOverflow == RadioLabelOverflow.NoWrap)
                .AddClass("label-scroll", RadioLabelOverflow == RadioLabelOverflow.Scroll)
                .AddClass("disabled", allOptionsDisabled)
                .Build();

        _readOnlyIconClass =
            new CssBuilder()
                .AddClass("material-icons lock")
                .Build();

        _clearIconClass =
            new CssBuilder()
                .AddClass("material-icons backspace")
                .AddClass("show-clear-icon", _showClear)
                .AddClass("hide-clear-icon", !_showClear)
                .Build();
    }
}
