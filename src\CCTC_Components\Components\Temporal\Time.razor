﻿@using static Common.Helpers.StringMask
@using System.Globalization
@using CCTC_Components.Components.TextBox.TextInteraction
@inherits CCTC_Components.Components.__CCTC.CCTCBase
@typeparam TValue

<cctc-input data-cctc-input-type="time" id="@Id" class="@CssClass" style="@Style" data-author="cctc">
    <div class="component-wrapper">
        <div class="time-text-box-wrapper">
            <Text
                Id="@($"{Id}-time-text")"
                Value="@_textTimeValue"
                ValueChanged="OnTextValueChanged"
                Mask="@(DateFormat is null ? _defaultTimeMask : getTimeMask(DateFormat))"
                Placeholder="@GetPlaceholder(DateFormat)"
                ThrottleMs="@ThrottleMs"
                PreventWhitespace="@(!_canClear)"
                HideReadOnlyIcon="@HideReadOnlyIcon"
                Disabled="@Disabled"
                ReadOnly="@ReadOnly">
            </Text>
        </div>
        @if (!ReadOnly && !Disabled)
        {
            <div class="val-icon-wrapper">
            @if (_displayValidationIcon)
            {
                <i class="material-icons @_validationIcon">
                    @_validationIcon
                </i>
            }
            </div>
        }
    </div>
</cctc-input>
@code {

    /// <summary>
    /// The input value
    /// </summary>
    [Parameter]
    public TValue? Value { get; set; }

    /// <summary>
    /// A callback which fires when the input value changes
    /// </summary>
    [Parameter]
    public EventCallback<TValue> ValueChanged { get; set; }

    /// <summary>
    /// A second callback which fires when the input value changes. Useful when consuming using @bind-Value
    /// </summary>
    [Parameter]
    public EventCallback<ChangeEventArgs> TimeChanged { get; set; }

    /// <summary>
    /// The date format. See <see cref="Common.Services.Clock.availableDateFormats"/> for the supported formats
    /// </summary>
    [Parameter]
    public string? DateFormat { get; set; }

    /// <summary>
    /// Provide a minimum time
    /// </summary>
    [Parameter]
    public TimeOnly? MinTime { get; set; }

    /// <summary>
    /// Provide a maximum time
    /// </summary>
    [Parameter]
    public TimeOnly? MaxTime { get; set; }

    /// <summary>
    /// Disabled if true
    /// </summary>
    [Parameter]
    public bool Disabled { get; set; }

    /// <summary>
    /// Read-only if true
    /// </summary>
    [Parameter]
    public bool ReadOnly { get; set; }

    /// <summary>
    /// Hides the read-only icon when <see cref="ReadOnly"/> is true
    /// </summary>
    [Parameter]
    public bool HideReadOnlyIcon { get; set; }

    /// <summary>
    /// The Throttle speed
    /// </summary>
    [Parameter]
    public int ThrottleMs { get; set; }

    /// <summary>
    /// Control the feedback icon display
    /// </summary>
    [Parameter]
    public FeedbackIcon? FeedbackIcon { get; set; }

    /// <summary>
    /// Allows the current value to be cleared from the UI
    /// </summary>
    [Parameter]
    public bool AllowClear { get; set; }

    string? _textTimeValue;
    const string _defaultDateFormat = "HH:mm:ss";
    string _defaultTimeMask = getTimeMask(_defaultDateFormat);
    bool _typeParamAllowsNull;
    bool _canClear;
    string? _validationIcon;
    bool _displayValidationIcon;
    FeedbackIcon _feedbackIcon;

    string GetTimeValue(object? value, string? format = null)
    {
        string dateFormat = format is null ? _defaultDateFormat : format;
        var time = value as TimeOnly?;

        if (time is not null)
        {
            return time.Value.ToString(dateFormat);
        }
        else if (value is not null)
        {
            return value.ToString()!;
        }
        else
        {
            return string.Empty;
        }
    }

    async Task OnTextValueChanged(string newValue)
    {
        bool timeCleared = string.IsNullOrEmpty(newValue);
        //note: update _textTimeValue even if the changed value is not a valid time
        _textTimeValue = newValue;

        if (timeCleared)
        {
            if (_canClear)
            {
                await OnValueChanged(null);
            }

            _displayValidationIcon = false;
            return;
        }

        bool result = TimeOnly.TryParseExact(newValue, DateFormat ?? _defaultDateFormat, CultureInfo.InvariantCulture, DateTimeStyles.None, out TimeOnly parsedTime);
        bool isTimeValid = result && IsTimeWithinLimits(parsedTime);
        ApplyValidation(parsedTime, isTimeValid);

        if (isTimeValid)
        {
            await OnValueChanged(parsedTime);
        }
    }

    async Task OnValueChanged(TimeOnly? parsedTime)
    {
        await ValueChanged.InvokeAsync((TValue?)(object?)parsedTime);
        await TimeChanged.InvokeAsync(new ChangeEventArgs() { Value = parsedTime is null ? null : GetTimeValue(parsedTime, DateFormat) });
    }

    string GetPlaceholder(string? dateFormat)
    {
        return (dateFormat ?? _defaultDateFormat) switch
        {
            "h:mm tt" => "h:mm am/pm",
            "h:mm:ss tt" => "h:mm:ss am/pm",
            "HH:mm" => "HH:mm",
            _defaultDateFormat => _defaultDateFormat,
            _ => throw new ArgumentException("The time format does not currently have an associated placeholder")
        };
    }

    void DisplayPassValidation()
    {
        _validationIcon = "check";
        _displayValidationIcon = true;
    }

    void DisplayFailValidation()
    {
        _validationIcon = "priority_high";
        _displayValidationIcon = true;
    }

    bool IsTimeWithinLimits(TimeOnly? timeValue)
    {
        if (timeValue is null)
        {
            return true;
        }

        bool minResult = true;
        bool maxResult = true;

        if (MinTime is not null)
        {
            minResult = timeValue >= MinTime;
        }

        if (MaxTime is not null)
        {
            maxResult = timeValue <= MaxTime;
        }

        return minResult && maxResult;
    }

    void ApplyValidation(TimeOnly? timeValue, bool isTimeValid)
    {
        if (timeValue is null || _feedbackIcon == Temporal.FeedbackIcon.None)
        {
            _displayValidationIcon = false;
        }
        else if ((_feedbackIcon == Temporal.FeedbackIcon.Both || _feedbackIcon == Temporal.FeedbackIcon.Error) && !isTimeValid)
        {
            DisplayFailValidation();
        }
        else if ((_feedbackIcon == Temporal.FeedbackIcon.Both || _feedbackIcon == Temporal.FeedbackIcon.Valid) && isTimeValid)
        {
            DisplayPassValidation();
        }
        else
        {
            _displayValidationIcon = false;
        }
    }

    void CheckConfig(bool typeParamAllowsNull)
    {
        if (typeof(TValue) != typeof(TimeOnly) && !typeof(TimeOnly).IsAssignableFrom(Nullable.GetUnderlyingType(typeof(TValue))))
        {
            throw new ArgumentException("Expected a timeonly or nullable timeonly type", nameof(TValue));
        }

        if (AllowClear && !typeParamAllowsNull)
        {
            throw new ArgumentException($"{nameof(AllowClear)} should only be set to true when {nameof(TValue)} is of type nullable timeonly", nameof(AllowClear));
        }
    }

    ///<inheritdoc />
    protected override void OnInitialized()
    {
        _typeParamAllowsNull = default(TValue) == null;
        CheckConfig(_typeParamAllowsNull);
    }

    /// <inheritdoc/>
    protected override void OnParametersSet()
    {
        _canClear = _typeParamAllowsNull && AllowClear && !ReadOnly && !Disabled;
        _feedbackIcon = FeedbackIcon ?? Temporal.FeedbackIcon.Error;

        var timeValue = Value as TimeOnly?;
        _textTimeValue = GetTimeValue(timeValue, DateFormat);
        ApplyValidation(timeValue, IsTimeWithinLimits(timeValue));
    }
}
