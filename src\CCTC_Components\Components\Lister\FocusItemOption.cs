﻿namespace CCTC_Components.Components.Lister;

/// <summary>
/// Provides the behaviour when trying to set an item in focus
/// </summary>
public enum FocusItemOption
{

    /// <summary>
    /// Focussing on an item is not permitted. The user cannot select and highlight items
    /// in the list
    /// </summary>
    None,

    /// <summary>
    /// Focussing on an item is permitted. Users can highlight and select items using
    /// the mouse and keyboard
    /// </summary>
    Allow,

    /// <summary>
    /// Focussing on an item is permitted. Users can highlight and select items using
    /// the mouse and keyboard. Additionally, when the data is loaded, the focus will automatically
    /// focus on the first item (assuming at least one item is loaded)
    /// </summary>
    AllowAndAlwaysFocusOnLoad
}