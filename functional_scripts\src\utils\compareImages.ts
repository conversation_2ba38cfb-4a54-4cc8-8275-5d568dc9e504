// Added by https://github.com/ortsevlised

import { config } from '../support/config';
import { ICustomWorld } from '../support/custom-world';
import { ensureFile, pathExists } from 'fs-extra';
import pixelmatch from 'pixelmatch';
import { PNG } from 'pngjs';
import * as fs from 'fs';
import { writeFileSync } from 'fs';
import { join } from 'path';

interface ImagePathOptions {
  skipOs: boolean;
}

export function getImagePath(
  customWorld: ICustomWorld,
  name: string,
  options?: ImagePathOptions
): string {
  return join(
    'screenshots',
    customWorld.feature?.uri ?? '',
    options?.skipOs ? '' : `platform_${process.platform}`,
    config.browser,
    `${name}.png`
  );
}

/**
 * Compares a screenshot to a base image,
 * if the base image doesn't exist it fails the test but creates a new base image based on
 * the screenshot passed so it can be used on the next run.
 * @param screenshot a playwright screenshot
 * @param customWorld needed to create the base image path
 * @param threshold the difference threshold
 */
export async function compareToBaseImage(
  customWorld: ICustomWorld,
  name: string,
  screenshot: Buffer,
  threshold?: { threshold: number }
) {
  let baseImage;
  const baseImagePath = getImagePath(customWorld, name);
  const baseImgExist = await pathExists(baseImagePath);
  if (baseImgExist) {
    baseImage = PNG.sync.read(fs.readFileSync(baseImagePath));
  } else {
    await ensureFile(baseImagePath);
    writeFileSync(baseImagePath, screenshot);
    customWorld.log(
      `The base Image doesn't exist, a screenshot was taken to ${baseImagePath} so it can be used for next run`
    );
    return;
  }
  const img1 = PNG.sync.read(screenshot);

  // attach images to facilitate troubleshooting
  customWorld.attach('The base image is attached below');
  customWorld.attach(fs.readFileSync(baseImagePath), 'image/png;base64');
  customWorld.attach('The screenshot is attached below');
  customWorld.attach(screenshot, 'image/png;base64');

  const difference = getDifference(img1, baseImage, threshold);
  if (difference) {
    customWorld.attach(
      'The image attached below shows the difference between the screenshot and the base image'
    );
    customWorld.attach(difference, 'image/png;base64');
    throw new Error(`Screenshot does not match : ${baseImagePath}`);
  }
}

/**
 * Returns the difference between 2 images
 * @param img1
 * @param img2
 * @param threshold the difference threshold
 */
export function getDifference(
  img1: PNG,
  img2: PNG,
  threshold = config.IMG_THRESHOLD
): Buffer | undefined {
  const { width, height } = img2;
  const diff = new PNG({ width, height });
  const difference = pixelmatch(img1.data, img2.data, diff.data, width, height, threshold);
  if (difference > 0) {
    return PNG.sync.write(diff);
  }
  return undefined;
}

/**
 * Returns whether there are differences between two screenshots
 * @param screenshot1
 * @param screenshot2
 * @param threshold the difference threshold
 */
export function hasDifferences(
  screenshot1: Buffer,
  screenshot2: Buffer,
  threshold = config.IMG_THRESHOLD
) {
  const img1 = PNG.sync.read(screenshot1);
  const img2 = PNG.sync.read(screenshot2);
  const difference = getDifference(img1, img2, threshold);
  return difference ? true : false;
}
