#!meta

{"kernelInfo":{"defaultKernelName":"csharp","items":[{"aliases":[],"languageName":"csharp","name":"csharp"}]}}

#!csharp

var a = typeof(bool);
var b = typeof(bool?);
var x = typeof(bool).IsAssignableFrom(Nullable.GetUnderlyingType(typeof(bool?)));
var y = Nullable.GetUnderlyingType(typeof(bool?));
Console.WriteLine(a);
Console.WriteLine(b);
Console.WriteLine(x);
Console.WriteLine(y);

#!csharp

var myList = new List<string> { "One", "Two", null, null };
string value = "One";
string match = myList.Single<string>(x => x == value);
Console.WriteLine(match);
