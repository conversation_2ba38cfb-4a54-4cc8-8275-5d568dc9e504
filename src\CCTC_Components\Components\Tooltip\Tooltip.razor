﻿@using Microsoft.JSInterop;
@inject IJSRuntime JSRuntime
@inherits CCTC_Components.Components.__CCTC.CCTCBase
@implements IAsyncDisposable

<cctc-tooltip id="@Id" class="@CssClass" style="@Style" data-author="cctc" data-bs-toggle="tooltip" data-bs-placement="@_tooltipPlacement"
     data-bs-container="@($"#{Id}")">
    @ChildContent
</cctc-tooltip>

@code {

    /// <summary>
    /// The content to display in the tooltip
    /// </summary>
    [Parameter, EditorRequired]
    public string? Content { get; set; }

    /// <summary>
    /// The target item that the tooltip wraps i.e. the element that should show the tooltip when in focus
    /// </summary>
    [Parameter, EditorRequired]
    public RenderFragment ChildContent { get; set; } = default!;

    /// <summary>
    /// The placement of the tooltip relative to the target
    /// </summary>
    [Parameter]
    public TooltipPlacement TooltipPlacement { get; set; }

    /// <summary>
    /// The behaviour of the tooltip
    /// </summary>
    [Parameter]
    public TooltipBehaviour? TooltipBehaviour { get; set; }

    string? _tooltipPlacement;
    TooltipBehaviour? _tooltipBehaviour;
    IJSObjectReference? _jsModuleTooltip;
    IJSObjectReference? _utilsModule;
    string? _enabledOnOverflowSelector;
    bool _isOverflowing;

    ///<inheritdoc />
    protected override void OnInitialized()
    {
        _tooltipPlacement = TooltipPlacement.ToString().ToLower();
        _tooltipBehaviour = TooltipBehaviour ?? new TooltipBehaviour.Enabled();

        if (_tooltipBehaviour is TooltipBehaviour.EnabledOnOverflow tooltipBehavior)
        {
            _enabledOnOverflowSelector = tooltipBehavior.EnabledOnOverflowSelector;
        }
    }

    ///<inheritdoc />
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            _jsModuleTooltip = await JSRuntime.InvokeAsync<IJSObjectReference>("import", "./_content/CCTC_Components/scripts/bootstrap_tooltip.js");
            await _jsModuleTooltip.InvokeVoidAsync("addTooltip", Id);
        }

        if (firstRender && _tooltipBehaviour is TooltipBehaviour.EnabledOnOverflow)
        {
            _utilsModule = await JSRuntime.InvokeAsync<IJSObjectReference>("import", "./_content/CCTC_Components/scripts/utils.js");
        }

        if (_utilsModule is not null && _enabledOnOverflowSelector is not null)
        {
            _isOverflowing = await _utilsModule.InvokeAsync<bool>("isOverflowing", _enabledOnOverflowSelector);
        }

        if (_jsModuleTooltip is not null)
        {
            bool enableTooltip = _tooltipBehaviour is TooltipBehaviour.Enabled || (_tooltipBehaviour is TooltipBehaviour.EnabledOnOverflow && _isOverflowing);
            // Tooltip title set and updated here with a small delay required to prevent tooltip freeze when changing the content
            await _jsModuleTooltip.InvokeVoidAsync("updateTooltipTitle", Id, Content ?? string.Empty, 100, enableTooltip);
        }
    }

    ///<inheritdoc />
    public override async ValueTask DisposeAsync()
    {
        await base.DisposeAsync();

        if (_jsModuleTooltip is not null)
        {
            await _jsModuleTooltip.InvokeVoidAsync("disposeTooltip", Id);
            await _jsModuleTooltip.DisposeAsync();
        }

        if (_utilsModule is not null) await _utilsModule.DisposeAsync();
    }
}
