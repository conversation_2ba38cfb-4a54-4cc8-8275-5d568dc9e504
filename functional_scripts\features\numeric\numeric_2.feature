@component @numeric @numeric_2
Feature: the numeric component has the facility to redact text and / or add a placeholder
    Scenario: the numeric component has the facility to redact text
        Given the user is at the home page
        Given the user selects the "Numeric" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "No format with a null initial value, redact text, allows whitespace, max length"
        When the user enters "123" into the Numeric component
        Then the Numeric component image matches the base image "numeric-redacted"

    Scenario: the numeric component can add a placeholder
        Given the user is at the home page
        Given the user selects the "Numeric" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "No format with a null initial value, redact text, allows whitespace, max length"
        Then the Numeric component has the placeholder "123"
        And the Numeric component image matches the base image "numeric-placeholder"