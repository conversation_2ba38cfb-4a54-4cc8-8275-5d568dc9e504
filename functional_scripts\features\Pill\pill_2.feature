@component @pill @pill_2

Feature: the pill can be filled or outline
    <PERSON><PERSON><PERSON>: the pill is filled
        Given the user is at the home page
        And the user selects the "Pill" component
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "PillContext: None, ColorStyle: Fill, Size: Medium, Icon: circle, with OnClick callback"
        Then the pill is filled
        And the pill component image matches the base image "filled pill"
    
    Scenario: the pill is outline
        Given the user is at the home page
        And the user selects the "Pill" component
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "PillContext: Success, ColorStyle: Outline, Size: Small, Icon: done_outline"
        Then the pill is outline 
        And the pill component image matches the base image "outline pill"

