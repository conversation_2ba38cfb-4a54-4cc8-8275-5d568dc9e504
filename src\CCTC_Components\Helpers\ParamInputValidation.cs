﻿using System.Text.RegularExpressions;

namespace CCTC_Components.Helpers
{
    /// <summary>
    /// Component parameter input validation helpers
    /// </summary>
    public static class ParamInputValidation
    {
        /// <summary>
        /// Validates a number format
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public static bool NumberFormatValid(string? value)
        {
            return value is null || Regex.IsMatch(value, Constants.NumberFormatPattern);
        }

        /// <summary>
        /// Validates a css width or height value
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public static bool CssWidthOrHeightValid(string? value)
        {
            return value is not null && Regex.IsMatch(value, Constants.CssWidthOrHeightPattern);
        }

        /// <summary>
        /// Validates a css margin value
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public static bool CssMarginValid(string? value)
        {
            return value is not null && Regex.IsMatch(value, Constants.CssMarginPattern);
        }
    }
}
