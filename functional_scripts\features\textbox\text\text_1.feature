@component @textbox @text @text_1
Feature: the text component can receive text input
    <PERSON>enario: the text component sample page is available
        Given the user is at the home page
        When the user selects the "Text" component in the container "Input"
        Then the url ending is "textsample"

    Scenario: the text component can receive text input
        Given the user is at the home page
        And the user selects the "Text" component in the container "Input"
        When the user enters "Test data" into the Text component
        Then the Text component has the value "Test data"
