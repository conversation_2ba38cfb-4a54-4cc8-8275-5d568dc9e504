@component @concertina @concertina_7
Feature: the 'collapse others?' box is optional

Scenario: the concertina has no collapse others? button but can still collapses others. 
    Given the user is at the home page
    And the user selects the "Concertina" component in the container "Concertina"
    And the user clicks the "Usage" tab
    And the user expands the concertina by clicking on the header with the text "Collapse others"
    And the Concertina component image matches the base image "Collapse has no collapse others checkbox"
    And the user presses the collapse or expand button of header "this is header 1"
    And the user presses the collapse or expand button of header "header 2"
    And the Concertina component image matches the base image "No collapse others checkbox, section 3 only expanded"
    And concertina content "this is the content 3" is visible
    When the user presses the collapse or expand button of header "header 2"
    Then concertina content "this is the content 3" is hidden
    And concertina content "some content 2" is visible
    And the Concertina component image matches the base image "No collapse others checkbox, section 2 only expanded"