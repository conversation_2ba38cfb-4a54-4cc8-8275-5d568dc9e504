<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>grid menu collapse</title>
</head>
<style>
    body {
        padding: 0;
        margin: 0;
    }

    .header {
        grid-area: header;
    }

    .sidebar {
        grid-area: sidebar;
    }

    .main {
        grid-area: main;
    }

    .sub-main {
        grid-area: sub-main;
    }

    .right-sidebar {
        background-color: pink;
        grid-area: right-sidebar;
    }

    .footer {
        background-color: wheat;
        grid-area: footer;
    }

    .grid-container {
        display: grid;
        grid-template-areas:
            'header header header header header header'
            'sidebar main main main main main'
        ;
        background-color: #2196F3;
        gap: 0.2rem;
        height: calc(99.7vh);

        grid-template-columns: [sidebar] 15% [main] auto;
        grid-template-rows: [header] 5% [main] 95%;
    }

    .grid-container>div {
        background-color: rgba(255, 255, 255, 0.8);
        text-align: center;
        font-size: 30px;
    }

    .inner-grid-with-sidebar {
        display: grid;
        grid-template-areas:
            'sub-main right-sidebar'
            'footer footer'
        ;
        grid-template-columns: [sub-main] auto [right-sidebar] 20%;
        grid-template-rows: [sub-main] auto [footer] 10%;
        gap: 0.3rem;
    }

    .inner-grid {
        display: grid;
        grid-template-areas:
            'sub-main'
            'footer'
        ;
        grid-template-columns: [sub-main] auto;
        grid-template-rows: [sub-main] auto [footer] 10%;
        gap: 0.3rem;
    }

    .hide {
        display: none;
        width: 0;
    }

    .show {
        display: block;
    }

    /* * { border: 1px solid red;} */
</style>

<body>

    <div class="grid-container">
        <div class="header">
            header sticky
            <button onclick="toggleSideBar()">toggle r sidebar</button>
            <button onclick="hidemenu()">hide menu</button>
        </div>
        <div class="sidebar show">menu sticky</div>
        <div id="inner" class="main inner-grid-with-sidebar">
            <div class="sub-main">Main scrolls with body</div>
            <div class="right-sidebar">Right sidebar sticky - collapseable or hidden entirely</div>
            <div class="footer">footer sticky - collapseable or hidden entirely</div>
        </div>
    </div>

</body>

<script>

    let menuVisible = true;

    function hidemenu() {

        let sidebar = document.querySelector(".sidebar");

        if(menuVisible)
        {
            sidebar.classList.remove("show");
            sidebar.classList.add("hide");
            menuVisible = false;
        } else {
            sidebar.classList.remove("hide");
            sidebar.classList.add("show");
            menuVisible = true;
        }




    }


    let sideBarVisible = true;

    function toggleSideBar() {
        let inner = document.querySelector("#inner");

        if (sideBarVisible) {
            inner.classList.remove("inner-grid-with-sidebar");
            inner.classList.add("inner-grid");
            document.querySelector(".right-sidebar").remove();
            sideBarVisible = false;
        } else {
            inner.classList.remove("inner-grid");
            inner.classList.add("inner-grid-with-sidebar");
            let submain = document.querySelector("#inner > div:nth-child(1)");
            submain.insertAdjacentHTML('afterend', '<div class="right-sidebar">Right sidebar</div>')
            sideBarVisible = true;
        }



    }
</script>

</html>