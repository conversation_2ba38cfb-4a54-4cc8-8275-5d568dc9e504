.panelmenu-item {
    padding-top: 0.4rem;
    padding-bottom: 0.4rem;
    padding-right: 0.6rem;
}

.panelmenu-item-icon-text {
    font-size: 0.6rem;
    width: 35px;
    margin-left: 2px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.panelmenu-container > div:first-child {
    border-radius: var(--cctc-panelmenu-border-radius) var(--cctc-panelmenu-border-radius) 0 0;
}

.panelmenu-container > div:last-child, .panelmenu-container > div:last-child > div:last-child {
    border-radius: 0 0 var(--cctc-panelmenu-border-radius) var(--cctc-panelmenu-border-radius);
}

.panelmenu-item:hover {
    background-color: var(--cctc-panelmenu-hover-background-color);
}

.panelmenu-item-in-scope {
    padding-left: 8px;
    border-left: 5px solid var(--cctc-panelmenu-highlight-border-color);
    cursor: default;
    background-color: var(--cctc-panelmenu-active-background-color);
}

.panelmenu-item-not-in-scope {
    padding-left: 13px;
    border-left: 0;
    cursor: pointer;
}

.panelmenu-item-icon {
    margin-right: 1rem;
    font-size: large;
}

.panelmenu-item-icon-hide {
    visibility: hidden;
}

.panelmenu-item-title {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

/*tablet*/
@media (max-width: 1200px) and (min-width: 576px) {

    .panelmenu-item {
        padding-top: 0.2rem;
        padding-bottom: 0.2rem;
        padding-right: 0.2rem;
    }

    .panelmenu-item-in-scope {
        padding-left: 3px;
    }

    .panelmenu-item-not-in-scope {
        padding-left: 8px;
    }
}
