@component @confirmmodal @confirmmodal_4

Feature: the confirm result depends on which of the 2 responses are selected in the confirm modal
    Scenario: the confirm result is true when selecting the ok response 
        Given the user is at the home page
        And the user selects the "Confirm modal" component in the container "Modals"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Default positive and negative responses, Modal options classes: UI theme plus custom class defining a max width"
        When the confirm modal button is clicked
        And the confirm modal ok response is clicked
        Then the confirm result is "True"
        And the confirm result image in confirm matches the base image "confirm result true after ok response"

    Scenario: the confirm result is cancelled when selecting the cancel response 
        Given the user is at the home page
        And the user selects the "Confirm modal" component in the container "Modals"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Default positive and negative responses, Modal options classes: UI theme plus custom class defining a max width"
        When the confirm modal button is clicked
        And the confirm modal cancel response is clicked
        Then the confirm result is "cancelled"
        And the confirm result image in confirm matches the base image "confirm result cancelled after cancel response"
