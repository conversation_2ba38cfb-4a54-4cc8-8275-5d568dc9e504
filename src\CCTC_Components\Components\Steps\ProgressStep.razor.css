.selected > div > svg > text {
    stroke: var(--cctc-steps-header-color);
    fill: var(--cctc-steps-header-color);
}

.selected > div > svg > circle {
    stroke: var(--cctc-steps-icon-active-color);
    fill: var(--cctc-steps-icon-active-color);
}

.not-selected > div > svg > text {
    stroke: var(--cctc-steps-header-inactive-color);
    fill: var(--cctc-steps-header-inactive-color);
}

.not-selected > div > svg > circle {
    stroke: var(--cctc-steps-header-inactive-color);
    fill: var(--cctc-background-color);
}

.selected > div > div {
    border-bottom: 1px solid var(--cctc-steps-header-background-color);
}

.not-selected > div > div, .not-selected > div > sup {
    color: var(--cctc-steps-header-inactive-color);
}
