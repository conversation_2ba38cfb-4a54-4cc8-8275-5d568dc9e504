﻿using CCTC_Components.Components.__CCTC.Models;
using CCTC_Lib.Models.UI;
using Xunit.Abstractions;

namespace CCTC_Components.bUnit.test.Helpers
{
    /// <inheritdoc />
    public class CCTCComponentsTestContext : TestContext
    {
        readonly ITestOutputHelper? _output;

        protected CCTCComponentsTestContext()
        {
            Services.AddTestContextServices();
            AddJavascriptInvocations();
        }

        protected CCTCComponentsTestContext(ITestOutputHelper output) : this()
        {
            _output = output;
        }

        protected ITestOutputHelper Output =>
            _output
                ?? throw new NullReferenceException("ITestOutputHelper has not been intialized. " +
                                                    "Use the appropriate constructor in your test class to make use of ITestOutputHelper");

        BunitJSModuleInterop? _tooltipModule;
        BunitJSModuleInterop? _popoverModule;
        BunitJSModuleInterop? _utilsModule;

        //any javascript invocations should be added here
        //see: https://bunit.dev/docs/test-doubles/emulating-ijsruntime.html
        //NOTE: Setup<T> invocations MUST return a result, and similarly,
        //NOTE: SetupVoid invocations need to call x.SetVoidResult() to complete the task
        void AddJavascriptInvocations()
        {
            _tooltipModule = JSInterop.SetupModule("./_content/CCTC_Components/scripts/bootstrap_tooltip.js");
            _popoverModule = JSInterop.SetupModule("./_content/CCTC_Components/scripts/bootstrap_popover.js");
            _utilsModule = JSInterop.SetupModule("./_content/CCTC_Components/scripts/utils.js");
        }

        protected JSRuntimeInvocationHandler AddScrollIntoViewFromQuery() =>
            _utilsModule!.SetupVoid("scrollIntoViewFromQuery", _ => true);

        protected JSRuntimeInvocationHandler<bool> AddElementExists(bool ret) =>
            _utilsModule!.Setup<bool>("elementExists", _ => true)
                .SetResult(ret);

        protected JSRuntimeInvocationHandler<BoundingRect> AddGetElementBoundRect(BoundingRect ret) =>
            _utilsModule!.Setup<BoundingRect>("getElementBoundRect", _ => true)
                .SetResult(ret);

        protected JSRuntimeInvocationHandler<BoundingRect> AddGetElementBoundRectFromQuery(BoundingRect ret) =>
            _utilsModule!.Setup<BoundingRect>("getElementBoundRectFromQuery", _ => true)
                .SetResult(ret);

        protected JSRuntimeInvocationHandler<CursorSelection> AddGetCursorSelection(CursorSelection ret) =>
            _utilsModule!.Setup<CursorSelection>("getCursorSelection", _ => true)
                .SetResult(ret);

        protected JSRuntimeInvocationHandler<bool> AddHasElementGotFocus(bool ret) =>
            _utilsModule!.Setup<bool>("hasElementGotFocus", _ => true)
                .SetResult(ret);

        protected JSRuntimeInvocationHandler AddScrollDown() =>
            _utilsModule!.SetupVoid("scrollDown", _ => true);

        protected JSRuntimeInvocationHandler AddScrollUp() =>
            _utilsModule!.SetupVoid("scrollUp", _ => true);

        protected JSRuntimeInvocationHandler AddSetFocusOnSelector() =>
            _utilsModule!.SetupVoid("setFocusOnSelector", _ => true);

        protected JSRuntimeInvocationHandler AddAddTooltip() =>
            _tooltipModule!.SetupVoid("addTooltip", _ => true);

        protected JSRuntimeInvocationHandler AddUpdateTooltipTitle() =>
            _tooltipModule!.SetupVoid("updateTooltipTitle", _ => true);

        protected JSRuntimeInvocationHandler AddAddPopover() =>
            _popoverModule!.SetupVoid("addPopover", _ => true);

        protected JSRuntimeInvocationHandler<bool> AddIsOverflowing(bool ret) =>
            _utilsModule!.Setup<bool>("isOverflowing", _ => true)
                .SetResult(ret);

        protected JSRuntimeInvocationHandler<string> AddGetSelectedText(string ret) =>
            _utilsModule!.Setup<string>("getSelectedText", _ => true)
                .SetResult(ret);

        protected JSRuntimeInvocationHandler AddJSAlert() =>
            JSInterop.SetupVoid("alert", _ => true);
    }
}