﻿cctc-input {
    width: 100%;
}

.component-wrapper {
    display: flex;
    align-items: center;
}

.material-icons {
    color: var(--cctc-input-readonly-icon-color);
}

.checkbox-wrapper {
    width: 100%;
    display: flex;
    align-items: center;
    column-gap: 1rem;
    height: 100%;
    min-height: var(--cctc-input-height);
    padding-left: var(--cctc-input-padding-left);
    padding-right: max(var(--cctc-input-padding-right), 1.5rem);
    color: var(--cctc-input-color);
    background-color: var(--cctc-input-background-color);
    border-radius: var(--cctc-input-border-radius);
    border-color: var(--cctc-input-border-color);
    border-width: var(--cctc-input-border-width);
    border-style: var(--cctc-input-border-style);
}

.checkbox-wrapper:has(input:disabled) {
    color: var(--cctc-input-disabled-color);
    border-color: var(--cctc-input-disabled-background-color);
    background-color: var(--cctc-input-disabled-background-color);
}

.checkbox-wrapper.row {
    flex-direction: row;
}

.checkbox-wrapper.row-reverse {
    flex-direction: row-reverse;
}

.checkbox-wrapper.label-nowrap ::deep cctc-tooltip, .checkbox-wrapper.label-scroll ::deep cctc-tooltip {
    display: flex;
    min-width: 0;
}

input {
    flex: 0 0 auto;
    transform: scale(1);
    height: calc(var(--cctc-input-height) * 0.677);
    width: calc(var(--cctc-input-height) * 0.677);
}

.label-wrap label {
    display: -webkit-box;
    -webkit-line-clamp: var(--cctc-input-webkit-line-clamp);
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.label-nowrap label {
    white-space: nowrap;
    overflow-x: hidden;
    text-overflow: ellipsis;
}

.label-scroll label {
    white-space: nowrap;
    overflow-x: auto;
    scrollbar-gutter: stable;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar {
    width: 0.4rem;
    height: 0.4rem;
}

.icon-wrapper {
    align-self: start;
    margin-left: -1.35rem;
    margin-top: 0.3rem;
}

.checkbox-wrapper.row-reverse .icon-wrapper {
    order: -1;
}
