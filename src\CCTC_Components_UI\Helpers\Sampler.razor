﻿@using TextCopy
@inject IClipboard Clipboard
@inject NavigationManager NavManager
@inherits SamplerBase

<div>
    <h4 class="flex-fill">@ComponentName</h4>
    <code>@(GetComponentCustomElement(ComponentCssName, ComponentTypeName))</code>
    <hr/>

    <div class="section">
        @if (Description.Any())
        {
            <h5>Description</h5>
            foreach (var desc in Description)
            {
                <p>@((MarkupString)desc)</p>
            }
        }
    </div>

    @if (RelatedComponents is not null && RelatedComponents.Any())
    {
        <div class="section">
            <h5 class="mb-3">Related components</h5>
            <dl class="grid" style="--bs-gap:0.5rem">
                @foreach (var (rel, display, url, description) in RelatedComponents)
                {
                    <dt class="g-col-4 g-col-md-2">
                        <div class="link-to-related" @onclick="@(() => NavManager.NavigateTo(url))">@((MarkupString)display)</div>
                    </dt>
                    <dd class="g-col-3 g-col-md-2">
                        @rel type
                    </dd>
                    <dd class="g-col-5 g-col-md-8">
                        @((MarkupString)description)
                    </dd>
                }
            </dl>

        </div>
    }

    <hr/>

    @if (TabHasContent)
    {
        <div class="section">
            <Tabs @ref="@_tabs" Id="@Name" ContentHeightPixels="@ContentHeightPixels"
                  TabHeaderPlacement="@TabContentHeaderPlacement"
                  OnTabItemSelected="OnTabItemSelected">
                @if (HasExample)
                {
                    <TabItem Id="example" Header="Example">
                        <div class="tabitem-content">
                            @if (ExampleText is not null)
                            {
                                <div class="subsection">@((MarkupString)ExampleText)</div>
                            }

                            @if (ExampleTemplate is not null)
                            {
                                <div class="subsection">@ExampleTemplate</div>
                            }

                            @if (ExampleTextFragmentList is not null)
                            {
                                <div class="subsection">
                                    @foreach (var (text, fragment) in ExampleTextFragmentList)
                                    {
                                        <div class="subsubsection">
                                            <div>@((MarkupString)text)</div>
                                            <div>@fragment</div>
                                        </div>
                                    }
                                </div>
                            }

                            @if (ExampleTextList is not null)
                            {
                                <div class="subsection">
                                    @foreach (var text in ExampleTextList)
                                    {
                                        <div class="subsubsection">@((MarkupString)text)</div>
                                    }
                                </div>
                            }
                        </div>
                    </TabItem>
                }
                @if (HasUsage)
                {
                    <TabItem Id="usage" Header="Usage">
                        <div class="tabitem-content">
                            @if (UsageText is not null)
                            {
                                <div class="subsection">@((MarkupString)UsageText)</div>
                            }

                            @if (UsageTemplate is not null)
                            {
                                <div class="subsection">@UsageTemplate</div>
                            }

                            @if (UsageTextFragmentList is not null)
                            {
                                <div class="subsection">
                                    @foreach (var (text, fragment) in UsageTextFragmentList)
                                    {
                                        <div class="subsubsection">
                                            <div>@((MarkupString)text)</div>
                                            <div>@fragment</div>
                                        </div>
                                    }
                                </div>
                            }

                            @if (UsageTextList is not null)
                            {
                                <div class="subsection">
                                    @foreach (var text in UsageTextList)
                                    {
                                        <div class="subsubsection">@((MarkupString)text)</div>
                                    }
                                </div>
                            }

                            @if (UsageCodeList is not null)
                            {
                                <div class="subsection">
                                    <Concertina
                                        Expanded="false"
                                        CanCollapseOrExpandAll="true"
                                        CollapseOthersOption="CollapseOthersOption.UserOption"
                                        Id="sampler-usage-list"
                                        OnCollapseOrExpand="@OnCollapseOrExpandUsage">
                                        <HeaderContent>
                                            <h5>@ComponentName usage</h5>
                                        </HeaderContent>
                                        <ChildContent>
                                            @foreach (var (title, description, code, fragment) in UsageCodeList)
                                            {
                                                var index = UsageCodeList.IndexOf((title, description, code, fragment));
                                                TimeOut? timeOutRef = null;
                                                AddTimeOutRef(title, timeOutRef);

                                                <ConcertinaItem Id="@($"sampler-usage-list-{index}")">
                                                    <HeaderContent>
                                                        <div class="d-flex gap-4 align-items-center">
                                                            <div>@title</div>
                                                            <div @onclick:stopPropagation="true" class="material-icons copy" @onclick="@(async () => await CopyCode(title, code))">content_copy</div>
                                                            <div>
                                                                <TimeOut
                                                                    Id="@($"copy-timeout-{index}")"
                                                                    @ref="@_timeOutRefs[title]"
                                                                    ReserveSpace="true"
                                                                    ShowContentFor="TimeSpan.FromSeconds(2)"
                                                                    FadeFor="TimeSpan.FromSeconds(3)">
                                                                    <small>
                                                                        code copied to clipboard
                                                                    </small>
                                                                </TimeOut>
                                                            </div>
                                                        </div>
                                                    </HeaderContent>
                                                    <SubHeaderContent>
                                                        <small>@description</small>
                                                    </SubHeaderContent>
                                                    <ContentTemplate>
                                                        <div class="fragment">@fragment</div>
                                                        <pre class="code">@(code)</pre>
                                                    </ContentTemplate>
                                                </ConcertinaItem>
                                            }
                                        </ChildContent>
                                    </Concertina>
                                </div>
                            }
                        </div>
                    </TabItem>
                }
                @if (HasVariables)
                {
                    <TabItem Id="css-variables" Header="Css variables">
                        <div class="tabitem-content">
                            <div class="subsubsection">The following variables are used to style the component. Override these in your application to restyle.</div>

                            <hr/>
                            @foreach (var v in _variables)
                            {
                                <div class="font-monospace">
                                    <small>
                                        <code>@v</code>
                                    </small>
                                </div>
                            }
                            <br/>
                            <small>Source:<i>@ComponentVariableSource</i></small>
                        </div>
                    </TabItem>
                }
                @if (HasSubParts)
                {
                    <TabItem Id="sub-parts" Header="Component sub parts">
                        <div class="tabitem-content">

                            @if (_customElements.Any())
                            {
                                <div class="subsubsection">Logical component sub parts have custom element names;</div>
                                @foreach (var ce in _customElements)
                                {
                                    <div class="font-monospace">
                                        <small>
                                            <code>@ce</code>
                                        </small>
                                    </div>
                                }

                                <hr/>
                            }

                            @if (SubParts is not null)
                            {
                                <div class="subsubsection">The following ids are used to identify sub-parts of the component; i.e. sections within the overall component structure;</div>

                                @foreach (var s in SubParts)
                                {
                                    var content = s.Contains("@") ? $"<code>{s}</code> where i is the index of the item" : $"<code>{s}</code>";

                                    <div class="font-monospace">
                                        <small>@((MarkupString)content)</small>
                                    </div>
                                }

                                <br/>
                                <div class="subsubsection">
                                    <div class="mb-2">You can target your css to the components using a selector such as the below. Note the use of the <code>::deep</code> directive and the requirement in the component to prefix sub parts with the Id</div>
                                    <code>
                                        <pre>
::deep #{Id}{subpart} {
    color: green;
}
</pre>
                                    </code>
                                    <div class="mb-2">e.g.</div>
                                    <code>
                                        <pre>
::deep #lister-sample-items-container {
    color: green;
}
</pre>
                                    </code>
                                </div>
                            }

                        </div>
                    </TabItem>
                }
                @if (HasConfiguration)
                {
                    <TabItem Id="configuration" Header="Configuration">
                        <div class="tabitem-content">
                            <div class="subsubsection">@ComponentName configuration details.</div>

                            <hr />
                            @foreach (var (term, description) in Configuration!)
                            {
                                <small>
                                    <span>
                                        <em>@((MarkupString)term)</em>
                                    </span> - @((MarkupString)description)
                                </small>
                            }
                        </div>
                    </TabItem>
                }
                @if (HasKeyboardControls)
                {
                    <TabItem Id="keyboardcontrols" Header="Keyboard controls">
                        <div class="tabitem-content">
                            <div class="subsubsection">In order to interact with @ComponentName via the keyboard, refer to the following.</div>

                            <hr />
                            @foreach (var keyboardControl in KeyboardControls!)
                            {
                                <small>
                                    <span>@((MarkupString)keyboardControl)</span>
                                </small>
                            }
                        </div>
                    </TabItem>
                }
            </Tabs>
            <hr/>
    </div>
    }
</div>

@if (Features is not null && Features.Any())
{
    <div class="section">
        <h5 class="mb-3">Features and functionality</h5>
        <dl class="grid" style="--bs-gap:0.5rem">
            @foreach (var (term, description) in Features)
            {
                <dt class="g-col-5 g-col-md-2">
                    <em>@((MarkupString)term)</em>
                </dt>
                <dd class="g-col-7 g-col-md-10">@((MarkupString)description)</dd>
            }
        </dl>

    </div>
}

@if (Gotchas is not null && Gotchas.Any())
{
    <div class="section">
        <h5>Gotchas</h5>
        @foreach (var (term, description) in Gotchas)
        {
            <div>
                -
                <span>
                    <em>@((MarkupString)term)</em>
                </span> - @((MarkupString)description)
            </div>
        }
    </div>
}

@if (Tips is not null && Tips.Any())
{
    <div class="section">
        <h5>Tips</h5>
        @foreach (var (term, description) in Tips)
        {
            <div>
                -
                <span>
                    <em>@((MarkupString)term)</em>
                </span> - @((MarkupString)description)
            </div>
        }
    </div>
}

@if (_allResources.Any())
{
    <div class="section">
        <h5>Additional resources</h5>
        @foreach (var (displayText, uri) in _allResources)
        {
            <div><a href="@uri.ToString()" target="_blank">@displayText</a></div>
        }
    </div>
}

@code {

    Tabs? _tabs;

    [Parameter, EditorRequired]
    public required string ComponentName { get; set; }

    [Parameter, EditorRequired]
    public required string ComponentCssName { get; set; }

    [Parameter]
    public string? ComponentTypeName { get; set; }

    [Parameter]
    public TabHeaderPlacement TabContentHeaderPlacement { get; set; } = TabHeaderPlacement.Top;

    [Parameter]
    public RenderFragment? ExampleTemplate { get; set; }

    [Parameter]
    public List<(string text, RenderFragment fragment)>? ExampleTextFragmentList { get; set; }

    [Parameter]
    public List<string>? ExampleTextList { get; set; }

    [Parameter]
    public string? ExampleText { get; set; }

    [Parameter]
    public int ContentHeightPixels { get; set; } = 150;

    [Parameter]
    public RenderFragment? UsageTemplate { get; set; }

    [Parameter]
    public List<(string text, RenderFragment fragment)>? UsageTextFragmentList { get; set; }

    [Parameter]
    public List<string>? UsageTextList { get; set; }

    [Parameter]
    public string? UsageText { get; set; }

    [Parameter]
    public List<(string title, string description, string code, RenderFragment fragment)>? UsageCodeList { get; set; }

    ///<summary>
    /// List of sub-parts in the component that can be targeted by css
    /// </summary>
    [Parameter]
    public List<string>? SubParts { get; set; }

    [Parameter]
    public List<(string term, string description)>? Configuration { get; set; }

    [Parameter]
    public List<string>? KeyboardControls { get; set; }

    [Parameter, EditorRequired]
    public required List<string> Description { get; set; } = new();

    [Parameter]
    public List<(Relation relation, string display, string url, string description)>? RelatedComponents { get; set; }

    [Parameter]
    public List<(string term, string description)>? Features { get; set; }

    [Parameter]
    public List<(string term, string description)>? Gotchas { get; set; }

    [Parameter]
    public List<(string term, string description)>? Tips { get; set; }

    [Parameter]
    public List<(string displayText, Uri link)>? FurtherResources { get; set; }

    [Parameter]
    public EventCallback<TabItem> OnTabItemSelected { get; set; }

    [Parameter]
    public EventCallback<ConcertinaItem> OnCollapseOrExpandUsage { get; set; }

    string Name => $"{ComponentName}-sampler";
    Dictionary<string, TimeOut?> _timeOutRefs = new();

    List<string> _variables = new();
    List<string> _customElements = new();
    List<(string displayText, Uri link)> _allResources = new();

    bool HasExample =>
        ExampleText is not null || ExampleTemplate is not null || ExampleTextFragmentList is not null
        || ExampleTextList is not null;

    bool HasUsage =>
        UsageText is not null || UsageTemplate is not null || UsageTextFragmentList is not null
        || UsageTextList is not null || UsageCodeList is not null;

    bool HasVariables => _variables.Any();

    bool HasSubParts => SubParts is not null || _customElements.Any();

    bool HasConfiguration => Configuration is not null;

    bool HasKeyboardControls => KeyboardControls is not null;

    bool TabHasContent => HasExample || HasUsage || HasVariables || HasSubParts || HasConfiguration || HasKeyboardControls;

    async Task GetCssVariables()
    {
        _variables = await GetCssVariables(ComponentCssName);
    }

    async Task GetCustomElements()
    {
        _customElements = await GetCustomElements(ComponentCssName, ComponentTypeName);
    }

    Task CopyCode(string timeOutKey, string code)
    {
        _timeOutRefs[timeOutKey]?.Initiate();
        return Clipboard.SetTextAsync(code);
    }

    protected override async Task OnInitializedAsync()
    {
        CheckConfig();

        await GetCssVariables();
        await GetCustomElements();

        AddComponentDocumentationResource();

        if (FurtherResources is not null)
        {
            AdditionalResources.AddRange(FurtherResources);
        }

        _allResources = AdditionalResources;
    }

    void CheckConfig()
    {
        if (UsageCodeList?.Select(x => x.title).Distinct().Count() != UsageCodeList?.Count)
        {
            throw new ArgumentException("All titles in the Usage Code List should be unique");
        }
    }

    void AddTimeOutRef(string timeOutKey, TimeOut? timeOutRef)
    {
        _timeOutRefs.TryAdd(timeOutKey, timeOutRef);
    }

    void AddComponentDocumentationResource()
    {
        var docSource = ($"Api documentation for {ComponentName}", new Uri($"{Constants.DocsBaseUrl}{Constants.ComponentsNamespace}.{ComponentName}.html"));
        AdditionalResources.Add(docSource);
    }


}