@component @steps @steps_2
Feature: previously entered data in later steps can optionally be wiped
    Scenario: As a user steps backwards through the steps, previously entered data in later steps is wiped
        Given the user is at the home page
        And the user selects the "Steps" component in the container "Steps"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Required data"
        And the current selected dropdown option has the text ""
        And the user clicks on the current selected dropdown option
        And the user clicks on the dropdown option with the text "ASUS Prime AP201 - Black"
        And the current selected dropdown option has the text "ASUS Prime AP201 - Black"
        And the "Case" step is selected
        And the "Motherboard" step is not selected
        And the user clicks on the next button
        And the "Case" step is not selected
        And the "Motherboard" step is selected
        When the user clicks on the previous button
        And the "Case" step is selected
        And the "Motherboard" step is not selected
        Then the current selected dropdown option has the text ""

    Scenario: As a user steps backwards through the steps, previously entered data in later steps is not wiped
        Given the user is at the home page
        And the user selects the "Steps" component in the container "Steps"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Updateable name, without wiping data when moving back"
        And the current selected dropdown option has the text ""
        And the user clicks on the current selected dropdown option
        When the user clicks on the dropdown option with the text "Freelance"
        And the current selected dropdown option has the text "Freelance"
        And the "Job Type" step is selected
        Then the "Share your Portfolio" step is not selected
        When the user clicks on the next button
        And the "Job Type" step is not selected
        Then the "Share your Portfolio" step is selected
        And the user clicks on the previous button
        And the "Job Type" step is selected
        And the current selected dropdown option has the text "Freelance"




