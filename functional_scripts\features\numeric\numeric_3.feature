@component @numeric @numeric_3
Feature: the numeric component input can be constrained by preventing whitespace (with a configurable response delay) or setting a max length
    Scenario: the numeric component can prevent whitespace
        Given the user is at the home page
        Given the user selects the "Numeric" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: -##00"
        When the user focusses on the Numeric component
        And the user presses the backspace key 3 times
        Then the Numeric component has the value "123"

    Scenario: the numeric component can set a max length
        Given the user is at the home page
        Given the user selects the "Numeric" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "No format with a null initial value, redact text, allows whitespace, max length"
        When the user enters "12345678" into the Text component
        Then the Text component has the value "1234567"
