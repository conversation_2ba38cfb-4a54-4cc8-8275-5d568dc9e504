// Global readiness state
window.CCTCAppState = {
  isReady: false,
  readinessCallbacks: []
};

// Function to set app as ready
function setAppReady() {
  window.CCTCAppState.isReady = true;
  
  // Execute any pending callbacks
  window.CCTCAppState.readinessCallbacks.forEach(callback => callback());
  window.CCTCAppState.readinessCallbacks = [];
  
  console.log('[CCTC_Components_UI] Application is now ready - ' + new Date().toISOString());
}

// Function to wait for readiness (returns a promise)
function waitForAppReady() {
  if (window.CCTCAppState.isReady) {
    console.log('[CCTC_Components_UI] App was already ready when check started');
    return Promise.resolve();
  } else {
    console.log('[CCTC_Components_UI] Waiting for app to become ready...');
    return new Promise((resolve) => {
      window.CCTCAppState.readinessCallbacks.push(() => {
        console.log('[CCTC_Components_UI] Wait completed - app is now ready');
        resolve();
      });
    });
  }
}

// Expose functions to be called from .NET
window.CCTCAppReadiness = {
  setAppReady: setAppReady,
  waitForAppReady: waitForAppReady
};