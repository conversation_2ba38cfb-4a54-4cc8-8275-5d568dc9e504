@component @infoicon @infoicon_1
Feature: the info icon can display a tooltip and can register a click
    Sc<PERSON><PERSON>: the infoicon component sample page is available
        Given the user is at the home page
        When the user selects the "Info icon" component
        Then the url ending is "infoiconsample"
        And the infoicon component image matches the base image "infoicon"

    Scenario: the info icon can display a tooltip
        Given the user is at the home page
        And the user selects the "Info icon" component
        When the infoicon has a tooltip
        Then the info icon tooltip has text "some tooltip message"

    Scenario: the info icon registers a click when being clicked on
        Given the user is at the home page
        And the user selects the "Info icon" component
        And the info icon button counter number is 0
        And the user clicks the infoicon component
        And the info icon button counter number is 1
        And the user clicks the infoicon component
        And the info icon button counter number is 2
        When the user clicks the infoicon component
        Then the info icon button counter number is 3
