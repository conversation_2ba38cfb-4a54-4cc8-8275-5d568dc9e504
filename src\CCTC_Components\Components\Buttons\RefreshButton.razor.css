cctc-button {
    width: fit-content;
}

.button-wrapper {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    user-select: none;
    padding: var(--cctc-button-y-padding) var(--cctc-button-x-padding);
}

.button-wrapper.button-border {
    border-radius: var(--cctc-button-border-radius);
    border-color: var(--cctc-button-border-color);
    border-width: var(--cctc-button-border-width);
    border-style: var(--cctc-button-border-style);
}

.button-wrapper:hover.button-border {
    border-color: var(--cctc-button-border-hover-color);
}

.button-wrapper:hover {
    cursor: pointer;
}

.button-wrapper.icon-right {
    justify-content: start;
    flex-direction: row-reverse;
}

.button-wrapper .button-icon {
    color: var(--cctc-button-icon-color);
}

.button-wrapper:hover .button-icon {
    color: var(--cctc-button-icon-hover-color);
}

.button-wrapper .button-text {
    color: var(--cctc-button-color);
}

.button-wrapper:hover .button-text {
    color: var(--cctc-button-hover-color);
}

.button-wrapper.disabled {
    pointer-events: none;
}

.button-wrapper .confirmation-icon-wrapper {
    display: flex;
    align-items: center;
}

.button-wrapper .confirmation-icon {
    font-size: 1.3rem;
    color: var(--cctc-button-icon-color);
}

.button-wrapper:hover .confirmation-icon {
    color: var(--cctc-button-icon-hover-color);
}