import { ICustomWorld } from '../support/custom-world';
import { compareToBaseImage } from '../utils/compareImages';
import * as Helpers from '../support/helper-functions';
import { When, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';

When(
  'the user clicks the timeout component initiate button',
  async function (this: ICustomWorld) {
    const initiateButton = Helpers.getSamplerTabsSelectedContent(this)
      .locator('cctc-concertina-content[style*="display: block"] .initiate')
      .or(Helpers.getSamplerTabsSelectedContent(this).locator('.initiate'))
      .last();
    await initiateButton.click();
  }
);

When(
  'the user waits for {int} milliseconds',
  async function (this: ICustomWorld, milliseconds: number) {
    await this.page!.waitForTimeout(milliseconds);
  }
);

Then(
  'the timeout component content is visible',
  async function (this: ICustomWorld) {
    // Check in expanded concertina first, then main example
    const visibleTimeout = Helpers.getSamplerTabsSelectedContent(this)
      .locator('cctc-concertina-content[style*="display: block"] cctc-timeout > div.visible')
      .or(Helpers.getSamplerTabsSelectedContent(this).locator('cctc-timeout > div.visible'))
      .last();
    await expect(visibleTimeout).toBeVisible();
  }
);

Then(
  'the timeout component content is not visible',
  async function (this: ICustomWorld) {
    // This step should FAIL if content IS visible
    const timeoutContent = Helpers.getSamplerTabsSelectedContent(this)
      .locator('cctc-concertina-content[style*="display: block"] cctc-timeout > div')
      .or(Helpers.getSamplerTabsSelectedContent(this).locator('cctc-timeout > div'))
      .last();
    // Check that the class attribute does NOT contain the exact word 'visible'
    const classAttribute = await timeoutContent.getAttribute('class');
    if (classAttribute && classAttribute.split(' ').includes('visible')) {
      throw new Error(`Expected content to NOT be visible, but element has class 'visible'. Classes: ${classAttribute}`);
    }
    // Also ensure the element is not visually visible to users
    await expect(timeoutContent).not.toBeVisible();
  }
);

Then(
  /^the timeout component (reserves space|does not reserve space)$/,
  async function (this: ICustomWorld, spaceReservation: string) {
    const expectedClass = spaceReservation === 'reserves space' ? 'reserve' : 'no-reserve';
    // Check in expanded concertina first, then main example
    const timeoutDiv = Helpers.getSamplerTabsSelectedContent(this)
      .locator(`cctc-concertina-content[style*="display: block"] cctc-timeout > div.${expectedClass}`)
      .or(Helpers.getSamplerTabsSelectedContent(this).locator(`cctc-timeout > div.${expectedClass}`))
      .last();
    await expect(timeoutDiv).toHaveCount(1);
  }
);

Then(
  'the timeout component is fading',
  async function (this: ICustomWorld) {
    // Check in expanded concertina first, then main example
    const fadingTimeout = Helpers.getSamplerTabsSelectedContent(this)
      .locator('cctc-concertina-content[style*="display: block"] cctc-timeout > div.timeout-fade')
      .or(Helpers.getSamplerTabsSelectedContent(this).locator('cctc-timeout > div.timeout-fade'))
      .last();
    await expect(fadingTimeout).toBeVisible();
  }
);

Then(
  'the timeout component is not fading',
  async function (this: ICustomWorld) {
    // Check that the element does NOT have the timeout-fade class
    const timeoutContainer = Helpers.getSamplerTabsSelectedContent(this)
      .locator('cctc-concertina-content[style*="display: block"] cctc-timeout > div')
      .or(Helpers.getSamplerTabsSelectedContent(this).locator('cctc-timeout > div'))
      .last();
    const classAttribute = await timeoutContainer.getAttribute('class');
    if (classAttribute && classAttribute.split(' ').includes('timeout-fade')) {
      throw new Error(`Expected element to NOT be fading, but it has 'timeout-fade' class. Classes: ${classAttribute}`);
    }
  }
);

Then(
  'the timeout component displays content',
  async function (this: ICustomWorld) {
    // Check in expanded concertina first, then main example
    const timeoutComponent = Helpers.getSamplerTabsSelectedContent(this)
      .locator('cctc-concertina-content[style*="display: block"] cctc-timeout')
      .or(Helpers.getSamplerTabsSelectedContent(this).locator('cctc-timeout'))
      .last();
    // Test that the component is displaying content (visible state)
    await expect(timeoutComponent).toBeVisible();
    // Verify content is in the visible state (using semantic CSS class check like other components)
    const contentContainer = timeoutComponent.locator('.visible, .timeout-content[style*="visible"], [class*="visible"]').first();
    await expect(contentContainer).toBeAttached();
  }
);

Then(
  'the timeout component image matches the base image {string}',
  async function (this: ICustomWorld, name: string) {
    // Target timeout in expanded concertina first, then main example
    const timeoutComponent = Helpers.getSamplerTabsSelectedContent(this)
      .locator('cctc-concertina-content[style*="display: block"] cctc-timeout')
      .or(Helpers.getSamplerTabsSelectedContent(this).locator('cctc-timeout'))
      .last();
    const screenshot = await Helpers.tryGetStableScreenshot(timeoutComponent);
    await compareToBaseImage(this, name, screenshot);
  }
);

When(
  'the user clicks the timeout component stop button',
  async function (this: ICustomWorld) {
    // Try expanded concertina first (for Usage tab), then fall back to main example
    const stopButton = Helpers.getSamplerTabsSelectedContent(this)
      .locator('cctc-concertina-content[style*="display: block"] .stop')
      .or(Helpers.getSamplerTabsSelectedContent(this).locator('.stop'))
      .last();
    await stopButton.click();
  }
); 