import { ICustomWorld } from '../support/custom-world';
import * as TextboxRoleFunctions from '../support/step-functions/textbox-role-functions';
import { When, Then } from '@cucumber/cucumber';

When('the user focusses on the Numeric component', async function (this: ICustomWorld) {
  await TextboxRoleFunctions.focus(this);
});

When(
  'the user enters {string} into the Numeric component',
  async function (this: ICustomWorld, value: string) {
    await TextboxRoleFunctions.inputText(this, value);
  }
);

Then(
  'the Numeric component has the value {string}',
  async function (this: ICustomWorld, expectedValue: string) {
    await TextboxRoleFunctions.assertHasValue(this, expectedValue);
  }
);

Then(
  'the Numeric component has the stable value {string}',
  async function (this: ICustomWorld, expectedValue: string) {
    await TextboxRoleFunctions.assertHasStableValue(this, expectedValue, 1000);
  }
);

Then(
  'the Numeric component has the placeholder {string}',
  async function (this: ICustomWorld, placeholderText: string) {
    await TextboxRoleFunctions.assertHasPlaceholder(this, placeholderText);
  }
);

Then(
  'the Numeric component image matches the base image {string}',
  async function (this: ICustomWorld, name: string) {
    await TextboxRoleFunctions.assertImageMatchesBaseImage(
      this,
      'cctc-input[data-cctc-input-type="numeric"]',
      name
    );
  }
);

Then('the Numeric component is disabled', async function (this: ICustomWorld) {
  await TextboxRoleFunctions.assertIsDisabled(this);
});

Then('the Numeric component is not editable', async function (this: ICustomWorld) {
  await TextboxRoleFunctions.assertIsNotEditable(this);
});
