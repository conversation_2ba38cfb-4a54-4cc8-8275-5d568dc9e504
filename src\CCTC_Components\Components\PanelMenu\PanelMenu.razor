﻿@using Microsoft.AspNetCore.Components.Rendering
@using CCTC_Components.Components.PanelMenu
@using CCTC_Components.Components.Tabs
@inject NavigationManager NavManager
@inherits CCTC_Components.Components.__CCTC.CCTCBase
@implements IDisposable

<CascadingValue Value="this">
    <cctc-panelmenu id="@Id" class="@CssClass" style="@Style" data-author="cctc">
        @if (CanUserCollapse)
        {
            <div class="panelmenu-collapse" @onclick="OnCollapse">
                <div class="material-icons" style="font-size: small">@(IsCollapsed ? "keyboard_double_arrow_right" : "keyboard_double_arrow_left")</div>
            </div>
        }

        <div>
            @ChildContent
        </div>
    </cctc-panelmenu>
</CascadingValue>

@code {

    /// <summary>
    /// The panel menu content
    /// </summary>
    [Parameter, EditorRequired]
    public required RenderFragment ChildContent { get; set; }

    /// <summary>
    /// When true, show only the icons and no text.
    /// </summary>
    [Parameter]
    public bool IconsOnly { get; set; }

    /// <summary>
    /// When true, shows the icon allowing the user to manually collapse the menu
    /// </summary>
    [Parameter]
    public bool CanUserCollapse { get; set; }

    /// <summary>
    /// Whether to allow many headers to be expanded (Multi) or collapse others on selected (Single)
    /// </summary>
    [Parameter]
    public PanelMenuExpand PanelMenuExpand { get; set; } = PanelMenuExpand.Multi;

    ///<summary>
    /// Predicate to decide how to match the location to the path to determine whether the menu item is in scope
    /// Defaults to (tup.location, tup.path) => location.EndsWith(path)
    /// </summary>
    [Parameter]
    public Predicate<(string location, string path)>? MatchPathPred { get; set; }

    /// <summary>
    /// A callback that is true when the panel menu was collapsed, or false when expanded
    /// </summary>
    [Parameter]
    public EventCallback<bool> PanelMenuWasCollapsed { get; set; }

    void OnCollapse()
    {
        IsCollapsed = !IsCollapsed;
        PanelMenuWasCollapsed.InvokeAsync(IsCollapsed);
        StateHasChanged();
    }

    /// <summary>
    /// True when the panel menu is collapsed and should show icons only
    /// </summary>
    public bool IsCollapsed { get; private set; }

    List<PanelMenuHeader> _children = new();

    /// <summary>
    /// Registers a PanelMenuHeader i.e. a direct child, first checking whether the item already exists
    /// and ignoring it if it does
    /// </summary>
    /// <param name="child">The PanelMenuHeader to register</param>
    public void RegisterChild(PanelMenuHeader child)
    {
        if (!_children.Exists(r => r.Id == child.Id))
        {
            _children.Add(child);
        }
    }

    List<PanelMenuItem> _grandChildren = new();

    /// <summary>
    /// Registers a PanelMenuItem. These items can be children of the PanelMenuHeader or a direct
    /// (grand)child of the PanelMenu. If the the item already exists it is not added again.
    /// </summary>
    /// <param name="grandChild">The PanelMenuItem to register</param>
    public void RegisterGrandChild(PanelMenuItem grandChild)
    {
        if (!_grandChildren.Exists(r => r.Id == grandChild.Id))
        {
            _grandChildren.Add(grandChild);
        }
    }

    /// <summary>
    /// Selects the item using the header id
    /// </summary>
    /// <param name="selectedHeaderId"></param>
    public void HandleHeaderSelected(string selectedHeaderId)
    {
        foreach (var panelMenuHeader in _children)
        {
            panelMenuHeader.ForceCollapseUnlessIsSelected(selectedHeaderId);
        }
    }

    /// <inheritdoc />
    protected override void OnInitialized()
    {
        IsCollapsed = IconsOnly;
    }

    /// <inheritdoc />
    public void Dispose()
    {
        foreach (var child in _children)
        {
            child.Dispose();
        }

        foreach (var grandChild in _grandChildren)
        {
            grandChild.Dispose();
        }
    }
}