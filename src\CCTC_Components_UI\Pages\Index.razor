﻿@page "/"
@inject NavigationManager NavManager

<section class="main-section">
    <h1>CCTC Blazor Components</h1>
    <h3>A set of reusable Blazor components available via NuGet</h3>
    <div class="button-container">
        <Button
            Id="start"
            ButtonText="Get Started"
            ButtonIcon="chevron_right"
            StyledWithBorder="true"
            OnClick="GetStartedClick">
        </Button>
        <a href="https://github.com/CCTC-team/CCTC_Components" target="_blank">
            <img class="github-logo" src="@MainLayout?.GitHubLogoSource" alt="github repo" />
        </a>
    </div>
</section>

@code {

    [CascadingParameter]
    public MainLayout? MainLayout { get; set; }

    void GetStartedClick()
    {
        NavManager.NavigateTo("/get-started");
    }
}