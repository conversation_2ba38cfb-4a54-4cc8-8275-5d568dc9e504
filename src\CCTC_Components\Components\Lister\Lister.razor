@using System.Reactive.Linq
@using CCTC_Components.Components.TextBox.TextInteraction
@using CCTC_Lib.Extensions.Collections
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using CCTC_Components.Components.InfoIcon
@using CCTC_Lib.Contracts.Interaction
@using CCTC_Lib.Contracts.Data
@using CCTC_Lib.Enums.Data
@using CCTC_Lib.Enums.UI
@using Common.Helpers
@using System.Reactive.Threading.Tasks
@using CCTC_Lib.Contracts.Reactive
@using CCTC_Lib.Models.UI
@using Microsoft.Extensions.Logging

@implements IAsyncDisposable
@implements IDisposable
@inject IDelayService DelayService
@inject ILogger<Lister<TData>> Logger
@inject ISchedulerProvider SchedulerProvider
@inherits CCTC_Components.Components.__CCTC.CCTCBase

@typeparam TData where TData : IUniqueIdentity<string>, ISortable, ISearchable

<cctc-lister id="@Id" class="@CssClass" style="@Style" data-author="cctc" tabindex="@TabIndex">
    @if (ReadOnly && !HideReadOnlyIcon && ReadOnlyIconPlacement is ListerReadOnlyIconPlacement.TopRight)
    {
        @ReadOnlyLock
    }

    @if (CanSelectAll || CanFilter || CanCancelThenRefresh)
    {
        <cctc-lister-filter-header class="d-flex flex-row align-items-center">
            @if (SelectionType == ListerSelectionType.Multiple && CanSelectAll && !Disabled && !ReadOnly)
            {
                <div class="me-4 d-flex align-items-center">
                    <div class="me-2">
                        <InfoIcon
                            Id=@($"{Id}-select-all-info-icon")
                            InfoType="@(InfoType.Tooltip)"
                            Content="When filtering is applied, selecting or deselecting items only applies to items in the filtered list"
                            ImageIcon="info"
                            Size="@(Size.XLarge)">
                        </InfoIcon>
                    </div>

                    <small id="@Id-select-all" style="width: 4rem; cursor: pointer" @onclick="SelectAllChanged">
                        @_selectAllLabel
                    </small>
                </div>
            }

            @if (CanFilter && !Disabled)
            {
                var textName = $"{Id}-filter-input-text";

                <div id="@Id-filter-input-container" class="d-flex align-items-center w-100">
                    <div class="material-icons">
                        @FilterIcon
                    </div>

                    @* Text component throttle set to 2000 to allow the user enough time to write the filter before loading is initiated. The text component is readonly during load *@
                    <div class="mx-2 flex-grow-1" tabindex="0">
                        <Text
                            @ref="_textFilter"
                            Id="@textName"
                            Value="@_filterText"
                            ValueChanged="FilterValueChanged"
                            ThrottleMs="@FilterThrottleMs"
                            PreventWhitespace="false"
                            Placeholder="@FilterPlaceholder"
                            Disabled="@Disabled"
                            ReadOnly="@_working"
                            HideReadOnlyIcon="true"
                            Interaction="TextInteractionCallback">
                        </Text>
                    </div>

                </div>
            }

            @if (CanCancelThenRefresh)
            {
                const string cls = "material-icons cancel-resume-load ms-auto me-2";
                if (_working && !_isCancelled)
                {
                    <div id="@Id-cancel-button" class="@cls visible" @onclick="OnCancel">close</div>
                }
                else if (_isCancelled)
                {
                    <div id="@Id-refresh-button" class="@cls visible" @onclick="OnRefresh">refresh</div>
                }
                else
                {
                    <div id="@Id-cancel-refresh-placeholder" class="@cls cancel-resume-placeholder invisible"></div>
                }
            }
        </cctc-lister-filter-header>

        if (ListHeader is not null)
        {
            <cctc-lister-items-header>
                @ListHeader
            </cctc-lister-items-header>
        }
    }

    @{ var addLineToBase = CountDisplay == ListerCountDisplay.None ? "" : "border-bottom: 1px solid grey;"; }

    <cctc-lister-items-container id="@Id-items-container" class="items-container"
            style="height: @($"{ItemsContainerHeightPixels}px;") @addLineToBase"
            tabindex="@(FocusItemOption == FocusItemOption.None ? -1 : 0)"
            @onkeydown:stopPropagation="true"
            @onkeydown:preventDefault="true"
            @onkeydown="@ListerKeyDown"
            @ref="@_listerItemsContainerElementRef">
        @if (!UsesDataProvider && ShowLoadingDisplay && (!_isLoaded || _isFiltering))
        {
            <div loading-template>
                @_loadingTemplate
            </div>
        }

        @if (_isLoaded && !_isFiltering)
        {
            @if (UsesGivenData)
            {
                if (_data is not null)
                {
                    if (_data.Any())
                    {
                        if (Virtualize)
                        {
                            @AllItemsVirtualization
                        }
                        else
                        {
                            @AllItemsNotVirtualized
                        }
                    }
                    else
                    {
                        @_emptyListTemplate
                    }
                }
            }

            @if (UsesDataProvider)
            {
                @ItemsProviderVirtualization
            }

            @if (UsesDataLoading)
            {
                if (_totalCount.HasValue && _totalCount.Value != 0)
                {
                    if (Virtualize)
                    {
                        @AllItemsVirtualization
                    }
                    else
                    {
                        @AllItemsNotVirtualized
                    }
                }
                else
                {
                    @_emptyListTemplate
                }
            }
        }
    </cctc-lister-items-container>

    @{ var displayClass = ReadOnlyIconPlacement is ListerReadOnlyIconPlacement.Start ? "display-text-wrapper-between" : "display-text-wrapper-end"; }
    <cctc-lister-display-counts class="ms-1 my-2 me-1 px-2 @displayClass" style="min-height: 2rem;">
        @if (ReadOnly && !HideReadOnlyIcon && ReadOnlyIconPlacement is ListerReadOnlyIconPlacement.Start)
        {
            @ReadOnlyLock
        }

        @if (CountDisplay != ListerCountDisplay.None || ShowLoadingDisplay)
        {
            var alignRight = ReadOnly ? "" : "ms-auto";

            <div class="d-flex align-items-center @alignRight">
                @if (ShowLoadingDisplay && _working && UsesDataProvider)
                {
                    <div working-template id="@Id-working-template" class="me-3">@WorkingTemplate</div>
                }
                else if (CountDisplay != ListerCountDisplay.None)
                {
                    var countDisplay =
                        _isCancelled
                            ? "cancelled"
                            : $"{GetCountsForDisplay()} {CountDisplayPostText}".Trim();

                    <div id="@Id-display-counts">@countDisplay</div>
                }
            </div>
        }

        @if (ReadOnly && !HideReadOnlyIcon && ReadOnlyIconPlacement is ListerReadOnlyIconPlacement.End)
        {
            @ReadOnlyLock
        }
    </cctc-lister-display-counts>
</cctc-lister>

@code {

    Virtualize<ListerItem<TData>>? _virtualizeComponent;

    #region StateVars

    int? _totalCount;
    int? _includedCount;
    int? _selectedCount;

    //provides the current focus for the lister. This is used to provide highlighting for an item
    //if focussing is not allowed (due to FocusItemOption being None), this value is unused and
    //should remain at -1.
    //the focus should take into account any filtering
    int _currFocusIndex = -1;

    bool _working;
    bool _isLoaded;
    bool _isCancelled;

    string GetCountsForDisplay()
    {
        return (CountDisplay, _totalCount, _includedCount, _selectedCount) switch
        {
            (ListerCountDisplay.None, _, _, _) => "",
            (_, null, _, _) => "",
            (ListerCountDisplay.TotalOnly, _, _, _) => $"{_totalCount}",
            (ListerCountDisplay.FilterAndTotal, _, null, _) => $"{_totalCount} of {_totalCount}",
            (ListerCountDisplay.FilterAndTotal, _, not null, _) => $"{_includedCount} of {_totalCount}",
            (ListerCountDisplay.SelectedAndTotal, _, _, null) => $"0 selected of {_totalCount}",
            (ListerCountDisplay.SelectedAndTotal, _, _, not null) => $"{_selectedCount} selected of {_totalCount}",
            (ListerCountDisplay.FilteredAndTotalAndSelected, _, null, null) => $"{_totalCount} of {_totalCount} (0 selected)",
            (ListerCountDisplay.FilteredAndTotalAndSelected, _, null, not null) => $"{_totalCount} of {_totalCount} ({_selectedCount} selected)",
            (ListerCountDisplay.FilteredAndTotalAndSelected, _, not null, null) => $"{_includedCount} of {_totalCount} (0 selected)",
            (ListerCountDisplay.FilteredAndTotalAndSelected, _, not null, not null) => $"{_includedCount} of {_totalCount} ({_selectedCount} selected)",
            _ => throw new ArgumentOutOfRangeException()
        };
    }

    #endregion StateVars

    #region InitParams

    /// <summary>
    /// The template to display within a lister row
    /// </summary>
    [Parameter, EditorRequired]
    public required RenderFragment<TData> Template { get; set; }

    /// <summary>
    /// The template to show whilst waiting for the list to render.
    /// It is only visible when a control or component on the page is causing a long render time i.e. not virtualizing a lister and loading a lot of elements.
    /// Uses the DefaultLoadingTemplate unless given
    /// </summary>
    [Parameter]
    public RenderFragment? LoadingTemplate { get; set; }

    /// <summary>
    /// The template to show if the list is empty
    /// Uses the DefaultEmptyListTemplate unless given
    /// </summary>
    [Parameter]
    public RenderFragment? EmptyListTemplate { get; set; }

    /// <summary>
    /// Allow virtualization - defaults to true
    /// </summary>
    [Parameter]
    public bool Virtualize { get; set; } = true;

    /// <summary>
    /// Data is the content for the lister on set up when using pre-loaded data
    /// </summary>
    [Parameter]
    public IList<TData>? Data { get; set; }

    /// <summary>
    /// Used for virtualization to determine the height of each row
    /// </summary>
    [Parameter]
    public float ItemSizePixels { get; set; } = 50f;

    /// <summary>
    /// Used for virtualization - the higher the number the more redundant ui is
    /// generated but less calls to db when items are provided on request
    /// </summary>
    /// <remarks>The formula to work out how many items are rendered (but not necessarily visible) when
    /// virtualizing is: ((OverScanCount * 2) + number items on screen [ItemsContainerHeight / ItemSizePixels] + 3</remarks>
    [Parameter]
    public int OverscanCount { get; set; }

    /// <summary>
    /// The <see cref="IItemsService{T}"/> to provide data on request
    /// </summary>
    [Parameter]
    public IItemsService<TData>? ItemsService { get; set; }

    /// <summary>
    /// The loading func that will load the data on initialisation of the list
    /// </summary>
    [Parameter]
    public Func<CancellationToken, Task<List<TData>>>? ListerLoadingFunc { get; set; }

    /// <summary>
    /// Items in the list can be reordered if true
    /// </summary>
    [Parameter]
    public bool CanReOrder { get; set; }

    /// <summary>
    /// Items in the list can be deleted if true
    /// </summary>
    [Parameter]
    public bool CanDeleteItems { get; set; }

    /// <summary>
    /// The template used to show a header row above the listed items
    /// </summary>
    [Parameter]
    public RenderFragment? ListHeader { get; set; }

    #region ReadOnly and Disabling

    /// <summary>
    /// The Lister is disabled if true
    /// </summary>
    [Parameter]
    public bool Disabled { get; set; }

    /// <summary>
    /// The Lister is read-only if true
    /// </summary>
    [Parameter]
    public bool ReadOnly { get; set; }

    /// <summary>
    /// Hides the read-only icon if true
    /// </summary>
    [Parameter]
    public bool HideReadOnlyIcon { get; set; }

    /// <summary>
    /// Sets the position of the read-only icon
    /// </summary>
    [Parameter]
    public ListerReadOnlyIconPlacement ReadOnlyIconPlacement { get; set; } = ListerReadOnlyIconPlacement.End;

    #endregion ReadOnly and Disabling

    #region Selecting

    /// <summary>
    /// The selection type permitted. Defaults to None.
    /// </summary>
    [Parameter]
    public ListerSelectionType SelectionType { get; set; } = ListerSelectionType.None;

    /// <summary>
    /// Every item can be selected when true
    /// </summary>
    [Parameter]
    public bool CanSelectAll { get; set; }

    /// <summary>
    /// Determines whether a selection can occur by clicking on the item itself rather than just the check box
    /// </summary>
    [Parameter]
    public bool? AllowSelectionWithItemClick { get; set; }

    /// <summary>
    /// Sets the items with the given indices to selected
    /// </summary>
    [Parameter]
    public List<int>? PreSelectedItemsAtIndices { get; set; }

    #endregion Selecting

    #region Filtering

    /// <summary>
    /// Filtering is enabled when true
    /// </summary>
    [Parameter]
    public bool CanFilter { get; set; }

    /// <summary>
    /// The icon shown when filtering is enabled. Defaults to 'filter_list'
    /// </summary>
    [Parameter]
    public string? FilterIcon { get; set; } = "filter_list";

    /// <summary>
    /// A text placeholder for the filter. Defaults to 'apply filter'
    /// </summary>
    [Parameter]
    public string? FilterPlaceholder { get; set; } = "apply filter";

    /// <summary>
    /// Determines how the filter comparison is applied using <see cref="FilterComparisonType"/>
    /// </summary>
    [Parameter]
    public FilterComparisonType FilterComparisonType { get; set; } = FilterComparisonType.Cleaned;

    /// <summary>
    /// Allows a user to provide an filter value
    /// </summary>
    [Parameter]
    public string? SetExternalFilter { get; set; }

    /// <summary>
    /// Set the throttle for the filter text input. Defaults to 1000
    /// </summary>
    [Parameter]
    public int FilterThrottleMs { get; set; } = 1000;

    #endregion Filtering

    /// <summary>
    /// Determines what type of counts are displayed using <see cref="ListerCountDisplay"/>
    /// <remarks>Not all may be relevant depending on other settings</remarks>
    /// </summary>
    [Parameter]
    public ListerCountDisplay CountDisplay { get; set; } = ListerCountDisplay.None;

    /// <summary>
    /// Text to display after the count
    /// </summary>
    [Parameter]
    public string? CountDisplayPostText { get; set; }

    /// <summary>
    /// Shows visual loading clues to the user when true (default)
    /// </summary>
    [Parameter]
    public bool ShowLoadingDisplay { get; set; } = true;

    #endregion InitParams

    #region GeneralStylingParams

    /// <summary>
    /// The height for the container of the lister items in pixels. Defaults to 300.
    /// </summary>
    [Parameter, EditorRequired]
    public int ItemsContainerHeightPixels { get; set; } = 300;

    #endregion GeneralStylingParams

    #region ActionParams

    /// <summary>
    /// A function to determine whether the given item can be clicked. Ignored if null.
    /// </summary>
    [Parameter]
    public Func<TData, bool>? CanClick { get; set; }

    /// <summary>
    /// A function to determine whether the given item can be selected. ignored if null
    /// </summary>
    [Parameter]
    public Func<TData, bool>? CanSelect { get; set; }

    /// <summary>
    /// Callback on the down on an item
    /// </summary>
    [Parameter]
    public EventCallback<(KeyboardEventArgs keyboardEventArgs, TData item)> KeyDownOnItem { get; set; }

    /// <summary>
    /// When set to None, no user interaction with the keyboard is permitted.
    /// When set to Allow, the user can interact with the keyboard and set focus on an item
    /// When set to AllowAndAlwaysFocusOnLoad, the user can interact with the keyboard and
    /// additionally the first item (if present) in the loaded list will automatically
    /// receive focus
    /// </summary>
    /// <remarks><see cref="Lister.FocusItemOption"/> for details.</remarks>
    [Parameter]
    public FocusItemOption FocusItemOption { get; set; } = FocusItemOption.None;

    /// <summary>
    /// An action that, when given, is invoked after the items container has stolen the focus in after render event
    /// </summary>
    /// <remarks>To allow keyboard and mouse events to play nicely with virtualization, the after render event
    /// handles the callback that is fired when the last load in virtualization has fired. This steals the focus
    /// onto the items container in order that the mouse and keyboard events succeeds. This means that when the
    /// lister component is a child component in a parent component, the parent component struggles to maintain
    /// focus as it is lost when the lister renders. This Action can be used to allow the parent to regain the
    /// focus once the lister has completed with the mouse or keyboard interactions</remarks>
    [Parameter]
    public Action? AfterFocusOnItemsContainer { get; set; }

    #endregion ActionParams

    #region Events

    /// <summary>
    /// Fires when the load completes.
    /// For non-virtualized and virtualized all at once, this is at the end of the completed loading.
    /// For virtualized items provider, this fires repeatedly
    /// returns a count of items loaded, and start index and num items for items provider (null if not)
    /// </summary>
    [Parameter]
    public EventCallback<(int? totalCount, int? startIndex, int? numItems)> OnLoadComplete { get; set; }

    /// <summary>
    /// A callback when an item is clicked
    /// </summary>
    [Parameter]
    public EventCallback<(int index, bool isSelected, TData item)> OnClickedItem { get; set; }

    /// <summary>
    /// A callback of the current counts.
    /// The total is always available, selected and included can be null when not available
    /// </summary>
    [Parameter]
    public EventCallback<(int? total, int? selected, int? included)> CurrentCounts { get; set; }

    /// <summary>
    /// A callback for when items deleted returning the index and data deleted
    /// </summary>
    [Parameter]
    public EventCallback<(int index, TData item)> OnItemDeleted { get; set; }

    /// <summary>
    /// A callback providing the new data order
    /// </summary>
    [Parameter]
    public EventCallback<List<TData>> OnOrderedDataChanged { get; set; }

    /// <summary>
    /// A callback providing the filtered items
    /// </summary>
    [Parameter]
    public EventCallback<List<TData>> OnFilteredItems { get; set; }

    /// <summary>
    /// A callback providing the selected items
    /// </summary>
    [Parameter]
    public EventCallback<List<TData>> OnSelectedChanged { get; set; }

    #endregion Events

    #region CancelRefresh

    /// <summary>
    /// Allows cancellation and refresh if true
    /// </summary>
    [Parameter]
    public bool CanCancelThenRefresh { get; set; }

    CancellationTokenSource _cancellationTokenSource = new();

    void OnCancel()
    {
        _cancellationTokenSource.Cancel();
        _isCancelled = true;
        _working = false;
        _isLoaded = true;
        _lastProcessedDataProviderRequest = null;
    }

    async Task OnRefresh()
    {
        _cancellationTokenSource = new();
        _isCancelled = false;
        _working = !UsesGivenData;

        if (UsesDataProvider)
        {
            await RefreshAsync();
            return;
        }

        if (UsesGivenData)
        {
            await RefreshData(Data!);
            return;
        }

        if (UsesDataLoading)
        {
            await RefreshData(ListerLoadingFunc!);
        }
    }

    #endregion CancelRefresh

    #region Filtering

    //TODO: changing the filter comparison type should be done via the user profile
    //TODO: ? should there be an option to do this per lister instance ?

    Text? _textFilter;
    string? _filterText;
    bool IsFiltering => !string.IsNullOrEmpty(_filterText);

    // a 'cleaned' filter type is similar to the cleaning in the apoc.text.clean proc here:
    // https://neo4j.com/labs/apoc/4.1/overview/apoc.text/apoc.text.clean/
    // case insensitivity is implied when the Cleaned option is chosen
    // NOTE: even though this function is NOT used for items provider (where the apoc proc is actually used),
    // NOTE: adding a Case sensitive option is NOT viable as code stands because it would require different
    // NOTE: functionality depending on whether using Items provider or not
    string ApplyFilterType(string value)
    {
        return
            FilterComparisonType == FilterComparisonType.Cleaned
                ? new string(value.Where(char.IsLetterOrDigit).ToArray()).ToLower()
                : value;
    }

    string GetSearchString(TData dataItem)
    {
        return
            dataItem is ISearchable searchable
                ? searchable.SearchText()
                : dataItem.ToString()!;
    }

    async Task ApplyFilter(string filterText, Func<ListerItem<TData>, string, bool> updateIncludedFunc, CancellationToken token)
    {
        var filterWithText = ApplyFilterType(filterText);

        var onePercent = _data!.Count / 100;
        var i = 0;

        foreach (var item in _data!.OrderBy(x => x.Index))
        {
            item.Included = updateIncludedFunc.Invoke(item, filterWithText);
            if (item.Included)
            {
                item.Index = i;
                i++;
            }

            //required to keep the ui responsive
            //see: https://stackoverflow.com/questions/65131456/blazor-ui-locking
            //TODO: not implemented in .net 8 - hopefully coming at some point
            if (onePercent > 0 && i % onePercent == 0)
            {
                if (token.IsCancellationRequested)
                {
                    break;
                }

                await DelayService.Delay(TimeSpan.FromMilliseconds(1), token);
            }
        }
    }

    bool _isFiltering;
    CancellationTokenSource? _filteringCs;

    /// <summary>
    /// Invoked when the filter text changes
    /// </summary>
    /// <param name="filterText">The filter text</param>
    protected async Task FilterValueChanged(string? filterText)
    {
        if (_filterText == filterText)
            return;

        if (!UsesDataProvider && _data is null)
        {
            return;
        }

        _filterText = filterText;

        //TODO: this maintains the filter but not sure if correct like this?
        SetExternalFilter = filterText;

        _filteringCs?.Cancel();
        _filteringCs = new CancellationTokenSource();

        if (!UsesDataProvider)
        {
            _isFiltering = true;
            StateHasChanged();

            if (string.IsNullOrWhiteSpace(_filterText))
            {
                //set all data to include
                for (var i = 0; i < _data!.Count; i++)
                {
                    _data[i].Included = true;

                    /*
                    * this effectively wipes out any reordering undertaken previously but has to be set otherwise
                    * the index used when filtering mucks up the order
                    * the fix is then to sort the data
                    */
                    _data[i].Index = i;
                }

                await SortData();
            }
            else
            {
                //apply a filter function
                var func =
                    new Func<ListerItem<TData>, string, bool>((item, filterWithText) =>
                        ApplyFilterType(GetSearchString(item.ItemData)).Contains(filterWithText));

                await ApplyFilter(filterText!, func, _filteringCs.Token);
            }

            _isFiltering = false;
            StateHasChanged();

            await OnFilteredItems.InvokeAsync(
                _data!
                    .Where(x => x.Included)
                    .OrderBy(x => x.Index)
                    .Select(x => x.ItemData)
                    .ToList());

            _includedCount = _data?.Count(x => x.Included);
            await CurrentCounts.InvokeAsync((_totalCount, _selectedCount, _includedCount));
        }
        else
        {
            await RefreshAsync();
        }
    }

    Task TextInteractionCallback(InteractionArgs interactionArgs)
    {
        if (interactionArgs is KeyboardInteractionArgs args
            && args.KeyboardEventArgs.Key == "Enter")
        {
            _textFilter?.ForceComplete();
        }

        return Task.CompletedTask;
    }

    #endregion Filtering

    #region Selecting

    //selection only supported when not using items provider

    void SetSelectedCount()
    {
        _selectedCount = _data!.Count(x => x.IsSelected);
    }

    Task ReturnSelected()
    {
        var selected =
            _data!
                .Where(x => x.IsSelected)
                .OrderBy(x => x.Index)
                .Select(x => x.ItemData)
                .ToList();

        return OnSelectedChanged.InvokeAsync(selected);
    }

    bool CanSelectItem(TData itemData)
    {
        return CanSelect is null || CanSelect(itemData);
    }

    async Task InvokeOnClickedItem(ListerItem<TData> listerItem)
    {
        if (OnClickedItem.HasDelegate && (CanClick is null || CanClick(listerItem.ItemData)))
        {
            await OnClickedItem.InvokeAsync((listerItem.Index, listerItem.IsSelected, listerItem.ItemData));
        }
    }

    async Task CheckBoxChanged(ChangeEventArgs args, ListerItem<TData> listerItem)
    {
        var selectedTrue = (bool)args.Value!;

        if (selectedTrue && SelectionType == ListerSelectionType.Single)
        {
            foreach (var d in _data!)
            {
                d.IsSelected = false;
            }
        }

        listerItem.IsSelected = selectedTrue;
        SetSelectedCount();
        await ReturnSelected();
        await InvokeOnClickedItem(listerItem);
    }

    string _selectAllLabel = "select all";

    bool _selectAllState;

    async Task SelectAllChanged()
    {
        _selectAllState = !_selectAllState;
        _selectAllLabel = _selectAllState ? "deselect all" : "select all";

        //NOTE: only changes items that are currently available if the filter is applied
        await Task.Run(() =>
        {
            foreach (var d in _data!.Where(x => x.Included && CanSelectItem(x.ItemData)))
            {
                d.IsSelected = _selectAllState;
            }
        });

        SetSelectedCount();
        await ReturnSelected();
        StateHasChanged();
    }

    //if given, select the items that match the preselected items
    void ApplyPreselectedItems()
    {
        if (PreSelectedItemsAtIndices is null || !PreSelectedItemsAtIndices.Any())
        {
            return;
        }

        if (_data is null)
        {
            throw new InvalidOperationException("data should not be null at this point");
        }

        //if no data items are included due to a filter then just return
        if (!_data.Any(x => x.Included))
        {
            return;
        }

        var filteredIfGiven = _data.Where(x => x.Included).ToList();
        var preSelected = PreSelectedItemsAtIndices!;

        //if single mode, only take the first preselected item
        if (SelectionType == ListerSelectionType.Single)
        {
            preSelected = new List<int> { preSelected[0] };
        }

        var targetItems =
            _data.IntersectBy(filteredIfGiven.Select(x => x.Index), item => item.Index)
                .IntersectBy(preSelected, item2 => item2.Index)
                .ToList();

        foreach (var it in targetItems.Select(item => _data.Single(x => x.Index == item.Index)))
        {
            it.IsSelected = true;
        }

        SetSelectedCount();
    }

    void ToggleSelected(TData item)
    {
        if (_data is not null)
        {
            var itemFromCollection = _data.Single(x => x.ItemData.Equals(item));
            itemFromCollection.IsSelected = !itemFromCollection.IsSelected;
        }
    }

    Task MakeSelectionOnItemClick(ListerItem<TData> listerItem)
    {
        if (SelectionType != ListerSelectionType.None && _allowSelectionWithItemClick && CanSelectItem(listerItem.ItemData))
        {
            ToggleSelected(listerItem.ItemData);
            SetSelectedCount();
            return ReturnSelected();
        }

        return Task.CompletedTask;
    }

    #endregion Selecting

    #region Navigation

    //indicates that an item has been clicked, and provides a payload for updating
    //the visible range
    (bool doit, int clickedItem, double mouseClientY) _mouseSelectPayload;

    async Task OnItemClick(MouseEventArgs args, ListerItem<TData> listerItem)
    {
        await MakeSelectionOnItemClick(listerItem);
        await InvokeOnClickedItem(listerItem);

        if (FocusItemOption == FocusItemOption.None)
            return;

        _currFocusIndex = listerItem.Index;

        //flags a mouse update
        _mouseSelectPayload = (true, listerItem.Index, args.ClientY);
    }

    bool HasKeyDownEnabled => KeyDownOnItem.HasDelegate;

    //maintain an internal counter for the current index of the loaded items
    //this will determine if the item is going off screen and should be scrolled

    int NumItemsInView => (int)Math.Round(ItemsContainerHeightPixels / ItemSizePixels);

    (int top, int bottom) _visibleRange;

    /// <summary>
    /// Returns the visible range of items to aid testing
    /// </summary>
    public (int top, int bottom) VisibleRange => _visibleRange;

    /// <summary>
    /// Returns the current focus index to aid testing
    /// </summary>
    public int GetCurrentFocusIndex => _currFocusIndex;


    ElementReference? _listerItemsContainerElementRef;


    /// <summary>
    /// Sets the current focus index to the given value
    /// </summary>
    /// <param name="index">The index to set the index to</param>
    /// <param name="setFocusToPermitKeystrokes">If true, the lister gets the focus so keystrokes can be received</param>
    public void SetCurrentFocusIndex(int index, bool setFocusToPermitKeystrokes = false)
    {
        _currFocusIndex = index;

        if (setFocusToPermitKeystrokes)
        {
            _listerItemsContainerElementRef!.Value.FocusAsync();
        }

        StateHasChanged();
    }

    (bool doit, int pageBy) _keyboardSelectPayload = (false, 0);

    //handles scrolling when the lister is in focus
    async Task ListerKeyDown(KeyboardEventArgs args)
    {
        var inView = await ElementExists("[data-has-focus]");
        if (args.Repeat || !inView || FocusItemOption == FocusItemOption.None)
            return;

        var adjustedItemsCount = _wrappedData.Count(x => x.Included) - 1;

        switch (args.Key)
        {
            case "ArrowDown":
                if (_currFocusIndex < adjustedItemsCount)
                {
                    _currFocusIndex += 1;
                    _keyboardSelectPayload = (true, 1);
                }

                break;
            case "PageDown":
                if (_currFocusIndex < adjustedItemsCount)
                {
                    var pageBy = Math.Min(adjustedItemsCount - _currFocusIndex, _itemsOnDisplay);
                    _currFocusIndex += pageBy;
                    _keyboardSelectPayload = (true, pageBy);
                }

                break;
            case "End":
                var focusIndex1 = _currFocusIndex;
                _currFocusIndex = adjustedItemsCount;
                _keyboardSelectPayload = (true, Math.Min(adjustedItemsCount - focusIndex1, adjustedItemsCount));
                break;
            case "ArrowUp":
                if (_currFocusIndex > 0)
                {
                    _currFocusIndex -= 1;
                    _keyboardSelectPayload = (true, -1);
                }

                break;
            case "PageUp":
                if (_currFocusIndex > 0)
                {
                    var pageBy = Math.Min(_currFocusIndex, _itemsOnDisplay);
                    _currFocusIndex -= pageBy;
                    _keyboardSelectPayload = (true, pageBy);
                }

                break;
            case "Home":
                var focusIndex2 = _currFocusIndex;
                _currFocusIndex = 0;
                _keyboardSelectPayload = (true, focusIndex2);
                break;
        }

        //call the key down event call back if needed
        if (HasKeyDownEnabled)
        {
            await KeyDownOnItem.InvokeAsync((args, _wrappedData[_currFocusIndex].ItemData));
        }
    }

    async Task UpdateFocusByKeyboard(int moveBy)
    {
        if (moveBy == 0)
            return;

        var pixels = int.Abs((int)(moveBy * ItemSizePixels));
        var absMoveBy = int.Abs(moveBy);

        //if the new value takes the current index out of the visible range
        //a scroll is required
        if (_currFocusIndex > _visibleRange.bottom)
        {
            await ScrollDown("cctc-lister-items-container", pixels);
            _visibleRange = (_visibleRange.top + absMoveBy, _visibleRange.bottom + absMoveBy);
        }
        else if (_currFocusIndex < _visibleRange.top)
        {
            await ScrollUp("cctc-lister-items-container", pixels);
            _visibleRange = (Math.Max(0, _visibleRange.top - absMoveBy), Math.Max(NumItemsInView - 1, _visibleRange.bottom - absMoveBy));
        }

        //Console.WriteLine($"keyboard nav | moveBy: {moveBy},  curr ind: {_currFocusIndex}, range: {_visibleRange}");
    }


    Task UpdateFocusByMouse(int clickedIndex, double mouseClientY)
    {
        var indexInVisibleRange = -1;
        if (_currFocusIndex < _visibleRange.top || _currFocusIndex > _visibleRange.bottom)
        {
            indexInVisibleRange = (int)Math.Floor((mouseClientY - _boundingRect!.Top) / ItemSizePixels);
            _visibleRange = (_currFocusIndex - indexInVisibleRange, _currFocusIndex + (NumItemsInView - indexInVisibleRange - 1));
        }

        var indexString =
            indexInVisibleRange != -1
                ? $", indexInRange: {indexInVisibleRange}"
                : "";

        //Console.WriteLine($"mouse nav | curr ind: {_currFocusIndex}, range: {_visibleRange}{indexString}");

        return Task.CompletedTask;
    }

    #endregion Navigation

    #region Refreshing

    ///apply given filter or given preselected indices
    async Task ApplyGivenFilterAndSelectedIndices()
    {
        await FilterValueChanged(SetExternalFilter);

        //if preselected indexes given apply them now
        if (PreSelectedItemsAtIndices is not null && PreSelectedItemsAtIndices.Any()
                                                  && SelectionType != ListerSelectionType.None)
        {
            ApplyPreselectedItems();
        }
    }

    /// <summary>
    /// Refresh the underlying component
    /// </summary>
    public Task RefreshAsync()
    {
        return
            _virtualizeComponent is null
                ? Task.CompletedTask
                : _virtualizeComponent.RefreshDataAsync();
    }

    /// <summary>
    /// Refresh the data using the loading function
    /// </summary>
    /// <param name="loadingFunc">The loading function</param>
    /// <exception cref="InvalidOperationException">Thrown if the loading function isn't available</exception>
    public async Task RefreshData(Func<CancellationToken, Task<List<TData>>> loadingFunc)
    {
        if (UsesDataLoading)
        {
            _isLoaded = false;
            var locallyLoadedData = await loadingFunc.Invoke(_cancellationTokenSource.Token);
            await Load(locallyLoadedData);

            await ApplyGivenFilterAndSelectedIndices();
            _isLoaded = true;
            _currFocusIndex = -1;
        }
        else
        {
            throw new InvalidOperationException("This method is only applicable when using data loading");
        }

        StateHasChanged();
    }

    /// <summary>
    /// Refresh the data from the given data
    /// </summary>
    /// <param name="data">The given data</param>
    /// <exception cref="InvalidOperationException">Thrown if the data for loading isn't available</exception>
    public async Task RefreshData(IEnumerable<TData> data)
    {
        if (UsesGivenData)
        {
            _isLoaded = false;
            await Load(data.ToList());
            await ApplyGivenFilterAndSelectedIndices();
            _currFocusIndex = -1;
        }
        else
        {
            throw new InvalidOperationException("This method is only applicable when using given data");
        }

        StateHasChanged();
    }

    class ItemRenderedEventArgs : EventArgs
    {
        public int ItemIndex { get; set; }
    }

    event EventHandler<ItemRenderedEventArgs>? ItemRenderedEvent;
    bool _itemRenderedCompleted;

    void SetItemRenderedObservable()
    {
        /* this observable;
            1. receives the ItemRenderedEvent when the item is rendered in the render fragment
            2. throttles the requests - i.e. if another request is instantly received because another item loaded
            3. runs the RunStateHasChanged method to trigger state change at end
            4. uses switch to cancel a previous request when starting a new one and simply running the last one
         */

        _itemRenderedObservable = Observable
            .FromEventPattern<EventHandler<ItemRenderedEventArgs>, ItemRenderedEventArgs>(
                h => ItemRenderedEvent += h,
                h => ItemRenderedEvent -= h)
            .Throttle(TimeSpan.FromMilliseconds(100), SchedulerProvider.Default)
            .Select(args => Observable.Return(args.EventArgs.ItemIndex))
            .Switch()
            .Subscribe(RunStateHasChanged);
    }

    //runs the StateHasChanged process to trigger a parent rerender
    void RunStateHasChanged(int index)
    {
        _itemRenderedCompleted = true;
        InvokeAsync(StateHasChanged);
    }

    void ItemHasLoaded(int index)
    {
        ItemRenderedEvent?.Invoke(this, new ItemRenderedEventArgs { ItemIndex = index });
    }

    #endregion Refreshing

    #region Loading

    IList<ListerItem<TData>> _wrappedData = new List<ListerItem<TData>>();

    /// <summary>
    /// A helper for external access to the wrapped data
    /// </summary>
    public IList<ListerItem<TData>> WrappedData => _wrappedData;

    //wrap the data in an item type
    //the offset is used for wrapping data when loaded via the items provider
    IList<ListerItem<TData>> WrapData(IEnumerable<TData> data, int offset = 0)
    {
        var ret = new List<ListerItem<TData>>();
        var arr = data as TData[] ?? data.ToArray();

        for (var i = 0; i < arr.Length; i++)
        {
            ret.Add(new ListerItem<TData>
                { Index = i + offset, IsSelected = false, Included = true, ItemData = arr[i] });
        }

        _isLoaded = true;
        _working = false;

        _wrappedData = ret;
        // For ItemsService, _includedCount is managed in RunLoad where we have access to the total filtered count
        if (!UsesDataProvider)
        {
            _includedCount = ret.Count(x => x.Included);
        }

        return ret;
    }

    IList<ListerItem<TData>>? _data;

    //processes loaded data
    //Note: the data is automatically sorted
    async Task Load(IList<TData>? data)
    {
        if (data is null)
        {
            throw new InvalidOperationException("data should not be null if this method is called");
        }

        _data = await Task.Run(() => WrapData(data), _cancellationTokenSource.Token);
        await SortData();

        _totalCount = _data.Count;
        await OnLoadComplete.InvokeAsync((_totalCount, null, null));
        await CurrentCounts.InvokeAsync((_totalCount, _selectedCount, _includedCount));
    }

    async ValueTask<ItemsProviderResult<ListerItem<TData>>> RunLoad(ItemsProviderRequest request, CancellationToken token)
    {
        _working = true;
        _isCancelled = false;
        await InvokeAsync(StateHasChanged);

        var numItems = request.Count;
        var sortBy = TData.SortBy;
        var sortDir = TData.SortDir;

        try
        {
            int totalItemsCount;
            List<TData>? items;

            if (!IsFiltering)
            {
                var (totNoFilt, itemsNoFilt) =
                    await ItemsService!.GetItemsAsync(
                        request.StartIndex, numItems, sortBy, sortDir, token);

                _totalCount = totNoFilt;
                items = itemsNoFilt.ToList();
                _includedCount = totNoFilt;
                totalItemsCount = totNoFilt;
            }
            else
            {
                var (tot, totWithFilt, itemWithFilt) =
                    await ItemsService!.GetItemsAsync(
                        _filterText!, FilterComparisonType,
                        request.StartIndex, numItems, sortBy, sortDir, token);

                _totalCount = tot;
                items = itemWithFilt.ToList();
                _includedCount = totWithFilt;
                totalItemsCount = totWithFilt;

                await OnFilteredItems.InvokeAsync(items);
            }

            await OnLoadComplete.InvokeAsync((_totalCount, request.StartIndex, numItems));
            await CurrentCounts.InvokeAsync((_totalCount, _selectedCount, _includedCount));

            if (token.IsCancellationRequested)
            {
                _working = false;
                return _reset;
            }

            return new ItemsProviderResult<ListerItem<TData>>(WrapData(items, request.StartIndex), totalItemsCount);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading items");
            _working = false;
            return _reset;
        }
    }

    ItemsProviderResult<ListerItem<TData>> _response;

    class LoadItemsEventArgs : EventArgs
    {
        public ItemsProviderRequest ItemsProviderRequest { get; set; }
    }

    event EventHandler<LoadItemsEventArgs>? LoadItemsEvent;

    const int ResetTotalCount = -99;
    readonly ItemsProviderResult<ListerItem<TData>> _reset = new(System.Linq.Enumerable.Empty<ListerItem<TData>>(), ResetTotalCount);
    (int startIndex, int count, string? filterText)? _lastProcessedDataProviderRequest;

    //make the items provider request
    async ValueTask<ItemsProviderResult<ListerItem<TData>>> LoadItems(ItemsProviderRequest request)
    {
        _response = _reset;

        if (ItemsService is null)
        {
            throw new InvalidOperationException("ItemsService should not be null if this method is called");
        }

        LoadItemsEvent?.Invoke(this, new LoadItemsEventArgs { ItemsProviderRequest = request });

        try
        {
             /* this observable;
                1. waits for 10 ms repeatedly
                2. checks whether the response has completed or whether cancellation has been requested
                3. returns as a task - if the task is cancelled this is handled in the try catch
             */
            await Observable
                .Interval(TimeSpan.FromMilliseconds(10))
                .TakeUntil(x => _response.TotalItemCount != ResetTotalCount || _cancellationTokenSource.IsCancellationRequested)
                .ToTask(_cancellationTokenSource.Token);
        }
        catch (TaskCanceledException)
        {
            _working = false;
            _cancellationTokenSource = new CancellationTokenSource();
            StateHasChanged();
            return _reset;
        }

        var ret = await Task.FromResult(_response);
        _working = false;
        StateHasChanged();
        return ret;
    }

    IDisposable? _loadItemsObservable;
    IDisposable? _itemRenderedObservable;

    void SetLoadItemsObservable()
    {
        /* this observable;
            1. receives the LoadItemsEvent from the virtualized components' LoadItems method
            2. throttles the requests - i.e. when the users drags the scroll bar down - and waits throttleMs before executing
            3. runs the RunLoad method async taking a token from this method and catching any exceptions
            4. uses switch to cancel a previous request when starting a new one and simply running the last one
            5. populates _response for the final query that completes
            6. tracks the last processed request (startIndex, count, filterText) to prevent duplicate requests
            7. logs request processing and skipping for debugging purposes
         */

        _loadItemsObservable = Observable
            .FromEventPattern<EventHandler<LoadItemsEventArgs>, LoadItemsEventArgs>(
                h => LoadItemsEvent += h,
                h => LoadItemsEvent -= h)
            .Select(evt => evt.EventArgs.ItemsProviderRequest)
#if DEBUG
            .Do(request => Logger.LogInformation("Processing request - StartIndex: {StartIndex}, Count: {Count}, Filter: {Filter}", 
                request.StartIndex, request.Count, _filterText))
#endif
            .Throttle(TimeSpan.FromMilliseconds(ItemsService!.ObservableThrottleMs), SchedulerProvider.Default)
            .Select(request =>
                Observable
                    .DeferAsync(async token =>
                    {
                        if (_isCancelled)
                        {
                            return Observable.Return(_reset);
                        }

                        var currentRequest = (request.StartIndex, request.Count, _filterText);

                        // Skip subsequent requests if:
                        // 1. We have a previous request (_lastProcessedDataProviderRequest.HasValue)
                        // 2. The current request is at index 0 (virtualization component reset)
                        // 3. The previous request for this filter returned no results (_includedCount == 0)
                        // 4. We're using the same filter text as the previous request
                        // This optimization prevents unnecessary server calls when we already know
                        // a filter returns no results, while still allowing new filters to be processed
                        if (_lastProcessedDataProviderRequest.HasValue
                            && currentRequest.StartIndex == 0
                            && _includedCount == 0
                            && _lastProcessedDataProviderRequest.Value.filterText == currentRequest._filterText)
                        {
#if DEBUG
                            Logger.LogInformation("Skipping request for filter with no results - StartIndex: {StartIndex}, Count: {Count}, Filter: {Filter}", 
                                request.StartIndex, request.Count, _filterText);
#endif
                            return Observable.Return(_response);
                        }
                        
                        // Skip duplicate requests by checking if we've already processed a request with
                        // identical parameters (same StartIndex, Count, and filter text). This prevents
                        // unnecessary server calls when the virtualization component makes repeated
                        // requests for the same data
                        if (_lastProcessedDataProviderRequest.HasValue && _lastProcessedDataProviderRequest.Value == currentRequest)
                        {
#if DEBUG
                            Logger.LogInformation("Skipping duplicate request - StartIndex: {StartIndex}, Count: {Count}, Filter: {Filter}", 
                                request.StartIndex, request.Count, _filterText);
#endif
                            return Observable.Return(_response);
                        }
                        
                        _lastProcessedDataProviderRequest = currentRequest;
                        var linkedTokenSource = CancellationTokenSource.CreateLinkedTokenSource(_cancellationTokenSource.Token, token);
                        return Observable.Return(await RunLoad(request, linkedTokenSource.Token));
                    })
                    .Catch((Exception ex) =>
                    {
#if DEBUG
                        Logger.LogError("Couldn't load the data page in Lister with exception: {ex}", ex);
#endif
                        throw new InvalidOperationException("the Lister observable reported an error");
                    })
            )
            .Switch()
            .Subscribe(
                itemRes => {
#if DEBUG
                    Logger.LogInformation("Observable received response");
#endif
                    _response = itemRes; 
                },
                ex =>
                {
                    Logger.LogError("Lister observable failed with exception: {ex}", ex);
                        throw new InvalidOperationException("the Lister observable reported an error", ex);
                });
    }

    /// <summary>
    /// Runs the loading of the items taking into account the type of lister, whether using given data,
    /// a loading function or the data provider
    /// </summary>
    /// <param name="runStateHasChanged">When true, forces a statehaschanged to be run</param>
    public async Task Load(bool runStateHasChanged = false)
    {
        if (Data is not null)
        {
            await Load(Data);
        }

        if (ListerLoadingFunc is not null)
        {
            _working = true;
            var locallyLoadedData =
                await ListerLoadingFunc.Invoke(_cancellationTokenSource.Token)
                    .ConfigureAwait(false);

            await Load(locallyLoadedData);
        }

        if (UsesDataProvider)
        {
            _isLoaded = true;
            _working = true;
            SetLoadItemsObservable();
        }

        //set variables to manage focus and the range of visible items
        if (WrappedData.Any())
        {
            _currFocusIndex = FocusItemOption == FocusItemOption.AllowAndAlwaysFocusOnLoad ? 0 : -1;
            _visibleRange = (0, Math.Min(WrappedData.Count - 1, NumItemsInView - 1));
        }
        else
        {
            _currFocusIndex = -1;
            _visibleRange = (-1, -1);
        }

        if (runStateHasChanged)
        {
            StateHasChanged();
        }
    }

    #endregion Loading

    #region Reordering

    async Task DoMove(ListerItem<TData> listerItem, bool moveUp)
    {
        var incOrDec = moveUp ? -1 : +1;

        var thisIndex = listerItem.Index;
        var other = _data!.Where(x => x.Included).Single(x => x.Index == listerItem.Index + incOrDec);
        var otherIndex = other.Index;
        other.Index = thisIndex;
        listerItem.Index = otherIndex;

        if (Virtualize)
        {
            await RefreshAsync();
        }
    }

    async Task ReturnOrdered()
    {
        var orderedData =
            _data!.OrderBy(x => x.Index)
                .Select(x => x.ItemData)
                .ToList();

        await OnOrderedDataChanged.InvokeAsync(orderedData);
    }

    async Task MoveUp(ListerItem<TData> listerItem)
    {
        await DoMove(listerItem, true);
        await ReturnOrdered();
    }

    async Task MoveDown(ListerItem<TData> listerItem)
    {
        await DoMove(listerItem, false);
        await ReturnOrdered();
    }

    #endregion Reordering

    #region Sorting

    /// <summary>
    /// Sort existing lister data according to the ISortable properties implemented on <typeparamref name="TData"/>
    /// </summary>
    /// <remarks>
    /// <typeparamref name="TData"/>.SortBy can be a property name on <typeparamref name="TData"/>
    /// or it can be a nested property name of the parent <typeparamref name="TData"/>
    /// </remarks>
    public async Task SortData()
    {
        if (!UsesDataProvider && _data is not null)
        {
            _data =
                _data
                    .AsQueryable()
                    .OrderByPropertyOrField($"ItemData.{TData.SortBy}", TData.SortDir)
                    .ToList();

            for (var i = 0; i < _data.Count; i++)
            {
                _data[i].Index = i;
            }

            await ReturnOrdered();
        }
    }

    #endregion

    #region Deleting

    async Task Delete(ListerItem<TData> listerItem)
    {
        if (!UsesDataProvider)
        {
            _data!.Remove(listerItem);
            _totalCount = _data.Count;
            _includedCount = _data.Count(x => x.Included);

            var laterItems = _data.Where(x => x.Index > listerItem.Index);
            foreach (var laterItem in laterItems)
            {
                laterItem.Index -= 1;
            }
        }
        else
        {
            throw new InvalidOperationException("Deleting items is not possible when the Lister uses an items provider");
        }

#if DEBUG
        DebugCheckContiguousIndexes();
#endif

        await OnItemDeleted.InvokeAsync((listerItem.Index, listerItem.ItemData));
        await CurrentCounts.InvokeAsync((_totalCount, _selectedCount, _includedCount));
    }

    #endregion Deleting

    RenderFragment? _loadingTemplate;
    RenderFragment? _emptyListTemplate;
    bool _allowSelectionWithItemClick;

    bool UsesDataProvider => ItemsService is not null;
    bool UsesDataLoading => ListerLoadingFunc is not null;
    bool UsesGivenData => Data is not null;

    /// <inheritdoc/>
    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        _allowSelectionWithItemClick =
            ItemsService is null && AllowSelectionWithItemClick is null
            || (AllowSelectionWithItemClick ?? false);

        var message = (Data, ItemsService, ListerLoadingFunc) switch
        {
            (null, null, null) =>
                "Either Data or ItemsService or ListerLoadingFunc must be provided",
            (not null, not null, _) or (_, not null, not null) or (not null, _, not null) =>
                "Only one data source should be provided",
            _ => null
        };

        if (message is not null)
        {
            throw new InvalidOperationException(message);
        }

        if (UsesDataProvider && CanDeleteItems)
        {
            throw new InvalidOperationException("When using an Items provider, Lister does not support deletion of items");
        }

        if (UsesDataProvider && CanReOrder)
        {
            throw new InvalidOperationException("When using an Items provider, Lister does not support reordering of items");
        }

        if (UsesDataProvider && (SelectionType != ListerSelectionType.None || CanSelectAll ||
                                 PreSelectedItemsAtIndices is not null || CanSelect is not null || _allowSelectionWithItemClick))
        {
            throw new InvalidOperationException("When using an Items provider, Lister does not support selecting items");
        }

        if (UsesGivenData && CanCancelThenRefresh)
        {
            throw new InvalidOperationException("When using preloaded data, cancellation is not available");
        }

        _loadingTemplate = LoadingTemplate ?? DefaultLoadingTemplate;
        _emptyListTemplate = EmptyListTemplate ?? DefaultEmptyListTemplate;

        await Load();
    }

    /// <inheritdoc />
    protected override async Task OnParametersSetAsync()
    {
        await ApplyGivenFilterAndSelectedIndices();

        //NOTE: shouldn't call Load() here but use the Load(true) function from a parent if needed
        //when the parent renders.
    }

    BoundingRect? _boundingRect;
    int _itemsOnDisplay;

    //handles the keyboard selection
    async Task HandleKeyboard()
    {
        if (_keyboardSelectPayload.doit)
        {
            var pageBy = _keyboardSelectPayload.pageBy;
            //reset the request
            _keyboardSelectPayload = (false, 0);

            await ScrollIntoViewFromSelector("[data-has-focus]");
            await UpdateFocusByKeyboard(pageBy);
        }
    }

    //ensures that items selected by mouse are fully on display by scrolling into view
    //also updates the _visibleRange based on the selected item to ensure scrolling by
    //keyboard is correct
    async Task HandleMouse()
    {
        if (_mouseSelectPayload.doit)
        {
            var clientY = _mouseSelectPayload.mouseClientY;
            //reset the request
            _mouseSelectPayload = (false, -1, 0);

            await ScrollIntoViewFromSelector("[data-has-focus]");
            await UpdateFocusByMouse(_mouseSelectPayload.clickedItem, clientY);
        }
    }

    /// <inheritdoc />
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender && !UsesDataProvider && FocusItemOption != FocusItemOption.None)
        {
            SetItemRenderedObservable();
        }

        //provides the _boundingRect for the items container and the _itemsOnDisplay
        if (_boundingRect is null)
        {
            _boundingRect = await GetElementBoundRectFromQuery("cctc-lister-items-container");
            if (_boundingRect is not null)
            {
                _itemsOnDisplay = (int)Math.Floor(_boundingRect.Height / ItemSizePixels);
            }
        }

        //focuses the items container after virtualization has completed
        if (_itemRenderedCompleted)
        {
            _itemRenderedCompleted = false;

            //only set the focus when AllowAndAlwaysFocusOnLoad is selected
            //when the container is focussed, it will also scroll the page by default which is not always desired
            await InvokeAsync(() => 
                    FocusItemOption == FocusItemOption.AllowAndAlwaysFocusOnLoad
                        ? SetFocusOnSelector("cctc-lister-items-container")
                        : Task.CompletedTask)
                .ContinueWith(_ => HandleKeyboard()
                    .ContinueWith(_ => HandleMouse()))
                .ContinueWith(_ => AfterFocusOnItemsContainer?.Invoke());
        }
        else
        {
            await HandleKeyboard();
            await HandleMouse();
        }

        //notify a parent that the rendering has completed
        HasRendered?.Invoke();
        if (HasRenderedAsync is not null)
        {
            await HasRenderedAsync.Invoke();
        }
    }


    /// <summary>
    /// Disposes asynchronously
    /// </summary>
    /// <inheritdoc/>
    public override async ValueTask DisposeAsync()
    {
        await base.DisposeAsync();

        if (_virtualizeComponent != null)
        {
            try
            {
                await _virtualizeComponent.DisposeAsync().ConfigureAwait(false);
            }
            catch (ObjectDisposedException)
            {
                //if the underlying object is disposed don't error
            }
        }

        //dispose of items if they implement IAsyncDisposable
        //not 100% about this as resharper warns of suspicious conversion
        if (typeof(TData).GetInterfaces().Contains(typeof(IAsyncDisposable)))
        {
            foreach (var item in _data!)
            {
                // ReSharper disable once SuspiciousTypeConversion.Global
                var di = (IAsyncDisposable)item;
                await di.DisposeAsync().ConfigureAwait(false);
            }
        }
    }

    /// <inheritdoc/>
    public void Dispose()
    {
        _loadItemsObservable?.Dispose();
        _itemRenderedObservable?.Dispose();
        _filteringCs?.Dispose();
        _cancellationTokenSource.Dispose();

        _loadItemsObservable = null;
        _itemRenderedObservable = null;
        _filteringCs = null;

        //dispose of items if they implement IDisposable
        //not 100% about this as resharper warns of suspicious conversion
        if (typeof(TData).GetInterfaces().Contains(typeof(IDisposable)))
        {
            foreach (var item in _data!)
            {
                // ReSharper disable once SuspiciousTypeConversion.Global
                var di = (IDisposable)item;
                di.Dispose();
            }
        }
    }

    //the common template to display items regardless of other settings
    //Note: use of onclick:stopPropagation="true" which prevents the OnClickedItem callback being called twice when the checkbox is clicked
    //and use on the move up and move down buttons to prevent also selecting an item when reordering it
    RenderFragment<ListerItem<TData>> ItemTemplate => item =>
        @<cctc-lister-item-row id="@<EMAIL>" class="item-row" style="height: @(ItemSizePixels)px"
                               @key="item"
                               @onclick="@(args => OnItemClick(args, item))"
                               tabindex="@(FocusItemOption == FocusItemOption.None ? -1 : 0)"
                               data-item-row="@item.Index"
                               data-has-focus="@(item.Index == _currFocusIndex)">
            @{
                var i = item.Index;
                var textStyle = Disabled ? "color: var(--disabled);" : "";
            }

            @if (SelectionType != ListerSelectionType.None)
            {
                <input class="mt-1 ms-1 mb-1 me-4" type="checkbox" id="@Id-select-@i" name="@Id-@i" value="@Id-@i"
                       style="cursor: pointer;"
                       checked="@item.IsSelected"
                       disabled="@(Disabled || !CanSelectItem(item.ItemData))"
                       readonly="@ReadOnly"
                       @onclick:stopPropagation="true"
                       @onchange="@(args => CheckBoxChanged(args, item))"/>
            }

            <div style="@textStyle" @key="item.ItemData.Key">
                <ListerDataWrapper TData="TData" Content="item"
                                   HasLoaded="@(() => ItemHasLoaded(item.Index))">
                    <Template>
                        @Template(item.ItemData)
                    </Template>
                </ListerDataWrapper>
            </div>

            <div class="ms-auto control-icons-group">
                @if (!Disabled && !ReadOnly && !IsFiltering)
                {
                    if (CanReOrder)
                    {
                        if (i > 0)
                        {
                            <div id="@Id-moveup-@i" class="material-icons control-icon"
                                 @onclick:stopPropagation="true"
                                 @onclick="@(_ => MoveUp(item))">
                                arrow_upward
                            </div>
                        }

                        if (i < _data!.Where(x => x.Included).MaxBy(x => x.Index)!.Index)
                        {
                            <div id="@Id-movedown-@i" class="material-icons control-icon"
                                 @onclick:stopPropagation="true"
                                 @onclick="@(_ => MoveDown(item))">
                                arrow_downward
                            </div>
                        }
                    }

                    if (CanDeleteItems)
                    {
                        <div id="@Id-delete-@i" class="material-icons control-icon"
                             @onclick="@(_ => Delete(item))">
                            delete
                        </div>
                    }
                }
            </div>
        </cctc-lister-item-row>;


    //displays the template using virtualization but loads all data into memory at once
    RenderFragment AllItemsVirtualization =>
        @<div id="@Id-virtualize">
            <Virtualize TItem="ListerItem<TData>" @ref="_virtualizeComponent"
                        Items="@(_data!.Where(x => x.Included).OrderBy(x => x.Index).ToList())"
                        Context="item"
                        OverscanCount="@OverscanCount"
                        ItemSize="@ItemSizePixels"
                        SpacerElement="cctc-lister-item-row">
                <ItemContent>
                    @ItemTemplate(item)
                </ItemContent>
                @* https://github.com/dotnet/aspnetcore/issues/28653 *@
                @* dont use a placeholder as breaks it *@
                @* <Placeholder> *@
                @*     <p style="height: @(ItemSizePixels)px">...</p> *@
                @* </Placeholder> *@
            </Virtualize>
        </div>;

    //displays the template using virtualization but uses the items provider to made repeated calls to service on scroll
    //requires the underlying service query to support paging to have any benefit
    RenderFragment ItemsProviderVirtualization =>
        @<div id="@Id-virtualize">
            <Virtualize TItem="ListerItem<TData>" @ref="_virtualizeComponent" ItemsProvider="@LoadItems" Context="item"
                        OverscanCount="@OverscanCount" ItemSize="@ItemSizePixels">
                <ItemContent>
                    @ItemTemplate(item)
                </ItemContent>
                @* https://github.com/dotnet/aspnetcore/issues/28653 *@
                @* dont use a placeholder as breaks it *@
                @* <Placeholder> *@
                @*     <p style="height: @(ItemSizePixels)px">...</p> *@
                @* </Placeholder> *@
            </Virtualize>
        </div>;


    //displays the template WITHOUT using virtualization
    RenderFragment AllItemsNotVirtualized =>
        @<div id="@Id-no-virtualize">
            @foreach (var item in _data!.Where(x => x.Included).OrderBy(x => x.Index).ToList())
            {
                @ItemTemplate(item)
            }
        </div>;

    RenderFragment DefaultLoadingTemplate => @<Wave Center="false" Size="30px;"
                                                    Color="var(--cctc-lister-highlight-color)"></Wave>;

    RenderFragment DefaultEmptyListTemplate =>
        @<div class="d-flex align-items-center">
            <div class="material-icons">report</div>
            <div class="ms-2">the list is empty</div>
        </div>;

    RenderFragment ReadOnlyLock =>
        @<div class="d-flex justify-content-end icon-wrapper">
            <span id="@Id-read-only-icon" class="material-icons">
        lock
    </span>
        </div>;

    // NOTE: changing the type of loader will require a change in the [working-template] isolated css
    RenderFragment WorkingTemplate => @<Plane Center="false" Size="20px;"></Plane>;


    //dev helpers
#if DEBUG
    void DebugCheckContiguousIndexes()
    {
        if (!UsesDataProvider)
        {
            var countAll = _data!.Count;
            var countDistinct = _data.DistinctBy(x => x.Index).Count();
            var maxIndex = _data!.MaxBy(x => x.Index)!.Index;

            //should be unique

            if (countAll != countDistinct)
            {
                throw new InvalidOperationException("expected count all to be same as count distinct");
            }

            //max count should match last index
            if (countAll != maxIndex + 1)
            {
                throw new InvalidOperationException("the items are not contiguous");
            }
        }
        else
        {
            //not applicable to items provider
        }
    }
#endif
}