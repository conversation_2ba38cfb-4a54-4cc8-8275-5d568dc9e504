<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
Document
</title>
</head>
<body>
<style>

    * {
        font-family: monospace, Courier;
    }

    table {
        border-collapse: collapse;
    }

    th, td {
        /* border: dashed grey 1px; */
        padding: 0.5rem 1rem;
    }

    .feature-body > *, .js-file-line {
        font-size: 12px;
    }

    .spec-script > * {
        font-size: 14px;        
        font-family: Arial, Helvetica, sans-serif;
    }

    .comments {
        font-size: 12px;
    }

    .comment {
        padding-bottom: 0.2rem;
    }

    .comment-edit {
        font-size: 11px;        
    }

    .timeline {
        font-size: 11px;
    }

    .index-good {
        font-size: 12px;
    }

    .index-errors {
        font-size: 11px;
    }

    .sub-header {
        font-size: 10px;
    }

</style>
<h2 id="cctc_components-validation-for-1.0.62">CCTC_Components
validation for 1.0.62</h2>
<div class="sub-header">
<p>Generated on 03/04/2025 13:45:45 from github project id 15 by
phillidgithub</p>
</div>
<div class="sub-header">
<ul>
<li>Note that currently the Project related events are not being picked
up see See https://github.com/orgs/community/discussions/57326 The audit
trail related to moving columns within a project are not being pulled by
graphql</li>
</ul>
</div>
<h3 id="index">Index</h3>
<div class="index-good">
<p><a id=index-CCTC_Components-33 href=#CCTC_Components-33>CCTC_Components-33
| Date and time component</a></p>
<p><a id=index-CCTC_Components-27 href=#CCTC_Components-27>CCTC_Components-27
| Text component</a></p>
<p><a id=index-CCTC_Components-28 href=#CCTC_Components-28>CCTC_Components-28
| Text area component</a></p>
<p><a id=index-CCTC_Components-25 href=#CCTC_Components-25>CCTC_Components-25
| Dropdown component</a></p>
<p><a id=index-CCTC_Components-37 href=#CCTC_Components-37>CCTC_Components-37
| Numeric component</a></p>
<p><a id=index-CCTC_Components-36 href=#CCTC_Components-36>CCTC_Components-36
| Radio component</a></p>
<p><a id=index-CCTC_Components-26 href=#CCTC_Components-26>CCTC_Components-26
| Date component</a></p>
<p><a id=index-CCTC_Components-32 href=#CCTC_Components-32>CCTC_Components-32
| Time component</a></p>
</div>
<section
id="the-following-features-cannot-be-retrieved-uid-title---error"
class="index-errors">
<h4>The following features cannot be retrieved (uid | title -&gt;
error)</h4>
<p>CCTC_Components-43 | Button</p>
<p>error -&gt; Could not get Ok as Result contains an error: expecting
all the urls for user specs to contain branch dp_dev</p>
<p>CCTC_Components-44 | Refresh Button</p>
<p>error -&gt; Could not get Ok as Result contains an error: expecting
all the urls for user specs to contain branch dp_dev</p>
<p>CCTC_Components-30 | Switch Component</p>
<p>error -&gt; Could not get Ok as Result contains an error: expecting
all the urls for user specs to contain branch dp_dev</p>
<p>CCTC_Components-53 | Steps</p>
<p>error -&gt; Could not get Ok as Result contains an error: expecting
all the urls for user specs to contain branch dp_dev</p>
<p>CCTC_Components-42 | Pill</p>
<p>error -&gt; Could not get Ok as Result contains an error: expecting
all the urls for user specs to contain branch dp_dev</p>
<p>CCTC_Components-40 | Confirm_Modal</p>
<p>error -&gt; Could not get Ok as Result contains an error: expecting
all the urls for user specs to contain branch dp_dev</p>
<p>CCTC_Components-35 | Checkbox Component</p>
<p>error -&gt; Could not get Ok as Result contains an error: expecting
all the urls for user specs to contain branch dp_dev</p>
<p>CCTC_Components-22 | Concertina component</p>
<p>error -&gt; Could not get Ok as Result contains an error: expecting
all the urls for user specs to contain branch dp_dev</p>
</section>
<p><br/></p>
<h3 id="features">Features</h3>
<hr />
<div id="CCTC_Components-33">
<strong>Feature:</strong> Date and time component
</div>
<hr />
<table>
<colgroup>
<col style="width: 60%" />
<col style="width: 40%" />
</colgroup>
<tbody>
<tr>
<td style="text-align: left;"><strong>Id:</strong>
I_kwDOKq1Kuc6OPDCf</td>
<td style="text-align: left;"><strong>Uid:</strong>
CCTC_Components-33</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Author:</strong>
phillidgithub</td>
<td style="text-align: left;"><strong>Created:</strong> 02/07/2024
13:44:39</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Assignees:</strong>
phillidgithub</td>
<td style="text-align: left;"><strong>Resource path:</strong>
<a href=https://github.com//CCTC-team/CCTC_Components/issues/33 target=_blank>/CCTC-team/CCTC_Components/issues/33</a></td>
</tr>
<tr>
<td style="text-align: left;"><strong>Milestone:</strong> |none|</td>
<td style="text-align: left;"><strong>Labels:</strong> Validation and
Verification || v1.0.0</td>
</tr>
</tbody>
</table>
<hr />
<table>
<colgroup>
<col style="width: 60%" />
<col style="width: 40%" />
</colgroup>
<tbody>
<tr>
<td style="text-align: left;"><strong>State:</strong> OPEN</td>
<td style="text-align: left;"><strong>State reason</strong>: |not
applicable|</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Includes created edit:</strong>
true</td>
<td style="text-align: left;"></td>
</tr>
<tr>
<td style="text-align: left;"><strong>Closed:</strong> false</td>
<td style="text-align: left;"><strong>Closed on:</strong> |none|</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Editor:</strong>
phillidgithub</td>
<td style="text-align: left;"><strong>Updated on:</strong> 11/07/2024
07:44:12</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Locked:</strong> false</td>
<td style="text-align: left;"><strong>Participants:</strong>
phillidgithub</td>
</tr>
</tbody>
</table>
<hr />
<strong>Project item body:</strong><br />

<div class="feature-body">
<p dir="auto">
Brief description:<br> The date and time component is used to input a
date and time<br> NOTE: the branch of dp_dev is used but should be main
</p>
<p dir="auto">
User specs:<br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/temporal/dateandtime.spec">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/temporal/dateandtime.spec</a>
</p>
<p dir="auto">
Functional scripts:<br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/dateandtime/dateandtime_1.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/dateandtime/dateandtime_1.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/dateandtime/dateandtime_2.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/dateandtime/dateandtime_2.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/dateandtime/dateandtime_3.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/dateandtime/dateandtime_3.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/dateandtime/dateandtime_4.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/dateandtime/dateandtime_4.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/dateandtime/dateandtime_5.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/dateandtime/dateandtime_5.feature</a>
</p>
<p dir="auto">
Test files:<br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/tests/CCTC_Components.bUnit.test/DateAndTimeTests.cs">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/tests/CCTC_Components.bUnit.test/DateAndTimeTests.cs</a>
</p>
<p dir="auto">
<strong>Pre Review:</strong>
</p>
<ul class="contains-task-list">
<li class="task-list-item">
<input type="checkbox" id="" disabled="" class="task-list-item-checkbox">
Script(s) target the correct feature sufficiently
</li>
<li class="task-list-item">
<input type="checkbox" id="" disabled="" class="task-list-item-checkbox">
The user requirements are met
</li>
</ul>
<p dir="auto">
<strong>Post Review:</strong>
</p>
<ul class="contains-task-list">
<li class="task-list-item">
<input type="checkbox" id="" disabled="" class="task-list-item-checkbox">
Script(s) target the correct feature sufficiently
</li>
<li class="task-list-item">
<input type="checkbox" id="" disabled="" class="task-list-item-checkbox">
Script(s) appropriately test the feature
</li>
</ul>
</div>
<hr />
<p><strong>User specification:</strong><br />
</p>
<div class="spec-script">
<div class="line-block"><br />
<em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/temporal/dateandtime.spec</em><br />
<br />
1 - the date and time component date can be typed in as text or can be
entered via a date picker. Time can be typed in as text<br />
2 - the date and time component can allow an empty value and an entered
value can be optionally cleared<br />
3 - the date and time component date and time are validated (date / time
format, min and / or max date / time) and there is feedback provided to
the user via an icon<br />
4 - the date and time component can be made read-only and / or disabled.
The read-only icon is optional<br />
5 - the date and time component has placeholders which match the date
and time formats</div>
</div>
<p><strong>Functional scripts:</strong><br />
</p>
<div class="spec-script">
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/dateandtime/dateandtime_1.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="temporal">@temporal</span> <span
class="citation" data-cites="dateandtime">@dateandtime</span> <span
class="citation" data-cites="dateandtime_1">@dateandtime_1</span>
Feature: the date and time component date can be typed in as text or can
be entered via a date picker. Time can be typed in as text Scenario: the
date and time component sample page is available Given the user is at
the home page When the user selects the “Date and time” component in the
container “Input” Then the url ending is “dateandtimesample”</p>
<pre><code>Scenario: the date and time component date and time can be typed in as text
    Given the user is at the home page
    And the user selects the &quot;Date and time&quot; component in the container &quot;Input&quot;
    When the user enters &quot;06November2023&quot; into the Date part of the Date and Time component
    And the user enters &quot;1214am&quot; into the Time part of the Date and Time component
    Then the Date part of the Date and Time component has the value &quot;06 November 2023&quot;
    And the Time part of the Date and Time component has the value &quot;12:14 am&quot;

Scenario: the date and time component date can be entered via a date picker
    Given the user is at the home page
    And the user selects the &quot;Date and time&quot; component in the container &quot;Input&quot;
    When the user enters &quot;2023-11-06&quot; into the Date part of the Date and Time component via the date picker
    Then the Date part of the Date and Time component has the value &quot;06 November 2023&quot;</code></pre>
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/dateandtime/dateandtime_2.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="temporal">@temporal</span> <span
class="citation" data-cites="dateandtime">@dateandtime</span> <span
class="citation" data-cites="dateandtime_2">@dateandtime_2</span>
Feature: the date and time component can allow an empty value and an
entered value can be optionally cleared Scenario: the date and time
component can allow an empty date and time value when allow clear is set
to true Given the user is at the home page And the user selects the
“Date and time” component in the container “Input” And the user clicks
the “Usage” tab When the user expands the concertina by clicking on the
header with the text “Format: default, allow clear, null initial value,
FeedbackIcon.Error” Then the Date part of the Date and Time component
has the value “” And the Time part of the Date and Time component has
the value “”</p>
<pre><code>Scenario: the date and time component date and time value can be cleared when allow clear is set to true
    Given the user is at the home page
    And the user selects the &quot;Date and time&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Format: default, allow clear, FeedbackIcon.Error&quot;
    When the user enters &quot;&quot; into the Date part of the Date and Time component
    And the user enters &quot;&quot; into the Time part of the Date and Time component
    Then the Date part of the Date and Time component has the value &quot;&quot;
    And the Time part of the Date and Time component has the value &quot;&quot;

Scenario: the date and time component date value can be cleared via the date picker when allow clear is set to true
    Given the user is at the home page
    And the user selects the &quot;Date and time&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Format: default, allow clear, FeedbackIcon.Error&quot;
    When the user enters &quot;&quot; into the Date part of the Date and Time component via the date picker
    Then the Date part of the Date and Time component has the value &quot;&quot;

Scenario: the date and time component can allow an empty date and time value when allow clear is set to false
    Given the user is at the home page
    And the user selects the &quot;Date and time&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    When the user expands the concertina by clicking on the header with the text &quot;Format: default, null initial value, min (2022-01-05 14:33:20) and max (2023-01-05 14:33:22), FeedbackIcon.Both&quot;
    Then the Date part of the Date and Time component has the value &quot;&quot;
    And the Time part of the Date and Time component has the value &quot;&quot;

Scenario: the date and time component date and time value can not be cleared when allow clear is set to false
    Given the user is at the home page
    And the user selects the &quot;Date and time&quot; component in the container &quot;Input&quot;
    When the user enters &quot;&quot; into the Date part of the Date and Time component
    And the user enters &quot;&quot; into the Time part of the Date and Time component
    Then the Date part of the Date and Time component has the value &quot;04 October 2022&quot;
    And the Time part of the Date and Time component has the value &quot;12:13 pm&quot;</code></pre>
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/dateandtime/dateandtime_3.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="temporal">@temporal</span> <span
class="citation" data-cites="dateandtime">@dateandtime</span> <span
class="citation" data-cites="dateandtime_3">@dateandtime_3</span>
Feature: the date and time component date and time are validated (date /
time format, min and / or max date / time) and there is feedback
provided to the user via an icon Scenario: an incomplete date and time
is shown as an error Given the user is at the home page And the user
selects the “Date and time” component in the container “Input” And the
user clicks the “Usage” tab And the user expands the concertina by
clicking on the header with the text “Format: default, allow clear,
FeedbackIcon.Error” When the user enters “2021010” into the Date part of
the Date and Time component And the user enters “11121” into the Time
part of the Date and Time component Then the Date part of the Date and
Time component has the value “2021-01-0” And the Time part of the Date
and Time component has the value “11:12:1” And the Date part of the Date
and Time component displays a red exclamation mark feedback icon And the
Time part of the Date and Time component displays a red exclamation mark
feedback icon And the Date and Time component image matches the base
image “dateandtime-error”</p>
<pre><code>Scenario: a complete date and time is shown as valid
    Given the user is at the home page
    And the user selects the &quot;Date and time&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Format: default, FeedbackIcon.Valid&quot;
    When the user enters &quot;20210105&quot; into the Date part of the Date and Time component
    And the user enters &quot;111215&quot; into the Time part of the Date and Time component
    Then the Date part of the Date and Time component has the value &quot;2021-01-05&quot;
    And the Time part of the Date and Time component has the value &quot;11:12:15&quot;
    And the Date part of the Date and Time component displays a green tick feedback icon
    And the Time part of the Date and Time component displays a green tick feedback icon
    And the Date and Time component image matches the base image &quot;dateandtime-valid&quot;

Scenario: a date earlier than the minimum date is shown as an error
    Given the user is at the home page
    And the user selects the &quot;Date and time&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Format: default, null initial value, min (2022-01-05 14:33:20) and max (2023-01-05 14:33:22), FeedbackIcon.Both&quot;
    When the user enters &quot;143320&quot; into the Time part of the Date and Time component
    And the user enters &quot;20220104&quot; into the Date part of the Date and Time component
    Then the Time part of the Date and Time component has the value &quot;14:33:20&quot;
    And the Date part of the Date and Time component has the value &quot;2022-01-04&quot;
    And the Time part of the Date and Time component displays a green tick feedback icon
    And the Date part of the Date and Time component displays a red exclamation mark feedback icon

Scenario: a date later than the maximum date is shown as an error
    Given the user is at the home page
    And the user selects the &quot;Date and time&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Format: default, null initial value, min (2022-01-05 14:33:20) and max (2023-01-05 14:33:22), FeedbackIcon.Both&quot;
    When the user enters &quot;143320&quot; into the Time part of the Date and Time component
    And the user enters &quot;20230106&quot; into the Date part of the Date and Time component
    Then the Time part of the Date and Time component has the value &quot;14:33:20&quot;
    And the Date part of the Date and Time component has the value &quot;2023-01-06&quot;
    And the Time part of the Date and Time component displays a green tick feedback icon
    And the Date part of the Date and Time component displays a red exclamation mark feedback icon

Scenario: a time earlier than the minimum time is shown as an error
    Given the user is at the home page
    And the user selects the &quot;Date and time&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Format: default, null initial value, min (2022-01-05 14:33:20) and max (2023-01-05 14:33:22), FeedbackIcon.Both&quot;
    When the user enters &quot;20220105&quot; into the Date part of the Date and Time component
    And the user enters &quot;143319&quot; into the Time part of the Date and Time component
    Then the Date part of the Date and Time component has the value &quot;2022-01-05&quot;
    And the Time part of the Date and Time component has the value &quot;14:33:19&quot;
    And the Date part of the Date and Time component displays a green tick feedback icon
    And the Time part of the Date and Time component displays a red exclamation mark feedback icon

Scenario: a time later than the maximum time is shown as an error
    Given the user is at the home page
    And the user selects the &quot;Date and time&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Format: default, null initial value, min (2022-01-05 14:33:20) and max (2023-01-05 14:33:22), FeedbackIcon.Both&quot;
    When the user enters &quot;20220105&quot; into the Date part of the Date and Time component
    And the user enters &quot;143323&quot; into the Time part of the Date and Time component
    Then the Date part of the Date and Time component has the value &quot;2022-01-05&quot;
    And the Time part of the Date and Time component has the value &quot;14:33:23&quot;
    And the Date part of the Date and Time component displays a green tick feedback icon
    And the Time part of the Date and Time component displays a red exclamation mark feedback icon</code></pre>
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/dateandtime/dateandtime_4.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="temporal">@temporal</span> <span
class="citation" data-cites="dateandtime">@dateandtime</span> <span
class="citation" data-cites="dateandtime_4">@dateandtime_4</span>
Feature: the date and time component can be made read-only and / or
disabled. The read-only icon is optional Scenario: the date and time
component can be made read-only Given the user is at the home page And
the user selects the “Date and time” component in the container “Input”
When the user clicks the “Usage” tab And the user expands the concertina
by clicking on the header with the text “Format: dd MMMM yyyy h:mm tt,
FeedbackIcon: default, read-only” Then the Date part of the Date and
Time component is not editable And the Time part of the Date and Time
component is not editable And the Date and Time component image matches
the base image “dateandtime-readonly”</p>
<pre><code>Scenario: the date and time component can be disabled
    Given the user is at the home page
    And the user selects the &quot;Date and time&quot; component in the container &quot;Input&quot;
    When the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Format: dd MMMM yyyy h:mm tt, FeedbackIcon: default, disabled&quot;
    Then the Date part of the Date and Time component is disabled
    And the Time part of the Date and Time component is disabled
    And the Date and Time component image matches the base image &quot;dateandtime-disabled&quot;

Scenario: the date and time component can be made read-only and disabled
    Given the user is at the home page
    And the user selects the &quot;Date and time&quot; component in the container &quot;Input&quot;
    When the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Format: dd MMMM yyyy h:mm tt, FeedbackIcon: default, read-only and disabled&quot;
    Then the Date part of the Date and Time component is not editable
    And the Time part of the Date and Time component is not editable
    And the Date part of the Date and Time component is disabled
    And the Time part of the Date and Time component is disabled
    And the Date and Time component image matches the base image &quot;dateandtime-readonly-disabled&quot;

Scenario: the date and time component can be made read-only without a read-only icon
    Given the user is at the home page
    And the user selects the &quot;Date and time&quot; component in the container &quot;Input&quot;
    When the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Format: dd MMMM yyyy h:mm tt, FeedbackIcon: default, read-only, read-only icon hidden&quot;
    Then the Date part of the Date and Time component is not editable
    And the Time part of the Date and Time component is not editable
    And the Date and Time component image matches the base image &quot;dateandtime-readonly-no-icon&quot;

Scenario: the date and time component can be made read-only and disabled without a read-only icon
    Given the user is at the home page
    And the user selects the &quot;Date and time&quot; component in the container &quot;Input&quot;
    When the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Format: dd MMMM yyyy h:mm tt, FeedbackIcon: default, read-only, disabled and read-only icon hidden&quot;
    Then the Date part of the Date and Time component is not editable
    And the Time part of the Date and Time component is not editable
    And the Date part of the Date and Time component is disabled
    And the Time part of the Date and Time component is disabled
    And the Date and Time component image matches the base image &quot;dateandtime-readonly-disabled-no-icon&quot;</code></pre>
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/dateandtime/dateandtime_5.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="temporal">@temporal</span> <span
class="citation" data-cites="dateandtime">@dateandtime</span> <span
class="citation" data-cites="dateandtime_5">@dateandtime_5</span>
Feature: the date and time component has placeholders which match the
date and time formats Scenario: the date and time component has
placeholders which match the date and time formats Given the user is at
the home page And the user selects the “Date and time” component in the
container “Input” When the user clicks the “Usage” tab And the user
expands the concertina by clicking on the header with the text “Format:
default, allow clear, null initial value, FeedbackIcon.Error” Then the
Date part of the Date and Time component has the placeholder
“yyyy-MM-dd” And the Time part of the Date and Time component has the
placeholder “HH:mm:ss” And the Date and Time component image matches the
base image “dateandtime-placeholder”</p>
</div>
<hr />
<p><strong>Comments:</strong><br />
</p>
<div class="comments">

</div>
<hr />
<p><strong>Timeline events:</strong><br />
</p>
<div class="timeline">
<div class="timeline">
<p>LABELLED | <em>User:</em> phillidgithub <em>Created on:</em>
02/07/2024 13:44:39 <em>Label name:</em> Validation and Verification
<em>Label description:</em> Use this label to identify the issue as
relating to the validation and verification of a feature</p>
<p>LABELLED | <em>User:</em> phillidgithub <em>Created on:</em>
02/07/2024 13:44:39 <em>Label name:</em> v1.0.0 <em>Label
description:</em> Version 1.0.0</p>
<p>ASSIGNED | <em>User:</em> phillidgithub <em>Created on:</em>
11/07/2024 07:44:12 <em>Assignee:</em> phillidgithub</p>
</div>
</div>
<p><br/></p>
<p>————— feature ends —————</p>
<p><br/></p>
<hr />
<div id="CCTC_Components-27">
<strong>Feature:</strong> Text component
</div>
<hr />
<table>
<colgroup>
<col style="width: 60%" />
<col style="width: 40%" />
</colgroup>
<tbody>
<tr>
<td style="text-align: left;"><strong>Id:</strong>
I_kwDOKq1Kuc6NChuI</td>
<td style="text-align: left;"><strong>Uid:</strong>
CCTC_Components-27</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Author:</strong>
phillidgithub</td>
<td style="text-align: left;"><strong>Created:</strong> 21/06/2024
10:34:51</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Assignees:</strong>
phillidgithub</td>
<td style="text-align: left;"><strong>Resource path:</strong>
<a href=https://github.com//CCTC-team/CCTC_Components/issues/27 target=_blank>/CCTC-team/CCTC_Components/issues/27</a></td>
</tr>
<tr>
<td style="text-align: left;"><strong>Milestone:</strong> |none|</td>
<td style="text-align: left;"><strong>Labels:</strong> Validation and
Verification || v1.0.0</td>
</tr>
</tbody>
</table>
<hr />
<table>
<colgroup>
<col style="width: 60%" />
<col style="width: 40%" />
</colgroup>
<tbody>
<tr>
<td style="text-align: left;"><strong>State:</strong> OPEN</td>
<td style="text-align: left;"><strong>State reason</strong>: |not
applicable|</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Includes created edit:</strong>
true</td>
<td style="text-align: left;"></td>
</tr>
<tr>
<td style="text-align: left;"><strong>Closed:</strong> false</td>
<td style="text-align: left;"><strong>Closed on:</strong> |none|</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Editor:</strong>
phillidgithub</td>
<td style="text-align: left;"><strong>Updated on:</strong> 11/07/2024
07:44:49</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Locked:</strong> false</td>
<td style="text-align: left;"><strong>Participants:</strong>
phillidgithub</td>
</tr>
</tbody>
</table>
<hr />
<strong>Project item body:</strong><br />

<div class="feature-body">
<p dir="auto">
<strong>Brief description:</strong><br> The text component is used to
input text<br> NOTE: the branch of dp_dev is used but should be main
</p>
<p dir="auto">
<strong>User specs:</strong><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/textbox/text.spec">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/textbox/text.spec</a>
</p>
<p dir="auto">
<strong>Functional scripts:</strong><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/text/text_1.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/text/text_1.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/text/text_2.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/text/text_2.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/text/text_3.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/text/text_3.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/text/text_4.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/text/text_4.feature</a>
</p>
<p dir="auto">
<strong>Test files:</strong><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/tests/CCTC_Components.bUnit.test/TextTests.cs">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/tests/CCTC_Components.bUnit.test/TextTests.cs</a>
</p>
<p dir="auto">
<strong>Pre Review:</strong>
</p>
<ul class="contains-task-list">
<li class="task-list-item">
<input type="checkbox" id="" disabled="" class="task-list-item-checkbox">
Script(s) target the correct feature sufficiently
</li>
<li class="task-list-item">
<input type="checkbox" id="" disabled="" class="task-list-item-checkbox">
The user requirements are met
</li>
</ul>
<p dir="auto">
<strong>Post Review:</strong>
</p>
<ul class="contains-task-list">
<li class="task-list-item">
<input type="checkbox" id="" disabled="" class="task-list-item-checkbox">
Script(s) target the correct feature sufficiently
</li>
<li class="task-list-item">
<input type="checkbox" id="" disabled="" class="task-list-item-checkbox">
Script(s) appropriately test the feature
</li>
</ul>
</div>
<hr />
<p><strong>User specification:</strong><br />
</p>
<div class="spec-script">
<div class="line-block"><br />
<em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/textbox/text.spec</em><br />
<br />
1 - the text component can receive text input<br />
2 - the text component has the facility to redact text and / or add a
placeholder<br />
3 - the text component input can be constrained by applying an input
mask, preventing whitespace (with a configurable response delay) or
setting a max length<br />
4 - the text component can be made read-only and / or disabled. The
read-only icon is optional</div>
</div>
<p><strong>Functional scripts:</strong><br />
</p>
<div class="spec-script">
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/text/text_1.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="textbox">@textbox</span> <span
class="citation" data-cites="text">@text</span> <span class="citation"
data-cites="text_1">@text_1</span> Feature: the text component can
receive text input Scenario: the text component sample page is available
Given the user is at the home page When the user selects the “Text”
component in the container “Input” Then the url ending is
“textsample”</p>
<pre><code>Scenario: the text component can receive text input
    Given the user is at the home page
    And the user selects the &quot;Text&quot; component in the container &quot;Input&quot;
    When the user enters &quot;Test data&quot; into the Text component
    Then the Text component has the value &quot;Test data&quot;</code></pre>
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/text/text_2.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="textbox">@textbox</span> <span
class="citation" data-cites="text">@text</span> <span class="citation"
data-cites="text_2">@text_2</span> Feature: the text component has the
facility to redact text and / or add a placeholder Scenario: the text
component can redact text Given the user is at the home page And the
user selects the “Text” component in the container “Input” When the user
clicks the “Usage” tab And the user expands the concertina by clicking
on the header with the text “Reactive with prevent whitespace and
redacted text” Then the Text component image matches the base image
“text-redacted”</p>
<pre><code>Scenario: the text component can add a placeholder
    Given the user is at the home page
    And the user selects the &quot;Text&quot; component in the container &quot;Input&quot;
    When the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;With mask and style applied&quot;
    Then the Text component has the placeholder &quot;(000)000/000/00&quot;
    And the Text component image matches the base image &quot;text-placeholder&quot;</code></pre>
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/text/text_3.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="textbox">@textbox</span> <span
class="citation" data-cites="text">@text</span> <span class="citation"
data-cites="text_3">@text_3</span> Feature: the text component input can
be constrained by applying an input mask, preventing whitespace (with a
configurable response delay) or setting a max length Scenario: the text
component input can be constrained by applying an input mask Given the
user is at the home page And the user selects the “Text” component in
the container “Input” And the user clicks the “Usage” tab And the user
expands the concertina by clicking on the header with the text “With
alternative mask” And the Text component has the placeholder
“&gt;L&lt;??-00-0” When the user enters “lp12456” into the Text
component Then the Text component has the value “Lp-12-4”</p>
<pre><code>Scenario: the text component can prevent whitespace
    Given the user is at the home page
    And the user selects the &quot;Text&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Reactive with prevent whitespace and redacted text&quot;
    When the user focusses on the Text component
    And the user presses the backspace key 9 times
    Then the Text component has the value &quot;Demo Text&quot;

Scenario: the text component can set a max length
    Given the user is at the home page
    And the user selects the &quot;Text&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;With callback and max length of 20&quot;
    When the user enters &quot;Some demo Text greater than max length&quot; into the Text component
    Then the Text component has the value &quot;Some demo Text great&quot;</code></pre>
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/text/text_4.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="textbox">@textbox</span> <span
class="citation" data-cites="text">@text</span> <span class="citation"
data-cites="text_4">@text_4</span> Feature: the text component can be
made read-only and / or disabled. The read-only icon is optional
Scenario: the text component can be made read-only Given the user is at
the home page And the user selects the “Text” component in the container
“Input” When the user clicks the “Usage” tab And the user expands the
concertina by clicking on the header with the text “Read-only” Then the
Text component is not editable And the Text component image matches the
base image “text-readonly”</p>
<pre><code>Scenario: the text component can be disabled
    Given the user is at the home page
    And the user selects the &quot;Text&quot; component in the container &quot;Input&quot;
    When the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Disabled&quot;
    Then the Text component is disabled
    And the Text component image matches the base image &quot;text-disabled&quot;

Scenario: the text component can be made read-only and disabled
    Given the user is at the home page
    And the user selects the &quot;Text&quot; component in the container &quot;Input&quot;
    When the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Disabled and read-only&quot;
    Then the Text component is not editable
    And the Text component is disabled
    And the Text component image matches the base image &quot;text-readonly-disabled&quot;

Scenario: the text component can be made read-only without a read-only icon
    Given the user is at the home page
    And the user selects the &quot;Text&quot; component in the container &quot;Input&quot;
    When the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Read-only long (hide read-only icon)&quot;
    Then the Text component is not editable
    And the Text component image matches the base image &quot;text-readonly-no-icon&quot;

Scenario: the text component can be made read-only and disabled without a read-only icon
    Given the user is at the home page
    And the user selects the &quot;Text&quot; component in the container &quot;Input&quot;
    When the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Disabled and read-only (hide read-only icon)&quot;
    Then the Text component is not editable
    And the Text component is disabled
    And the Text component image matches the base image &quot;text-readonly-disabled-no-icon&quot;</code></pre>
</div>
<hr />
<p><strong>Comments:</strong><br />
</p>
<div class="comments">

</div>
<hr />
<p><strong>Timeline events:</strong><br />
</p>
<div class="timeline">
<div class="timeline">
<p>LABELLED | <em>User:</em> phillidgithub <em>Created on:</em>
21/06/2024 10:34:51 <em>Label name:</em> Validation and Verification
<em>Label description:</em> Use this label to identify the issue as
relating to the validation and verification of a feature</p>
<p>LABELLED | <em>User:</em> phillidgithub <em>Created on:</em>
21/06/2024 10:34:51 <em>Label name:</em> v1.0.0 <em>Label
description:</em> Version 1.0.0</p>
<p>ASSIGNED | <em>User:</em> phillidgithub <em>Created on:</em>
11/07/2024 07:44:49 <em>Assignee:</em> phillidgithub</p>
</div>
</div>
<p><br/></p>
<p>————— feature ends —————</p>
<p><br/></p>
<hr />
<div id="CCTC_Components-28">
<strong>Feature:</strong> Text area component
</div>
<hr />
<table>
<colgroup>
<col style="width: 60%" />
<col style="width: 40%" />
</colgroup>
<tbody>
<tr>
<td style="text-align: left;"><strong>Id:</strong>
I_kwDOKq1Kuc6NCiSe</td>
<td style="text-align: left;"><strong>Uid:</strong>
CCTC_Components-28</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Author:</strong>
phillidgithub</td>
<td style="text-align: left;"><strong>Created:</strong> 21/06/2024
10:36:16</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Assignees:</strong>
phillidgithub</td>
<td style="text-align: left;"><strong>Resource path:</strong>
<a href=https://github.com//CCTC-team/CCTC_Components/issues/28 target=_blank>/CCTC-team/CCTC_Components/issues/28</a></td>
</tr>
<tr>
<td style="text-align: left;"><strong>Milestone:</strong> |none|</td>
<td style="text-align: left;"><strong>Labels:</strong> Validation and
Verification || v1.0.0</td>
</tr>
</tbody>
</table>
<hr />
<table>
<colgroup>
<col style="width: 60%" />
<col style="width: 40%" />
</colgroup>
<tbody>
<tr>
<td style="text-align: left;"><strong>State:</strong> OPEN</td>
<td style="text-align: left;"><strong>State reason</strong>: |not
applicable|</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Includes created edit:</strong>
true</td>
<td style="text-align: left;"></td>
</tr>
<tr>
<td style="text-align: left;"><strong>Closed:</strong> false</td>
<td style="text-align: left;"><strong>Closed on:</strong> |none|</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Editor:</strong>
phillidgithub</td>
<td style="text-align: left;"><strong>Updated on:</strong> 11/07/2024
07:44:16</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Locked:</strong> false</td>
<td style="text-align: left;"><strong>Participants:</strong>
phillidgithub</td>
</tr>
</tbody>
</table>
<hr />
<strong>Project item body:</strong><br />

<div class="feature-body">
<p dir="auto">
<strong>Brief description:</strong><br> The text area component is used
to input text<br> NOTE: the branch of dp_dev is used but should be main
</p>
<p dir="auto">
<strong>User specs:</strong><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/textbox/textarea.spec">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/textbox/textarea.spec</a>
</p>
<p dir="auto">
<strong>Functional scripts:</strong><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/textarea/textarea_1.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/textarea/textarea_1.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/textarea/textarea_2.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/textarea/textarea_2.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/textarea/textarea_3.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/textarea/textarea_3.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/textarea/textarea_4.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/textarea/textarea_4.feature</a>
</p>
<p dir="auto">
<strong>Test files:</strong><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/tests/CCTC_Components.bUnit.test/TextTests.cs">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/tests/CCTC_Components.bUnit.test/TextTests.cs</a>
</p>
<p dir="auto">
<strong>Pre Review:</strong>
</p>
<ul class="contains-task-list">
<li class="task-list-item">
<input type="checkbox" id="" disabled="" class="task-list-item-checkbox">
Script(s) target the correct feature sufficiently
</li>
<li class="task-list-item">
<input type="checkbox" id="" disabled="" class="task-list-item-checkbox">
The user requirements are met
</li>
</ul>
<p dir="auto">
<strong>Post Review:</strong>
</p>
<ul class="contains-task-list">
<li class="task-list-item">
<input type="checkbox" id="" disabled="" class="task-list-item-checkbox">
Script(s) target the correct feature sufficiently
</li>
<li class="task-list-item">
<input type="checkbox" id="" disabled="" class="task-list-item-checkbox">
Script(s) appropriately test the feature
</li>
</ul>
</div>
<hr />
<p><strong>User specification:</strong><br />
</p>
<div class="spec-script">
<div class="line-block"><br />
<em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/textbox/textarea.spec</em><br />
<br />
1 - the text area component can receive text input<br />
2 - the text area component has the facility to add a placeholder and /
or set an initial number of display rows<br />
3 - the text area component input can be constrained by applying an
input mask, preventing whitespace (with a configurable response delay)
or setting a max length<br />
4 - the text area component can be made read-only and / or disabled. The
read-only icon is optional</div>
</div>
<p><strong>Functional scripts:</strong><br />
</p>
<div class="spec-script">
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/textarea/textarea_1.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="textbox">@textbox</span> <span
class="citation" data-cites="textarea">@textarea</span> <span
class="citation" data-cites="textarea_1">@textarea_1</span> Feature: the
text area component can receive text input Scenario: the text area
component sample page is available Given the user is at the home page
When the user selects the “Text area” component in the container “Input”
Then the url ending is “textareasample”</p>
<pre><code>Scenario: the text area component can receive text input
    Given the user is at the home page
    And the user selects the &quot;Text area&quot; component in the container &quot;Input&quot;
    When the user enters &quot;Test data&quot; into the Text area component
    Then the Text area component has the value &quot;Test data&quot;</code></pre>
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/textarea/textarea_2.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="textbox">@textbox</span> <span
class="citation" data-cites="textarea">@textarea</span> <span
class="citation" data-cites="textarea_2">@textarea_2</span> Feature: the
text area component has the facility to add a placeholder and / or set
an initial number of display rows Scenario: the text area component can
add a placeholder Given the user is at the home page And the user
selects the “Text area” component in the container “Input” When the user
clicks the “Usage” tab And the user expands the concertina by clicking
on the header with the text “With mask and style applied” Then the Text
area component has the placeholder “(000)000/000/00” And the Text area
component image matches the base image “textarea-placeholder”</p>
<pre><code>Scenario: the text area component can set an initial number of display rows
    Given the user is at the home page
    And the user selects the &quot;Text area&quot; component in the container &quot;Input&quot;
    When the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;With four given rows and CssClass applied&quot;
    Then the Text area component has 4 rows</code></pre>
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/textarea/textarea_3.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="textbox">@textbox</span> <span
class="citation" data-cites="textarea">@textarea</span> <span
class="citation" data-cites="textarea_3">@textarea_3</span> Feature: the
text area component input can be constrained by applying an input mask,
preventing whitespace (with a configurable response delay) or setting a
max length Scenario: the text area component input can be constrained by
applying an input mask Given the user is at the home page And the user
selects the “Text area” component in the container “Input” And the user
clicks the “Usage” tab And the user expands the concertina by clicking
on the header with the text “With mask and style applied” And the Text
area component has the placeholder “(000)000/000/00” When the user
enters “4857463545867” into the Text area component Then the Text area
component has the value “(485)746/354/58”</p>
<pre><code>Scenario: the text area component can prevent whitespace
    Given the user is at the home page
    And the user selects the &quot;Text area&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Reactive with prevent whitespace&quot;
    When the user focusses on the Text area component
    And the user presses the backspace key 13 times
    Then the Text area component has the value &quot;Demo TextArea&quot;

Scenario: the text area component can set a max length
    Given the user is at the home page
    And the user selects the &quot;Text area&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;With callback and max length of 20&quot;
    When the user enters &quot;Some demo Text greater than max length&quot; into the Text area component
    Then the Text area component has the value &quot;Some demo Text great&quot;</code></pre>
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/textarea/textarea_4.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="textbox">@textbox</span> <span
class="citation" data-cites="textarea">@textarea</span> <span
class="citation" data-cites="textarea_4">@textarea_4</span> Feature: the
text area component can be made read-only and / or disabled. The
read-only icon is optional Scenario: the text area component can be made
read-only Given the user is at the home page And the user selects the
“Text area” component in the container “Input” When the user clicks the
“Usage” tab And the user expands the concertina by clicking on the
header with the text “Read-only” Then the Text area component is not
editable And the Text area component image matches the base image
“textarea-readonly”</p>
<pre><code>Scenario: the text area component can be disabled
    Given the user is at the home page
    And the user selects the &quot;Text area&quot; component in the container &quot;Input&quot;
    When the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Disabled&quot;
    Then the Text area component is disabled
    And the Text area component image matches the base image &quot;textarea-disabled&quot;

Scenario: the text area component can be made read-only and disabled
    Given the user is at the home page
    And the user selects the &quot;Text area&quot; component in the container &quot;Input&quot;
    When the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Disabled and read-only&quot;
    Then the Text area component is not editable
    And the Text area component is disabled
    And the Text area component image matches the base image &quot;textarea-readonly-disabled&quot;

Scenario: the text area component can be made read-only without a read-only icon
    Given the user is at the home page
    And the user selects the &quot;Text area&quot; component in the container &quot;Input&quot;
    When the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Read-only (hide read-only icon)&quot;
    Then the Text area component is not editable
    And the Text area component image matches the base image &quot;textarea-readonly-no-icon&quot;

Scenario: the text area component can be made read-only and disabled without a read-only icon
    Given the user is at the home page
    And the user selects the &quot;Text area&quot; component in the container &quot;Input&quot;
    When the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Disabled and read-only (hide read-only icon)&quot;
    Then the Text area component is not editable
    And the Text area component is disabled
    And the Text area component image matches the base image &quot;textarea-readonly-disabled-no-icon&quot;</code></pre>
</div>
<hr />
<p><strong>Comments:</strong><br />
</p>
<div class="comments">

</div>
<hr />
<p><strong>Timeline events:</strong><br />
</p>
<div class="timeline">
<div class="timeline">
<p>LABELLED | <em>User:</em> phillidgithub <em>Created on:</em>
21/06/2024 10:36:16 <em>Label name:</em> Validation and Verification
<em>Label description:</em> Use this label to identify the issue as
relating to the validation and verification of a feature</p>
<p>LABELLED | <em>User:</em> phillidgithub <em>Created on:</em>
21/06/2024 10:36:16 <em>Label name:</em> v1.0.0 <em>Label
description:</em> Version 1.0.0</p>
<p>RENAMED TITLE | <em>User:</em> phillidgithub <em>Created on:</em>
24/06/2024 11:20:01 <em>Previous title:</em> Text area <em>Current
title:</em> Text area component</p>
<p>ASSIGNED | <em>User:</em> phillidgithub <em>Created on:</em>
11/07/2024 07:44:15 <em>Assignee:</em> phillidgithub</p>
</div>
</div>
<p><br/></p>
<p>————— feature ends —————</p>
<p><br/></p>
<hr />
<div id="CCTC_Components-25">
<strong>Feature:</strong> Dropdown component
</div>
<hr />
<table>
<colgroup>
<col style="width: 60%" />
<col style="width: 40%" />
</colgroup>
<tbody>
<tr>
<td style="text-align: left;"><strong>Id:</strong>
I_kwDOKq1Kuc6NCbJN</td>
<td style="text-align: left;"><strong>Uid:</strong>
CCTC_Components-25</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Author:</strong>
phillidgithub</td>
<td style="text-align: left;"><strong>Created:</strong> 21/06/2024
10:20:54</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Assignees:</strong>
phillidgithub</td>
<td style="text-align: left;"><strong>Resource path:</strong>
<a href=https://github.com//CCTC-team/CCTC_Components/issues/25 target=_blank>/CCTC-team/CCTC_Components/issues/25</a></td>
</tr>
<tr>
<td style="text-align: left;"><strong>Milestone:</strong> |none|</td>
<td style="text-align: left;"><strong>Labels:</strong> Validation and
Verification || v1.0.0</td>
</tr>
</tbody>
</table>
<hr />
<table>
<colgroup>
<col style="width: 60%" />
<col style="width: 40%" />
</colgroup>
<tbody>
<tr>
<td style="text-align: left;"><strong>State:</strong> OPEN</td>
<td style="text-align: left;"><strong>State reason</strong>: |not
applicable|</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Includes created edit:</strong>
true</td>
<td style="text-align: left;"></td>
</tr>
<tr>
<td style="text-align: left;"><strong>Closed:</strong> false</td>
<td style="text-align: left;"><strong>Closed on:</strong> |none|</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Editor:</strong>
phillidgithub</td>
<td style="text-align: left;"><strong>Updated on:</strong> 11/07/2024
07:43:09</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Locked:</strong> false</td>
<td style="text-align: left;"><strong>Participants:</strong>
phillidgithub</td>
</tr>
</tbody>
</table>
<hr />
<strong>Project item body:</strong><br />

<div class="feature-body">
<p dir="auto">
<strong>Brief description:</strong><br> The dropdown component is used
to select an option from a list of options<br> NOTE: the branch of
dp_dev is used but should be main
</p>
<p dir="auto">
<strong>User specs:</strong><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/dropdown.spec">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/dropdown.spec</a>
</p>
<p dir="auto">
<strong>Functional scripts:</strong><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/dropdown/dropdown_1.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/dropdown/dropdown_1.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/dropdown/dropdown_2.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/dropdown/dropdown_2.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/dropdown/dropdown_3.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/dropdown/dropdown_3.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/dropdown/dropdown_4.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/dropdown/dropdown_4.feature</a>
</p>
<p dir="auto">
<strong>Test files:</strong><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/tests/CCTC_Components.bUnit.test/DropDownTests.cs">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/tests/CCTC_Components.bUnit.test/DropDownTests.cs</a>
</p>
<p dir="auto">
<strong>Pre Review:</strong>
</p>
<ul class="contains-task-list">
<li class="task-list-item">
<input type="checkbox" id="" disabled="" class="task-list-item-checkbox">
Script(s) target the correct feature sufficiently
</li>
<li class="task-list-item">
<input type="checkbox" id="" disabled="" class="task-list-item-checkbox">
The user requirements are met
</li>
</ul>
<p dir="auto">
<strong>Post Review:</strong>
</p>
<ul class="contains-task-list">
<li class="task-list-item">
<input type="checkbox" id="" disabled="" class="task-list-item-checkbox">
Script(s) target the correct feature sufficiently
</li>
<li class="task-list-item">
<input type="checkbox" id="" disabled="" class="task-list-item-checkbox">
Script(s) appropriately test the feature
</li>
</ul>
</div>
<hr />
<p><strong>User specification:</strong><br />
</p>
<div class="spec-script">
<div class="line-block"><br />
<em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/dropdown.spec</em><br />
<br />
1 - the dropdown component option can be changed using the mouse or the
keyboard<br />
2 - the dropdown component can allow an empty value and an entered value
can be optionally cleared<br />
3 - the dropdown component handles options that have a large amount of
text and can display tooltips when required<br />
4 - the dropdown component can be made read-only and individual options
can be disabled or set as visible. The read-only icon is optional</div>
</div>
<p><strong>Functional scripts:</strong><br />
</p>
<div class="spec-script">
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/dropdown/dropdown_1.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="dropdown">@dropdown</span> <span
class="citation" data-cites="dropdown_1">@dropdown_1</span> Feature: the
dropdown component option can be changed using the mouse or the keyboard
Scenario: the dropdown component sample page is available Given the user
is at the home page When the user selects the “Dropdown” component in
the container “Input” Then the url ending is “dropdownsample”</p>
<pre><code>Scenario: the dropdown component option can be changed using the mouse
    Given the user is at the home page
    And the user selects the &quot;Dropdown&quot; component in the container &quot;Input&quot;
    And the current selected dropdown option has the text &quot;One&quot;
    When the user clicks on the current selected dropdown option
    And the dropdown is expanded
    And the user clicks on the dropdown option with the text &quot;Two&quot;
    Then the current selected dropdown option has the text &quot;Two&quot;

Scenario: the dropdown component option can be changed using the keyboard
    Given the user is at the home page
    And the user selects the &quot;Dropdown&quot; component in the container &quot;Input&quot;
    And the current selected dropdown option has the text &quot;One&quot;
    When the user clicks on the current selected dropdown option
    And the dropdown is expanded
    And the user presses the down arrow key 2 times
    And the user presses the up arrow key once
    Then the current selected dropdown option has the text &quot;Two&quot;</code></pre>
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/dropdown/dropdown_2.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="dropdown">@dropdown</span> <span
class="citation" data-cites="dropdown_2">@dropdown_2</span> Feature: the
dropdown component can allow an empty value and an entered value can be
optionally cleared Scenario: the current selected dropdown option can be
empty when show clear is set to true Given the user is at the home page
And the user selects the “Dropdown” component in the container “Input”
When the user clicks the “Usage” tab And the user expands the concertina
by clicking on the header with the text “Dropdown type: Nullable Tuple
data with show clear” Then the current selected dropdown option has the
text “” And the Dropdown component image matches the base image
“dropdown-cleared-hide-clear-icon”</p>
<pre><code>Scenario: the current selected dropdown option can be cleared when show clear is set to true
    Given the user is at the home page
    And the user selects the &quot;Dropdown&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Dropdown type: nullable int, with callback, show clear and disabled options applied&quot;
    And the current selected dropdown option has the text &quot;2&quot;
    And the Dropdown component image matches the base image &quot;dropdown-show-clear-icon&quot;
    When the user clicks on the dropdown clear icon
    Then the current selected dropdown option has the text &quot;&quot;
    And the dropdown clear icon is no longer in view
    And the Dropdown component image matches the base image &quot;dropdown-cleared-hide-clear-icon&quot;

Scenario: the current selected dropdown option can be empty when show clear is set to false
    Given the user is at the home page
    And the user selects the &quot;Dropdown&quot; component in the container &quot;Input&quot;
    When the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Dropdown type: Person class, null initial value, do not show clear&quot;
    Then the current selected dropdown option has the text &quot;&quot;
    And the Dropdown component image matches the base image &quot;dropdown-cleared-no-clear-icon&quot;</code></pre>
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/dropdown/dropdown_3.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="dropdown">@dropdown</span> <span
class="citation" data-cites="dropdown_3">@dropdown_3</span> Feature: the
dropdown component handles options that have a large amount of text and
can display tooltips when required Scenario: the current selected option
can be truncated Given the user is at the home page And the user selects
the “Dropdown” component in the container “Input” When the user clicks
on the current selected dropdown option And the user moves the mouse
relative to the main viewport with an x coordinate of 0 and y coordinate
of 0 And the dropdown is expanded And the user presses the down arrow
key 5 times And the user presses the enter key once Then the Dropdown
component image matches the base image “dropdown-truncated”</p>
<pre><code>Scenario: long dropdown options in the listbox can wrap after three lines
    Given the user is at the home page
    And the user selects the &quot;Dropdown&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Dropdown type: Person record, DropDownOptionOverflow: Wrap, TooltipPlacement: Right, DropDownTooltipBehaviour: EnabledOnOptionOverflow&quot;
    When the user clicks on the current selected dropdown option
    And the user moves the mouse relative to the main viewport with an x coordinate of 0 and y coordinate of 0
    And the dropdown is expanded
    And the user presses the up arrow key once
    Then the Dropdown component listbox image matches the base image &quot;dropdown-option-wrap&quot;

Scenario: long dropdown options in the listbox can truncate on one line
    Given the user is at the home page
    And the user selects the &quot;Dropdown&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Dropdown type: Person record, DropDownOptionOverflow: NoWrap, TooltipPlacement: Right, DropDownTooltipBehaviour: EnabledOnOptionOverflow&quot;
    When the user clicks on the current selected dropdown option
    And the user moves the mouse relative to the main viewport with an x coordinate of 0 and y coordinate of 0
    And the dropdown is expanded
    And the user presses the up arrow key once
    Then the Dropdown component listbox image matches the base image &quot;dropdown-option-nowrap&quot;

Scenario: long dropdown options in the listbox can scroll on one line
    Given the user is at the home page
    And the user selects the &quot;Dropdown&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Dropdown type: Person record, DropDownOptionOverflow: Scroll, DropDownTooltipBehaviour: Disabled&quot;
    When the user clicks on the current selected dropdown option
    And the user moves the mouse relative to the main viewport with an x coordinate of 0 and y coordinate of 0
    And the dropdown is expanded
    And the user presses the up arrow key once
    Then the dropdown options can scroll
    And the Dropdown component listbox image matches the base image &quot;dropdown-option-scroll&quot;

Scenario: long dropdown options in the listbox be displayed in full
    Given the user is at the home page
    And the user selects the &quot;Dropdown&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Dropdown type: Person record, DropDownOptionOverflow: None, TooltipPlacement: Right, DropDownTooltipBehaviour: EnabledOnOptionOverflow&quot;
    When the user clicks on the current selected dropdown option
    And the user moves the mouse relative to the main viewport with an x coordinate of 0 and y coordinate of 0
    And the dropdown is expanded
    And the user presses the up arrow key once
    Then the Dropdown component listbox image matches the base image &quot;dropdown-option-full&quot;

Scenario: the current selected option can have a tooltip
    Given the user is at the home page
    And the user selects the &quot;Dropdown&quot; component in the container &quot;Input&quot;
    When the user clicks on the current selected dropdown option
    And the dropdown is expanded
    And the user presses the down arrow key 5 times
    And the user presses the enter key once
    Then the current selected dropdown option has a tooltip enabled containing the full option text

Scenario: the current selected option can not have a tooltip
    Given the user is at the home page
    And the user selects the &quot;Dropdown&quot; component in the container &quot;Input&quot;
    When the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Dropdown type: Person record, DropDownOptionOverflow: Scroll, DropDownTooltipBehaviour: Disabled&quot;
    Then the current selected dropdown option does not have a tooltip enabled

Scenario: dropdown options can have a tooltip
    Given the user is at the home page
    When the user selects the &quot;Dropdown&quot; component in the container &quot;Input&quot;
    Then the dropdown options have tooltips enabled containing the full option text

Scenario: dropdown options can not have a tooltip
    Given the user is at the home page
    And the user selects the &quot;Dropdown&quot; component in the container &quot;Input&quot;
    When the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Dropdown type: Person record, DropDownOptionOverflow: Scroll, DropDownTooltipBehaviour: Disabled&quot;
    Then the dropdown options do not have tooltips enabled</code></pre>
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/dropdown/dropdown_4.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="dropdown">@dropdown</span> <span
class="citation" data-cites="dropdown_4">@dropdown_4</span> Feature: the
dropdown component can be made read-only and individual options can be
disabled or set as visible. The read-only icon is optional Scenario: the
dropdown component can be made read-only Given the user is at the home
page And the user selects the “Dropdown” component in the container
“Input” And the user clicks the “Usage” tab And the user expands the
concertina by clicking on the header with the text “Dropdown type: Tuple
data, read-only, TooltipPlacement.Bottom” When the user clicks on the
current selected dropdown option And the user moves the mouse relative
to the main viewport with an x coordinate of 0 and y coordinate of 0
Then the dropdown is not expanded And the Dropdown component image
matches the base image “dropdown-readonly”</p>
<pre><code>Scenario: the dropdown component can be disabled
    Given the user is at the home page
    And the user selects the &quot;Dropdown&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Dropdown type: Tuple data, disabled, TooltipPlacement: Right&quot;
    When the user clicks on the current selected dropdown option
    And the user moves the mouse relative to the main viewport with an x coordinate of 0 and y coordinate of 0
    Then the dropdown is not expanded
    And the Dropdown component image matches the base image &quot;dropdown-disabled&quot;

Scenario: the dropdown component can be made read-only and disabled
    Given the user is at the home page
    And the user selects the &quot;Dropdown&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Dropdown type: Tuple data, read-only, disabled, TooltipPlacement: Right&quot;
    When the user clicks on the current selected dropdown option
    And the user moves the mouse relative to the main viewport with an x coordinate of 0 and y coordinate of 0
    Then the dropdown is not expanded
    And the Dropdown component image matches the base image &quot;dropdown-readonly-disabled&quot;

Scenario: the dropdown component can be made read-only without a read-only icon
    Given the user is at the home page
    And the user selects the &quot;Dropdown&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Dropdown type: Tuple data, read-only, hide read-only icon, TooltipPlacement: Top&quot;
    When the user clicks on the current selected dropdown option
    And the user moves the mouse relative to the main viewport with an x coordinate of 0 and y coordinate of 0
    Then the dropdown is not expanded
    And the Dropdown component image matches the base image &quot;dropdown-readonly-no-icon&quot;

Scenario: the dropdown component can be made read-only and disabled without a read-only icon
    Given the user is at the home page
    And the user selects the &quot;Dropdown&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Dropdown type: Tuple data, read-only, disabled, hide read-only icon, TooltipPlacement: Bottom&quot;
    When the user clicks on the current selected dropdown option
    And the user moves the mouse relative to the main viewport with an x coordinate of 0 and y coordinate of 0
    Then the dropdown is not expanded
    And the Dropdown component image matches the base image &quot;dropdown-readonly-disabled-no-icon&quot;

Scenario: the dropdown component individual options can be disabled
    Given the user is at the home page
    And the user selects the &quot;Dropdown&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Dropdown type: nullable int, with callback, show clear and disabled options applied&quot;
    And the current selected dropdown option has the text &quot;2&quot;
    When the user clicks on the current selected dropdown option
    And the user moves the mouse relative to the main viewport with an x coordinate of 0 and y coordinate of 0
    And the dropdown is expanded
    And the Dropdown component listbox image matches the base image &quot;dropdown-option-disabled&quot;
    And the user clicks on the dropdown option with the text &quot;4&quot;
    Then the current selected dropdown option has the text &quot;2&quot;</code></pre>
</div>
<hr />
<p><strong>Comments:</strong><br />
</p>
<div class="comments">

</div>
<hr />
<p><strong>Timeline events:</strong><br />
</p>
<div class="timeline">
<div class="timeline">
<p>LABELLED | <em>User:</em> phillidgithub <em>Created on:</em>
21/06/2024 10:20:55 <em>Label name:</em> Validation and Verification
<em>Label description:</em> Use this label to identify the issue as
relating to the validation and verification of a feature</p>
<p>LABELLED | <em>User:</em> phillidgithub <em>Created on:</em>
21/06/2024 10:21:20 <em>Label name:</em> v1.0.0 <em>Label
description:</em> Version 1.0.0</p>
<p>ASSIGNED | <em>User:</em> phillidgithub <em>Created on:</em>
11/07/2024 07:43:09 <em>Assignee:</em> phillidgithub</p>
</div>
</div>
<p><br/></p>
<p>————— feature ends —————</p>
<p><br/></p>
<hr />
<div id="CCTC_Components-37">
<strong>Feature:</strong> Numeric component
</div>
<hr />
<table>
<colgroup>
<col style="width: 60%" />
<col style="width: 40%" />
</colgroup>
<tbody>
<tr>
<td style="text-align: left;"><strong>Id:</strong>
I_kwDOKq1Kuc6PMx_8</td>
<td style="text-align: left;"><strong>Uid:</strong>
CCTC_Components-37</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Author:</strong>
phillidgithub</td>
<td style="text-align: left;"><strong>Created:</strong> 11/07/2024
07:40:52</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Assignees:</strong>
phillidgithub</td>
<td style="text-align: left;"><strong>Resource path:</strong>
<a href=https://github.com//CCTC-team/CCTC_Components/issues/37 target=_blank>/CCTC-team/CCTC_Components/issues/37</a></td>
</tr>
<tr>
<td style="text-align: left;"><strong>Milestone:</strong> |none|</td>
<td style="text-align: left;"><strong>Labels:</strong> Validation and
Verification || v1.0.0</td>
</tr>
</tbody>
</table>
<hr />
<table>
<colgroup>
<col style="width: 60%" />
<col style="width: 40%" />
</colgroup>
<tbody>
<tr>
<td style="text-align: left;"><strong>State:</strong> OPEN</td>
<td style="text-align: left;"><strong>State reason</strong>: |not
applicable|</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Includes created edit:</strong>
true</td>
<td style="text-align: left;"></td>
</tr>
<tr>
<td style="text-align: left;"><strong>Closed:</strong> false</td>
<td style="text-align: left;"><strong>Closed on:</strong> |none|</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Editor:</strong>
phillidgithub</td>
<td style="text-align: left;"><strong>Updated on:</strong> 11/07/2024
07:43:16</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Locked:</strong> false</td>
<td style="text-align: left;"><strong>Participants:</strong>
phillidgithub</td>
</tr>
</tbody>
</table>
<hr />
<strong>Project item body:</strong><br />

<div class="feature-body">
<p dir="auto">
Brief description:<br> The numeric component is used to input a
number<br> NOTE: the branch of dp_dev is used but should be main
</p>
<p dir="auto">
User specs:<br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/numeric.spec">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/numeric.spec</a>
</p>
<p dir="auto">
Functional scripts:<br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/numeric/numeric_1.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/numeric/numeric_1.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/numeric/numeric_2.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/numeric/numeric_2.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/numeric/numeric_3.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/numeric/numeric_3.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/numeric/numeric_4.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/numeric/numeric_4.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/numeric/numeric_5.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/numeric/numeric_5.feature</a>
</p>
<p dir="auto">
Test files:<br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/tests/CCTC_Components.bUnit.test/NumericTests.cs">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/tests/CCTC_Components.bUnit.test/NumericTests.cs</a>
</p>
<p dir="auto">
<strong>Pre Review:</strong>
</p>
<ul class="contains-task-list">
<li class="task-list-item">
<input type="checkbox" id="" disabled="" class="task-list-item-checkbox">
Script(s) target the correct feature sufficiently
</li>
<li class="task-list-item">
<input type="checkbox" id="" disabled="" class="task-list-item-checkbox">
The user requirements are met
</li>
</ul>
<p dir="auto">
<strong>Post Review:</strong>
</p>
<ul class="contains-task-list">
<li class="task-list-item">
<input type="checkbox" id="" disabled="" class="task-list-item-checkbox">
Script(s) target the correct feature sufficiently
</li>
<li class="task-list-item">
<input type="checkbox" id="" disabled="" class="task-list-item-checkbox">
Script(s) appropriately test the feature
</li>
</ul>
</div>
<hr />
<p><strong>User specification:</strong><br />
</p>
<div class="spec-script">
<div class="line-block"><br />
<em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/numeric.spec</em><br />
<br />
1 - the numeric component can receive numeric input<br />
2 - the numeric component has the facility to redact text and / or add a
placeholder<br />
3 - the numeric component input can be constrained by preventing
whitespace (with a configurable response delay) or setting a max
length<br />
4 - the numeric component input can be constrained by applying an
optional number format and the mathematical rounding method can be
specified<br />
5 - the numeric component can be made read-only and / or disabled. The
read-only icon is optional</div>
</div>
<p><strong>Functional scripts:</strong><br />
</p>
<div class="spec-script">
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/numeric/numeric_1.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="numeric">@numeric</span> <span
class="citation" data-cites="numeric_1">@numeric_1</span> Feature: the
numeric component can receive numeric input Scenario: the numeric
component sample page is available Given the user is at the home page
When the user selects the “Numeric” component in the container “Input”
Then the url ending is “numericsample”</p>
<pre><code>Scenario: the numeric component can receive numeric input
    Given the user is at the home page
    Given the user selects the &quot;Numeric&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Format: -##00&quot;
    When the user enters &quot;123456&quot; into the Numeric component
    Then the Numeric component has the value &quot;1234&quot;</code></pre>
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/numeric/numeric_2.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="numeric">@numeric</span> <span
class="citation" data-cites="numeric_2">@numeric_2</span> Feature: the
numeric component has the facility to redact text and / or add a
placeholder Scenario: the numeric component has the facility to redact
text Given the user is at the home page Given the user selects the
“Numeric” component in the container “Input” And the user clicks the
“Usage” tab And the user expands the concertina by clicking on the
header with the text “No format with a null initial value, redact text,
allows whitespace, max length” When the user enters “123” into the
Numeric component Then the Numeric component image matches the base
image “numeric-redacted”</p>
<pre><code>Scenario: the numeric component can add a placeholder
    Given the user is at the home page
    Given the user selects the &quot;Numeric&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    When the user expands the concertina by clicking on the header with the text &quot;No format with a null initial value, redact text, allows whitespace, max length&quot;
    Then the Numeric component has the placeholder &quot;123&quot;
    And the Numeric component image matches the base image &quot;numeric-placeholder&quot;</code></pre>
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/numeric/numeric_3.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="numeric">@numeric</span> <span
class="citation" data-cites="numeric_3">@numeric_3</span> Feature: the
numeric component input can be constrained by preventing whitespace
(with a configurable response delay) or setting a max length Scenario:
the numeric component can prevent whitespace Given the user is at the
home page Given the user selects the “Numeric” component in the
container “Input” And the user clicks the “Usage” tab And the user
expands the concertina by clicking on the header with the text “Format:
-##00” When the user focusses on the Numeric component And the user
presses the backspace key 3 times Then the Numeric component has the
value “123”</p>
<pre><code>Scenario: the numeric component can set a max length
    Given the user is at the home page
    Given the user selects the &quot;Numeric&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;No format with a null initial value, redact text, allows whitespace, max length&quot;
    When the user enters &quot;12345678&quot; into the Text component
    Then the Text component has the value &quot;1234567&quot;</code></pre>
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/numeric/numeric_4.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="numeric">@numeric</span> <span
class="citation" data-cites="numeric_4">@numeric_4</span> Feature: the
numeric component input can be constrained by applying an optional
number format and the mathematical rounding method can be specified
Scenario: the numeric component input can have no format Given the user
is at the home page Given the user selects the “Numeric” component in
the container “Input” And the user clicks the “Usage” tab And the user
expands the concertina by clicking on the header with the text “No
format with a null initial value, redact text, allows whitespace, max
length” When the user enters “1.2” into the Numeric component Then the
Numeric component has the stable value “1.2”</p>
<pre><code>Scenario: the numeric component input can apply a number format
    Given the user is at the home page
    Given the user selects the &quot;Numeric&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Exponential Format: E4&quot;
    When the user enters &quot;15678&quot; into the Numeric component
    Then the Numeric component has the value &quot;1.5678E+004&quot;

Scenario: the numeric component input can apply a mathematical rounding method
    Given the user is at the home page
    Given the user selects the &quot;Numeric&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Format: -#000.0, rounding to even, CssClass applied&quot;
    When the user enters &quot;296.45&quot; into the Numeric component
    Then the Numeric component has the value &quot;296.4&quot;</code></pre>
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/numeric/numeric_5.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="numeric">@numeric</span> <span
class="citation" data-cites="numeric_5">@numeric_5</span> Feature: the
numeric component can be made read-only and / or disabled. The read-only
icon is optional Scenario: the numeric component can be made read-only
Given the user is at the home page Given the user selects the “Numeric”
component in the container “Input” When the user clicks the “Usage” tab
And the user expands the concertina by clicking on the header with the
text “Read-only” Then the Numeric component is not editable And the
Numeric component image matches the base image “numeric-readonly”</p>
<pre><code>Scenario: the numeric component can be disabled
    Given the user is at the home page
    Given the user selects the &quot;Numeric&quot; component in the container &quot;Input&quot;
    When the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Disabled&quot;
    Then the Numeric component is disabled
    And the Numeric component image matches the base image &quot;numeric-disabled&quot;

Scenario: the numeric component can be made read-only and disabled
    Given the user is at the home page
    Given the user selects the &quot;Numeric&quot; component in the container &quot;Input&quot;
    When the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Disabled and read-only&quot;
    Then the Numeric component is not editable
    And the Numeric component is disabled
    And the Numeric component image matches the base image &quot;numeric-readonly-disabled&quot;

Scenario: the numeric component can be made read-only without a read-only icon
    Given the user is at the home page
    Given the user selects the &quot;Numeric&quot; component in the container &quot;Input&quot;
    When the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Read-only long (hide read-only icon)&quot;
    Then the Numeric component is not editable
    And the Numeric component image matches the base image &quot;numeric-readonly-no-icon&quot;

Scenario: the numeric component can be made read-only and disabled without a read-only icon
    Given the user is at the home page
    Given the user selects the &quot;Numeric&quot; component in the container &quot;Input&quot;
    When the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Disabled and read-only (hide read-only icon)&quot;
    Then the Numeric component is not editable
    And the Numeric component is disabled
    And the Numeric component image matches the base image &quot;numeric-readonly-disabled-no-icon&quot;</code></pre>
</div>
<hr />
<p><strong>Comments:</strong><br />
</p>
<div class="comments">

</div>
<hr />
<p><strong>Timeline events:</strong><br />
</p>
<div class="timeline">
<div class="timeline">
<p>LABELLED | <em>User:</em> phillidgithub <em>Created on:</em>
11/07/2024 07:40:52 <em>Label name:</em> Validation and Verification
<em>Label description:</em> Use this label to identify the issue as
relating to the validation and verification of a feature</p>
<p>LABELLED | <em>User:</em> phillidgithub <em>Created on:</em>
11/07/2024 07:40:52 <em>Label name:</em> v1.0.0 <em>Label
description:</em> Version 1.0.0</p>
<p>ASSIGNED | <em>User:</em> phillidgithub <em>Created on:</em>
11/07/2024 07:43:16 <em>Assignee:</em> phillidgithub</p>
</div>
</div>
<p><br/></p>
<p>————— feature ends —————</p>
<p><br/></p>
<hr />
<div id="CCTC_Components-36">
<strong>Feature:</strong> Radio component
</div>
<hr />
<table>
<colgroup>
<col style="width: 60%" />
<col style="width: 40%" />
</colgroup>
<tbody>
<tr>
<td style="text-align: left;"><strong>Id:</strong>
I_kwDOKq1Kuc6OygC3</td>
<td style="text-align: left;"><strong>Uid:</strong>
CCTC_Components-36</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Author:</strong>
phillidgithub</td>
<td style="text-align: left;"><strong>Created:</strong> 08/07/2024
13:10:01</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Assignees:</strong>
phillidgithub</td>
<td style="text-align: left;"><strong>Resource path:</strong>
<a href=https://github.com//CCTC-team/CCTC_Components/issues/36 target=_blank>/CCTC-team/CCTC_Components/issues/36</a></td>
</tr>
<tr>
<td style="text-align: left;"><strong>Milestone:</strong> |none|</td>
<td style="text-align: left;"><strong>Labels:</strong> Validation and
Verification || v1.0.0</td>
</tr>
</tbody>
</table>
<hr />
<table>
<colgroup>
<col style="width: 60%" />
<col style="width: 40%" />
</colgroup>
<tbody>
<tr>
<td style="text-align: left;"><strong>State:</strong> OPEN</td>
<td style="text-align: left;"><strong>State reason</strong>: |not
applicable|</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Includes created edit:</strong>
false</td>
<td style="text-align: left;"></td>
</tr>
<tr>
<td style="text-align: left;"><strong>Closed:</strong> false</td>
<td style="text-align: left;"><strong>Closed on:</strong> |none|</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Editor:</strong> |unknown|</td>
<td style="text-align: left;"><strong>Updated on:</strong> 11/07/2024
07:43:21</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Locked:</strong> false</td>
<td style="text-align: left;"><strong>Participants:</strong>
phillidgithub</td>
</tr>
</tbody>
</table>
<hr />
<strong>Project item body:</strong><br />

<div class="feature-body">
<p dir="auto">
Brief description:<br> The radio component is used to select an option
from a list of options<br> NOTE: the branch of dp_dev is used but should
be main
</p>
<p dir="auto">
User specs:<br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/radio.spec">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/radio.spec</a>
</p>
<p dir="auto">
Functional scripts:<br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_1.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_1.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_2.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_2.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_3.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_3.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_4.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_4.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_5.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_5.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_6.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_6.feature</a>
</p>
<p dir="auto">
Test files:<br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/tests/CCTC_Components.bUnit.test/RadioTests.cs">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/tests/CCTC_Components.bUnit.test/RadioTests.cs</a>
</p>
<p dir="auto">
<strong>Pre Review:</strong>
</p>
<ul class="contains-task-list">
<li class="task-list-item">
<input type="checkbox" id="" disabled="" class="task-list-item-checkbox">
Script(s) target the correct feature sufficiently
</li>
<li class="task-list-item">
<input type="checkbox" id="" disabled="" class="task-list-item-checkbox">
The user requirements are met
</li>
</ul>
<p dir="auto">
<strong>Post Review:</strong>
</p>
<ul class="contains-task-list">
<li class="task-list-item">
<input type="checkbox" id="" disabled="" class="task-list-item-checkbox">
Script(s) target the correct feature sufficiently
</li>
<li class="task-list-item">
<input type="checkbox" id="" disabled="" class="task-list-item-checkbox">
Script(s) appropriately test the feature
</li>
</ul>
</div>
<hr />
<p><strong>User specification:</strong><br />
</p>
<div class="spec-script">
<div class="line-block"><br />
<em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/radio.spec</em><br />
<br />
1 - the radio component option can be changed using the mouse by
clicking on a radio button or radio label<br />
2 - the radio component option can be changed using the keyboard<br />
3 - the radio component can allow an empty value and an entered value
can be optionally cleared<br />
4 - the radio component handles options that have a large amount of text
and can display tooltips when required<br />
5 - the radio component can be made read-only and individual options can
be disabled or set as visible. The read-only icon is optional<br />
6 - the radio component orientation can be vertical left, vertical right
or horizontal</div>
</div>
<p><strong>Functional scripts:</strong><br />
</p>
<div class="spec-script">
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_1.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="radio">@radio</span> <span class="citation"
data-cites="radio_1">@radio_1</span> Feature: the radio component option
can be changed using the mouse by clicking on a radio button or radio
label Scenario: the radio component sample page is available Given the
user is at the home page When the user selects the “Radio” component in
the container “Input” Then the url ending is “radiosample”</p>
<pre><code>Scenario: the radio component option can be changed using the mouse by clicking on a radio button
    Given the user is at the home page
    And the user selects the &quot;Radio&quot; component in the container &quot;Input&quot;
    And the current selected radio option has the text &quot;Option 1&quot;
    When the user clicks on the radio option button with the associated label text &quot;Option 2&quot;
    Then the current selected radio option has the text &quot;Option 2&quot;

Scenario: the radio component option can be changed using the mouse by clicking on a radio label
    Given the user is at the home page
    And the user selects the &quot;Radio&quot; component in the container &quot;Input&quot;
    And the current selected radio option has the text &quot;Option 1&quot;
    When the user clicks on the radio option label with the text &quot;Option 2&quot;
    Then the current selected radio option has the text &quot;Option 2&quot;</code></pre>
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_2.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="radio">@radio</span> <span class="citation"
data-cites="radio_2">@radio_2</span> Feature: the radio component option
can be changed using the keyboard Scenario: the radio component option
can be changed using the keyboard Given the user is at the home page And
the user selects the “Radio” component in the container “Input” And the
current selected radio option has the text “Option 1” When the user
clicks on the radio option button with the associated label text “Option
1” And the user presses the down arrow key once Then the current
selected radio option has the text “Option 2”</p>
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_3.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="radio">@radio</span> <span class="citation"
data-cites="radio_3">@radio_3</span> Feature: the radio component can
allow an empty value and an entered value can be optionally cleared
Scenario: the current selected radio option can be empty when show clear
is set to true Given the user is at the home page And the user selects
the “Radio” component in the container “Input” And the user clicks the
“Usage” tab When the user expands the concertina by clicking on the
header with the text “RadioOrientation: Horizontal, null initial value
with show clear” Then the Radio component does not have a selected radio
option And the Radio component image matches the base image
“radio-cleared-hide-clear-icon”</p>
<pre><code>Scenario: the current selected radio option can be cleared when show clear is set to true
    Given the user is at the home page
    And the user selects the &quot;Radio&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;RadioOrientation: VerticalLeft, nullable tuple data with show clear&quot;
    And the current selected radio option has the text &quot;Yes&quot;
    And the Radio component image matches the base image &quot;radio-show-clear-icon&quot;
    When the user clicks on the radio clear icon
    Then the Radio component does not have a selected radio option
    And the radio clear icon is no longer in view

Scenario: the current selected radio option can be empty when show clear is set to false
    Given the user is at the home page
    And the user selects the &quot;Radio&quot; component in the container &quot;Input&quot;
    When the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;RadioOrientation: Horizontal, null initial value, CssClass applied&quot;
    Then the Radio component does not have a selected radio option</code></pre>
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_4.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="radio">@radio</span> <span class="citation"
data-cites="radio_4">@radio_4</span> Feature: the radio component
handles options that have a large amount of text and can display
tooltips when required Scenario: long radio options can wrap after three
lines Given the user is at the home page And the user selects the
“Radio” component in the container “Input” And the user clicks the
“Usage” tab And the user expands the concertina by clicking on the
header with the text “RadioOrientation: VerticalLeft,
RadioLabelOverflow: Wrap, RadioLabelTooltipPlacement: Right,
RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, with callback” When
the user clicks on the radio option button with the associated label
text “Option 3” And the user presses the down arrow key once Then the
Radio component image matches the base image “radio-option-wrap”</p>
<pre><code>Scenario: long radio options can truncate on one line
    Given the user is at the home page
    And the user selects the &quot;Radio&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;RadioOrientation: VerticalLeft, RadioLabelOverflow: NoWrap, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, with callback&quot;
    When the user clicks on the radio option button with the associated label text &quot;One&quot;
    And the user presses the down arrow key 2 times
    Then the Radio component image matches the base image &quot;radio-option-nowrap&quot;

Scenario: long radio options can scroll on one line
    Given the user is at the home page
    And the user selects the &quot;Radio&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;RadioOrientation: VerticalLeft, RadioLabelOverflow: Scroll, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: Disabled, with callback&quot;
    When the user clicks on the radio option button with the associated label text &quot;One&quot;
    And the user presses the down arrow key 2 times
    Then the radio options can scroll
    And the Radio component image matches the base image &quot;radio-option-scroll&quot;

Scenario: long radio options can be displayed in full
    Given the user is at the home page
    And the user selects the &quot;Radio&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;RadioOrientation: VerticalLeft, RadioLabelOverflow: None, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: Disabled, with callback, input height modified via Style parameter&quot;
    When the user clicks on the radio option button with the associated label text &quot;Option 3&quot;
    And the user presses the down arrow key once
    And the Radio component image matches the base image &quot;radio-option-full&quot;

Scenario: radio options can have a tooltip
    Given the user is at the home page
    When the user selects the &quot;Radio&quot; component in the container &quot;Input&quot;
    Then the radio options have tooltips enabled containing the full option text

Scenario: radio options can not have a tooltip
    Given the user is at the home page
    And the user selects the &quot;Radio&quot; component in the container &quot;Input&quot;
    When the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;RadioOrientation: VerticalLeft, RadioLabelOverflow: Scroll, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: Disabled, with callback&quot;
    Then the radio options do not have tooltips enabled</code></pre>
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_5.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="radio">@radio</span> <span class="citation"
data-cites="radio_5">@radio_5</span> Feature: the radio component can be
made read-only and individual options can be disabled or set as visible.
The read-only icon is optional Scenario: the radio component can be made
read-only Given the user is at the home page And the user selects the
“Radio” component in the container “Input” And the user clicks the
“Usage” tab And the user expands the concertina by clicking on the
header with the text “RadioOrientation: VerticalLeft,
RadioLabelOverflow: Wrap, RadioLabelTooltipPlacement: Top,
RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, read-only” And the
current selected radio option has the text “Option 3” When the user
clicks on the radio option button with the associated label text “Option
2” Then the current selected radio option has the text “Option 3” Then
the Radio component image matches the base image “radio-readonly”</p>
<pre><code>Scenario: the radio component individual options can be disabled
    Given the user is at the home page
    And the user selects the &quot;Radio&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;RadioOrientation: VerticalLeft, RadioLabelOverflow: NoWrap, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, second radio option disabled, fifth radio option not visible&quot;
    Then the radio option button with the associated label text &quot;Option 2&quot; is disabled
    And the Radio component image matches the base image &quot;radio-option-disabled&quot;

Scenario: the radio component can be made read-only and disabled
    Given the user is at the home page
    And the user selects the &quot;Radio&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;RadioOrientation: VerticalLeft, RadioLabelOverflow: NoWrap, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, read-only and all radio options disabled, scroll items due to restricted height&quot;
    Then the radio option buttons are all disabled
    And the Radio component image matches the base image &quot;radio-readonly-disabled&quot;

Scenario: the radio component can be made read-only without a read-only icon
    Given the user is at the home page
    And the user selects the &quot;Radio&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;RadioOrientation: VerticalLeft, RadioLabelOverflow: NoWrap, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, read-only, hide read-only icon, scroll items due to restricted height&quot;
    And the current selected radio option has the text &quot;Option 2&quot;
    When the user clicks on the radio option button with the associated label text &quot;Option 1&quot;
    Then the current selected radio option has the text &quot;Option 2&quot;
    Then the Radio component image matches the base image &quot;radio-readonly-no-icon&quot;</code></pre>
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_6.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="radio">@radio</span> <span class="citation"
data-cites="radio_6">@radio_6</span> Feature: the radio component
orientation can be vertical left, vertical right or horizontal Scenario:
the radio component orientation can be vertical left Given the user is
at the home page And the user selects the “Radio” component in the
container “Input” And the user clicks the “Usage” tab When the user
expands the concertina by clicking on the header with the text
“RadioOrientation: VerticalLeft, RadioLabelOverflow: Wrap,
RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour:
EnabledOnLabelOverflow, with callback” Then the Radio component image
matches the base image “radio-vertical-left”</p>
<pre><code>Scenario: the radio component orientation can be vertical right
    Given the user is at the home page
    And the user selects the &quot;Radio&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    When the user expands the concertina by clicking on the header with the text &quot;RadioOrientation: VerticalRight, RadioLabelOverflow: Wrap, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, with callback&quot;
    Then the Radio component image matches the base image &quot;radio-vertical-right&quot;

Scenario: the radio component orientation can be horizontal
    Given the user is at the home page
    And the viewport has a width of 1920 and a height of 1200
    And the user selects the &quot;Radio&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    When the user expands the concertina by clicking on the header with the text &quot;RadioOrientation: Horizontal, RadioLabelOverflow: Wrap, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, with callback&quot;
    Then the Radio component image matches the base image &quot;radio-horizontal&quot;</code></pre>
</div>
<hr />
<p><strong>Comments:</strong><br />
</p>
<div class="comments">

</div>
<hr />
<p><strong>Timeline events:</strong><br />
</p>
<div class="timeline">
<div class="timeline">
<p>LABELLED | <em>User:</em> phillidgithub <em>Created on:</em>
08/07/2024 13:10:01 <em>Label name:</em> Validation and Verification
<em>Label description:</em> Use this label to identify the issue as
relating to the validation and verification of a feature</p>
<p>LABELLED | <em>User:</em> phillidgithub <em>Created on:</em>
08/07/2024 13:11:03 <em>Label name:</em> v1.0.0 <em>Label
description:</em> Version 1.0.0</p>
<p>ASSIGNED | <em>User:</em> phillidgithub <em>Created on:</em>
11/07/2024 07:43:20 <em>Assignee:</em> phillidgithub</p>
</div>
</div>
<p><br/></p>
<p>————— feature ends —————</p>
<p><br/></p>
<hr />
<div id="CCTC_Components-26">
<strong>Feature:</strong> Date component
</div>
<hr />
<table>
<colgroup>
<col style="width: 60%" />
<col style="width: 40%" />
</colgroup>
<tbody>
<tr>
<td style="text-align: left;"><strong>Id:</strong>
I_kwDOKq1Kuc6NCdbg</td>
<td style="text-align: left;"><strong>Uid:</strong>
CCTC_Components-26</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Author:</strong>
phillidgithub</td>
<td style="text-align: left;"><strong>Created:</strong> 21/06/2024
10:25:15</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Assignees:</strong>
phillidgithub</td>
<td style="text-align: left;"><strong>Resource path:</strong>
<a href=https://github.com//CCTC-team/CCTC_Components/issues/26 target=_blank>/CCTC-team/CCTC_Components/issues/26</a></td>
</tr>
<tr>
<td style="text-align: left;"><strong>Milestone:</strong> |none|</td>
<td style="text-align: left;"><strong>Labels:</strong> Validation and
Verification || v1.0.0</td>
</tr>
</tbody>
</table>
<hr />
<table>
<colgroup>
<col style="width: 60%" />
<col style="width: 40%" />
</colgroup>
<tbody>
<tr>
<td style="text-align: left;"><strong>State:</strong> OPEN</td>
<td style="text-align: left;"><strong>State reason</strong>: |not
applicable|</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Includes created edit:</strong>
true</td>
<td style="text-align: left;"></td>
</tr>
<tr>
<td style="text-align: left;"><strong>Closed:</strong> false</td>
<td style="text-align: left;"><strong>Closed on:</strong> |none|</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Editor:</strong>
phillidgithub</td>
<td style="text-align: left;"><strong>Updated on:</strong> 11/07/2024
07:44:01</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Locked:</strong> false</td>
<td style="text-align: left;"><strong>Participants:</strong>
phillidgithub</td>
</tr>
</tbody>
</table>
<hr />
<strong>Project item body:</strong><br />

<div class="feature-body">
<p dir="auto">
<strong>Brief description:</strong><br> The date component is used to
input a date<br> NOTE: the branch of dp_dev is used but should be main
</p>
<p dir="auto">
<strong>User specs:</strong><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/temporal/date.spec">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/temporal/date.spec</a>
</p>
<p dir="auto">
<strong>Functional scripts:</strong><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/date/date_1.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/date/date_1.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/date/date_2.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/date/date_2.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/date/date_3.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/date/date_3.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/date/date_4.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/date/date_4.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/date/date_5.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/date/date_5.feature</a>
</p>
<p dir="auto">
<strong>Test files:</strong><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/tests/CCTC_Components.bUnit.test/DateTests.cs">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/tests/CCTC_Components.bUnit.test/DateTests.cs</a>
</p>
<p dir="auto">
<strong>Pre Review:</strong>
</p>
<ul class="contains-task-list">
<li class="task-list-item">
<input type="checkbox" id="" disabled="" class="task-list-item-checkbox">
Script(s) target the correct feature sufficiently
</li>
<li class="task-list-item">
<input type="checkbox" id="" disabled="" class="task-list-item-checkbox">
The user requirements are met
</li>
</ul>
<p dir="auto">
<strong>Post Review:</strong>
</p>
<ul class="contains-task-list">
<li class="task-list-item">
<input type="checkbox" id="" disabled="" class="task-list-item-checkbox">
Script(s) target the correct feature sufficiently
</li>
<li class="task-list-item">
<input type="checkbox" id="" disabled="" class="task-list-item-checkbox">
Script(s) appropriately test the feature
</li>
</ul>
</div>
<hr />
<p><strong>User specification:</strong><br />
</p>
<div class="spec-script">
<div class="line-block"><br />
<em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/temporal/date.spec</em><br />
<br />
1 - the date component date can be typed in as text or can be entered
via a date picker<br />
2 - the date component can allow an empty value and an entered value can
be optionally cleared<br />
3 - the date component date is validated (date format, min and / or max
date) and there is feedback provided to the user via an icon<br />
4 - the date component can be made read-only and / or disabled. The
read-only icon is optional<br />
5 - the date component has a placeholder which matches the date
format</div>
</div>
<p><strong>Functional scripts:</strong><br />
</p>
<div class="spec-script">
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/date/date_1.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="temporal">@temporal</span> <span
class="citation" data-cites="date">@date</span> <span class="citation"
data-cites="date_1">@date_1</span> Feature: the date component date can
be typed in as text or can be entered via a date picker Scenario: the
date component sample page is available Given the user is at the home
page When the user selects the “Date” component in the container “Input”
Then the url ending is “datesample”</p>
<pre><code>Scenario: the date component date can be typed in as text
    Given the user is at the home page
    And the user selects the &quot;Date&quot; component in the container &quot;Input&quot;
    When the user enters &quot;20210105&quot; into the Date component
    Then the Date component has the value &quot;2021-01-05&quot;

Scenario: the date component date can be entered via a date picker
    Given the user is at the home page
    And the user selects the &quot;Date&quot; component in the container &quot;Input&quot;
    When the user enters &quot;2021-01-05&quot; into the Date component via the date picker
    Then the Date component has the value &quot;2021-01-05&quot;</code></pre>
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/date/date_2.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="temporal">@temporal</span> <span
class="citation" data-cites="date">@date</span> <span class="citation"
data-cites="date_2">@date_2</span> Feature: the date component can allow
an empty value and an entered value can be optionally cleared Scenario:
the date component can allow an empty value when allow clear is set to
true Given the user is at the home page And the user selects the “Date”
component in the container “Input” And the user clicks the “Usage” tab
When the user expands the concertina by clicking on the header with the
text “Format: default, allow clear, null initial value,
FeedbackIcon.Error” Then the Date component has the value “”</p>
<pre><code>Scenario: the date component value can be cleared when allow clear is set to true
    Given the user is at the home page
    And the user selects the &quot;Date&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Format: default, allow clear, FeedbackIcon.Error&quot;
    When the user enters &quot;&quot; into the Date component
    Then the Date component has the value &quot;&quot;

Scenario: the date component value can be cleared via the date picker when allow clear is set to true
    Given the user is at the home page
    And the user selects the &quot;Date&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Format: default, allow clear, FeedbackIcon.Error&quot;
    When the user enters &quot;&quot; into the Date component via the date picker
    Then the Date component has the value &quot;&quot;

Scenario: the date component can allow an empty value when allow clear is set to false
    Given the user is at the home page
    And the user selects the &quot;Date&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    When the user expands the concertina by clicking on the header with the text &quot;Format: yyyy-MM-dd, null initial value&quot;
    Then the Date component has the value &quot;&quot;

Scenario: the date component value can not be cleared when allow clear is set to false
    Given the user is at the home page
    And the user selects the &quot;Date&quot; component in the container &quot;Input&quot;
    When the user enters &quot;&quot; into the Date component
    Then the Date component has the value &quot;2022-05-06&quot;

Scenario: an alert is shown when the date is cleared via the date picker and allow clear is set to false
    Given the user is at the home page
    When the user selects the &quot;Date&quot; component in the container &quot;Input&quot;
    Then on clearing the Date component value via the date picker an alert is displayed with the text &quot;The date is not permitted to be cleared&quot;</code></pre>
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/date/date_3.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="temporal">@temporal</span> <span
class="citation" data-cites="date">@date</span> <span class="citation"
data-cites="date_3">@date_3</span> Feature: the date component date is
validated (date format, min and / or max date) and there is feedback
provided to the user via an icon Scenario: an incomplete date is shown
as an error Given the user is at the home page And the user selects the
“Date” component in the container “Input” And the user clicks the
“Usage” tab And the user expands the concertina by clicking on the
header with the text “Format: default, FeedbackIcon.Error” When the user
enters “2021010” into the Date component Then the Date component has the
value “2021-01-0” And the Date component displays a red exclamation mark
feedback icon And the Date component image matches the base image
“date-error”</p>
<pre><code>Scenario: a complete date is shown as valid
    Given the user is at the home page
    And the user selects the &quot;Date&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Format: dd MMM yy, FeedbackIcon.Valid&quot;
    When the user enters &quot;06May22&quot; into the Date component
    Then the Date component has the value &quot;06 May 22&quot;
    And the Date component displays a green tick feedback icon
    And the Date component image matches the base image &quot;date-valid&quot;

Scenario: a date earlier than the minimum date is shown as an error
    Given the user is at the home page
    And the user selects the &quot;Date&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Format: dd/MM/yyyy, Min (11/12/2023) and Max (16/12/2023), FeedbackIcon.Both&quot;
    When the user enters &quot;10122023&quot; into the Date component
    Then the Date component has the value &quot;10/12/2023&quot;
    And the Date component displays a red exclamation mark feedback icon

Scenario: a date later than the maximum date is shown as an error
    Given the user is at the home page
    And the user selects the &quot;Date&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Format: dd/MM/yyyy, Min (11/12/2023) and Max (16/12/2023), FeedbackIcon.Both&quot;
    When the user enters &quot;17122023&quot; into the Date component
    Then the Date component has the value &quot;17/12/2023&quot;
    And the Date component displays a red exclamation mark feedback icon</code></pre>
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/date/date_4.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="temporal">@temporal</span> <span
class="citation" data-cites="date">@date</span> <span class="citation"
data-cites="date_4">@date_4</span> Feature: the date component can be
made read-only and / or disabled. The read-only icon is optional
Scenario: the date component can be made read-only Given the user is at
the home page And the user selects the “Date” component in the container
“Input” When the user clicks the “Usage” tab And the user expands the
concertina by clicking on the header with the text “Format: yyyy-MM-dd,
read-only” Then the Date component is not editable And the Date
component image matches the base image “date-readonly”</p>
<pre><code>Scenario: the date component can be disabled
    Given the user is at the home page
    And the user selects the &quot;Date&quot; component in the container &quot;Input&quot;
    When the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Format: yyyy-MM-dd, disabled&quot;
    Then the Date component is disabled
    And the Date component image matches the base image &quot;date-disabled&quot;

Scenario: the date component can be made read-only and disabled
    Given the user is at the home page
    And the user selects the &quot;Date&quot; component in the container &quot;Input&quot;
    When the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Format: yyyy-MM-dd, read-only and disabled&quot;
    Then the Date component is not editable
    And the Date component is disabled
    And the Date component image matches the base image &quot;date-readonly-disabled&quot;

Scenario: the date component can be made read-only without a read-only icon
    Given the user is at the home page
    And the user selects the &quot;Date&quot; component in the container &quot;Input&quot;
    When the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Format: yyyy-MM-dd, read-only and read-only icon hidden&quot;
    Then the Date component is not editable
    And the Date component image matches the base image &quot;date-readonly-no-icon&quot;

Scenario: the date component can be made read-only and disabled without a read-only icon
    Given the user is at the home page
    And the user selects the &quot;Date&quot; component in the container &quot;Input&quot;
    When the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Format: yyyy-MM-dd, read-only, disabled and read-only icon hidden&quot;
    Then the Date component is not editable
    And the Date component is disabled
    And the Date component image matches the base image &quot;date-readonly-disabled-no-icon&quot;</code></pre>
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/date/date_5.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="temporal">@temporal</span> <span
class="citation" data-cites="date">@date</span> <span class="citation"
data-cites="date_5">@date_5</span> Feature: the date component has a
placeholder which matches the date format Scenario: the date component
has a placeholder which matches the date format Given the user is at the
home page And the user selects the “Date” component in the container
“Input” When the user clicks the “Usage” tab And the user expands the
concertina by clicking on the header with the text “Format: yyyy-MM-dd,
null initial value” Then the Date component has the placeholder
“yyyy-MM-dd” And the Date component image matches the base image
“date-placeholder”</p>
</div>
<hr />
<p><strong>Comments:</strong><br />
</p>
<div class="comments">

</div>
<hr />
<p><strong>Timeline events:</strong><br />
</p>
<div class="timeline">
<div class="timeline">
<p>LABELLED | <em>User:</em> phillidgithub <em>Created on:</em>
21/06/2024 10:25:15 <em>Label name:</em> Validation and Verification
<em>Label description:</em> Use this label to identify the issue as
relating to the validation and verification of a feature</p>
<p>LABELLED | <em>User:</em> phillidgithub <em>Created on:</em>
21/06/2024 10:25:15 <em>Label name:</em> v1.0.0 <em>Label
description:</em> Version 1.0.0</p>
<p>ASSIGNED | <em>User:</em> phillidgithub <em>Created on:</em>
11/07/2024 07:44:00 <em>Assignee:</em> phillidgithub</p>
</div>
</div>
<p><br/></p>
<p>————— feature ends —————</p>
<p><br/></p>
<hr />
<div id="CCTC_Components-32">
<strong>Feature:</strong> Time component
</div>
<hr />
<table>
<colgroup>
<col style="width: 60%" />
<col style="width: 40%" />
</colgroup>
<tbody>
<tr>
<td style="text-align: left;"><strong>Id:</strong>
I_kwDOKq1Kuc6ODwYr</td>
<td style="text-align: left;"><strong>Uid:</strong>
CCTC_Components-32</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Author:</strong>
phillidgithub</td>
<td style="text-align: left;"><strong>Created:</strong> 01/07/2024
09:39:35</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Assignees:</strong>
phillidgithub</td>
<td style="text-align: left;"><strong>Resource path:</strong>
<a href=https://github.com//CCTC-team/CCTC_Components/issues/32 target=_blank>/CCTC-team/CCTC_Components/issues/32</a></td>
</tr>
<tr>
<td style="text-align: left;"><strong>Milestone:</strong> |none|</td>
<td style="text-align: left;"><strong>Labels:</strong> Validation and
Verification || v1.0.0</td>
</tr>
</tbody>
</table>
<hr />
<table>
<colgroup>
<col style="width: 60%" />
<col style="width: 40%" />
</colgroup>
<tbody>
<tr>
<td style="text-align: left;"><strong>State:</strong> OPEN</td>
<td style="text-align: left;"><strong>State reason</strong>: |not
applicable|</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Includes created edit:</strong>
true</td>
<td style="text-align: left;"></td>
</tr>
<tr>
<td style="text-align: left;"><strong>Closed:</strong> false</td>
<td style="text-align: left;"><strong>Closed on:</strong> |none|</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Editor:</strong>
phillidgithub</td>
<td style="text-align: left;"><strong>Updated on:</strong> 11/07/2024
07:44:05</td>
</tr>
<tr>
<td style="text-align: left;"><strong>Locked:</strong> false</td>
<td style="text-align: left;"><strong>Participants:</strong>
phillidgithub</td>
</tr>
</tbody>
</table>
<hr />
<strong>Project item body:</strong><br />

<div class="feature-body">
<p dir="auto">
Brief description:<br> The time component is used to input a time<br>
NOTE: the branch of dp_dev is used but should be main
</p>
<p dir="auto">
User specs:<br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/temporal/time.spec">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/temporal/time.spec</a>
</p>
<p dir="auto">
Functional scripts:<br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/time/time_1.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/time/time_1.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/time/time_2.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/time/time_2.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/time/time_3.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/time/time_3.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/time/time_4.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/time/time_4.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/time/time_5.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/time/time_5.feature</a>
</p>
<p dir="auto">
Test files:<br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/tests/CCTC_Components.bUnit.test/TimeTests.cs">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/tests/CCTC_Components.bUnit.test/TimeTests.cs</a>
</p>
<p dir="auto">
<strong>Pre Review:</strong>
</p>
<ul class="contains-task-list">
<li class="task-list-item">
<input type="checkbox" id="" disabled="" class="task-list-item-checkbox">
Script(s) target the correct feature sufficiently
</li>
<li class="task-list-item">
<input type="checkbox" id="" disabled="" class="task-list-item-checkbox">
The user requirements are met
</li>
</ul>
<p dir="auto">
<strong>Post Review:</strong>
</p>
<ul class="contains-task-list">
<li class="task-list-item">
<input type="checkbox" id="" disabled="" class="task-list-item-checkbox">
Script(s) target the correct feature sufficiently
</li>
<li class="task-list-item">
<input type="checkbox" id="" disabled="" class="task-list-item-checkbox">
Script(s) appropriately test the feature
</li>
</ul>
</div>
<hr />
<p><strong>User specification:</strong><br />
</p>
<div class="spec-script">
<div class="line-block"><br />
<em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/temporal/time.spec</em><br />
<br />
1 - the time component can receive time input<br />
2 - the time component can allow an empty value and an entered value can
be optionally cleared<br />
3 - the time component time is validated (time format, min and / or max
time) and there is feedback provided to the user via an icon<br />
4 - the time component can be made read-only and / or disabled. The
read-only icon is optional<br />
5 - the time component has a placeholder which matches the time
format</div>
</div>
<p><strong>Functional scripts:</strong><br />
</p>
<div class="spec-script">
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/time/time_1.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="temporal">@temporal</span> <span
class="citation" data-cites="time">@time</span> <span class="citation"
data-cites="time_1">@time_1</span> Feature: the time component can
receive time input</p>
<pre><code>Scenario: the time component sample page is available
    Given the user is at the home page
    When the user selects the &quot;Time&quot; component in the container &quot;Input&quot;
    Then the url ending is &quot;timesample&quot;

Scenario: the time component can receive time input
    Given the user is at the home page
    And the user selects the &quot;Time&quot; component in the container &quot;Input&quot;
    When the user enters &quot;141618&quot; into the Time component
    Then the Time component has the value &quot;14:16:18&quot;</code></pre>
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/time/time_2.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="temporal">@temporal</span> <span
class="citation" data-cites="time">@time</span> <span class="citation"
data-cites="time_2">@time_2</span> Feature: the time component can allow
an empty value and an entered value can be optionally cleared Scenario:
the time component can allow an empty value when allow clear is set to
true Given the user is at the home page And the user selects the “Time”
component in the container “Input” And the user clicks the “Usage” tab
When the user expands the concertina by clicking on the header with the
text “Format: default, allow clear, null initial value,
FeedbackIcon.Error” Then the Time component has the value “”</p>
<pre><code>Scenario: the time component value can be cleared when allow clear is set to true
    Given the user is at the home page
    And the user selects the &quot;Time&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Format: default, allow clear, FeedbackIcon.Error&quot;
    When the user enters &quot;&quot; into the Time component
    Then the Time component has the value &quot;&quot;

Scenario: the time component can allow an empty value when allow clear is set to false
    Given the user is at the home page
    And the user selects the &quot;Time&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    When the user expands the concertina by clicking on the header with the text &quot;Format: h:mm tt, null initial value, FeedbackIcon.Valid&quot;
    Then the Time component has the value &quot;&quot;

Scenario: the time component value can not be cleared when allow clear is set to false
    Given the user is at the home page
    And the user selects the &quot;Time&quot; component in the container &quot;Input&quot;
    When the user enters &quot;&quot; into the Time component
    Then the Time component has the value &quot;13:46:22&quot;</code></pre>
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/time/time_3.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="temporal">@temporal</span> <span
class="citation" data-cites="time">@time</span> <span class="citation"
data-cites="time_3">@time_3</span> Feature: the time component time is
validated (time format, min and / or max time) and there is feedback
provided to the user via an icon Scenario: an incomplete time is shown
as an error Given the user is at the home page And the user selects the
“Time” component in the container “Input” And the user clicks the
“Usage” tab And the user expands the concertina by clicking on the
header with the text “Format: HH:mm:ss, FeedbackIcon.Error” When the
user enters “14151” into the Time component Then the Time component has
the value “14:15:1” And the Time component displays a red exclamation
mark feedback icon And the Time component image matches the base image
“time-error”</p>
<pre><code>Scenario: a complete time is shown as valid
    Given the user is at the home page
    And the user selects the &quot;Time&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Format: h:mm tt, null initial value, FeedbackIcon.Valid&quot;
    When the user enters &quot;1213pm&quot; into the Time component
    Then the Time component has the value &quot;12:13 pm&quot;
    And the Time component displays a green tick feedback icon
    And the Time component image matches the base image &quot;time-valid&quot;

Scenario: a time earlier than the minimum time is shown as an error
    Given the user is at the home page
    And the user selects the &quot;Time&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Format: h:mm tt, Min (11:05 am) and max (1:10 pm), FeedbackIcon.Both, CssClass applied&quot;
    When the user enters &quot;1104am&quot; into the Time component
    Then the Time component has the value &quot;11:04 am&quot;
    And the Time component displays a red exclamation mark feedback icon

Scenario: a time later than the maximum time is shown as an error
    Given the user is at the home page
    And the user selects the &quot;Time&quot; component in the container &quot;Input&quot;
    And the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Format: h:mm tt, Min (11:05 am) and max (1:10 pm), FeedbackIcon.Both, CssClass applied&quot;
    When the user enters &quot;1:11pm&quot; into the Time component
    Then the Time component has the value &quot;1:11 pm&quot;
    And the Time component displays a red exclamation mark feedback icon</code></pre>
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/time/time_4.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="temporal">@temporal</span> <span
class="citation" data-cites="time">@time</span> <span class="citation"
data-cites="time_4">@time_4</span> Feature: the time component can be
made read-only and / or disabled. The read-only icon is optional
Scenario: the time component can be made read-only Given the user is at
the home page And the user selects the “Time” component in the container
“Input” When the user clicks the “Usage” tab And the user expands the
concertina by clicking on the header with the text “Format: h:mm tt,
FeedbackIcon.Valid, read-only” Then the Time component is not editable
And the Time component image matches the base image “time-readonly”</p>
<pre><code>Scenario: the time component can be disabled
    Given the user is at the home page
    And the user selects the &quot;Time&quot; component in the container &quot;Input&quot;
    When the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Format: h:mm tt, FeedbackIcon.Valid, disabled&quot;
    Then the Time component is disabled
    And the Time component image matches the base image &quot;time-disabled&quot;

Scenario: the time component can be made read-only and disabled
    Given the user is at the home page
    And the user selects the &quot;Time&quot; component in the container &quot;Input&quot;
    When the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Format: h:mm tt, FeedbackIcon.Valid, read-only and disabled&quot;
    Then the Time component is not editable
    And the Time component is disabled
    And the Time component image matches the base image &quot;time-readonly-disabled&quot;

Scenario: the time component can be made read-only without a read-only icon
    Given the user is at the home page
    And the user selects the &quot;Time&quot; component in the container &quot;Input&quot;
    When the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Format: h:mm tt, FeedbackIcon.Valid, read-only and read-only icon hidden&quot;
    Then the Time component is not editable
    And the Time component image matches the base image &quot;time-readonly-no-icon&quot;

Scenario: the time component can be made read-only and disabled without a read-only icon
    Given the user is at the home page
    And the user selects the &quot;Time&quot; component in the container &quot;Input&quot;
    When the user clicks the &quot;Usage&quot; tab
    And the user expands the concertina by clicking on the header with the text &quot;Format: h:mm tt, FeedbackIcon.Valid, read-only, disabled and read-only icon hidden&quot;
    Then the Time component is not editable
    And the Time component is disabled
    And the Time component image matches the base image &quot;time-readonly-disabled-no-icon&quot;</code></pre>
<p><em>https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/time/time_5.feature</em></p>
<p><span class="citation" data-cites="component">@component</span> <span
class="citation" data-cites="temporal">@temporal</span> <span
class="citation" data-cites="time">@time</span> <span class="citation"
data-cites="time_5">@time_5</span> Feature: the time component has a
placeholder which matches the time format Scenario: the time component
has a placeholder which matches the time format Given the user is at the
home page And the user selects the “Time” component in the container
“Input” When the user clicks the “Usage” tab And the user expands the
concertina by clicking on the header with the text “Format: h:mm tt,
null initial value, FeedbackIcon.Valid” Then the Time component has the
placeholder “h:mm am/pm” And the Time component image matches the base
image “time-placeholder”</p>
</div>
<hr />
<p><strong>Comments:</strong><br />
</p>
<div class="comments">

</div>
<hr />
<p><strong>Timeline events:</strong><br />
</p>
<div class="timeline">
<div class="timeline">
<p>LABELLED | <em>User:</em> phillidgithub <em>Created on:</em>
01/07/2024 09:39:36 <em>Label name:</em> Validation and Verification
<em>Label description:</em> Use this label to identify the issue as
relating to the validation and verification of a feature</p>
<p>LABELLED | <em>User:</em> phillidgithub <em>Created on:</em>
01/07/2024 09:39:36 <em>Label name:</em> v1.0.0 <em>Label
description:</em> Version 1.0.0</p>
<p>ASSIGNED | <em>User:</em> phillidgithub <em>Created on:</em>
11/07/2024 07:44:05 <em>Assignee:</em> phillidgithub</p>
</div>
</div>
<p><br/></p>
<p>————— feature ends —————</p>
<p><br/></p>
</body>
</html>
