﻿cctc-infoicon {
    width: fit-content;
}

cctc-infoicon-image-container {
    user-select: none;
    -webkit-user-select: none;
    display: flex;
    align-items: center;
    background-color: var(--cctc-infoicon-image-container-background-color);
    border-radius: var(--cctc-infoicon-image-container-border-radius);
}

cctc-infoicon-image-container > img {
    width: 100%;
    height: auto;
    border-radius: var(--cctc-infoicon-image-border-radius);
}

::deep .custom-popover {
    --bs-popover-body-color: var(--cctc-infoicon-popover-color);
    --bs-popover-bg: var(--cctc-infoicon-popover-background-color);
    --bs-popover-header-color: var(--cctc-infoicon-popover-header-color);
    --bs-popover-header-bg: var(--cctc-infoicon-popover-header-background-color);
    --bs-popover-border-color: var(--cctc-infoicon-popover-background-color);
    --bs-popover-body-padding-x: 0.75rem;
    --bs-popover-body-padding-y: 0.5rem;
}