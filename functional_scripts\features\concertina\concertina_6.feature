@component @concertina @concertina_6
Feature: selecting an item should bring it into view to the top of the concertina

Scenario: The screen adjusts so that the selected concertina is in view
    Given the user is at the home page
    And the user selects the "Concertina" component in the container "Concertina"
    And the user clicks the "Usage" tab
    And the Concertina component image matches the base image "Before concertina selection and screen adjustment"
    When the user expands the concertina by clicking on the header with the text "Collapse others"
    Then the Concertina component image matches the base image "After concertina selection and screen adjustment"