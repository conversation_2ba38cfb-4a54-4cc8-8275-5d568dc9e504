﻿@using CCTC_Lib.Enums.UI
@inherits CCTC_Components.Components.__CCTC.CCTCBase
@typeparam TValue

<cctc-input data-cctc-input-type="checkbox" id="@Id" class="@CssClass" style="@Style" data-author="cctc">
    <div class="component-wrapper">
        <div class="@_checkBoxWrapperClass" @onclick:preventDefault="@ReadOnly">
            <input
                type="checkbox"
                id="@Id-checkbox"
                @onchange="OnValueChanged"
                disabled="@Disabled"
                checked="@_checked"/>
@{
    if (!string.IsNullOrWhiteSpace(Label))
    {
        RenderFragment checkBoxLabel = @<label id="@_labelId" for="@Id-checkbox">@Label</label>;
        if (CheckBoxLabelTooltipBehaviour != CheckBoxLabelTooltipBehaviour.Disabled)
        {
            <Tooltip
                Id="@($"{Id}-checkbox-label-tooltip")"
                Content="@Label"
                TooltipPlacement="CheckBoxLabelTooltipPlacement"
                TooltipBehaviour="_tooltipBehaviour">
                @checkBoxLabel
            </Tooltip>
        }
        else
        {
            @checkBoxLabel
        }
    }
}
        </div>
    @if (ReadOnly && !HideReadOnlyIcon)
    {
        <div class="icon-wrapper">
            <span class="material-icons">
                lock
            </span>
        </div>
    }
    </div>
</cctc-input>

@code {

    /// <summary>
    /// The input value
    /// </summary>
    [Parameter]
    public TValue? Value { get; set; }

    /// <summary>
    /// A callback which fires when the input value changes
    /// </summary>
    [Parameter]
    public EventCallback<TValue> ValueChanged { get; set; }

    /// <summary>
    /// A second callback which fires when the input value changes. Useful when consuming using @bind-Value
    /// </summary>
    [Parameter]
    public EventCallback<ChangeEventArgs> CheckBoxChanged { get; set; }

    /// <summary>
    /// The checkbox label
    /// </summary>
    [Parameter]
    public string? Label { get; set; }

    /// <summary>
    /// Disabled if true
    /// </summary>
    [Parameter]
    public bool Disabled { get; set; }

    /// <summary>
    /// Read-only if true
    /// </summary>
    [Parameter]
    public bool ReadOnly { get; set; }

    /// <summary>
    /// Hides the read-only icon when <see cref="ReadOnly"/> is true
    /// </summary>
    [Parameter]
    public bool HideReadOnlyIcon { get; set; }

    /// <summary>
    /// The orientation of the checkbox
    /// </summary>
    [Parameter]
    public CheckBoxOrientation CheckBoxOrientation { get; set; }

    /// <summary>
    /// Configure the checkbox label overflow
    /// </summary>
    [Parameter]
    public CheckBoxLabelOverflow CheckBoxLabelOverflow { get; set; }

    /// <summary>
    /// Configure the checkbox label tooltip placement
    /// </summary>
    [Parameter]
    public TooltipPlacement CheckBoxLabelTooltipPlacement { get; set; }

    /// <summary>
    /// Configure the checkbox label tooltip behavior
    /// </summary>
    [Parameter]
    public CheckBoxLabelTooltipBehaviour CheckBoxLabelTooltipBehaviour { get; set; }

    bool _checked;
    string? _checkBoxWrapperClass;
    string? _labelId;
    TooltipBehaviour? _tooltipBehaviour;

    async Task OnValueChanged(ChangeEventArgs args)
    {
        _checked =
            args.Value is null ?
                false :
                (bool)args.Value;

        await ValueChanged.InvokeAsync((TValue)(object)_checked);
        await CheckBoxChanged.InvokeAsync(args);
    }

    TooltipBehaviour GetTooltipBehaviour(CheckBoxLabelTooltipBehaviour checkBoxLabelTooltipBehaviour, string labelSelector)
    {
        return checkBoxLabelTooltipBehaviour switch
        {
            CheckBoxLabelTooltipBehaviour.EnabledOnLabelOverflow => new TooltipBehaviour.EnabledOnOverflow(labelSelector),
            CheckBoxLabelTooltipBehaviour.Enabled => new TooltipBehaviour.Enabled(),
            CheckBoxLabelTooltipBehaviour.Disabled => new TooltipBehaviour.Disabled(),
            _ => throw new ArgumentException("case not handled", nameof(checkBoxLabelTooltipBehaviour))
        };
    }

    ///<inheritdoc />
    protected override void OnInitialized()
    {
        if (typeof(TValue) != typeof(bool) && !typeof(bool).IsAssignableFrom(Nullable.GetUnderlyingType(typeof(TValue))))
        {
            throw new ArgumentException("Expected a boolean or nullable boolean type", nameof(TValue));
        }

        _labelId = $"{Id}-label";
        _tooltipBehaviour = GetTooltipBehaviour(CheckBoxLabelTooltipBehaviour, $"#{_labelId}");
    }

    ///<inheritdoc />
    protected override void OnParametersSet()
    {
        _checked = object.Equals(Value, true);
        _checkBoxWrapperClass =
            new CssBuilder()
                .AddClass("checkbox-wrapper")
                .AddClass("row", CheckBoxOrientation == CheckBoxOrientation.Left)
                .AddClass("row-reverse", CheckBoxOrientation == CheckBoxOrientation.Right)
                .AddClass("label-wrap", CheckBoxLabelOverflow == CheckBoxLabelOverflow.Wrap)
                .AddClass("label-nowrap", CheckBoxLabelOverflow == CheckBoxLabelOverflow.NoWrap)
                .AddClass("label-scroll", CheckBoxLabelOverflow == CheckBoxLabelOverflow.Scroll)
                .Build();
    }
}
