﻿@page "/tabitemsample"

@{
    var description = new List<string>
    {
        "A TabItem component represents each tab of the parent Tabs component. " +
        "Each TabItem has a header and content as defined when creating a Tabs component.",
    };

    var relatedComponents = new List<(Relation relation, string display, string url, string description)>
    {
        (Relation.Parent, "Tabs", "tabssample", "A TabItem has a single Tabs parent")
    };

}

<Sampler
    ComponentName="TabItem"
    ComponentCssName="tabitem"
    Description="@description"
    RelatedComponents="@relatedComponents">
</Sampler>

@code {

}