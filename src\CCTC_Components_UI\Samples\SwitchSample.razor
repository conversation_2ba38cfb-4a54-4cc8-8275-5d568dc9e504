﻿@page "/switchsample"

@{
    var description = new List<string>
    {
        "The Switch component"
    };

    var features = new List<(string, string)>
    {
        ("Configuration", "Customise the optional switch label. The switch label can be placed on the left or right. The switch can be disabled"),
        ("Interactivity", "A callback can be associated with user switch click")
    };

    var usageCode = new List<(string title, string description, string code, RenderFragment fragment)>
    {
        ("LabelPosition: Right (default), with callback", "",
@"<Switch
    Id=""usage1""
    @bind-Value=""SwitchValue1""
    Label=""@SwitchLabel""
    LabelPosition=""PositionX.Right""
    SwitchChanged=""OnSwitchChanged"">
</Switch>

@code {

    bool SwitchValue1 { get; set; } = false;

    string SwitchLabel { get; set; } = ""The configurable switch label"";

    void OnSwitchChanged(ChangeEventArgs args)
    {
        Console.WriteLine(args?.Value?.ToString());
    }
}",
        @<Switch
            Id="usage1"
            @bind-Value="SwitchValue1"
            Label="@SwitchLabel"
            LabelPosition="PositionX.Right"
            SwitchChanged="OnSwitchChanged">
        </Switch>),
        ("LabelPosition: Left, with callback", "",
@"<Switch
    Id=""usage2""
    @bind-Value=""SwitchValue1""
    Label=""@SwitchLabel""
    LabelPosition=""PositionX.Left""
    SwitchChanged=""OnSwitchChanged"">
</Switch>

@code {

    bool SwitchValue1 { get; set; } = false;

    string SwitchLabel { get; set; } = ""The configurable switch label"";

    void OnSwitchChanged(ChangeEventArgs args)
    {
        Console.WriteLine(args?.Value?.ToString());
    }
}",
        @<Switch
            Id="usage2"
            @bind-Value="SwitchValue1"
            Label="@SwitchLabel"
            LabelPosition="PositionX.Left"
            SwitchChanged="OnSwitchChanged">
        </Switch>),
        ("No label, with callback", "",
@"<Switch
    Id=""usage3""
    @bind-Value=""SwitchValue1""
    SwitchChanged=""OnSwitchChanged"">
</Switch>

@code {

    bool SwitchValue1 { get; set; } = false;

    void OnSwitchChanged(ChangeEventArgs args)
    {
        Console.WriteLine(args?.Value?.ToString());
    }
}",
        @<Switch
            Id="usage3"
            @bind-Value="SwitchValue1"
            SwitchChanged="OnSwitchChanged">
        </Switch>),
        ("Null initial value, with callback", "",
@"<Switch
    Id=""usage4""
    @bind-Value=""SwitchValue2""
    Label=""@SwitchLabel""
    SwitchChanged=""OnSwitchChanged"">
</Switch>

@code {

    bool? SwitchValue2 { get; set; } = null;

    string SwitchLabel { get; set; } = ""The configurable switch label"";

    void OnSwitchChanged(ChangeEventArgs args)
    {
        Console.WriteLine(args?.Value?.ToString());
    }
}",
        @<Switch
            Id="usage4"
            @bind-Value="SwitchValue2"
            Label="@SwitchLabel"
            SwitchChanged="OnSwitchChanged">
        </Switch>),
        ("Disabled", "",
@"<Switch
    Id=""usage5""
    @bind-Value=""SwitchValue1""
    Label=""@SwitchLabel""
    Disabled=""true"">
</Switch>

@code {

    bool SwitchValue1 { get; set; } = false;

    string SwitchLabel { get; set; } = ""The configurable switch label"";
}",
        @<Switch
            Id="usage5"
            @bind-Value="SwitchValue1"
            Label="@SwitchLabel"
            Disabled="true">
        </Switch>),
        ("Switch and label wider than container", "",
@"<Switch
    Id=""usage6""
    CssClass=""narrow-switch""
    @bind-Value=""SwitchValue1""
    Label=""@SwitchLabel""
    SwitchChanged=""OnSwitchChanged"">
</Switch>

@code {

    bool SwitchValue1 { get; set; } = false;

    string SwitchLabel { get; set; } = ""The configurable switch label"";

    void OnSwitchChanged(ChangeEventArgs args)
    {
        Console.WriteLine(args?.Value?.ToString());
    }
}",
        @<Switch
            Id="usage6"
            CssClass="narrow-switch"
            @bind-Value="SwitchValue1"
            Label="@SwitchLabel"
            SwitchChanged="OnSwitchChanged">
        </Switch>)
    };
}

@* The wrapper div is necessary here for ::deep isolated css rules to be applied to the Switch component *@
<div>
    <Sampler
        ComponentName="Switch"
        ComponentCssName="switch"
        Description="@description"
        Features="@features"
        UsageText="Typical usages of the <code>Switch</code> component are shown below"
        UsageCodeList="@usageCode"
        ContentHeightPixels="375">
        <ExampleTemplate>
            <Switch
                Id="example1"
                @bind-Value="SwitchValue1"
                Label="The configurable switch label in the default position"
                SwitchChanged="OnSwitchChanged">
            </Switch>
        </ExampleTemplate>
    </Sampler>
</div>

@code {

    bool SwitchValue1 { get; set; } = false;

    bool? SwitchValue2 { get; set; } = null;

    string SwitchLabel { get; set; } = "The configurable switch label";

    void OnSwitchChanged(ChangeEventArgs args)
    {
        Console.WriteLine(args?.Value?.ToString());
    }
}