import { ICustomWorld } from '../../support/custom-world';
import * as Helpers from '../../support/helper-functions';
import { Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';

Then('the refresh button component has a tick time out', async function (this: ICustomWorld) {
  const tickTimeOutLocator = Helpers.getSamplerTabsSelectedContent(this).locator(
    'cctc-button .button-wrapper cctc-timeout'
  );
  await expect(tickTimeOutLocator).toHaveCount(1);
});
