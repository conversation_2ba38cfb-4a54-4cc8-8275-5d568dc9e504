@component @temporal @time @time_2
Feature: the time component can allow an empty value and an entered value can be optionally cleared
    Scenario: the time component can allow an empty value when allow clear is set to true
        Given the user is at the home page
        And the user selects the "Time" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "Format: default, allow clear, null initial value, FeedbackIcon.Error"
        Then the Time component has the value ""

    Scenario: the time component value can be cleared when allow clear is set to true
        Given the user is at the home page
        And the user selects the "Time" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: default, allow clear, FeedbackIcon.Error"
        When the user enters "" into the Time component
        Then the Time component has the value ""

    Scenario: the time component can allow an empty value when allow clear is set to false
        Given the user is at the home page
        And the user selects the "Time" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "Format: h:mm tt, null initial value, FeedbackIcon.Valid"
        Then the Time component has the value ""

    Scenario: the time component value can not be cleared when allow clear is set to false
        Given the user is at the home page
        And the user selects the "Time" component in the container "Input"
        When the user enters "" into the Time component
        Then the Time component has the value "13:46:22"