﻿using System.Reactive.Concurrency;
using CCTC_Lib.Contracts.Reactive;

namespace CCTC_Components.Services
{
    /// <summary>
    /// Adds a default implementation of the ScheduleProvider
    /// </summary>
    /// <remarks>The IScheduleProvider is used mainly for testing purpose for components using System.Reactive</remarks>
    public sealed class SchedulerProvider : ISchedulerProvider
    {
        /// <summary>
        /// The current thread scheduler
        /// </summary>
        public IScheduler CurrentThread => Scheduler.CurrentThread;

        /// <summary>
        /// The immediate thread scheduler
        /// </summary>
        public IScheduler Immediate => Scheduler.Immediate;

        /// <summary>
        /// The default thread scheduler
        /// </summary>
        public IScheduler Default => Scheduler.Default;
    }
}
