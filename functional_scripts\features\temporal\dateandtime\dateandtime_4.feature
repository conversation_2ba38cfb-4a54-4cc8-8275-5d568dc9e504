@component @temporal @dateandtime @dateandtime_4
Feature: the date and time component can be made read-only and / or disabled. The read-only icon is optional
    Scenario: the date and time component can be made read-only
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: dd MMMM yyyy h:mm tt, FeedbackIcon: default, read-only"
        Then the Date part of the Date and Time component is not editable
        And the Time part of the Date and Time component is not editable
        And the Date and Time component image matches the base image "dateandtime-readonly"

    Scenario: the date and time component can be disabled
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: dd MMMM yyyy h:mm tt, FeedbackIcon: default, disabled"
        Then the Date part of the Date and Time component is disabled
        And the Time part of the Date and Time component is disabled
        And the Date and Time component image matches the base image "dateandtime-disabled"

    Scenario: the date and time component can be made read-only and disabled
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: dd MMMM yyyy h:mm tt, FeedbackIcon: default, read-only and disabled"
        Then the Date part of the Date and Time component is not editable
        And the Time part of the Date and Time component is not editable
        And the Date part of the Date and Time component is disabled
        And the Time part of the Date and Time component is disabled
        And the Date and Time component image matches the base image "dateandtime-readonly-disabled"

    Scenario: the date and time component can be made read-only without a read-only icon
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: dd MMMM yyyy h:mm tt, FeedbackIcon: default, read-only, read-only icon hidden"
        Then the Date part of the Date and Time component is not editable
        And the Time part of the Date and Time component is not editable
        And the Date and Time component image matches the base image "dateandtime-readonly-no-icon"

    Scenario: the date and time component can be made read-only and disabled without a read-only icon
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: dd MMMM yyyy h:mm tt, FeedbackIcon: default, read-only, disabled and read-only icon hidden"
        Then the Date part of the Date and Time component is not editable
        And the Time part of the Date and Time component is not editable
        And the Date part of the Date and Time component is disabled
        And the Time part of the Date and Time component is disabled
        And the Date and Time component image matches the base image "dateandtime-readonly-disabled-no-icon"
