﻿@using System.Reactive.Subjects
@using System.Reactive.Linq
@using System.Text.RegularExpressions
@using CCTC_Lib.Contracts.Reactive
@using static Common.Helpers.Number
@inherits CCTC_Components.Components.__CCTC.CCTCBase
@inject ISchedulerProvider SchedulerProvider
@implements IDisposable

<cctc-input data-cctc-input-type="numeric" id="@Id" class="@CssClass" style="@Style" data-author="cctc">
    <div class="component-wrapper">
        <input
            id="@Id-input"
            type="@(RedactText ? "password" : "text")"
            @ref="ElementRef"
            value="@_numericValue"
            @oninput="@(args => OnValueChanged(args))"
            @onblur="@(_ => ForceComplete())"
            disabled="@Disabled"
            readonly="@ReadOnly"
            maxlength="@(MaxLength > 0 ? MaxLength : null)"
            placeholder="@Placeholder"
            autocomplete="off"/>
        @if (ReadOnly && !HideReadOnlyIcon)
        {
            <div class="icon-wrapper">
                <span class="material-icons">
                    lock
                </span>
            </div>
        }
    </div>
</cctc-input>

@code {

    /// <summary>
    /// The input value
    /// </summary>
    [Parameter]
    public string? Value { get; set; }

    /// <summary>
    /// A callback which fires when the input value changes
    /// </summary>
    [Parameter]
    public EventCallback<string?> ValueChanged { get; set; }

    /// <summary>
    /// A second callback which fires when the input value changes. Useful when consuming using @bind-Value
    /// </summary>
    [Parameter]
    public EventCallback<ChangeEventArgs> NumberChanged { get; set; }

    /// <summary>
    /// The number format
    /// </summary>
    [Parameter]
    public string? Format { get; set; }

    /// <summary>
    /// The mathematical rounding method
    /// </summary>
    [Parameter]
    public MidpointRounding Rounding { get; set; } = MidpointRounding.AwayFromZero;

    /// <summary>
    /// Disabled if true
    /// </summary>
    [Parameter]
    public bool Disabled { get; set; }

    /// <summary>
    /// Read-only if true
    /// </summary>
    [Parameter]
    public bool ReadOnly { get; set; }

    /// <summary>
    /// Hides the read-only icon when <see cref="ReadOnly"/> is true
    /// </summary>
    [Parameter]
    public bool HideReadOnlyIcon { get; set; }

    /// <summary>
    /// The maximum character length
    /// </summary>
    [Parameter]
    public int MaxLength { get; set; }

    /// <summary>
    /// The Throttle speed
    /// </summary>
    [Parameter]
    public int ThrottleMs { get; set; }

    /// <summary>
    /// When set to true will not allow all existing text to be cleared
    /// </summary>
    [Parameter]
    public bool PreventWhitespace { get; set; }

    /// <summary>
    /// Adds a placeholder
    /// </summary>
    [Parameter]
    public string? Placeholder { get; set; }

    /// <summary>
    /// Redacts the text when true
    /// </summary>
    [Parameter]
    public bool RedactText { get; set; }

    public ElementReference ElementRef { get; set; }
    Subject<string?> _reactiveValue = new();
    string? _numericValue;
    int _throttleMs;

    void OnValueChanged(ChangeEventArgs args)
    {
        _numericValue =
            args.Value is null ?
                string.Empty :
                args.Value.ToString()!;

        _reactiveValue.OnNext(_numericValue);
    }

    void CheckFormat()
    {
        if (!ParamInputValidation.NumberFormatValid(Format))
        {
            throw new ArgumentException($"The format should match the following regular expression: {Constants.NumberFormatPattern}", nameof(Format));
        }
    }

    (bool success, string? result) ValidateUserInput(string? value)
    {
        if (!PreventWhitespace && string.IsNullOrWhiteSpace(value))
        {
            return (true, null);
        }
        else if (PreventWhitespace && string.IsNullOrWhiteSpace(value))
        {
            return (false, null);
        }

        string formattedValue = FormatValue(value, Format, Rounding);
        return formattedValue == string.Empty ? (false, null) : (true, formattedValue);
    }

    string FormatValue(string? value, string? format, MidpointRounding midpointRounding = MidpointRounding.AwayFromZero)
    {
        if (value is null) return string.Empty;
        bool success = Double.TryParse(value, out double result);
        if (!success) return string.Empty;
        return format is null ? result.ToString() : formatNum(midpointRounding, format, result);
    }

    /// <summary>
    /// Forces the subscription to complete and sets it again
    /// </summary>
    public void ForceComplete()
    {
        _reactiveValue.OnCompleted();
        SetSubscription();
    }

    void SetSubscription()
    {
        _reactiveValue = new Subject<string?>();

        var seed = _numericValue;

        _reactiveValue
            .Throttle(TimeSpan.FromMilliseconds(_throttleMs), SchedulerProvider.Default)
            .DistinctUntilChanged()
            .Scan((seed, seed),
                (acc, current) => (acc.Item2, current))
            .Subscribe(prevCurr =>
            {
                var (prev, curr) = prevCurr;
                var currResult = ValidateUserInput(curr);
                if (currResult.success)
                {
                    InvokeAsync(() => ValueChanged.InvokeAsync(currResult.result));
                    InvokeAsync(() => NumberChanged.InvokeAsync(new ChangeEventArgs { Value = currResult.result }));
                }
                else
                {
                    var prevResult = ValidateUserInput(prev);
                    InvokeAsync(() => ValueChanged.InvokeAsync(prevResult.result));
                    InvokeAsync(() => NumberChanged.InvokeAsync(new ChangeEventArgs { Value = prevResult.result }));
                }
            });
    }

    ///<inheritdoc />
    protected override void OnInitialized()
    {
        //1000 ms is the minimum throttle speed
        _throttleMs = ThrottleMs <= 1000 ? 1000 : ThrottleMs;
    }

    ///<inheritdoc />
    protected override void OnParametersSet()
    {
        CheckFormat();
        _numericValue = FormatValue(Value, Format, Rounding);
        SetSubscription();
    }

    public void Dispose()
    {
        _reactiveValue.Dispose();
    }
}