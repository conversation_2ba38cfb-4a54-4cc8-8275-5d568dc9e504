1 - the lister can display data in a list which can be reordered
2 - the lister can display specific data according to the filter applied
3 - items within the lister can be selected and deselected
4 - the current counts for the Lister are configurable, and can show totals, total filtered, and total selected
5 - the lister can be sorted by multiple factors in ascending or descending order
6 - data loading within the lister can be cancelled and restarted
7 - items in the lister can be deleted and the list reordered