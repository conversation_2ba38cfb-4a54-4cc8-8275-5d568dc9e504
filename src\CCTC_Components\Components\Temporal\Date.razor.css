﻿cctc-input {
    width: 100%;
}

.component-wrapper {
    display: flex;
}

.material-icons {
    color: var(--cctc-input-icon-color);
    font-size: 1.25rem;
}

.date-text-box-wrapper {
    flex: 1 1 auto;
}

::deep cctc-input[data-cctc-input-type="text"] input:not(:read-only, :disabled) {
    border-right-width: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    padding-right: 0;
}

.background-block {
    flex: 0 0 3.2rem;
    display: flex;
    height: var(--cctc-input-height);
    background-color: var(--cctc-input-background-color);
    border-width: var(--cctc-input-border-width) var(--cctc-input-border-width) var(--cctc-input-border-width) 0;
    border-style: var(--cctc-input-border-style);
    border-radius: 0 var(--cctc-input-border-radius) var(--cctc-input-border-radius) 0;
    border-color: var(--cctc-input-border-color);
}

.val-icon-wrapper {
    flex: 0 0 1.8rem;
    display: flex;
    align-items: center;
    z-index: 3;
}

.check {
    color: green;
}

.priority_high {
    font-size: 1rem;
    color: red;
}

.date-input-wrapper {
    margin-left: -1.8rem;
    display: flex;
    opacity: 0;
    z-index: 2;
}

.date-input {
    width: 3.2rem;
}

.cal-icon-wrapper {
    margin-left: -1.4rem;
    display: flex;
    align-items: center;
    z-index: 1;
}
