﻿using Microsoft.JSInterop;

namespace CCTC_Components_UI.Services
{
    public class BrowserService : IBrowserService
    {
        private readonly IJSRuntime _jSRuntime;
        private IJSObjectReference? _jsModule;

        public BrowserService(IJSRuntime jSRuntime)
        {
            _jSRuntime = jSRuntime;
        }

        public async ValueTask LoadJSModule()
        {
            _jsModule = await _jSRuntime.InvokeAsync<IJSObjectReference>("import", "./scripts/identifyBrowser.js");
        }

        public async ValueTask<string?> GetBrowserName()
        {
            var browserName = string.Empty;
            if (_jsModule is not null)
            {
                browserName = await _jsModule.InvokeAsync<string?>("identifyBrowser");
            }
            return browserName;
        }

        public async ValueTask DisposeAsync()
        {
            if (_jsModule is not null)
            {
                await _jsModule.DisposeAsync();
            }
        }
    }
}
