﻿using System.Text.RegularExpressions;
using CCTC_Components_UI.Shared;
using Microsoft.AspNetCore.Components;

namespace CCTC_Components_UI.Helpers;

public class SamplerBase : ComponentBase
{
    const string Prefix = "cctc";

    [Inject]
    public NavigationManager? NavigationManager { get; set; }

    [CascadingParameter]
    public MainLayout? MainLayout { get; set; }


    /// <summary>
    /// Provides common additional resources related to the component
    /// </summary>
    protected List<(string displayText, Uri link)> AdditionalResources { get; set; } = new();

    protected string ComponentVariableSource => "./_content/CCTC_Components/cctc-components.css";

    string? _cssFileText;

    async Task<string> GetFileText()
    {
        using var client = new HttpClient();

        client.BaseAddress = new Uri(NavigationManager?.BaseUri!);
        var ret= await client.GetStringAsync(ComponentVariableSource).ConfigureAwait(false);
        _cssFileText = ret ?? throw new InvalidOperationException("the css text should never be null here");

        return ret;
    }

    protected async Task<List<string>> GetCssVariables(string componentCssName)
    {
        var cssFileText = _cssFileText ?? await GetFileText();

        var regex = $"--{Prefix}-{componentCssName}.*(?=:)";
        var matches = Regex.Matches(cssFileText, regex, RegexOptions.Multiline);
        return matches.Select(m => m.Value).Distinct().ToList();
    }

    protected async Task<List<string>> GetCustomElements(string componentCssName, string? componentTypeName)
    {
        var cssFileText = _cssFileText ?? await GetFileText();

        var regex = $"(?<!--){Prefix}-{componentCssName}{(componentTypeName is null ? "(-[a-z]+)+" : $"-{componentTypeName}(-[a-z]+)*")}";
        var matches = Regex.Matches(cssFileText, regex, RegexOptions.Multiline);
        return matches.Select(m => m.Value).Distinct().ToList();
    }

    protected string GetComponentCustomElement(string componentCssName, string? componentInputType)
        => $"{Prefix}-{componentCssName}{(componentInputType is null ? string.Empty : $"[data-cctc-input-type='{componentInputType}']")}";

    protected string GetTheme() => MainLayout!.GetTheme();
}