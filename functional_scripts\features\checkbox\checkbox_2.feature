@component @checkbox @checkbox_2

Feature: the checkbox component label is optional and can be on the left or right of the checkbox and clicking the label toggles the checkbox

    Scenario: the label is to the right of the checkbox and clicking the label toggles the checkbox
        Given the user is at the home page
        And the user selects the "Checkbox" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "CheckBoxOrientation: Left, CheckBoxLabelOverflow: Wrap, CheckBoxLabelTooltipPlacement: Right, CheckBoxLabelTooltipBehaviour: EnabledOnLabelOverflow, with callback"
        And the Checkbox component is checked
        When the user clicks the Label within the Checkbox component
        And the user moves the mouse relative to the main viewport with an x coordinate of 0 and y coordinate of 0
        Then the Checkbox component is unchecked
        And the Checkbox component image matches the base image "checkbox-label-right"

    Scenario: the label is to the left of the checkbox and clicking the label toggles the checkbox
        Given the user is at the home page
        And the user selects the "Checkbox" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "CheckBoxOrientation: Right, CheckBoxLabelOverflow: Wrap, CheckBoxLabelTooltipPlacement: Left, CheckBoxLabelTooltipBehaviour: EnabledOnLabelOverflow, with callback"
        And the Checkbox component is unchecked
        When the user clicks the Label within the Checkbox component
        And the user moves the mouse relative to the main viewport with an x coordinate of 0 and y coordinate of 0
        Then the Checkbox component is checked
        And the Checkbox component image matches the base image "checkbox-label-left"

    Scenario: the label is optional
        Given the user is at the home page
        And the user selects the "Checkbox" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "No label with style applied and default configuration"
        Then the Checkbox component image matches the base image "checkbox-no-label"
