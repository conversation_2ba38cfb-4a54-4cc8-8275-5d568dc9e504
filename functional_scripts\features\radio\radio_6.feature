@component @radio @radio_6
Feature: the radio component orientation can be vertical left, vertical right or horizontal
    Scenario: the radio component orientation can be vertical left
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "RadioOrientation: VerticalLeft, RadioLabelOverflow: Wrap, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, with callback"
        Then the Radio component image matches the base image "radio-vertical-left"

    Scenario: the radio component orientation can be vertical right
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "RadioOrientation: VerticalRight, RadioLabelOverflow: Wrap, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, with callback"
        Then the Radio component image matches the base image "radio-vertical-right"

    Scenario: the radio component orientation can be horizontal
        Given the user is at the home page
        And the viewport has a width of 1920 and a height of 1200
        And the user selects the "Radio" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "RadioOrientation: Horizontal, RadioLabelOverflow: Wrap, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, with callback"
        Then the Radio component image matches the base image "radio-horizontal"