﻿namespace CCTC_Components_UI.Helpers
{
    public static class Constants
    {
        public static int DefaultThrottleMs => 1000;

        public static string DisplayJoinString => " | ";

        const string KeyValueDelimiter = "¦";

        public static string DisplayKeyValueDelimiter => $" {KeyValueDelimiter} ";

        public static string DocsBaseUrl => "https://zealous-bush-0c1bee603.5.azurestaticapps.net/api/";
        public static string ComponentsNamespace => "CCTC_Components.Components";


        /// <summary>
        /// Required for charting purposes as styling does not use css
        /// </summary>
        public static class ColorsForCharting
        {
            //TODO: add a change note

            public const string DefaultColor = "white";
            public const string DefaultBorderColor = "#505050";
            public const string DefaultBackgroundColor = "#505050";
            public const string DefaultBackgroundChartColor = "#cccccc";

            public const string TVColor = "white";
            public const string TVBorderColor = "#0f2537";
            public const string TVBackgroundColor = "#0f2537";
            public const string TVBackgroundChartColor = "#cccccc";


            public const string LightColor = "#141414";
            public const string LightBorderColor = "white";
            public const string LightBackgroundColor = "white";
            public const string LightackgroundChartColor = "#62676b";
        }
    }
}
