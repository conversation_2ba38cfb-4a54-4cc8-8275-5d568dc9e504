1 - the radio component option can be changed using the mouse by clicking on a radio button or radio label
2 - the radio component option can be changed using the keyboard
3 - the radio component can allow an empty value and an entered value can be optionally cleared
4 - the radio component handles options that have a large amount of text and can display tooltips when required
5 - the radio component can be made read-only and individual options can be disabled or set as visible. The read-only icon is optional
6 - the radio component orientation can be vertical left, vertical right or horizontal
