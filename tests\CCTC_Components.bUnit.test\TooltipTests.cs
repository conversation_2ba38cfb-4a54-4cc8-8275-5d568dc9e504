﻿using CCTC_Components.Components.Tooltip;

namespace CCTC_Components.bUnit.test
{
    public class TooltipTests : CCTCComponentsTestContext
    {
        [Fact]
        public void TooltipAttributesConfiguredCorrectly()
        {
            AddAddTooltip();

            var cut = RenderComponent<Tooltip>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Content, "Some tooltip message")
                .Add(p => p.ChildContent, "<div id='content-id'>Some text</div>")
                .Add(p => p.TooltipBehaviour, new TooltipBehaviour.EnabledOnOverflow("#content-id"))
            );

            cut.MarkupMatches("<cctc-tooltip id=\"test-id\" data-author=\"cctc\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-container=\"#test-id\" >\r\n" +
                "<div id=\"content-id\">Some text</div>\r\n</cctc-tooltip>");
        }

        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public void TooltipBehaviourEnabledOnOverflowEnablesTooltip(bool isOverflowing)
        {
            var addTooltipsHandler = AddAddTooltip();
            AddIsOverflowing(isOverflowing);
            var updateTooltipHandler = AddUpdateTooltipTitle();

            addTooltipsHandler.SetVoidResult();
            updateTooltipHandler.SetVoidResult();

            var cut = RenderComponent<Tooltip>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Content, "Some tooltip message")
                .Add(p => p.ChildContent, "<div id='content-id'>Some text</div>")
                .Add(p => p.TooltipBehaviour, new TooltipBehaviour.EnabledOnOverflow("#content-id"))
            );

            var invocation = JSInterop.Invocations["isOverflowing"].Single();
            Assert.Equal("#content-id", invocation.Arguments.Single());

            invocation = JSInterop.Invocations["updateTooltipTitle"].Single();
            var expectedArguments = new List<object?> { "test-id", "Some tooltip message", 100, isOverflowing };
            Assert.Equal(expectedArguments, invocation.Arguments);
        }

        [Fact]
        public void TooltipBehaviourEnabledEnablesTooltip()
        {
            var addTooltipsHandler = AddAddTooltip();
            var updateTooltipHandler = AddUpdateTooltipTitle();

            addTooltipsHandler.SetVoidResult();
            updateTooltipHandler.SetVoidResult();

            var cut = RenderComponent<Tooltip>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Content, "Some tooltip message")
                .Add(p => p.ChildContent, "<div id='content-id'>Some text</div>")
                .Add(p => p.TooltipBehaviour, new TooltipBehaviour.Enabled())
            );

            var invocation = JSInterop.Invocations["updateTooltipTitle"].Single();
            var expectedArguments = new List<object?> { "test-id", "Some tooltip message", 100, true };
            Assert.Equal(expectedArguments, invocation.Arguments);
        }

        [Fact]
        public void TooltipBehaviourDisabledDisablesTooltip()
        {
            var addTooltipsHandler = AddAddTooltip();
            var updateTooltipHandler = AddUpdateTooltipTitle();

            addTooltipsHandler.SetVoidResult();
            updateTooltipHandler.SetVoidResult();

            var cut = RenderComponent<Tooltip>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Content, "Some tooltip message")
                .Add(p => p.ChildContent, "<div id='content-id'>Some text</div>")
                .Add(p => p.TooltipBehaviour, new TooltipBehaviour.Disabled())
            );

            var invocation = JSInterop.Invocations["updateTooltipTitle"].Single();
            var expectedArguments = new List<object?> { "test-id", "Some tooltip message", 100, false };
            Assert.Equal(expectedArguments, invocation.Arguments);
        }
    }
}