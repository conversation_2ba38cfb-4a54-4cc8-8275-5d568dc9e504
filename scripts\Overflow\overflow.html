<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Overflow testing</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <link href="./overflow.css" rel="stylesheet">
</head>
<body>
    <div class="outer-wrapper overflow">
        <div id="inner-wrapper-overflow" class="inner-wrapper overflow">
            <label>Some label that overflows its container</label>
        </div>
    </div>
    <button class="m-2 btn btn-info" onclick="alert(isOverflowing('inner-wrapper-overflow'))">Overflow?</button>
    <div class="outer-wrapper no-overflow">
        <div id="inner-wrapper-no-overflow" class="inner-wrapper no-overflow">
            <label>Some label that does not overflow its container</label>
        </div>
    </div>
    <button class="m-2 btn btn-info" onclick="alert(isOverflowing('inner-wrapper-no-overflow'))">Overflow?</button>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
    <script src="./overflow.js"></script>
</body>
</html>