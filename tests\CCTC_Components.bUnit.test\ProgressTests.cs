﻿using CCTC_Components.Components;

namespace CCTC_Components.bUnit.test
{
    public class ProgressTests : CCTCComponentsTestContext
    {
        [Fact]
        public void ProgressVisibilityConfiguredCorrectly()
        {
            var cut = RenderComponent<Progress>(parameters => parameters
                .Add(p => p.Id, "test-id")
            );

            TestHelpers.AssertHasClass(() => cut.GetElementAt("div", 0), "show");

            cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.Show, false)
            );

            TestHelpers.AssertDoesNotHaveClass(() => cut.GetElementAt("div", 0), "show");
        }

        [Theory]
        [InlineData(500, 50, "10 %")]
        [InlineData(100, 12, "12 %")]
        [InlineData(100, 200, "100 %")]
        [InlineData(0, 50, "100 %")]
        public void ProgressValueCalculatedCorrectly(int max, int value, string expected)
        {
            var cut = RenderComponent<Progress>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Max, max)
                .Add(p => p.Value, value)
            );

            var progressValue = cut.Find(".component-wrapper span");

            progressValue.InnerHtml.MarkupMatches(expected);
        }

        [Fact]
        public void ProgressIndeterminateWithValueThrowsArgumentException()
        {
            var cut = () => RenderComponent<Progress>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.IsIndeterminate, true)
                .Add(p => p.Value, 25)
            );

            var actual = Assert.Throws<ArgumentException>(cut);
            Assert.Equal($"A value should not be provided when progress is indeterminate", actual.Message);
        }
    }
}
