import { ICustomWorld } from '../support/custom-world';
import { compareToBaseImage } from '../utils/compareImages';
import * as Helpers from '../support/helper-functions';
import { When, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';

Then(
  'the lister component image matches the base image {string}',
  async function (this: ICustomWorld, name: string) {
    await this.page!.mouse.move(0, 0);
    const screenshot = await Helpers.tryGetStableScreenshot(
      Helpers.getSamplerTabsSelectedContent(this).locator('cctc-lister')
    );
    await compareToBaseImage(this, name, screenshot);
  }
);

When('the user clicks the upward arrow for row with content {string}', async function (this: ICustomWorld, rowText: string) {
  const page = this.page!;
  const row = Helpers.getSamplerTabsSelectedContent(this).locator('.item-row', {
    has: page.locator('div', { hasText: rowText })
  });
  await row.click();
  await row.locator('.material-icons.control-icon', { hasText: 'arrow_upward' }).click();
});

When('the user clicks the downward arrow for row with content {string}', async function (this: ICustomWorld, rowText: string) {
  const page = this.page!;
  const row = Helpers.getSamplerTabsSelectedContent(this).locator('.item-row', {
    has: page.locator('div', { hasText: rowText })
  });
  await row.click();
  await row.locator('.material-icons.control-icon', { hasText: 'arrow_downward' }).click();
});

Then('the row with content {string} has row index {int}', async function (this: ICustomWorld, rowText: string, rowNumber: number) {
  const page = this.page!;
  const row = Helpers.getSamplerTabsSelectedContent(this).locator('.item-row', {
    has: page.locator('div', { hasText: rowText })
  });
  await expect(row).toHaveAttribute('data-item-row', rowNumber.toString());
});

When('the user types {string} in the filter input', async function (this: ICustomWorld, filterText: string) {
  await Helpers.getSamplerTabsSelectedContent(this)
    .getByRole('textbox')
    .fill(filterText);
});

Then('the lister display shows {string}', async function (this: ICustomWorld, countText: string) {
  const countDisplay = Helpers.getSamplerTabsSelectedContent(this).locator(
    'cctc-lister-display-counts div[id$="-display-counts"]'
  );
  await expect(countDisplay).toHaveText(countText);
});

When('the user clicks the checkbox in the row with content {string}', async function (this: ICustomWorld, rowText: string) {
  const page = this.page!;
  const row = Helpers.getSamplerTabsSelectedContent(this).locator('.item-row', {
    has: page.locator('div', { hasText: rowText })
  });
  await row.locator('input[type="checkbox"]').first().click();
});

Then('the row with content {string} is checked', async function (this: ICustomWorld, rowText: string) {
  const page = this.page!;
  const row = Helpers.getSamplerTabsSelectedContent(this).locator('.item-row', {
    has: page.locator('div', { hasText: rowText })
  });
  await expect(row.locator('input[type="checkbox"]').first()).toBeChecked();
});

Then('the row with content {string} is unchecked', async function (this: ICustomWorld, rowText: string) {
  const page = this.page!;
  const row = Helpers.getSamplerTabsSelectedContent(this).locator('.item-row', {
    has: page.locator('div', { hasText: rowText })
  });
  await expect(row.locator('input[type="checkbox"]').first()).not.toBeChecked();
});

When('the user scrolls to the bottom of the lister', async function (this: ICustomWorld) {
  const listerContainer = Helpers.getSamplerTabsSelectedContent(this).locator('cctc-lister-items-container');
  await listerContainer.evaluate((element) => {
    element.scrollTop = element.scrollHeight;
  });
  const elementHandle = await listerContainer.elementHandle();
  if (!elementHandle) {
    throw new Error('Could not locate the lister container element.');
  }
  await this.page!.waitForFunction((element) => {
    const el = element as HTMLElement;
    return el.scrollTop + el.clientHeight >= el.scrollHeight;
  }, elementHandle);
});

When('the user clicks the refresh button in the lister', async function (this: ICustomWorld) {
  await Helpers.getSamplerTabsSelectedContent(this)
    .locator('cctc-lister .material-icons.cancel-resume-load:has-text("refresh")')
    .click();
});

When('the user clicks the cancel button in the lister', async function (this: ICustomWorld) {
  await Helpers.getSamplerTabsSelectedContent(this)
    .locator('cctc-lister .material-icons.cancel-resume-load:has-text("close")')
    .click();
});

Then('the lister contains {int} items', async function (this: ICustomWorld, expectedCount: number) {
  const listerContainer = Helpers.getSamplerTabsSelectedContent(this).locator('cctc-lister-items-container');
  const itemRows = listerContainer.locator('.item-row');
  if (expectedCount > 0) {
    await expect(itemRows.first()).toBeVisible({ timeout: 15000 });
  }
  await expect(itemRows).toHaveCount(expectedCount);
});

Then('the lister is virtualised', async function (this: ICustomWorld) {
  const lister = Helpers.getSamplerTabsSelectedContent(this).locator('cctc-lister');
  const virtualizeContainer = lister.locator('[id$="-virtualize"]');
  await expect(virtualizeContainer).toBeVisible();
});

Then('the lister is not virtualised', async function (this: ICustomWorld) {
  const lister = Helpers.getSamplerTabsSelectedContent(this).locator('cctc-lister');
  const nonVirtualizeContainer = lister.locator('[id$="-no-virtualize"]');
  await expect(nonVirtualizeContainer).toBeVisible();
});

When('the user clicks the delete icon for row with content {string}', async function (this: ICustomWorld, rowText: string) {
  const page = this.page!;
  const row = Helpers.getSamplerTabsSelectedContent(this).locator('.item-row', {
    has: page.locator('div', { hasText: rowText })
  });
  await row.click();
  await row.locator('.material-icons.control-icon', { hasText: 'delete' }).click();
});

Then('the row with content {string} is not visible', async function (this: ICustomWorld, rowText: string) {
  const page = this.page!;
  const row = Helpers.getSamplerTabsSelectedContent(this).locator('.item-row', {
    has: page.locator('.truncate', { hasText: rowText })
  });
  await expect(row).toHaveCount(0);
});
