/*contains the template*/
.items-container {
    margin-top: 0.2rem;
    margin-bottom: 0.2rem;
    margin-left: 0.4rem;

    overflow: hidden;
    scrollbar-gutter: stable;
    position:relative;

    /*scroll-snap-type: y mandatory;*/

}

.items-container:hover {
    overflow: auto;
}

.items-container .item-row {
    border: 2px solid var(--cctc-lister-background-color);
    outline: none;
}

.items-container .item-row:hover {
    border: 2px dotted var(--cctc-lister-hover-background-color);
    cursor: default;
}

.items-container .item-row[data-has-focus] {
    background-color: var(--cctc-lister-highlight-background-color) !important;
    cursor: default;

}

.item-row {
    display: flex;
    align-content: space-between;
    align-items: center;
    padding-left: 0.3rem;
    padding-top: 0.1rem;
    padding-bottom: 0.1rem;
    overflow: hidden;

    /*scroll-snap-align: start none;*/
}

::deep cctc-lister-item-row > div {
    flex-grow: 1;
}

.control-icons-group {
    white-space: nowrap;
    margin-top: 0.3rem;
    margin-left: 0.3rem;
    margin-right: 0.3rem;
    flex-grow: 0;
}

.control-icon {
    visibility: hidden;
    font-size: 1.3rem;
    margin-left: 0.3rem;
}

/*.item-row:hover .control-icon {*/
/*    visibility: visible;*/
/*}*/

.item-row[data-has-focus] .control-icon, .item-row:hover .control-icon {
    visibility: visible;
}

.item-row:hover .control-icon:hover {
    visibility: visible;
    color: var(--cctc-lister-highlight-color);
    cursor: pointer;
}

cctc-lister-filter-header {
    margin-bottom: 0.5rem;
    width: inherit;
}

.display-text-wrapper-end {
    display: flex;
    justify-content: end;
    align-items: center;
}

.display-text-wrapper-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.icon-wrapper {
    margin-left: 1rem;
    margin-right: 1rem;
}

input[type='checkbox'][readonly]{
    pointer-events: none;
}

.cancel-resume-load {
    font-size: 1.5rem;
    color: var(--cctc-lister-filter-header-color);
}

.cancel-resume-load:hover {
    color: var(--cctc-lister-color);
    cursor: pointer;
}

.cancel-resume-placeholder {
    height: 1.5rem;
}

/*NOTE: this works specifically for the plane loader type and will need changing if something other than Plane is used*/
::deep  [working-template] > .sk-plane {
    background-color: var(--cctc-lister-working-template-color);
}

::deep [loading-template] > div {
    position: absolute;
    top: calc(50% - 3rem);
    left: calc(50% - 4rem);
    width: 8rem;
    height: 6rem;
}

cctc-lister-filter-header {
    color: var(--cctc-lister-filter-header-color);
    background-color: var(--cctc-lister-filter-header-background-color);
    --cctc-input-color: var(--cctc-lister-filter-header-color);
    --cctc-input-background-color: transparent;
    --cctc-input-border-style: none;
}

cctc-lister-items-header {
    color: var(--cctc-lister-items-header-color);
    background-color: var(--cctc-lister-items-header-background-color);
    margin-top: 0.5rem;
    padding-top: 0.1rem;
    padding-bottom: 0.3rem;
}

cctc-lister-display-counts {
    color: var(--cctc-lister-display-counts-color);
    background-color: var(--cctc-lister-display-counts-background-color);
}
