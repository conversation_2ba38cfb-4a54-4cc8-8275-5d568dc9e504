@component @button @button_4
Feature: the button icon is customisable and can be omitted
    Scenario: the button has an icon
        Given the user is at the home page
        And the user selects the "Button" component in the container "Buttons"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "ButtonIcon: circle, IconPosition: Left, styled with border"
        Then the button component has an icon

    Scenario: the button has no icon
        Given the user is at the home page
        And the user selects the "Button" component in the container "Buttons"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "ButtonIcon: no icon"
        Then the button component has no icon
        And the button component image matches the base image "button with no icon"

    Scenario: the button icon style is circle
        Given the user is at the home page
        And the user selects the "Button" component in the container "Buttons"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "ButtonIcon: circle, IconPosition: Left, styled with border"
        Then the button icon is "circle"
        And the button component image matches the base image "button with a circle icon"

    Scenario: the button icon style is in light mode
        Given the user is at the home page
        And the user selects the "Button" component in the container "Buttons"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "ButtonIcon: light_mode, IconPosition: Right, overridden icon color via Style"
        Then the button icon is "light_mode"
        And the button component image matches the base image "button with a light icon"

