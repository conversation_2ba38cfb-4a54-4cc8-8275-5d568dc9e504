﻿@page "/chartsample"
@using System.Drawing
@using System.Runtime.CompilerServices
@using Common.Helpers

@{
    #region setup

    var description = new List<string>
    {
        "The Chart component is a wrapper around the established <a href='https://www.chartjs.org/docs/latest/' target='_blank'>Chart.js</a> javascript charting library. It can be used to render different types " +
        "of common charts, such as pie and doughnut charts, bar charts, line charts and area charts amongst others. The Chart component provides " +
        "a series of helper methods for common interactions with the chart. However, it does not provide a complete .net version of all the features " +
        "and configurations available within Chart.js. Instead, it uses c# anonymous types as non-type safe objects that are serialised as part of " +
        "Blazor's javascript interop services.\nConsult with the Chart.js documentation about how to style and configure specific chart types using the " +
        "principles shown in the Chart component. For example, tooltips can be configured using the documentation here: <a href='https://www.chartjs.org/docs/latest/configuration/tooltip.html' target='_blank'>Tooltip configuration</a> " +
        "\nThere are literally hundreds of configurations that can be made and applied."
    };

    var features = new List<(string, string)>
    {
        ("Multiple chart types", "The Chart component can be configured to render any of the available Chart.js chart types"),
        ("Responsive", "Charts can be created to be responsive to the viewport. Changes to the chart are made via javascript so the consumer of the chart " +
                       "needs to be configured to understand its current viewport and apply the changes via the provided methods on the chart component")
    };

    var gotchas = new List<(string, string)>
    {
        ("flexible", "The Chart component has a light touch approach to the data structures required to render a chart. Rather than providing a type safe " +
                     "wrapper around the javascript structures in Chart.js, the component uses c# anonymous types. This gives the advantage that it mimics " +
                     "the flexibility of javascript and it is easy to adapt an object to different chart types and adapt for new versions of the library. " +
                     "The disadvantage is that the onus is on the consumer of the component to understand the required structures and construct non-type safe" +
                     "objects that may not render successfully"),
        ("sizing", "Providing an initial size via the Width and/or Height parameters will fix the size regardless of viewport. It is recommended to " +
                   "make the chart size responsive by using media queries at the location of use " +
                   "e.g. <code>@media (min-width: 75rem) { ::deep cctc-chart { width: 50%; margin-left: 2rem; }}</code>. The chart canvas will " +
                   "resize with its container. However, there are cases where setting a Width is sensible - e.g. when the chart is in a scrollable " +
                   "container, it will take up the full size available and a scrollable chart may hide part of the chart or axes."),
        ("setting width with height", "Internally Chart.js uses an aspect ratio to preserve the ratio of width to height. This may need be adjusted if setting " +
                                      "a width and height that do not conform to given ratio for the specific chart"),
        ("chart types", "Some chart types can be easily swapped out. For example, the doughnut chart is essentially a pie chart with a cutout centre " +
                        "so switching between them is as simple as changing the type property from 'pie' to 'doughnut'. Other chart types may also successfully " +
                        "render with limited changes e.g. changing a pie to a bar chart. However, in most cases, you should consider the structure of the " +
                        "datasets and impact on legends and styling before simply changing types."),
        ("chart container", "Be wary when putting the Chart within a flexbox or other dynamic container. The Chart's internal canvas used by Chart.js resizes " +
                            "as required based on the parent container and can cause flickering when resizing.")
    };

    var tips = new List<(string, string)>
    {
        ("don't set Width and Height directly", "Use the canvas's container to provide responsive charts"),
        ("extend common functions", "consider adding further helper methods to provide common options")
    };

    var usageCode = new List<(string title, string description, string code, RenderFragment fragment)>
    {
        ("Pie chart", "",
            @"<Chart Id=""pie-chart""
    @ref=""_pieChartRef""
    Config=""GetConfig()""
    Options=""GetOptions()""
    InitialColor=""white""
    Title=""Title provided as a parameter""
    SubTitle=""Subtitle provided as a parameter"">
</Chart>

@code {
    Chart? _pieChartRef;

    object GetConfig() =>
    new
    {
        type = ""pie"",
        data =
            new
            {
                labels = new[]
                {
                    ""January"",
                    ""February"",
                    ""March"",
                    ""April"",
                    ""May"",
                    ""June"",
                },
                datasets = new[]
                {
                    new
                    {
                        Label = ""Patients"",
                        BackgroundColor = GenColors(220),
                        BorderColor = GetBorderColor(),
                        BorderWidth = 3,
                        Data = GenerateRandomNumbers()
                    }
                }
            }
    };

    object GetOptions() =>
    new
    {
        plugins = new
        {
            title = new
            {
                display = true,
                text = ""Some title"",
                font = new
                {
                    size = 24
                }
            },
            subtitle = new
            {
                display = true,
                text = ""Some subtitle""
            },
            legend = new
            {
                display = true,
                position = GetLegendPosition(),
                labels = new
                {
                    font = new
                    {
                        size = _fontSize
                    }
                }
            }
        }
    }
};",
            @<Chart Id="pie-chart"
                    @ref="_pieChartRef"
                    Width="300px"
                    Config="@GetConfig()"
                    Options="@GetOptions()"
                    InitialColor="@GetColor()"
                    Title="Title provided as a parameter"
                    SubTitle="Subtitle provided as a parameter">
            </Chart>),
        ("Doughnut chart", "",
            @"<Chart Id=""doughnut-chart""
    @ref=""_doughnutChartRef""
    Config=""GetConfig()""
    Options=""GetOptions()""
    InitialColor=""white""
    Title=""Title provided as a parameter""
    SubTitle=""Subtitle provided as a parameter"">
</Chart>

@code {
    Chart? _doughnutChartRef;

    object GetConfig() =>
    new
    {
        type = ""pie"",
        data =
            new
            {
                labels = new[]
                {
                    ""January"",
                    ""February"",
                    ""March"",
                    ""April"",
                    ""May"",
                    ""June"",
                },
                datasets = new[]
                {
                    new
                    {
                        Label = ""Patients"",
                        BackgroundColor = GenColors(220),
                        BorderColor = GetBorderColor(),
                        BorderWidth = 3,
                        Data = GenerateRandomNumbers()
                    }
                }
            }
    };

    object GetOptions() =>
    new
    {
        plugins = new
        {
            title = new
            {
                display = true,
                text = ""Some title"",
                font = new
                {
                    size = 24
                }
            },
            subtitle = new
            {
                display = true,
                text = ""Some subtitle""
            },
            legend = new
            {
                display = true,
                position = GetLegendPosition(),
                labels = new
                {
                    font = new
                    {
                        size = _fontSize
                    }
                }
            }
        }
    }
};",
            @<Chart Id="doughnut-chart"
                    @ref="_doughnutChartRef"
                    Width="300px"
                    Config="@GetConfig("doughnut")"
                    Options="@GetOptions()"
                    InitialColor="@GetColor()"
                    Title="Title provided as a parameter"
                    SubTitle="Subtitle provided as a parameter">
            </Chart>),
        ("Bar chart", "",
            @"<Chart Id=""bar-chart""
    @ref=""_barChartRef""
    Config=""GetConfig()""
    Options=""GetOptions(false, 1.2)""
    InitialColor=""white""
    Title=""Title provided as a parameter""
    SubTitle=""Subtitle provided as a parameter"">
</Chart>

@code {
    Chart? _barChartRef;

    object GetBarConfig() =>
    new
    {
        type = ""bar"",
        data =
            new
            {
                labels = new[]
                {
                    ""January"",
                    ""February"",
                    ""March"",
                    ""April"",
                    ""May"",
                    ""June"",
                },
                datasets = new[]
                {
                    new
                    {
                        Label = ""Patients"",
                        BackgroundColor = ""hsl(220,60%,70%)"",
                        BorderColor = GetBorderColor(),
                        BorderWidth = 3,
                        Data = GenerateRandomNumbers()
                    }
                }
            }
    };

    object GetOptions(bool? maintainRatio = true, double aspectRatio = 1) =>
    new
    {
        maintainAspectRation = maintainRatio,
        aspectRatio,
        plugins = new
        {
            title = new
            {
                display = true,
                text = ""Some title"",
                font = new
                {
                    size = 24
                }
            },
            subtitle = new
            {
                display = true,
                text = ""Some subtitle""
            },
            legend = new
            {
                display = true,
                position = GetLegendPosition(),
                labels = new
                {
                    font = new
                    {
                        size = _fontSize
                    }
                }
            }
        }
    };
};",
            @<Chart Id="bar-chart"
                    @ref="_barChartRef"
                    Width="300px"
                    Config="@GetBarConfig()"
                    Options="@GetOptions(false, 1.2, true)"
                    InitialColor="@GetColor()"
                    Title="Title provided as a parameter"
                    SubTitle="Subtitle provided as a parameter">
            </Chart>),
        ("Line chart", "",
            @"<Chart Id=""line-chart""
    @ref=""_lineChartRef""
    Config=""GetLineConfig()""
    Options=""GetOptions(false, 1.2)""
    InitialColor=""white""
    Title=""Title provided as a parameter""
    SubTitle=""Subtitle provided as a parameter"">
</Chart>

@code {
    Chart? _lineChartRef;

    object GetLineConfig() =>
    new
    {
        type = ""line"",
        data =
            new
            {
                labels = Labels,
                datasets = new[]
                {
                    new
                    {
                        Label = ""Patients"",
                        BackgroundColor = ""hsl(220,60%,70%)"",
                        BorderColor = ""hsl(220,60%,70%)"",
                        Data = GenerateRandomNumbers(),
                        Tension = 0.1,
                        Fill = false,
                        ShowLine = true
                    }
                }
            }
    };

    object GetOptions(bool? maintainRatio = true, double aspectRatio = 1) =>
    new
    {
        maintainAspectRation = maintainRatio,
        aspectRatio,
        plugins = new
        {
            title = new
            {
                display = true,
                text = ""Some title"",
                font = new
                {
                    size = 24
                }
            },
            subtitle = new
            {
                display = true,
                text = ""Some subtitle""
            },
            legend = new
            {
                display = true,
                position = GetLegendPosition(),
                labels = new
                {
                    font = new
                    {
                        size = _fontSize
                    }
                }
            }
        }
    };
};",
            @<Chart Id="line-chart"
                    @ref="_lineChartRef"
                    Width="300px"
                    Config="@GetLineConfig()"
                    Options="@GetOptions(false, 1.2, true)"
                    InitialColor="@GetColor()"
                    Title="Title provided as a parameter"
                    SubTitle="Subtitle provided as a parameter">
            </Chart>)
    };

    var furtherResources = new List<(string displayText, Uri link)>
    {
        ("Chart.js - the underlying javascript charting library", new Uri("https://www.chartjs.org/docs/latest/"))
    };

    #endregion setup
}

@* The wrapper div is necessary here for ::deep isolated css rules to be applied to the Tooltip component *@
<div>
    <Sampler
        ComponentName="Chart"
        ComponentCssName="chart"
        Description="@description"
        Features="@features"
        UsageText="Typical usages of the <code>Chart</code> component are shown below"
        UsageCodeList="@usageCode"
        Gotchas="@gotchas"
        Tips="@tips"
        ContentHeightPixels="550"
        FurtherResources="@furtherResources"
        OnCollapseOrExpandUsage="UsageItemCollapsedOrExpanded">
        <ExampleTemplate>

            <em>Examples of how the chart can be changed programmatically are given below</em>

            <div class="example-template-container">
                <div class="example-controls">
                    <div class="control-button" @onclick="ChangeTitle">Change title</div>
                    <div class="control-button" @onclick="ToggleSubtitle">Toggle subtitle</div>
                    <div class="control-button" @onclick="ToggleLegendPosition">Toggle legend position</div>
                    <div class="control-button" @onclick="@(_ => ChangeLegendFont(true))">Change legend font</div>
                    <div class="control-button" @onclick="AddDataset">Add dataset</div>
                    <div class="control-button"
                         style="color: @(_currDataSets > 1 ? "var(--cctc-color)" : "var(--cctc-disabled-color)")"
                         @onclick="RemoveDataset">
                        Remove dataset
                    </div>
                    <div class="control-button" @onclick="ChangeDataValue">Change data value</div>
                    <div>
                        <div class="control-button" @onclick="SetOption">Set option</div>
                        <small>(fill = @_isFilled)</small>
                    </div>
                    <div>
                        <div class="control-button" @onclick="SetDatasetProp">Set property</div>
                        <small>(pointRadius = @_pointRadius)</small>
                    </div>
                </div>

                <Chart Id="my-example-chart"
                       @ref="_exampleChartRef"

                       Width="@GetWidth()"
                       Config="@GetLineConfig()"
                       Options="@GetOptions(false, 1.2)"
                       InitialColor="white"
                       Title="Title provided as a parameter"
                       SubTitle="Subtitle provided as a parameter">
                </Chart>
            </div>

        </ExampleTemplate>
    </Sampler>
</div>

@code {

    Chart? _exampleChartRef;
    Chart? _pieChartRef;
    Chart? _doughnutChartRef;
    Chart? _barChartRef;
    Chart? _lineChartRef;
    string? _origTheme;

    [CascadingParameter]
    public MainLayout? MainLayout { get; set; }

    static string[] Labels =>
    [
        "January",
        "February",
        "March",
        "April",
        "May",
        "June"
    ];

    object GetLineConfig()
    {
        var color = GenRandomColor();
        return
            new
            {
                type = "line",
                data =
                    new
                    {
                        labels = Labels,
                        datasets = new[]
                        {
                            new
                            {
                                Label = "Patients",
                                BackgroundColor = color,
                                BorderColor = color,
                                Data = GenerateRandomNumbers(),
                                Tension = 0.1
                            }
                        }
                    }
            };
    }


    object GetBarConfig() =>
        new
        {
            type = "bar",
            data =
                new
                {
                    labels = Labels,
                    datasets = new[]
                    {
                        new
                        {
                            Label = "Patients",
                            BackgroundColor = "hsl(220,60%,70%)",
                            BorderColor = GetBorderColor(),
                            BorderWidth = 3,
                            Data = GenerateRandomNumbers()
                        }
                    }
                }
        };


    object GetConfig(string type = "pie") =>
        new
        {
            type,
            data =
                new
                {
                    labels = Labels,
                    datasets = new[]
                    {
                        new
                        {
                            Label = "Patients",
                            BackgroundColor = GenColors(220),
                            BorderColor = GetBorderColor(),
                            BorderWidth = 3,
                            Data = GenerateRandomNumbers()
                        }
                    }
                }
        };

    object GetOptions(bool? maintainRatio = true, double aspectRatio = 1, bool includeScalesConfig = false) =>
        new
        {
            maintainAspectRation = maintainRatio,
            aspectRatio,
            scales = includeScalesConfig ? GetScaleConfig() : null,
            plugins = new
            {
                title = new
                {
                    display = true,
                    text = "Some title",
                    font = new
                    {
                        size = 20
                    }
                },
                subtitle = new
                {
                    display = true,
                    text = "Some subtitle"
                },
                legend = new
                {
                    display = true,
                    position = GetLegendPosition(),
                    labels = new
                    {
                        font = new
                        {
                            size = _fontSize
                        }
                    }
                }
            }
        };

    string GetLegendPosition() =>
        GetUIType() switch
        {
            UI.Mobile => "top",
            _ => "right"
        };


    int[] GenerateRandomNumbers()
    {
        var rnd = new Random();

        var numbers = new List<int>();
        for (var i = 0; i < 6; i++)
        {
            numbers.Add(rnd.Next(1, 250));
        }

        return numbers.ToArray();
    }


    string[] GenColors(int baseColor)
    {
        var colors = new List<string>();

        for (var i = 0; i < 7; i++)
        {
            colors.Add($"hsl({baseColor},60%,70%)");
            baseColor += 20;
        }

        return colors.ToArray();
    }


    string GetBorderColor() => MainLayout?.GetTheme() switch
    {
        "default" => Constants.ColorsForCharting.DefaultBorderColor,
        "tv" => Constants.ColorsForCharting.TVBorderColor,
        "light" => Constants.ColorsForCharting.LightBorderColor,
        _ => throw new InvalidOperationException("the current theme has not been handled by the charting infrastructure")
    };

    string GetColor() => MainLayout?.GetTheme() switch
    {
        "default" => Constants.ColorsForCharting.DefaultColor,
        "tv" => Constants.ColorsForCharting.TVColor,
        "light" => Constants.ColorsForCharting.LightColor,
        _ => throw new InvalidOperationException("the current theme has not been handled by the charting infrastructure")
    };

    string GetAdjustedColor() => MainLayout?.GetTheme() switch
    {
        "default" => Constants.ColorsForCharting.DefaultBackgroundChartColor,
        "tv" => Constants.ColorsForCharting.TVBackgroundChartColor,
        "light" => Constants.ColorsForCharting.LightackgroundChartColor,
        _ => throw new InvalidOperationException("the current theme has not been handled by the charting infrastructure")
    };

    UI GetUIType() => (UI)MainLayout?.CurrentUIType()!;

    string GetWidth() => GetUIType() switch
    {
        UI.Mobile or UI.Tablet => "100%",
        UI.DesktopSmall or UI.DesktopStandard => "600px",
        _ => throw new ArgumentOutOfRangeException()
    };

    string? _title;

    async Task ChangeTitle()
    {
        List<string> titles = ["A new title", "Another new title"];
        _title = _title == titles[0] ? titles[1] : titles[0];
        await _exampleChartRef!.SetTitle(true, _title);
    }

    bool _showSubTitle = true;

    async Task ToggleSubtitle()
    {
        _showSubTitle = !_showSubTitle;
        await _exampleChartRef!.SetSubtitle(_showSubTitle);
    }

    string _legendPosition = "top";

    async Task ToggleLegendPosition()
    {
        List<string> titles = ["top", "right", "bottom", "left", "chartArea"];
        var i = titles.IndexOf(_legendPosition);
        _legendPosition = i == titles.Count - 1 ? titles[0] : titles[i + 1];

        var newLegend = new
        {
            display = true,
            position = _legendPosition,
            labels = new
            {
                font = new
                {
                    size = _fontSize
                }
            }
        };

        await _exampleChartRef!.SetLegend(newLegend);
    }

    int _fontSize = 12;

    async Task ChangeLegendFont(bool display = true, bool updateFont = true)
    {
        _fontSize = _fontSize == 12 ? 18 : 12;

        object newLegend;

        if (updateFont)
        {
            newLegend = new
            {
                display = display,
                position = _legendPosition,
                labels = new
                {
                    font = new
                    {
                        size = _fontSize,
                        family = _fontSize == 18 ? "Times New Roman" : "Helvetica"
                    }
                }
            };
        }
        else
        {
            newLegend = new
            {
                display = display,
                position = _legendPosition
            };
        }

        await _exampleChartRef!.SetLegend(newLegend);
    }

    int _currDataSets = 1;

    string GenRandomColor() => $"hsl({new Random().Next(1, 200)},60%,70%)";

    async Task AddDataset()
    {
        await ChangeLegendFont(false, false);

        _currDataSets += 1;

        var color = GenRandomColor();

        var newDataset =
            new
            {
                Label = $"Dataset {_currDataSets}",
                BackgroundColor = color,
                BorderColor = color,
                BorderWidth = 3,
                Data = GenerateRandomNumbers()
            };

        await _exampleChartRef!.AddDataset(newDataset);
    }


    async Task RemoveDataset()
    {
        if (_currDataSets > 1)
        {
            _currDataSets -= 1;
            await _exampleChartRef!.RemoveDataset(null);
        }

        if (_currDataSets == 1)
        {
            await ChangeLegendFont(true, false);
        }
        else
        {
            await ChangeLegendFont(false, false);
        }
    }

    async Task ChangeDataValue()
    {
        var newValue = new Random().Next(0, 250);
        await _exampleChartRef!.SetDatasetDataValue(0, 1, newValue);
    }

    bool _isFilled;

    async Task SetOption()
    {
        await _exampleChartRef!.SetOption("fill", !_isFilled);
        _isFilled = !_isFilled;
    }

    int _pointRadius = 3;

    async Task SetDatasetProp()
    {
        _pointRadius = _pointRadius == 3 ? 10 : 3;
        await _exampleChartRef!.SetDatasetProperty("pointRadius", _pointRadius);
    }

    /// <inheritdoc />
    protected override void OnInitialized()
    {
        _origTheme = MainLayout?.GetTheme();
    }

    //runs the set color when an item is expanded
    void UsageItemCollapsedOrExpanded(ConcertinaItem concertinaItem)
    {
        Console.WriteLine($"Concertina item {concertinaItem.Id} was {(concertinaItem.IsExpanded ? "expanded" : "collapsed")}");
    }

    object GetEachScaleConfig() =>
        new
        {
            ticks = new
            {
                color = GetAdjustedColor()
            },
            grid = new
            {
                color = GetAdjustedColor()
            }
        };

    object GetScaleConfig() =>
        new
        {
            x = GetEachScaleConfig(),
            y = GetEachScaleConfig()
        };

    async Task SetChartColor(Chart? chartRef, bool includeBorderColor, bool updateScales)
    {
        if (chartRef is not null)
        {
            if (!chartRef.IsDisposed)
            {
                await chartRef.SetColor(GetColor());

                if (includeBorderColor)
                {
                    await chartRef.SetBorderColor(GetBorderColor());
                }

                if (updateScales)
                {
                    await chartRef.SetScales(GetScaleConfig());
                }
            }
        }
    }

    /// <inheritdoc />
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        var currTheme = MainLayout?.GetTheme();

        if (currTheme != _origTheme)
        {
            _origTheme = currTheme;

            await SetChartColor(_exampleChartRef, false, true);
            await SetChartColor(_pieChartRef, true, false);
            await SetChartColor(_doughnutChartRef, true,false);
            await SetChartColor(_barChartRef, true, true);
            await SetChartColor(_lineChartRef, false, true);
        }
    }

}