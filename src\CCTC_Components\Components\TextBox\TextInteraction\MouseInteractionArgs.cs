﻿using CCTC_Components.Components.__CCTC.Models;
using Microsoft.AspNetCore.Components.Web;

namespace CCTC_Components.Components.TextBox.TextInteraction;

/// <summary>
/// A payload for interaction via the mouse
/// </summary>
/// <param name="MouseEventArgs">The <see cref="MouseEventArgs"/> associated with the event</param>
/// <param name="CursorSelection">The cursor positioning as a <see cref="CursorSelection"/></param>
public record MouseInteractionArgs(MouseEventArgs MouseEventArgs, CursorSelection CursorSelection)
    : InteractionArgs(CursorSelection)
{}