@using BlazorComponentUtilities
@using CCTC_Components_UI.Services
@using CCTC_Lib.Models.UI
@inject IJSRuntime JSRuntime
@inject NavigationManager NavManager
@inject IAppReadinessService AppReadiness
@implements IAsyncDisposable
@inherits LayoutComponentBase

<CascadingValue Value="this">
    <div class="@_cssClass">
        <div class="header">

            @if (IsPreviewVersion())
            {
                <img class="preview-banner" src="images/preview.png" alt="preview label"/>
            }

            <div class="d-flex align-items-end lh-1">
                <img class="logo" src="images/CCTC_logo_25h.png" alt="logo" @onclick="@(() => NavManager.NavigateTo("/"))"/>
                <small class="component-package-version">v.1.0.62</small>
                <a href="https://github.com/CCTC-team/CCTC_Components" target="_blank">
                    <img class="github-logo" src="@GitHubLogoSource" alt="github repo" />
                </a>
            </div>
            <div class="ms-auto d-flex align-items-center lh-1 header-section">
                <small style="margin-right: 0.3rem">change theme</small>
                <small>
                    <DropDown
                        TData="string"
                        Id="theme-selector"
                        Data="@_availableThemes"
                        Value="@_theme"
                        ValueChanged="ChangeTheme"
                        DropDownTooltipBehaviour="DropDownTooltipBehaviour.Disabled">
                    </DropDown>
                </small>
                <div class="@_menuButtonClass header-section" @onclick="ToggleMobileMenu">
                    <button class="toggler-icon-wrapper" aria-expanded="@(!_isMobileMenuHidden)">
                        <span class="toggler-icon top-bar"></span>
                        <span class="toggler-icon middle-bar"></span>
                        <span class="toggler-icon bottom-bar"></span>
                    </button>
                </div>
            </div>
        </div>
        @if (ShouldShowMenu())
        {
            <div class="menu">
                <NavMenu PanelMenuIconsOnly="_isCollapsed" NavMenuWasCollapsed="NavMenuCollapse" CanUserCollapse="GetUIType != UI.Mobile"/>
            </div>
        }
        @if (ShouldShowMain())
        {
            <div class="@_mainClass">
                @Body
            </div>
        }
    </div>
</CascadingValue>

@code {

    IJSObjectReference? _jsUtils;
    DotNetObjectReference<MainLayout>? _objRef;

    string? _cssClass;
    string? _menuButtonClass;
    string? _mainClass;
    string? _theme;
    readonly List<string> _availableThemes = ["default", "tv", "light"];
    const int Mobile = 360;
    const int Tablet = 576;
    const int DesktopSmall = 1200;
    const int DesktopStandard = 1920;
    int _width;
    int _height;
    string? _githubLogoSource;


    bool IsPreviewVersion() => NavManager.BaseUri.Contains("-preview.");

    void ChangeTheme(string? theme)
    {
        _theme = theme;
        ChangeGitHubLogoSource(_theme);
        HandleUI();
        StateHasChanged();
    }

    void ChangeGitHubLogoSource(string? theme)
    {
        _githubLogoSource = theme == "tv" ? "images/github-mark-white.png" : "images/github-mark.png";
    }

    public string GetTheme() => _theme ?? "default";
    public string? GitHubLogoSource => _githubLogoSource;
    bool ShouldShowMain() => GetUIType != UI.Mobile || (GetUIType == UI.Mobile && _isMobileMenuHidden);
    bool ShouldShowMenu() => GetUIType != UI.Mobile || (GetUIType == UI.Mobile && !_isMobileMenuHidden);

    public UI CurrentUIType() => GetUIType;

    UI GetUIType =>
        _width switch
        {
            //note: brackets required
            (< Tablet) => UI.Mobile,
            (>= Tablet) and (< DesktopSmall) => UI.Tablet,
            (>= DesktopSmall) and (< DesktopStandard) => UI.DesktopSmall,
            _ => UI.DesktopStandard
        };

    bool _isCollapsed;
    bool _isMobileMenuHidden = true;

    void NavMenuCollapse(bool wasCollapsed)
    {
        _isCollapsed = wasCollapsed;
        HandleUI();
    }

    void ToggleMobileMenu()
    {
        _isMobileMenuHidden = !_isMobileMenuHidden;
        HandleUI();
    }

    void HandleUI()
    {
        var builder =
            new CssBuilder(_theme)
                .AddClass("page")
                .AddClass("grid")

                //manually add the component classes to apply the theme across the app
                //note that modal component classes are not applied here as they need to be applied higher in the DOM via the modal options class
                .AddClass($"input-{_theme}")
                .AddClass($"tabs-{_theme}")
                .AddClass($"concertina-{_theme}")
                .AddClass($"panelmenu-{_theme}")
                .AddClass($"datatextbox-{_theme}")
                .AddClass($"lister-{_theme}")
                .AddClass($"steps-{_theme}")
                .AddClass($"infotext-{_theme}")
                .AddClass($"infoicon-{_theme}")
                .AddClass($"progress-{_theme}")
                .AddClass($"animatedplaceholder-{_theme}")
                .AddClass($"pill-{_theme}")
                .AddClass($"button-{_theme}")
                .AddClass($"switch-{_theme}");

        if (GetUIType == UI.Mobile)
        {
            _menuButtonClass = new CssBuilder("show-menu-button").Build();
            _mainClass = new CssBuilder("main").Build();
            _cssClass = (_isMobileMenuHidden ? builder.AddClass("grid-container-collapsed") : builder.AddClass("grid-container")).Build();
        }
        else
        {
            switch (GetUIType, _isCollapsed)
            {
                case (UI.Tablet, false):
                    _cssClass =
                        builder
                            .AddClass("grid-container-tablet")
                            .Build();
                    _menuButtonClass = new CssBuilder("hide-menu-button").Build();
                    _mainClass = new CssBuilder("main").Build();
                    break;
                case (UI.Tablet, true):
                    _cssClass =
                        builder
                            .AddClass("grid-container-tablet-collapsed")
                            .Build();
                    _menuButtonClass = new CssBuilder("hide-menu-button").Build();
                    _mainClass = new CssBuilder("main").Build();
                    break;
                case (UI.DesktopSmall or UI.DesktopStandard, false):
                    _cssClass =
                        builder
                            .AddClass("grid-container-desktop")
                            .Build();
                    _menuButtonClass = new CssBuilder("hide-menu-button").Build();
                    _mainClass =
                        new CssBuilder("main")
                            .AddClass("main-desktop")
                            .Build();
                    break;
                case (UI.DesktopSmall or UI.DesktopStandard, true):
                    _cssClass =
                        builder
                            .AddClass("grid-container-desktop-collapsed")
                            .Build();
                    _menuButtonClass = new CssBuilder("hide-menu-button").Build();
                    _mainClass =
                        new CssBuilder("main")
                            .AddClass("main-desktop")
                            .Build();
                    break;
                default:
                    throw new InvalidOperationException("this should never be reached");
            }
        }
    }

    [JSInvokable]
    public void GetWindowsDimensions(int width, int height)
    {
        _width = width;
        _height = height;
        HandleUI();

        StateHasChanged();
    }

    WindowDimensions? _windowDimensions;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            try
            {
                _objRef = DotNetObjectReference.Create(this);
                _jsUtils = await JSRuntime.InvokeAsync<IJSObjectReference>("import", "./scripts/ui_utils.js");
                await JSRuntime.InvokeAsync<IJSObjectReference>("WindowInterop.init", _objRef);
                _windowDimensions = await _jsUtils.InvokeAsync<WindowDimensions>("getWindowSize");
                _width = _windowDimensions.Width;
                _height = _windowDimensions.Height;
                HandleUI();
                StateHasChanged();
            }
            catch (Exception)
            {
                Console.WriteLine("An error occurred during UI initialization");
            }
            finally
            {
                // Always signal readiness, even if there were errors
                await AppReadiness.SignalAppReadyAsync();
            }
        }
        
        // Call base implementation if it exists
        await base.OnAfterRenderAsync(firstRender);
    }

    void LocationChanged(object? sender, LocationChangedEventArgs e)
    {
        _isMobileMenuHidden = true;
        HandleUI();
        StateHasChanged();
    }

    protected override void OnInitialized()
    {
        _theme = "default";
        ChangeGitHubLogoSource(_theme);
        NavManager.LocationChanged += LocationChanged;
    }

    public async ValueTask DisposeAsync()
    {
        if (_jsUtils is not null)
        {
            await _jsUtils.DisposeAsync();
        }

        if (_objRef is not null)
        {
            await JSRuntime.InvokeAsync<IJSObjectReference>("WindowInterop.destroy", _objRef);
            _objRef.Dispose();
        }
    }
}