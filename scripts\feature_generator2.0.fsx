open System
open System.IO
open System.Text.RegularExpressions

// Function to read the numbered list from a file
let readNumberedListFromFile (inputFilePath: string) =
    printfn "Checking for input file at: %s" inputFilePath
    if File.Exists(inputFilePath) then
        File.ReadAllText(inputFilePath)
    else
        failwithf "Input file does not exist at the provided path: %s" inputFilePath

// Function to create files based on the numbered list and component name
let createFiles (componentName: string) (inputFilePath: string) (outputFilePath: string) =
    // Read the numbered list from the input file
    let numberedList = readNumberedListFromFile inputFilePath

    // Create a folder with the name of the component inside the output path
    let componentFolderPath = Path.Combine(outputFilePath, componentName)
    printfn "Creating folder at: %s" componentFolderPath
    Directory.CreateDirectory(componentFolderPath) |> ignore

    // Split the numbered list into lines
    let lines = numberedList.Split([| '\n'; '\r' |], StringSplitOptions.RemoveEmptyEntries)

    // Process each line to extract the number and text
    for line in lines do
        // Use regex to match the pattern "number - description"
        let pattern = @"^\s*(\d+)\s*-\s*(.+)"
        let m = Regex.Match(line, pattern)

        if m.Success then
            // Extract the number and description
            let number = m.Groups.[1].Value
            let description = m.Groups.[2].Value.Trim()

            // Define the filename
            let fileName = sprintf "%s_%s.feature" componentName number
            let fullPath = Path.Combine(componentFolderPath, fileName)

            // Define the content
            let content = sprintf "@component @%s @%s_%s\nFeature: %s" componentName componentName number description

            // Write the content to the file
            File.WriteAllText(fullPath, content)
            printfn "File successfully created: %s" fullPath
        else
            printfn "Skipped line (does not match pattern): %s" line

// ==== Only one set of variables below! ====
let inputFilePath = @"C:\Users\<USER>\source\repos\cctc_components\user_specs\timout.spec"
let componentName = "timeout"
let outputFilePath = @"C:\Users\<USER>\source\repos\CCTC_Components\functional_scripts\features"

// Run the file creation
createFiles componentName inputFilePath outputFilePath
