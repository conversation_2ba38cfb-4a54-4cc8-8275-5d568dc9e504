﻿// TODO: use typescript
// TODO: probably should remove the functions that use an id and simply have a single function that uses a query

//rounds the value val to the nearest integer of x
//e.g. with val of 101 and x of 50, will round to 100
//with val of 149 and x of 50 will round to 150
function roundToNearest(val, x) {
    return (Math.round(val * (1 / x)) / (1 / x));
}

//checks if an element exists
export function elementExists(selector) {
    return !!document.querySelector(selector);
}

//sets the focus to the element with id
//silently fails if the element is not found
//NOTE: this can be achieved in Blazor directly using elementreference.FocusAsync()
export function setFocusById(id) {
    let ele = document.getElementById(id);
    if (ele) ele.focus();
}

//gets the selection within an element
//returns -1 if selector does not find an appropriate element
//TODO: perhaps check whether is a valid input type
export function getCursorSelection(selector) {
    let ele = document.querySelector(selector);
    if (ele) {
        return {
            start: ele.selectionStart,
            end: ele.selectionEnd
        };
    }

    return {start: -1, end: -1};
}

//sets the cursor to the end of an element
//fails silently if the element is not found
//TODO: perhaps check whether is a valid input type
export function cursorToEnd(selector) {
    let input = document.querySelector(selector);
    if (input) {
        const end = input.value.length;
        input.setSelectionRange(end, end);
    }
}

//sets the cursor to the start of an element
//fails silently if the element is not found
//TODO: perhaps check whether is a valid input type
export function cursorToStart(selector) {
    let input = document.querySelector(selector);
    if (input) {
        input.setSelectionRange(0, 0);
    }
}

/*
    For scrolling using scrollTop, note that implementation depends in part on the resolution of
    the monitor being used. see
    https://stackoverflow.com/questions/52470549/subpixel-scroll-issue-cant-set-scrolltop-properly-on-chrome-69

    For example, if the monitor is zoomed, the moveBy amount may be changed and not be exactly the value
    expected. This can can cause a flicker.
    In the below scrollUp and scrollDown functions, the roundToNearest helps reduce the impact of this
    issue, but will still produce a minor flicker
 */

export function scrollUp(selector, moveBy) {
    let ele = document.querySelector(selector);
    if (ele) {
        ele.scrollTop = roundToNearest(ele.scrollTop - moveBy, moveBy);
    }
}

export function scrollDown(selector, moveBy) {
    let ele = document.querySelector(selector);
    if (ele) {
        ele.scrollTop = roundToNearest(ele.scrollTop + moveBy, moveBy);
    }
}

function getBounds(rect) {
    return {
        left: Math.round(rect.left),
        right: Math.round(rect.right),
        top: Math.round(rect.top),
        bottom: Math.round(rect.bottom),
        height: Math.round(rect.height),
        width: Math.round(rect.width),
        x: Math.round(rect.x),
        y: Math.round(rect.y)
    };
}

export function getElementBoundRect(elementId) {

    if (elementId == null) return;

    let ele = document.getElementById(elementId);
    let rect = ele.getBoundingClientRect();
    return getBounds(rect);
}

export function getElementBoundRectFromQuery(selector) {

    let element = document.querySelector(selector);
    if (element == null)
        return null;

    let rect = element.getBoundingClientRect();
    return getBounds(rect);
}

//scrolls the element with the given id into view
//args are defaulted but can be overridden
//will silently fail if selector doesn't find any targets
//Note: this should ONLY be called in the OnAfterRenderAsync method. If needs to be triggered by another
//method, update a boolean to trigger the call in the OnAfterRenderAsync method
//see ConcertinaItem for an example
export function scrollIntoViewFromQuery(selector, args) {
    let ele = document.querySelector(selector);
    if (ele) {
        let defArgs = {behavior: "smooth", block: "nearest", inline: "nearest", alignToTop: true};
        if (args) {
            defArgs = args;
        }
        ele.scrollIntoView(defArgs);
    }
}

//returns true if the given element has focus
export function hasElementGotFocus(element) {
    return element === document.activeElement;
}

//returns true if the element found by the selector has focus
export function hasSelectedElementGotFocus(selector) {
    let ele = document.querySelector(selector);
    if (ele) {
        return ele === document.activeElement;
    } else
        return false;
}

//returns true if any of the elements found by the selector has focus
export function hasAnySelectedElementGotFocus(selector) {
    let eles = document.querySelectorAll(selector);

    eles.forEach((ele) => {
        if (ele === document.activeElement) {
            return true;
        }
    });

    return false;
}

//returns the current active element
export function getActiveElement() {
    let activeId = document.activeElement.id;
    if(activeId == null || activeId === '')
        return "active element has no id " + document.activeElement;
    else
        return document.activeElement.id;
}

//focusses on the element found with the selector
export function setFocusOnSelector(selector) {
    document.querySelector(selector).focus();
}

//gets the scroll position of an element
export function getScrollTop(selector) {
    let ele = document.querySelector(selector);
    if (ele) {
        return ele.scrollTop;
    }

    return -1;
}

//returns true if the element found with the selector overflows its parent
export function isOverflowing(selector) {
    let element = document.querySelector(selector);

    if (element == null) {
        return false;
    }

    return element.clientWidth < element.scrollWidth
        || element.clientHeight < element.scrollHeight;
}

//returns user selected text
export function getSelectedText() {
    if (window.getSelection) {
        return window.getSelection().toString();
    }

    return "";
}