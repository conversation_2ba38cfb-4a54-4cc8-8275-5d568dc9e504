@component @button @button_2
Feature: the button text and be short, long, or not exist
    Scenario: The button text is short
        Given the user is at the home page
        And the user selects the "Button" component in the container "Buttons"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "ButtonIcon: circle, IconPosition: Left, styled with border"
        Then the button text is "some text"

    Scenario: The button text is long 
        Given the user is at the home page
        And the user selects the "Button" component in the container "Buttons"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "ButtonIcon: circle, IconPosition: Left, ButtonText: long text to demonstrate wrapping, CssClass: restricted-width"
        Then the button text is "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat."
        And the button component image matches the base image "button with wrapped text"

    Scenario: The button has no text
        Given the user is at the home page
        And the user selects the "Button" component in the container "Buttons"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "ButtonIcon: circle, ButtonText: no text"
        Then the button component image matches the base image "no button text"
