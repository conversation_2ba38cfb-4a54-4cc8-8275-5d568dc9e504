@component @pill @pill_6

Feature: the pill size can change according to the font size
    <PERSON><PERSON><PERSON>: the pill is small with small font
        Given the user is at the home page
        And the user selects the "Pill" component
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "PillContext: Success, ColorStyle: Outline, Size: Small, Icon: done_outline"
        Then the pill font size is "16px"
        And the pill component image matches the base image "small pill"

    Scenario: the pill is bigger with bigger font
        Given the user is at the home page
        And the user selects the "Pill" component
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "PillContext: None, ColorStyle: Fill, Size: Medium, Icon: circle, with OnClick callback"
        Then the pill font size is "20px"
        And the pill component image matches the base image "larger pill"

   
