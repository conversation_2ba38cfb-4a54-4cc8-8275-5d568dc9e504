﻿cctc-input {
    width: 100%;
}

.component-wrapper {
   display: flex;
}

.time-text-box-wrapper {
    flex: 1 1 auto;
}

::deep cctc-input[data-cctc-input-type="text"] input:not(:read-only, :disabled) {
    border-right-width: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    padding-right: 0;
}

.val-icon-wrapper {
    flex: 0 0 1.8rem;
    display: flex;
    align-items: center;
    background-color: var(--cctc-input-background-color);
    border-width: var(--cctc-input-border-width) var(--cctc-input-border-width) var(--cctc-input-border-width) 0;
    border-style: var(--cctc-input-border-style);
    border-radius: 0 var(--cctc-input-border-radius) var(--cctc-input-border-radius) 0;
    border-color: var(--cctc-input-border-color);
}

.check {
    font-size: 1.25rem;
    color: green;
}

.priority_high {
    font-size: 1rem;
    color: red;
}