@component @concertina @concertina_5
Feature: the concertina can contain many items as required

Scenario: the concertina can have the correct amount of various header types when usage 1 is expanded 
    Given the user is at the home page
    And the user selects the "Concertina" component in the container "Concertina"
    And the user clicks the "Usage" tab
    When the user expands the concertina by clicking on the header with the text "Includes collapse/expand all"
    Then the concertina has 1 concertina headers
    And the concertina has 3 item headers
    And the concertina has 1 sub-headers
    And the concertina has 0 item sub-headers
    And the Concertina component image matches the base image "Concertina 1 header, 3 item headers, 1 sub-header, 0 item sub-headers"

Scenario: the concertina can have the correct amount of various header types when usage 3 is expanded 
    Given the user is at the home page
    And the user selects the "Concertina" component in the container "Concertina"
    And the user clicks the "Usage" tab
    When the user expands the concertina by clicking on the header with the text "Collapse others"
    Then the concertina has 0 concertina headers
    And the concertina has 3 item headers
    And the concertina has 0 sub-headers
    And the concertina has 0 item sub-headers
    And the Concertina component image matches the base image "Concertina 0 header, 3 item headers, 0 sub-header, 0 item sub-headers"

Scenario: the concertina can have 1 concertina headers and 5 item headers when on the example page
    Given the user is at the home page
    And the user selects the "Concertina" component in the container "Concertina"
    When the url ending is "concertinasample"
    Then the concertina has 1 concertina headers
    And the concertina has 5 item headers
    And the concertina has 1 sub-headers
    And the concertina has 5 item sub-headers


