﻿.component-wrapper {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.component-wrapper.label-left {
    flex-direction: row-reverse;
    justify-content: start;
}

.component-wrapper .switch {
    position: relative;
    height: 1.4rem;
    min-width: 2.8rem;
}

.component-wrapper .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--cctc-switch-slider-inactive-background-color);
    border-radius: 1.4rem;
    transition: .4s;
}

.component-wrapper .slider::before {
    position: absolute;
    content: "";
    height: 1.1rem;
    width: 1.1rem;
    top: 0.15rem;
    left: 0.2rem;
    background-color: var(--cctc-switch-background-color);
    border-radius: 50%;
    transition: .4s;
}

.component-wrapper.disabled .slider::before {
    background-color: var(--cctc-switch-disabled-background-color);
}

.component-wrapper:hover .slider::before {
    box-shadow: var(--cctc-switch-inactive-hover-box-shadow);
}

.component-wrapper.checked:hover .slider::before {
    box-shadow: var(--cctc-switch-active-hover-box-shadow);
}

.component-wrapper.checked .slider {
    background-color: var(--cctc-switch-slider-active-background-color);
}

.component-wrapper.disabled .slider {
    background-color: var(--cctc-switch-slider-disabled-background-color);
}

.component-wrapper.checked .slider:before {
    transform: translateX(1.3rem);
}

.component-wrapper label {
    flex-grow: 1;
    max-width: fit-content;
    color: var(--cctc-switch-color);
}

.component-wrapper.disabled label {
    color: var(--cctc-switch-disabled-color);
}

.component-wrapper.disabled {
    pointer-events: none;
}
