﻿using CCTC_Components.Components.Pill;
using PillColorStyle = CCTC_Lib.Enums.UI.FillStyle;

namespace CCTC_Components.bUnit.test
{
    public class PillTests : CCTCComponentsTestContext
    {
        [Fact]
        public void PillClickInvokesCallback()
        {
            AddAddTooltip();
            AddUpdateTooltipTitle();

            var mockDummyService = new Mock<IDummyService>();
            var cut = RenderComponent<Pill>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.OnClick, async () => { await mockDummyService.Object.MethodOneAsync(); })
            );

            var pillContentContainer = cut.Find("cctc-pill-content-container");
            pillContentContainer.Click();

            mockDummyService.Verify(m => m.MethodOneAsync(), Times.Once);
        }

        [Theory]
        [InlineData(PillContext.Success, "pill-success")]
        [InlineData(PillContext.Info, "pill-info")]
        [InlineData(PillContext.Warning, "pill-warning")]
        [InlineData(PillContext.Danger, "pill-danger")]
        public void PillContextClassesCorrectlyConfigured(PillContext pillContext, string pillContextClass)
        {
            AddAddTooltip();
            AddUpdateTooltipTitle();

            var cut = RenderComponent<Pill>(parameters => parameters
                .Add(p => p.Id, "test-id")
            );

            var pillContentContainer = cut.Find("cctc-pill-content-container");
            TestHelpers.AssertDoesNotHaveClass(pillContentContainer, pillContextClass);

            cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.PillContext, pillContext)
            );

            TestHelpers.AssertHasClass(pillContentContainer, pillContextClass);
        }

        [Fact]
        public void PillColorStyleClassesCorrectlyConfigured()
        {
            AddAddTooltip();
            AddUpdateTooltipTitle();

            var cut = RenderComponent<Pill>(parameters => parameters
                .Add(p => p.Id, "test-id")
            );

            var pillContentContainer = cut.Find("cctc-pill-content-container");
            string pillOutlineClass = "pill-outline";
            TestHelpers.AssertDoesNotHaveClass(pillContentContainer, pillOutlineClass);

            cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.ColorStyle, PillColorStyle.Outline)
            );

            TestHelpers.AssertHasClass(pillContentContainer, pillOutlineClass);
        }
    }
}
