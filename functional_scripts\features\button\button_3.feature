@component @button @button_3
Feature: the button text can be on the left or right of the button 
    Scenario: The button text is on the left
        Given the user is at the home page
        And the user selects the "Button" component in the container "Buttons"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "ButtonIcon: light_mode, IconPosition: Right, overridden icon color via Style"
        Then the button text is on the left
        And the button component image matches the base image "button with text left"

    Scenario: The button text is on the right
        Given the user is at the home page
        And the user selects the "Button" component in the container "Buttons"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "ButtonIcon: circle, IconPosition: Left, styled with border"
        Then the button text is on the right
        And the button component image matches the base image "button with text right"
