﻿using CCTC_Lib.Contracts.Data;

namespace CCTC_Components_UI.Helpers.Models;

/// <summary>
/// Represents an instance of a node
/// </summary>
/// <param name="Node">The <see cref="Node"/></param>
/// <param name="Id">Id of the node</param>
public record NodeIdentifier(Node Node, Guid Id) : ISearchable
{
    public string AsId()
    {
        return Id.ToString();
    }

    public string AsShortId()
    {
        return Common.Helpers.String.truncate(Id.ToString(), 8, false);
    }

    public string AsLabel() => Node.AsLabel();

    public virtual string SearchText()
    {
        return $"{Node.SearchText()} {Id}";
    }
    public override string ToString()
    {
        return $"{Node.Schema}, {Node.NodeResource}, {Id}";
    }

    public string Schema => Node.Schema;

    public string NodeResource => Node.NodeResource;

    public NodeIdentifier Copy() => new (Node.Copy(), new Guid(Id.ToString()));

    public static NodeIdentifier Empty() => new (Node.Empty, Guid.Empty);

}