﻿using CCTC_Components.Services;
using CCTC_Lib.Contracts.Interaction;
using CCTC_Lib.Contracts.Reactive;
using Microsoft.Extensions.DependencyInjection;
using TextCopy;

namespace CCTC_Components.Helpers;

/// <summary>
/// Extensions to support the CCTC_Components for dependency injection
/// </summary>
public static class SeviceCollectionExtensions
{
    /// <summary>
    /// Adds the CCTC Components with the default services
    /// </summary>
    /// <param name="services">The <see cref="IServiceCollection"/></param>
    /// <returns><see cref="IServiceCollection"/></returns>
    public static IServiceCollection AddCCTCComponents(this IServiceCollection services)
    {
        services.AddScoped<ISchedulerProvider, SchedulerProvider>();
        services.AddScoped<IDelayService, DelayService>();
        services.AddScoped<IClipboard, Clipboard>();
        return services;
    }
}