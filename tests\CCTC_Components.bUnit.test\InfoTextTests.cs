﻿using CCTC_Components.Components.InfoText;

namespace CCTC_Components.bUnit.test
{
    public class InfoTextTests : CCTCComponentsTestContext
    {
        [Fact]
        public void InfoTextOverflowConfiguredCorrectly()
        {
            AddAddTooltip();
            AddUpdateTooltipTitle();
            AddAddPopover();

            var cut = RenderComponent<InfoText>(parameters => parameters
                .Add(p => p.Id, "test-id")
            );

            cut.GetElementAt("div", 1)?.ClassName?.MarkupMatches("infotext-wrap");

            cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.InfoTextOverflow, InfoTextOverflow.NoWrap)
            );

            cut.GetElementAt("div", 1)?.ClassName?.MarkupMatches("infotext-nowrap");

            cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.InfoTextOverflow, InfoTextOverflow.Scroll)
            );

            cut.GetElementAt("div", 1)?.ClassName?.MarkupMatches("infotext-scroll");

            cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.InfoTextOverflow, InfoTextOverflow.None)
            );

            cut.GetElementAt("div", 1)?.ClassName?.MarkupMatches(string.Empty);
        }

        [Fact]
        public void CopyIconClickCopiesText()
        {
            AddAddTooltip();
            AddUpdateTooltipTitle();
            AddAddPopover();

            TestHelpers.ClearClipboardText();
            var expectedText = "Test text";
            var cut = RenderComponent<InfoText>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Content, expectedText)
            );

            var copyIcon = cut.Find(".icon-wrapper i");

            copyIcon.Click();
            Assert.Equal(expectedText, TestHelpers.GetClipboardText());
        }

        [Fact]
        public void CopyIconClassesCorrectlyConfigured()
        {
            AddAddTooltip();
            AddUpdateTooltipTitle();
            AddAddPopover();

            var cut = RenderComponent<InfoText>(parameters => parameters
                .Add(p => p.Id, "test-id")
            );

            cut.GetElementAt("div", 2)?.ClassName?.MarkupMatches("icon-wrapper show-on-hover");

            cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.OnlyShowCopyIconOnHover, false)
            );

            cut.GetElementAt("div", 2)?.ClassName?.MarkupMatches("icon-wrapper");
        }
    }
}
