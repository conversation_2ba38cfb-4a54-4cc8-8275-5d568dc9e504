@component @textbox @text @text_2
Feature: the text component has the facility to redact text and / or add a placeholder
    Scenario: the text component can redact text
        Given the user is at the home page
        And the user selects the "Text" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Reactive with prevent whitespace and redacted text"
        Then the Text component image matches the base image "text-redacted"

    Scenario: the text component can add a placeholder
        Given the user is at the home page
        And the user selects the "Text" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "With mask and style applied"
        Then the Text component has the placeholder "(000)000/000/00"
        And the Text component image matches the base image "text-placeholder"
