﻿using AngleSharp.Html.Dom;
using CCTC_Components.Components.CheckBox;
using Microsoft.AspNetCore.Components;

namespace CCTC_Components.bUnit.test
{
    public class CheckBoxTests : CCTCComponentsTestContext
    {
        [Fact]
        public void CheckBoxInvokesEventCallbacksCorrectly()
        {
            AddAddTooltip();

            bool valueChangedRaised = false;
            bool checkBoxChangedRaised = false;
            bool expectedValue = true;
            bool? valueChangedNewValue = null;
            ChangeEventArgs? checkBoxChangedNewValue = null;

            var cut = RenderComponent<CheckBox<bool>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.ValueChanged, args => { valueChangedRaised = true; valueChangedNewValue = args; })
                .Add(p => p.CheckBoxChanged, args => { checkBoxChangedRaised = true; checkBoxChangedNewValue = args; })
            );

            cut.Find("input").Change(expectedValue);

            Assert.True(valueChangedRaised);
            Assert.True(checkBoxChangedRaised);
            Assert.Equal(expectedValue, valueChangedNewValue);
            Assert.Equal(expectedValue, checkBoxChangedNewValue?.Value);
        }

        [Fact]
        public void CheckBoxAttributesConfiguredCorrectly()
        {
            AddAddTooltip();

            var cut = RenderComponent<CheckBox<bool>>(parameters => parameters
                .Add(p => p.CssClass, "test-class")
                .Add(p => p.Style, "border: 5px dotted;")
                .Add(p => p.Id, "test-id")
                .Add(p => p.Value, true)
                .Add(p => p.Label, "some test label")
                .Add(p => p.Disabled, true)
            );

            var checkBoxWrapperElement = cut.Find("cctc-input[data-cctc-input-type=\"checkbox\"]");
            var expectedCheckBoxWrapperAttributes = new Dictionary<string, string?>()
            {
                { "id", "test-id" },
                { "class", "test-class" },
                { "style", "border: 5px dotted;" },
                { "data-author", "cctc" }
            };

            var actualCheckBoxWrapperAttributes = new Dictionary<string, string?>()
            {
                { "id", checkBoxWrapperElement.Id },
                { "class", checkBoxWrapperElement.ClassName },
                { "style", checkBoxWrapperElement.GetAttribute("style") },
                { "data-author", checkBoxWrapperElement.GetAttribute("data-author") }
            };

            Assert.Equal(expectedCheckBoxWrapperAttributes, actualCheckBoxWrapperAttributes);

            var inputElement = (IHtmlInputElement)cut.Find("input");
            var expectedInputAttributes = new Dictionary<string, string?>()
            {
                { "id", "test-id-checkbox" },
                { "type", "checkbox" }
            };

            var actualInputAttributes = new Dictionary<string, string?>()
            {
                { "id", inputElement.Id },
                { "type", inputElement.GetAttribute("type") }
            };

            Assert.Equal(expectedInputAttributes, actualInputAttributes);
            Assert.True(inputElement.IsDisabled);
            Assert.True(inputElement.IsChecked);

            var labelElement = (IHtmlLabelElement)cut.Find("label");
            var expectedLabelAttributes = new Dictionary<string, string?>()
            {
                { "id", "test-id-label" },
                { "for", "test-id-checkbox" },
                { "value", "some test label" }
            };

            var actualLabelAttributes = new Dictionary<string, string?>()
            {
                { "id", labelElement.Id },
                { "for", labelElement.HtmlFor },
                { "value", labelElement.InnerHtml }
            };

            Assert.Equal(expectedLabelAttributes, actualLabelAttributes);
        }

        [Fact]
        public void CheckBoxOrientationConfiguredCorrectly()
        {
            AddAddTooltip();

            var cut = RenderComponent<CheckBox<bool>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Value, true)
            );

            cut.GetElementAt("div", 1)?.ClassName?.MarkupMatches("checkbox-wrapper row label-wrap");

            cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.CheckBoxOrientation, CheckBoxOrientation.Right)
            );

            cut.GetElementAt("div", 1)?.ClassName?.MarkupMatches("checkbox-wrapper row-reverse label-wrap");
        }

        [Fact]
        public void CheckBoxLabelOverflowConfiguredCorrectly()
        {
            AddAddTooltip();

            var cut = RenderComponent<CheckBox<bool>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Value, true)
            );

            cut.GetElementAt("div", 1)?.ClassName?.MarkupMatches("checkbox-wrapper row label-wrap");

            cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.CheckBoxLabelOverflow, CheckBoxLabelOverflow.NoWrap)
            );

            cut.GetElementAt("div", 1)?.ClassName?.MarkupMatches("checkbox-wrapper row label-nowrap");

            cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.CheckBoxLabelOverflow, CheckBoxLabelOverflow.Scroll)
            );

            cut.GetElementAt("div", 1)?.ClassName?.MarkupMatches("checkbox-wrapper row label-scroll");

            cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.CheckBoxLabelOverflow, CheckBoxLabelOverflow.None)
            );

            cut.GetElementAt("div", 1)?.ClassName?.MarkupMatches("checkbox-wrapper row");
        }

        [Theory]
        [InlineData(true, true, 0)]
        [InlineData(true, false, 1)]
        [InlineData(false, false, 0)]
        public void CheckBoxReadOnlyIconConfiguredCorrectly(bool readOnly, bool hideReadOnlyIcon, int expectedIconCount)
        {
            AddAddTooltip();

            var cut = RenderComponent<CheckBox<bool>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.ReadOnly, readOnly)
                .Add(p => p.HideReadOnlyIcon, hideReadOnlyIcon)
            );

            var readOnlyIcon = cut.FindAll(".icon-wrapper .material-icons");
            Assert.Equal(expectedIconCount, readOnlyIcon.Count);
        }

        [Fact]
        public void CheckBoxOnClickPreventDefaultEnabled()
        {
            AddAddTooltip();

            var cut = RenderComponent<CheckBox<bool>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Value, true)
                .Add(p => p.ReadOnly, true)
            );

            string eventName = "blazor:onclick:preventdefault";
            Assert.Contains(eventName, cut.GetElementAt("div", 0)?.OuterHtml);
        }

        [Fact]
        public void CheckBoxOnClickPreventDefaultNotEnabled()
        {
            AddAddTooltip();

            var cut = RenderComponent<CheckBox<bool>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Value, true)
            );

            string eventName = "blazor:onclick:preventdefault";
            Assert.DoesNotContain(eventName, cut.GetElementAt("div", 0)?.OuterHtml);
        }

        [Fact]
        public void InvalidTypeParameterThrowsArgumentException()
        {
            var cut = () => RenderComponent<CheckBox<int>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Value, 2)
            );

            var actual = Assert.Throws<ArgumentException>(cut);
            string expectedParamName = "TValue";
            string expectedMessage = $"Expected a boolean or nullable boolean type (Parameter 'TValue')";
            Assert.Equal(expectedParamName, actual.ParamName);
            Assert.Equal(expectedMessage, actual.Message);
        }

        [Fact]
        public void ValidTypeParameterDoesNotThrowArgumentException()
        {
            AddAddTooltip();

            RenderComponent<CheckBox<bool>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Value, true)
            );

            RenderComponent<CheckBox<bool?>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Value, null)
            );
        }
    }
}
