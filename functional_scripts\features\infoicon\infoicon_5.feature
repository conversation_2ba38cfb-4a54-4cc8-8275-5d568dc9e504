@component @infoicon @infoicon_5
Feature: the info icon can have a popover which appears upon clicking the info icon and can display a single message or a header and a message
    <PERSON><PERSON><PERSON>: the info icon has a popover displaying a single message
        Given the user is at the home page
        And the user selects the "Info icon" component
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "InfoType: Popover, Size: XSmall, image source (svg), InfoPlacement: Right, with OnClick callback"
        When the user clicks the infoicon component
        Then the info icon popover has text "Popover message"


    Scenario: the info icon has a popover displaying a header and a message
        Given the user is at the home page
        And the user selects the "Info icon" component
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "InfoType: Popover, Size: XSmall, image source (svg), InfoPlacement: Right, with title"
        When the user clicks the infoicon component
        Then the info icon popover has text "Popover message" with the header "Popover title"
