@component @textbox @textarea @textarea_4
Feature: the text area component can be made read-only and / or disabled. The read-only icon is optional
    Scenario: the text area component can be made read-only
        Given the user is at the home page
        And the user selects the "Text area" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Read-only"
        Then the Text area component is not editable
        And the Text area component image matches the base image "textarea-readonly"

    Scenario: the text area component can be disabled
        Given the user is at the home page
        And the user selects the "Text area" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Disabled"
        Then the Text area component is disabled
        And the Text area component image matches the base image "textarea-disabled"

    Scenario: the text area component can be made read-only and disabled
        Given the user is at the home page
        And the user selects the "Text area" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Disabled and read-only"
        Then the Text area component is not editable
        And the Text area component is disabled
        And the Text area component image matches the base image "textarea-readonly-disabled"

    Scenario: the text area component can be made read-only without a read-only icon
        Given the user is at the home page
        And the user selects the "Text area" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Read-only (hide read-only icon)"
        Then the Text area component is not editable
        And the Text area component image matches the base image "textarea-readonly-no-icon"

    Scenario: the text area component can be made read-only and disabled without a read-only icon
        Given the user is at the home page
        And the user selects the "Text area" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Disabled and read-only (hide read-only icon)"
        Then the Text area component is not editable
        And the Text area component is disabled
        And the Text area component image matches the base image "textarea-readonly-disabled-no-icon"