@component @switch @switch_2
Feature: the switch component label is optional and can be on the left or right of the switch and clicking the label toggles the switch
    Scenario: the label is to the right of the switch and clicking the label toggles the switch
        Given the user is at the home page
        And the user selects the "Switch" component
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "LabelPosition: Right (default), with callback"
        And the Switch component is turned off
        When the user clicks the Label within the Switch component
        Then the Switch component is turned on
        And the Switch component image matches the base image "switch-label-right"

    Scenario: the label is to the left of the switch and clicking the label toggles the switch
        Given the user is at the home page
        And the user selects the "Switch" component
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "LabelPosition: Left, with callback"
        And the Switch component is turned off
        When the user clicks the Label within the Switch component
        Then the Switch component is turned on
        And the Switch component image matches the base image "switch-label-left"

    Scenario: the label is optional
        Given the user is at the home page
        And the user selects the "Switch" component
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "No label, with callback"
        Then the Switch component image matches the base image "switch-no-label"
