﻿@inherits CCTC_Components.Components.__CCTC.CCTCBase

@implements IDisposable

<CascadingValue Value="this">
    @if (Visible)
    {
        <cctc-panelmenuheader id="@Id" class="@CssClass" style="@Style" data-author="cctc">
            <div class="d-flex align-items-center panelmenu-header" @onclick="CollapseOrExpand">
                <div class="material-icons panelmenu-header-icon @(HideIcon ? "panelmenu-header-icon-hide" : "")">
                    @Icon
                </div>
                @if (!Parent.IsCollapsed)
                {
                    <div class="flex-grow-1 panelmenu-header-title">
                        @Title
                    </div>
                }

                <div class="material-icons align-self-end panelmenu-header-expandcollapse-icon">
                    @(_isExpanded ? "expand_less" : "expand_more")
                </div>
            </div>

            @if (_isExpanded)
            {
                <div>@ChildContent</div>
            }
        </cctc-panelmenuheader>
    }

</CascadingValue>

@code {

    /// <summary>
    /// The <see cref="PanelMenu"/> to which this header belongs
    /// </summary>
    [CascadingParameter]
    public required PanelMenu Parent { get; set; }

    /// <summary>
    /// The <see cref="RenderFragment"/> that represents the content of this header item
    /// </summary>
    [Parameter, EditorRequired]
    public required RenderFragment ChildContent { get; set; }

    /// <summary>
    /// The title to display to the user
    /// </summary>
    [Parameter, EditorRequired]
    public required string Title { get; set; }

    /// <summary>
    /// The icon to display
    /// </summary>
    [Parameter]
    public string? Icon { get; set; }

    /// <summary>
    /// If false, the header item is not rendered
    /// </summary>
    [Parameter]
    public bool Visible { get; set; } = true;

    /// <summary>
    /// Hides the icon when true
    /// </summary>
    [Parameter]
    public bool HideIcon { get; set; }

    /// <summary>
    /// Expands the headers content on initialization when true
    /// </summary>
    [Parameter]
    public bool ExpandOnInit { get; set; }

    bool _isExpanded;

    void CollapseOrExpand()
    {
        _isExpanded = !_isExpanded;
        if (_isExpanded && Parent.PanelMenuExpand == PanelMenuExpand.Single)
        {
            Parent.HandleHeaderSelected(Id);
        }

        StateHasChanged();
    }

    /// <summary>
    /// Forces the item to collapse unless it has the given id
    /// </summary>
    /// <param name="selectedId">The id of item in question</param>
    public void ForceCollapseUnlessIsSelected(string selectedId)
    {
        if (selectedId != Id)
        {
            _isExpanded = false;
            StateHasChanged();
        }
    }

    /// <summary>
    /// Expands the item
    /// </summary>
    public void Expand()
    {
        _isExpanded = true;
    }

    /// <inheritdoc />
    protected override void OnInitialized()
    {
        _isExpanded = ExpandOnInit;
        Parent.RegisterChild(this);
    }

    /// <inheritdoc />
    public void Dispose()
    {

    }
}
