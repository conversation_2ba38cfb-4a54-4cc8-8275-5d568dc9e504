﻿cctc-pill {
    max-width: fit-content;
}

cctc-pill-content-container {
    display: flex;
    align-items: center;
    border-radius: 50px;
    padding: 0.1rem 0.5rem 0.1rem 0.5rem;
    color: var(--cctc-pill-color);
    background-color: var(--cctc-pill-background-color);
    border-color: var(--cctc-pill-border-color);
    border-style: none;
}

cctc-pill-content-container.pill-success {
    color: var(--cctc-pill-success-color);
    background-color: var(--cctc-pill-success-background-color);
    border-color: var(--cctc-pill-success-border-color);
}

cctc-pill-content-container.pill-info {
    color: var(--cctc-pill-info-color);
    background-color: var(--cctc-pill-info-background-color);
    border-color: var(--cctc-pill-info-border-color);
}

cctc-pill-content-container.pill-warning {
    color: var(--cctc-pill-warning-color);
    background-color: var(--cctc-pill-warning-background-color);
    border-color: var(--cctc-pill-warning-border-color);
}

cctc-pill-content-container.pill-danger {
    color: var(--cctc-pill-danger-color);
    background-color: var(--cctc-pill-danger-background-color);
    border-color: var(--cctc-pill-danger-border-color);
}

cctc-pill-content-container.pill-outline {
    border-width: 1px;
    border-style: solid;
    background-color: var(--cctc-background-color);
}

cctc-pill ::deep cctc-tooltip {
    min-width: 0;
    display: flex;
    align-items: center;
}

.pill-icon {
    margin-right: 0.25rem;
    user-select: none;
}

.pill-content {
    white-space: nowrap;
    overflow-x: hidden;
    text-overflow: ellipsis;
}