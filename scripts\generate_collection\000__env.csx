// https://iliasshaikh.medium.com/c-scripting-with-vscode-a-recipe-c672dd44d6

/*
    use
        install using

            dotnet tool install -g dotnet-script

        run from command line using

            dotnet script 010__makedata.csx

*/



#r "nuget: Lib, 1.0.27"
#r "nuget: CCTC_Lib, 1.0.11"

#nullable enable

using CCTC_Lib.Contracts.Data;
using CCTC_Lib.Enums.Data;
using System;
using System.Collections;

public static class Constants
{
    public static string BaseUrl => "https://localhost:7021/";

    public static int DefaultThrottleMs => 1000;

    public static string DisplayJoinString => " | ";

    const string KeyValueDelimiter = "¦";

    public static string DisplayKeyValueDelimiter => $" {KeyValueDelimiter} ";
}

public record NodeIdentifier(string Schema, string NodeResource, Guid Id) : ISearchable
{
    public string AsId()
    {
        return Id.ToString();
    }

    public string AsShortId()
    {
        return Common.Helpers.String.truncate(Id.ToString(), 8, false);
    }

    public string AsLabel()
    {
        return $"{NodeResource} ({Schema})";
    }

    public virtual string SearchText()
    {
        return $"{Schema} {NodeResource} {Id}";
    }

    public override string ToString()
    {
        return $"{Schema}, {NodeResource}, {Id}";
    }

    public NodeIdentifier Copy() => this with { };

    public static NodeIdentifier Empty() => new NodeIdentifier("", "", Guid.Empty);

}

public sealed class NamedNodeIdentifier : IEquatable<NamedNodeIdentifier>, ISearchable, IIdentity, ISortable
{
    public static NamedNodeIdentifier Create(string schema, string nodeResource, Guid id, string[] name)
    {
        return new NamedNodeIdentifier(new NodeIdentifier(schema, nodeResource, id), name);
    }

    public NamedNodeIdentifier(NodeIdentifier nodeIdentifier, string[] name)
    {
        NodeIdentifier = nodeIdentifier;
        Name = name;
    }

    public NodeIdentifier NodeIdentifier { get; }

    public string[] Name { get; }

    public string Schema => NodeIdentifier.Schema;
    public string NodeResource => NodeIdentifier.NodeResource;
    public Guid Id => NodeIdentifier.Id;
    public string AsLabel => NodeIdentifier.AsLabel();
    public string AsId => NodeIdentifier.AsId();

    public bool ShowIdAndNameParts { get; set; }

    public string AsName()
    {
        return string.Join(Constants.DisplayJoinString, Name);
    }

    public string AsIdAndName()
    {
        return $"{NodeIdentifier.AsShortId()}{Constants.DisplayKeyValueDelimiter}{AsName()}";
    }

    public string AsName(int truncateNameChars)
    {
        return
            Common.Helpers.String.truncate(string.Join(Constants.DisplayJoinString, Name), truncateNameChars, true);
    }

    public string AsLabelAndName()
    {
        return $"{NodeIdentifier.AsLabel()}{Constants.DisplayKeyValueDelimiter}{AsName()}";
    }

    public string AsIdAndName(int truncateNameChars)
    {
        return $"{NodeIdentifier.AsShortId()}{Constants.DisplayKeyValueDelimiter}{AsName(truncateNameChars)}";
    }

    public string AsStringForDisplay()
    {
        return
            $"{NodeIdentifier.AsLabel()}{Constants.DisplayKeyValueDelimiter}{AsIdAndName()}";
    }

    public string AsStringForDisplay(int truncateNameChars)
    {
        return
            $"{NodeIdentifier.AsLabel()}{Constants.DisplayKeyValueDelimiter}{AsIdAndName(truncateNameChars)}";
    }

    public string AsShortId()
    {
        return Common.Helpers.String.truncate(Id.ToString(), 8, false);
    }

    public string AsIdAndNameParts()
    {
        return $"{AsShortId()}{Constants.DisplayKeyValueDelimiter}{AsName()}";
    }

    public string DisplayAs()
    {
        return
            ShowIdAndNameParts
                ? AsIdAndNameParts()
                : AsLabel;
    }

    public string SearchText()
    {
        return $"{NodeIdentifier.SearchText()} {AsName()}";
    }

    public NamedNodeIdentifier Clone() =>
        new(NodeIdentifier.Copy(), Name.Clone() as string[] ?? Array.Empty<string>());

    public static NamedNodeIdentifier Empty() => new(NodeIdentifier.Empty(), new[] { "" });

    public override string ToString()
    {
        return $"{NodeIdentifier}, {AsName()}";
    }

    public bool Equals(NamedNodeIdentifier? other)
    {
        if (ReferenceEquals(null, other)) return false;
        if (ReferenceEquals(this, other)) return true;

        return
            NodeIdentifier.Equals(other.NodeIdentifier) && AsName() == other.AsName();
    }

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(null, obj)) return false;
        if (ReferenceEquals(this, obj)) return true;
        if (obj.GetType() != this.GetType()) return false;
        return Equals((NamedNodeIdentifier)obj);
    }

    public override int GetHashCode()
    {
        return HashCode.Combine(NodeIdentifier, AsName());
    }

    public static string SortBy { get; set; } = "name";
    public static SortDir SortDir { get; set; } = SortDir.Asc;
    public Func<Guid> Key => () => Id;
}

public class TreeNode<T> : IEnumerable<TreeNode<T>>
{
    public T Data { get; set; }
    public TreeNode<T>? Parent { get; set; }
    public ICollection<TreeNode<T>> Children { get; set; }

    public bool IsRoot => Parent == null;

    public bool IsLeaf => Children.Count == 0;

    public int Level => IsRoot ? 0 : Parent!.Level + 1;

    public TreeNode(T data)
    {
        Data = data;
        Children = new LinkedList<TreeNode<T>>();
        ElementsIndex = new LinkedList<TreeNode<T>>();
        ElementsIndex.Add(this);
    }

    public TreeNode<T> AddChild(T child)
    {
        TreeNode<T> childNode = new(child) { Parent = this };
        Children.Add(childNode);
        RegisterChildForSearch(childNode);

        return childNode;
    }

    public TreeNode<T> AddChild(TreeNode<T> child)
    {
        Children.Add(child);
        RegisterChildForSearch(child);

        return child;
    }

    public override string ToString()
    {
        return Data!.ToString()!;
    }

    public string AsString => ToString();

    #region searching

    ICollection<TreeNode<T>> ElementsIndex { get; set; }

    void RegisterChildForSearch(TreeNode<T> node)
    {
        ElementsIndex.Add(node);
        if (Parent != null)
            Parent.RegisterChildForSearch(node);
    }

    public TreeNode<T> FindTreeNode(Func<TreeNode<T>, bool> predicate)
    {
        return ElementsIndex.FirstOrDefault(predicate)!;
    }

    #endregion


    #region iterating

    IEnumerator IEnumerable.GetEnumerator()
    {
        return GetEnumerator();
    }

    public IEnumerator<TreeNode<T>> GetEnumerator()
    {
        yield return this;
        foreach (var directChild in this.Children)
        {
            foreach (var anyChild in directChild)
                yield return anyChild;
        }
    }

    #endregion;
}