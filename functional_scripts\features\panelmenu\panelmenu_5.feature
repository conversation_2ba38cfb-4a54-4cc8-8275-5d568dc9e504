@component @panelmenu @panelmenu_5
Feature: one or multiple headers can be expanded in the panel menu
    Scenario: multiple headers are expanded in the panel menu
        Given the user is at the home page
        And the user selects the "Panel menu" component in the container "Panel menu"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Multi-expand"
        And the panel menu header "header 1" is "closed"
        When the user selects the "header 1" Panel menu header
        Then the panel menu header "header 1" is "open"
        And there is a panel menu item called "panel item 1A"
        And the panel menu header "header 2" is "closed"
        When the user selects the "header 2" Panel menu header
        Then the panel menu header "header 2" is "open"
        And there is a panel menu item called "panel item 2A"
        And the panel menu header "header 1" is "open"
        And there is a panel menu item called "panel item 1A"
        And the panel menu matches the base image "2 headers open at a time"
        
    Scenario: multiple headers are expanded in the panel menu
        Given the user is at the home page
        And the user selects the "Panel menu" component in the container "Panel menu"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Typical usage"
        And the panel menu header "header 1" is "closed"
        When the user selects the "header 1" Panel menu header
        Then the panel menu header "header 1" is "open"
        And there is a panel menu item called "panel item 1A"
        And the panel menu header "header 2" is "closed"
        When the user selects the "header 2" Panel menu header
        Then the panel menu header "header 2" is "open"
        And there is a panel menu item called "panel item 2A"
        And the panel menu header "header 1" is "closed"
        And the text "panel item 1A" is not visible in the panel menu
        And the panel menu matches the base image "1 header open at a time"

  

