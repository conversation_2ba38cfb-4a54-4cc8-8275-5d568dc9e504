import { ICustomWorld } from '../../support/custom-world';
import { compareToBaseImage } from '../../utils/compareImages';
import * as Helpers from '../../support/helper-functions';
import { When, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';

When('the user presses the button component', async function (this: ICustomWorld) {
  await Helpers.getSamplerTabsSelectedContent(this).locator('cctc-button').click();
});

Then(
  'the button counter number is {int}',
  async function (this: ICustomWorld, buttonCount: number) {
    const confirmResultOutcome = Helpers.getSamplerTabsSelectedContent(this).getByText('Counter: ');
    await expect(confirmResultOutcome).toHaveText(`Counter: ${buttonCount}`);
  }
);

Then('the button text is {string}', async function (this: ICustomWorld, buttonContent: string) {
  const buttonText = Helpers.getSamplerTabsSelectedContent(this).locator(
    'cctc-button .button-wrapper .button-text'
  );
  await expect(buttonText).toHaveText(buttonContent);
});

Then(
  'the button component image matches the base image {string}',
  async function (this: ICustomWorld, name: string) {
    const screenshot = await Helpers.tryGetStableScreenshot(
      Helpers.getSamplerTabsSelectedContent(this).locator('cctc-button')
    );
    await compareToBaseImage(this, name, screenshot);
  }
);

Then('the button text is on the right', async function (this: ICustomWorld) {
  await expect(
    Helpers.getSamplerTabsSelectedContent(this).locator('cctc-button .button-wrapper')
  ).toHaveClass(/.*\bicon-left\b.*/);
});

Then('the button text is on the left', async function (this: ICustomWorld) {
  await expect(
    Helpers.getSamplerTabsSelectedContent(this).locator('cctc-button .button-wrapper')
  ).toHaveClass(/.*\bicon-right\b.*/);
});

Then('the button component has no border', async function (this: ICustomWorld) {
  await expect(
    Helpers.getSamplerTabsSelectedContent(this).locator('cctc-button .button-wrapper')
  ).not.toHaveClass(/.*\bbutton-border\b.*/);
});

Then('the button component has a border', async function (this: ICustomWorld) {
  await expect(
    Helpers.getSamplerTabsSelectedContent(this).locator('cctc-button .button-wrapper')
  ).toHaveClass(/.*\bbutton-border\b.*/);
});
Then('the button component has no icon', async function (this: ICustomWorld) {
  await expect(
    Helpers.getSamplerTabsSelectedContent(this).locator('cctc-button .button-wrapper div').first()
  ).not.toHaveClass(/.*\bbutton-icon\b.*/);
});

Then('the button component has an icon', async function (this: ICustomWorld) {
  await expect(
    Helpers.getSamplerTabsSelectedContent(this).locator('cctc-button .button-wrapper div').first()
  ).toHaveClass(/.*\bbutton-icon\b.*/);
});

Then('the button icon is {string}', async function (this: ICustomWorld, iconName: string) {
  const buttonIcon = Helpers.getSamplerTabsSelectedContent(this).locator(
    'cctc-button .button-wrapper .button-icon'
  );
  await expect(buttonIcon).toHaveText(iconName);
});

Then('the button component is disabled', async function (this: ICustomWorld) {
  await expect(
    Helpers.getSamplerTabsSelectedContent(this).locator('cctc-button .button-wrapper')
  ).toHaveClass(/^(?=.*disabled).*$/);
});

Then(
  'the button component text has the RGB color model value {string} when in normal state',
  async function (this: ICustomWorld, expectedColor: string) {
    const buttonText = Helpers.getSamplerTabsSelectedContent(this).locator(
      'cctc-button .button-wrapper .button-text'
    );
    await expect(buttonText).toHaveCSS('color', expectedColor);
  }
);

Then(
  'the button component icon has the RGB color model value {string} when in normal state',
  async function (this: ICustomWorld, expectedColor: string) {
    const buttonIcon = Helpers.getSamplerTabsSelectedContent(this).locator(
      'cctc-button .button-wrapper .button-icon'
    );
    await expect(buttonIcon).toHaveCSS('color', expectedColor);
  }
);

Then(
  'the button icon has the RGB color model value {string} when in the hover state',
  async function (this: ICustomWorld, expectedColor: string) {
    const buttonWrapper = Helpers.getSamplerTabsSelectedContent(this).locator(
      'cctc-button .button-wrapper'
    );
    await buttonWrapper.hover();
    const buttonIcon = Helpers.getSamplerTabsSelectedContent(this).locator(
      'cctc-button .button-wrapper:hover .button-icon'
    );
    await expect(buttonIcon).toHaveCSS('color', expectedColor);
  }
);

Then(
  'the button text has the RGB color model value {string} when in the hover state',
  async function (this: ICustomWorld, expectedColor: string) {
    const buttonWrapper = Helpers.getSamplerTabsSelectedContent(this).locator(
      'cctc-button .button-wrapper'
    );
    await buttonWrapper.hover();
    const buttonText = Helpers.getSamplerTabsSelectedContent(this).locator(
      'cctc-button .button-wrapper:hover .button-text'
    );
    await expect(buttonText).toHaveCSS('color', expectedColor);
  }
);

Then(
  'the button border has the RGB color model value {string} when in the hover state',
  async function (this: ICustomWorld, expectedColor: string) {
    const buttonWrapper = Helpers.getSamplerTabsSelectedContent(this).locator(
      'cctc-button .button-wrapper'
    );
    await buttonWrapper.hover();
    const buttonBorder = Helpers.getSamplerTabsSelectedContent(this).locator(
      'cctc-button .button-wrapper:hover.button-border'
    );
    await expect(buttonBorder).toHaveCSS('border-color', expectedColor);
  }
);

Then(
  'the button border the RGB color model value {string} when in normal state',
  async function (this: ICustomWorld, expectedColor: string) {
    const buttonBorder = Helpers.getSamplerTabsSelectedContent(this).locator(
      'cctc-button .button-wrapper.button-border'
    );
    await expect(buttonBorder).toHaveCSS('border-color', expectedColor);
  }
);
