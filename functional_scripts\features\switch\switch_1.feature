@component @switch @switch_1
Feature: the switch component switch can be on or off
    Scenario: the switch component sample page is available
        Given the user is at the home page
        When the user selects the "Switch" component
        Then the url ending is "switchsample"

    Scenario: the switch component option can be turned on using the mouse
        Given the user is at the home page
        And the user selects the "Switch" component
        And the Switch component is turned off
        When the user clicks the Switch within the Switch component
        Then the Switch component is turned on

    Scenario: the switch component option can be turned off using the mouse
        Given the user is at the home page
        And the user selects the "Switch" component
        And the Switch component is turned off
        And the user clicks the Switch within the Switch component
        And the Switch component is turned on
        When the user clicks the Switch within the Switch component
        Then the Switch component is turned off

