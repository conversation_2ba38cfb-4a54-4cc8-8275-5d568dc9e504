param (
    [string]$FunctionalTestPath = $(Join-Path $env:USERPROFILE "source\repos\CCTC_Components\functional_scripts"),
    [string]$BlazorPath = $(Join-Path $env:USERPROFILE "source\repos\CCTC_Components\src\CCTC_Components_UI"),
    [string]$Browser,
    [int]$BlazorTimeout = 60, # seconds
    [switch]$StopOnFailure,
    [switch]$SkipCleanup,
    [switch]$NoParallelRetry,
    [switch]$Help
)

$script:blazorProcess = $null
$script:blazorProjectName = Split-Path $BlazorPath -Leaf

# Default supported browsers
$defaultBrowsers = @("chrome", "msedge", "firefox", "chromium")

# Trap for handling interrupts
trap [System.Management.Automation.PipelineStoppedException] {
    Write-Host "`nScript interrupted..." -ForegroundColor Yellow
    Stop-BlazorIfNeeded -SkipCleanup:$SkipCleanup
    exit 1
}

function Show-Help {
    Write-Host @"

Playwright Test Runner Script
============================

Purpose:
    Automates running Playwright tests across multiple browsers for $script:blazorProjectName.
    Starts the Blazor app if not already running.

Usage:
    .\run-tests.ps1 [options]

Options:
    -Help                 Shows this help message
    -FunctionalTestPath   Path to functional tests directory
                            Default: $FunctionalTestPath
    -BlazorPath           Path to Blazor project
                            Default: $BlazorPath
    -Browser <name>       Run tests on a specific browser ($($defaultBrowsers[0..($defaultBrowsers.Count-2)] -join ', ') or $($defaultBrowsers[-1]))
                            Default: run tests on all supported browsers
    -BlazorTimeout        Timeout in seconds for Blazor startup
                            Default: $BlazorTimeout
    -StopOnFailure       Stop running tests on remaining browsers after a failure (default: continue)
    -SkipCleanup         Skip cleanup of Blazor process after tests complete (default: cleanup)
    -NoParallelRetry     Run tests in sequential mode with no retries (default: parallel mode with retries)

Examples:
    # Show this help message
    .\run-tests.ps1 -Help

    # Run on all browsers with defaults (including Blazor cleanup)
    .\run-tests.ps1

    # Run tests but keep Blazor running after completion
    .\run-tests.ps1 -SkipCleanup

    # Run only on Chrome
    .\run-tests.ps1 -Browser chrome

    # Run with custom paths
    .\run-tests.ps1 -FunctionalTestPath "C:\MyTests" -BlazorPath "C:\MyBlazor"

    # Stop running remaining browsers after first failure
    .\run-tests.ps1 -StopOnFailure

    # Run tests without parallel retry
    .\run-tests.ps1 -NoParallelRetry

"@
    exit 0
}

function Test-TimeoutValue {
    param ([int]$Timeout)
    
    if ($Timeout -lt 10) {
        throw "BlazorTimeout is set too low ($Timeout seconds). Minimum recommended timeout is 10 seconds. Either:
        - Increase the timeout: .\run-tests.ps1 -BlazorTimeout 30
        - Start Blazor manually before running tests"
    }
}

function Test-UrlRunning {
    param ([string]$Url)
    try {
        $response = Invoke-WebRequest -Uri $Url -UseBasicParsing -ErrorAction SilentlyContinue
        return $response.StatusCode -eq 200
    }
    catch {
        return $false
    }
}

function Get-BlazorProcess {
    param (
        [string]$ProjectName
    )
    $processes = Get-WmiObject Win32_Process -Filter "name = 'dotnet.exe'" |
        Where-Object { $_.CommandLine -like "*$ProjectName*" } |
        Select-Object -First 1
    
    if ($processes) {
        return Get-Process -Id $processes.ProcessId
    }
    return $null
}

function Stop-BlazorProcess {
    if ($script:blazorProcess) {
        Stop-Process -Id $script:blazorProcess.Id -Force -ErrorAction SilentlyContinue
    }
}

function Wait-BlazorStart {
    param (
        [int]$Timeout,
        [string]$ProjectName,
        [string]$BaseUrl
    )
    
    $endTime = [DateTime]::Now.AddSeconds($Timeout)
    $checkInterval = 2
    $attempts = 0
    
    while ([DateTime]::Now -le $endTime) {
        $attempts++
        if (Test-UrlRunning -Url $BaseUrl) {
            Write-Host "$ProjectName started successfully after $($attempts * $checkInterval) seconds`n"
            return $true
        }
        Start-Sleep -Seconds $checkInterval
    }
    return $false
}

function Get-BaseUrl {
    param (
        [string]$FunctionalTestPath
    )
    
    $configPath = Join-Path $FunctionalTestPath "src\support\config.ts"
    if (Test-Path $configPath) {
        $config = Get-Content $configPath
        foreach ($line in $config) {
            if ($line -match "^\s*BASE_URL:\s*'(https?://[^']+)'") {
                return $matches[1]
            }
        }
    }
    throw "Could not find BASE_URL in $configPath. Please ensure the config file exists and BASE_URL is not commented out."
}

function Start-LocalBlazor {
    param (
        [string]$BaseUrl,
        [int]$BlazorTimeout,
        [string]$BlazorPath,
        [string]$ProjectName
    )
    
    Write-Host "Starting $ProjectName on base url: $BaseUrl ..."
    $script:blazorProcess = Start-Process "dotnet" -ArgumentList "run" -WorkingDirectory $BlazorPath -WindowStyle Hidden -PassThru
    
    $started = Wait-BlazorStart -Timeout $BlazorTimeout -ProjectName $ProjectName -BaseUrl $BaseUrl
    if (!$started) {
        Stop-BlazorProcess
        throw "Blazor failed to start within $BlazorTimeout seconds. Consider:
        - Increasing the timeout: .\run-tests.ps1 -BlazorTimeout 120
        - Starting Blazor manually before running tests
        - Checking for any Blazor startup issues"
    }
}

function Start-BlazorIfNeeded {
    param (
        [int]$BlazorTimeout,
        [string]$BlazorPath,
        [string]$ProjectName,
        [string]$FunctionalTestPath
    )
    
    $baseUrl = Get-BaseUrl -FunctionalTestPath $FunctionalTestPath
    $blazorRunning = Test-UrlRunning -Url $baseUrl
    
    if ($baseUrl -like "*localhost*") {
        if ($blazorRunning) {
            Write-Host "$ProjectName is already running on base url: $baseUrl"
            $script:blazorProcess = Get-BlazorProcess -ProjectName $ProjectName
            if ($script:blazorProcess) {
                Write-Host "Found existing Blazor process: $($script:blazorProcess.Id)"
            }
        }
        else {
            Start-LocalBlazor -BaseUrl $baseUrl -BlazorTimeout $BlazorTimeout -BlazorPath $BlazorPath -ProjectName $ProjectName
        }
    }
    else {
        if ($blazorRunning) {
            Write-Host "Site is accessible at: $baseUrl"
            $response = Read-Host "Do you want to run tests against this remote URL rather than localhost (Y/N)?"
            if ($response.ToUpper() -ne "Y") {
                throw "Test execution cancelled by user"
            }
            Write-Host "Proceeding with tests against remote URL"
        }
        else {
            throw "Cannot access site at $baseUrl. Please ensure the site is running and accessible."
        }
    }
}

function ConvertFrom-TestTime {
    param (
        [string]$TimeString
    )
    
    if ($TimeString -match "(\d+)m(\d+\.\d+)s") {
        $minutes = [int]$matches[1]
        $seconds = [double]$matches[2]
        return [TimeSpan]::FromSeconds(($minutes * 60) + $seconds)
    }
    return [TimeSpan]::Zero
}

function Start-BrowserTest {
    param (
        [string]$Browser,
        [string]$FunctionalTestPath,
        [string]$NpmCommand
    )
    
    Set-Location $FunctionalTestPath
    $env:BROWSER = $Browser
    $output = npm run $NpmCommand 2>&1
    
    if (Test-Path ".\reports\report.html") {
        Copy-Item ".\reports\report.html" ".\reports\report_$Browser.html" -Force
    }
    
    $results = @{
        Browser = $Browser
        Success = $true
        Output = $output
        Time = [TimeSpan]::Zero
        ExecutionTime = [TimeSpan]::Zero
    }
    
    # Parse the times and final test status
    $output | ForEach-Object {
        if ($_ -match "^(\d+m[\d.]+s).*executing steps: (\d+m[\d.]+s)\)$") {
            $results.Time = ConvertFrom-TestTime -TimeString $matches[1]
            $results.ExecutionTime = ConvertFrom-TestTime -TimeString $matches[2]
        }
        if ($_ -match "^\d+ scenarios? \(\d+ ([a-z]+).*\)$") {
            $results.Success = $matches[1] -eq "passed"
        }
    }
    
    return $results
}

function Write-TestResult {
    param (
        [hashtable]$TestResult,
        [switch]$StopOnFailure
    )
    
    Write-Host "`nResults for $($TestResult.Browser):"
    # Only show the three key lines from the output
    $TestResult.Output | Where-Object { 
        $_ -match "^\d+ scenarios? \(.*\)$" -or
        $_ -match "^\d+ steps? \(.*\)$" -or
        $_ -match "^\d+m[\d.]+s \(executing steps: .*\)$"
    } | ForEach-Object {
        Write-Host $_
    }
    
    if (!$TestResult.Success -and !$StopOnFailure) {
        Write-Host "`nTest failed but continuing with remaining browsers (-StopOnFailure is not set)" -ForegroundColor Yellow
    }
}

$stopOnFailureMessage = "`nTo run all browsers regardless of failures, omit -StopOnFailure`n"

function Start-BrowserTests {
    param (
        [array]$BrowsersToTest,
        [string]$FunctionalTestPath,
        [switch]$StopOnFailure,
        [switch]$NoParallelRetry
    )
    
    if ($NoParallelRetry) {
        $npmCommand = "test"
        $testMode = "sequential mode with no retries"
    }
    else {
        $npmCommand = "test:parallel-retry"
        $testMode = "parallel mode with retries"
    }
    Write-Host "`nExecuting tests in $testMode..." -ForegroundColor Cyan
    
    $results = @()
    foreach ($browserName in $BrowsersToTest) {
        try {
            Write-Host "`nRunning tests for $browserName..."
            $testResult = Start-BrowserTest -Browser $browserName -FunctionalTestPath $FunctionalTestPath -NpmCommand $npmCommand
            $results += $testResult
            
            Write-TestResult -TestResult $testResult -StopOnFailure:$StopOnFailure
            
            if (!$testResult.Success -and $StopOnFailure) {
                Write-Host $stopOnFailureMessage -ForegroundColor Yellow
                exit 1
            }
        }
        catch {
            if ($StopOnFailure) {
                Write-Host $stopOnFailureMessage -ForegroundColor Yellow
                exit 1
            }
            Write-Host "Error running tests for $browserName but continuing: $_" -ForegroundColor Yellow
        }
    }
    return $results
}

function Write-TestSummary {
    param (
        [array]$Results,
        [string]$FunctionalTestPath
    )
    
    Write-Host "`n=== Test Summary ==="
    Write-Host "Total browsers tested: $($Results.Count)"
    Write-Host "Successful runs: $($Results.Where({ $_.Success }).Count)"
    Write-Host "Failed runs: $($Results.Where({ !$_.Success }).Count)"

    # Get total seconds from all TimeSpans
    $totalSeconds = ($Results | ForEach-Object { $_.Time.TotalSeconds } | Measure-Object -Sum).Sum
    $executionSeconds = ($Results | ForEach-Object { $_.ExecutionTime.TotalSeconds } | Measure-Object -Sum).Sum
    
    $totalTimeSpan = [TimeSpan]::FromSeconds($totalSeconds)
    $totalExecutionTimeSpan = [TimeSpan]::FromSeconds($executionSeconds)
    Write-Host "Total time: $($totalTimeSpan.ToString('mm\:ss\.fff'))s (executing steps: $($totalExecutionTimeSpan.ToString('mm\:ss\.fff'))s)"
    Write-Host "`nBrowser-specific reports are available in the $FunctionalTestPath\reports directory"
}

function Test-DisplayScaling {
    try {
        Write-Host "`nIMPORTANT: Display scaling must be set to 100% for reliable test execution."
        Write-Host "Please check Windows Settings > System > Display > Scale`n"
        
        $response = Read-Host "Is your display scaling set to 100% for all monitors (Y/N)?"
        if ($response.ToUpper() -ne "Y") {
            throw "Display scaling must be set to 100%. Please adjust Windows Settings > System > Display > Scale for consistent test behavior."
        }
        Write-Host "Proceeding with display scaling confirmed at 100%"
    }
    catch {
        throw "Display scaling check failed: $_"
    }
}

function Stop-BlazorIfNeeded {
    param([switch]$SkipCleanup)
    
    if ($script:blazorProcess) {
        if (!$SkipCleanup) {
            Write-Host "Cleaning up Blazor process..." -ForegroundColor Cyan
            try {
                Stop-BlazorProcess
                Write-Host "$script:blazorProjectName stopped" -ForegroundColor Cyan
            }
            catch {
                Write-Host "Error stopping $script:blazorProjectName: $_" -ForegroundColor Red
            }
        }
        else {
            Write-Host "Leaving $script:blazorProjectName running (-SkipCleanup is set)" -ForegroundColor Cyan
        }
    }
}

# Main execution
try {
    if ($Help) {
        Show-Help
    }

    Test-TimeoutValue -Timeout $BlazorTimeout
    Test-DisplayScaling

    $browsersToTest = @()
    if ($Browser) {
        $browsersToTest += $Browser
    }
    else {
        $browsersToTest = $defaultBrowsers
    }

    Start-BlazorIfNeeded -BlazorTimeout $BlazorTimeout -BlazorPath $BlazorPath -ProjectName $script:blazorProjectName -FunctionalTestPath $FunctionalTestPath
    $results = Start-BrowserTests -BrowsersToTest $browsersToTest -FunctionalTestPath $FunctionalTestPath -StopOnFailure:$StopOnFailure -NoParallelRetry:$NoParallelRetry
    Write-TestSummary -Results $results -FunctionalTestPath $FunctionalTestPath
}
catch {
    Write-Host "`nError: $_" -ForegroundColor Red
    exit 1
}
finally {
    Stop-BlazorIfNeeded -SkipCleanup:$SkipCleanup
}