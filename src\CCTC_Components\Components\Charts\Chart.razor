﻿@using Microsoft.Extensions.Logging
@using Microsoft.JSInterop

@inherits CCTC_Components.Components.__CCTC.CCTCBase
@inject ILogger<Chart> Logger
@inject IJSRuntime JS

@* do not change canvas id to @Id-canvas as it crashes!!! *@

<cctc-chart id="@Id" class="@CssClass" style="@Style; @_style" data-author="cctc" tabindex="@TabIndex">
    <canvas id="@Id-chart" aria-label="chart canvas" role="figure" >
        <p>Chart component</p>
    </canvas>
</cctc-chart>


@code {

    DotNetObjectReference<Chart>? _objRef;
    IJSObjectReference? _module;

    string? _style;

    /// <summary>
    /// The anonymous object that represents the config for the chart
    /// </summary>
    [Parameter, EditorRequired]
    public required object Config { get; set; }

    /// <summary>
    /// The base options for the chart
    /// </summary>
    [Parameter]
    public object? Options { get; set; }

    /// <summary>
    /// The width of the chart's container
    /// </summary>
    /// <remarks>Providing a width directly will stop any responsive set up from working.
    /// The width becomes fixed regardless of viewport if provided via parameters</remarks>
    [Parameter]
    public string? Width { get; set; }

    /// <summary>
    /// The height of the chart's container
    /// </summary>
    /// <remarks>Providing a height directly will stop any responsive set up from working.
    /// The height becomes fixed regardless of viewport if provided via parameters</remarks>
    [Parameter]
    public string? Height { get; set; }

    /// <summary>
    /// The initial color must be provided on first set up
    /// </summary>
    [Parameter, EditorRequired]
    public string InitialColor { get; set; } = "white";

    /// <summary>
    /// The title for the chart
    /// </summary>
    [Parameter]
    public string? Title { get; set; }

    /// <summary>
    /// The subtitle for the chart
    /// </summary>
    [Parameter]
    public string? SubTitle { get; set; }

    /// <summary>
    /// Runs the chart update to refresh content
    /// </summary>
    public async Task Update()
    {
        if (_module is not null)
            await _module.InvokeVoidAsync("update", _chartId);
    }

    /// <summary>
    /// Triggers a resize of the chart
    /// </summary>
    /// <remarks>Useful when the container resizes as this forces the resize to occur</remarks>
    public async Task Resize()
    {
        if (_module is not null)
            await _module.InvokeVoidAsync("resize", _chartId);
    }

    /// <summary>
    /// Triggers a render
    /// </summary>
    /// <remarks>Does not add new data, use update for that</remarks>
    public async Task Render()
    {
        if (_module is not null)
            await _module.InvokeVoidAsync("render", _chartId);
    }

    /// <summary>
    /// Resets the chart to its state before animation then can retrigger using update
    /// </summary>
    public async Task Reset()
    {
        if (_module is not null)
            await _module.InvokeVoidAsync("reset", _chartId);
    }

    /// <summary>
    /// Turns off and on the aspect ratio maintenance
    /// </summary>
    /// <param name="newValue">The new value to set</param>
    /// <remarks>If a Width and Height are given, the maintenance of the aspect ratio must
    /// be set to off for the dimensions to have any effect</remarks>
    public async Task SetMaintainAspectRatio(bool newValue)
    {
        if (_module is not null)
            await _module.InvokeVoidAsync("setMaintainAspectRatio", _chartId, newValue);
    }

    // /// <summary>
    // /// Resets the chart from the Config and Color given in the parameters
    // /// </summary>
    // /// <remarks>First the existing chart is destroyed</remarks>
    // public async Task SetConfig()
    // {
    //     if (_module is not null)
    //     {
    //         await _module.InvokeVoidAsync("destroyChart");
    //         await _module.InvokeVoidAsync("setUpChart", $"{Id}-chart", Config, InitialColor);
    //         if (Options is not null)
    //         {
    //             await SetOptions();
    //         }
    //
    //         if (Title is not null)
    //         {
    //             await SetTitle(true, Title);
    //         }
    //
    //         if (SubTitle is not null)
    //         {
    //             await SetSubtitle(true, SubTitle);
    //         }
    //     }
    // }

    /// <summary>
    /// Resets the options
    /// </summary>
    public async Task SetOptions()
    {
        if (_module is not null)
        {
            await _module.InvokeVoidAsync("setOptions", _chartId, Options);
        }
    }

    /// <summary>
    /// Update the option
    /// </summary>
    /// <param name="option">The option to update</param>
    /// <param name="value">The value to set</param>
    public async Task SetOption(string option, object value)
    {
        if (_module is not null)
        {
            await _module.InvokeVoidAsync("setOption", _chartId, option, value);
        }
    }

    /// <summary>
    /// Updates the option of the option
    /// </summary>
    /// <param name="option1">The first level option</param>
    /// <param name="option2">The second level option</param>
    /// <param name="value">The value to set</param>
    public async Task SetOption(string option1, string option2, object value)
    {
        if (_module is not null)
        {
            await _module.InvokeVoidAsync("setOptionOption", _chartId, option1, option2, value);
        }
    }

    /// <summary>
    /// Sets the property of the dataset with the given index to the given value
    /// </summary>
    /// <param name="dataSetIndex">The index of the dataset to change</param>
    /// <param name="property">The property to update</param>
    /// <param name="value">The value to set the property to</param>
    public async Task SetDatasetProperty(int dataSetIndex, string property, object value)
    {
        if (_module is not null)
        {
            await _module.InvokeVoidAsync("setDatasetProperty", _chartId, dataSetIndex, property, value);
        }
    }

    /// <summary>
    /// Sets the property of all datasets to the given value
    /// </summary>
    /// <param name="property">The property to update</param>
    /// <param name="value">The value to set the property to</param>
    public async Task SetDatasetProperty(string property, object value)
    {
        if (_module is not null)
        {
            await _module.InvokeVoidAsync("setDatasetProperty", _chartId, null, property, value);
        }
    }

    /// <summary>
    /// Updates a data value in the given dataset at the given index
    /// </summary>
    /// <param name="dataSetIndex">The index of the dataset to update</param>
    /// <param name="dataPointIndex">The index of the datapoint to update</param>
    /// <param name="value">The new value</param>
    public async Task SetDatasetDataValue(int dataSetIndex, int dataPointIndex, int value)
    {
        if (_module is not null)
        {
            await _module.InvokeVoidAsync("setDatasetDataValue", _chartId, 0, 1, value);
        }
    }

    /// <summary>
    /// Sets the border color for all datasets
    /// </summary>
    /// <param name="borderColor">The color to set</param>
    public async Task SetBorderColor(string borderColor)
    {
        if (_module is not null)
        {
            await _module.InvokeVoidAsync("setBorderColor", _chartId, borderColor);
        }
    }

    /// <summary>
    /// Sets the border color for the dataset with the given index
    /// </summary>
    /// <param name="borderColor">The color to set</param>
    /// <param name="dataSetIndex">The index of the dataset to set the border color for</param>
    public async Task SetBorderColor(string borderColor, int dataSetIndex)
    {
        if (_module is not null)
        {
            await _module.InvokeVoidAsync("setBorderColor", _chartId, borderColor, dataSetIndex);
        }
    }

    /// <summary>
    /// Sets the default color for use with the chart
    /// </summary>
    /// <param name="color"></param>
    public async Task SetColor(string color)
    {
        if (_module is not null)
        {
            await _module.InvokeVoidAsync("setColor", _chartId, color);
        }
    }

    /// <summary>
    /// Sets the background colors for all datasets
    /// </summary>
    /// <param name="colors">An array of colors</param>
    public async Task SetBackgroundColors(string[] colors)
    {
        if (_module is not null)
        {
            await _module.InvokeVoidAsync("setBackgroundColors", _chartId, (object)colors);
        }
    }

    /// <summary>
    /// Sets the background colors for the dataset with the given index
    /// </summary>
    /// <param name="colors">An array of colors</param>
    /// <param name="dataSetIndex">The index of the dataset to set the colors for</param>
    public async Task SetBackgroundColors(string[] colors, int dataSetIndex)
    {
        if (_module is not null)
        {
            await _module.InvokeVoidAsync("setBackgroundColors", _chartId, (object)colors, dataSetIndex);
        }
    }

    /// <summary>
    /// Sets the legend
    /// </summary>
    /// <param name="legend">An object representing the legend</param>
    public async Task SetLegend(object legend)
    {
        if (_module is not null)
        {
            await _module.InvokeVoidAsync("setLegend", _chartId, (object)legend);
        }
    }

    /// <summary>
    /// Adds a dataset at the end of the datasets array
    /// </summary>
    /// <param name="dataset">The dataset to add</param>
    public async Task AddDataset(object dataset)
    {
        if (_module is not null)
            await _module.InvokeVoidAsync("addDataset", _chartId, dataset);
    }

    /// <summary>
    /// Adds a dataset to the datasets array at the given index
    /// </summary>
    /// <param name="dataset">The dataset to add</param>
    /// <param name="index">The index where the dataset should be added</param>
    public async Task AddDataset(object dataset, int? index)
    {
        if (_module is not null)
            await _module.InvokeVoidAsync("addDataset", _chartId, dataset, index);
    }

    /// <summary>
    /// Removes the last dataset or the dataset at the given index if provided
    /// </summary>
    /// <param name="index">The optional index of the dataset to remove</param>
    public async Task RemoveDataset(int? index)
    {
        if (_module is not null)
            await _module.InvokeVoidAsync("removeDataset", _chartId, index);
    }

    /// <summary>
    /// Prints the chart object to the console for debug purposes
    /// </summary>
    public async Task Print()
    {
        if (_module is not null)
            await _module.InvokeVoidAsync("print", _chartId);
    }

    /// <summary>
    /// Gets the current type of chart
    /// </summary>
    /// <returns>A task of string that is the chart config type</returns>
    public async Task<string?> GetChartType()
    {
        if (_module is not null)
        {
            return await _module.InvokeAsync<string>("getCurrentChartType", _chartId);;
        }

        return null;
    }

    /// <summary>
    /// Sets a new title. Can also be used to hide a current title without changing it
    /// </summary>
    /// <param name="display">If true displays the title</param>
    /// <param name="newTitle">The new title to use</param>
    public async Task SetTitle(bool? display = null, string? newTitle = null)
    {
        if (_module is not null)
        {
            await _module.InvokeVoidAsync("setTitle", _chartId, display, newTitle);;
        }
    }

    /// <summary>
    /// Sets a new subtitle. Can also be used to hide a current subtitle without changing it
    /// </summary>
    /// <param name="display">If true displays the subtitle</param>
    /// <param name="newTitle">The new subtitle to use</param>
    public async Task SetSubtitle(bool? display = null, string? newTitle = null)
    {
        if (_module is not null)
        {
            await _module.InvokeVoidAsync("setSubtitle", _chartId, display, newTitle);;
        }
    }

    /// <summary>
    /// Sets the scales for use with the chart
    /// </summary>
    /// <param name="scales">The scales object to use</param>
    public async Task SetScales(object scales)
    {
        if (_module is not null)
        {
            await _module.InvokeVoidAsync("setScales", _chartId, (object)scales);
        }
    }

    //this internal guid string is used in javascript to uniquely identify the chart
    string _chartId = default!;

    /// <inheritdoc />
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);

        if (firstRender)
        {
            _objRef = DotNetObjectReference.Create(this);
            _module = await JS.InvokeAsync<IJSObjectReference>("import", "./_content/CCTC_Components/Components/Charts/Chart.razor.js");

            //the chart must be set up everytime
            await _module.InvokeVoidAsync("setUpChart", $"{Id}-chart", _objRef, _chartId, Config, InitialColor);

            if (Options is not null)
            {
                await SetOptions();
            }

            if (Title is not null)
            {
                await SetTitle(true, Title);
            }

            if (SubTitle is not null)
            {
                await SetSubtitle(true, SubTitle);
            }

            //await Print();
        }

        await Resize();
    }

    /// <inheritdoc />
    protected override void OnInitialized()
    {
        base.OnInitialized();

        //creates the internal chart id that is used to identify this js Chart object amongst rest
        _chartId = Guid.NewGuid().ToString();
    }

    /// <inheritdoc />
    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        if (Width is not null && Height is not null)
        {
            await SetMaintainAspectRatio(false);
        }

        _style =
            new StyleBuilder()
                .AddStyle("position", "relative")
                .AddStyle("width", Width, Width is not null)
                .AddStyle("height", Height, Height is not null)
                .Build();
    }

    /// <inheritdoc/>
    public override async ValueTask DisposeAsync()
    {
        await base.DisposeAsync();

        if (_module is not null)
        {
            try
            {
                await _module.InvokeVoidAsync("destroy", _chartId);
                await _module.DisposeAsync();
            }
            catch (JSDisconnectedException)
            {
//deliberately silently handled as can be thrown when moving from server to wasm
            }
            finally
            {
                IsDisposed = true;
            }
        }
    }

    /// <inheritdoc cref="Dispose" />
    public virtual void Dispose()
    {
        _objRef?.Dispose();
        IsDisposed = true;
    }

}