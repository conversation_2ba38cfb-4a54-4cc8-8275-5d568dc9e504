#r "nuget: Lib"
#r "nuget: TextCopy"

open System
open System.Text.RegularExpressions
open TextCopy
open Common.Helpers

let targetComponent = "Tabs/Tabs.razor"
let file = __SOURCE_DIRECTORY__ + "../../../CCTC_Components/src/CCTC_Components/Components/" + targetComponent

let fileText = IO.File.ReadAllText(file)


(*
    pattern matches

    <div id="@Id-cancel-button"

    or

    <div id="@Id-cancel-button-@i"

    and

    <InfoIcon
        Id=@($"{Id}-select-all-icon")
*)
let pattern = """(?<=id=\"@Id)(-|@|[a-z])*(?=\")|(?<=Id=@\(\$\"\{Id\})(-|[a-z])*"""

let x = Regex.Matches(fileText, pattern)
x |> Seq.iter (fun y -> printfn "%s" y.Value)

//runs the matches and creates a list of results and copies to clipboard
Regex.Matches(fileText, pattern)
|> Seq.map(fun x -> x.Value)
|> Seq.filter(fun x -> x |> Seq.length > 0)
|> Seq.map(fun x -> $"\"{x}\"," )
|> Seq.distinct
|> String.joinStr "\n"
|> String.chomp
|> ClipboardService.SetText