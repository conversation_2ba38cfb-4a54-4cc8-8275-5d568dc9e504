@component @temporal @date @date_4
Feature: the date component can be made read-only and / or disabled. The read-only icon is optional
    Scenario: the date component can be made read-only
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: yyyy-MM-dd, read-only"
        Then the Date component is not editable
        And the Date component image matches the base image "date-readonly"

    Scenario: the date component can be disabled
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: yyyy-MM-dd, disabled"
        Then the Date component is disabled
        And the Date component image matches the base image "date-disabled"

    Scenario: the date component can be made read-only and disabled
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: yyyy-MM-dd, read-only and disabled"
        Then the Date component is not editable
        And the Date component is disabled
        And the Date component image matches the base image "date-readonly-disabled"

    Scenario: the date component can be made read-only without a read-only icon
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: yyyy-MM-dd, read-only and read-only icon hidden"
        Then the Date component is not editable
        And the Date component image matches the base image "date-readonly-no-icon"

    Scenario: the date component can be made read-only and disabled without a read-only icon
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: yyyy-MM-dd, read-only, disabled and read-only icon hidden"
        Then the Date component is not editable
        And the Date component is disabled
        And the Date component image matches the base image "date-readonly-disabled-no-icon"