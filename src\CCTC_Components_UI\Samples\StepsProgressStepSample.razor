﻿@page "/progressstepsample"

@{
    var description = new List<string>
    {
        "A ProgressStep component is an individual step within the Steps component. "
    };

    var relatedComponents = new List<(Relation relation, string display, string url, string description)>
    {
        (Relation.Parent, "Steps", "stepssample", "The containing Steps component to which an individual ProgressStep belongs"),
    };

}

<Sampler
    ComponentName="ProgressStep"
    ComponentCssName="progressstep"
    Description="@description"
    RelatedComponents="@relatedComponents">
</Sampler>

@code {

}