@component @infoicon @infoicon_3
Feature: the info icon can be adjacent to a field or on its own
    Scenario: the info icon can be adjacent to a field
        Given the user is at the home page
        And the user selects the "Info icon" component
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "InfoType: Tooltip, Size: XSmall, image source (svg), InfoPlacement: Right, with OnClick callback"
        Then the infoicon and its attached component matches the base image "icon adjacent to field"

    Scenario: the info icon can be on its own
        Given the user is at the home page
        And the user selects the "Info icon" component
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "InfoType: Tooltip, Size: Large, image icon (material icon), InfoPlacement: Right, CssClass applied"
        Then the infoicon and its attached component matches the base image "icon without field"