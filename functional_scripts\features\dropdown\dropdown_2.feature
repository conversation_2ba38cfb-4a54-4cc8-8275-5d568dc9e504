@component @dropdown @dropdown_2
Feature: the dropdown component can allow an empty value and an entered value can be optionally cleared
    Scenario: the current selected dropdown option can be empty when show clear is set to true
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Dropdown type: Nullable Tuple data with show clear"
        Then the current selected dropdown option has the text ""
        And the Dropdown component image matches the base image "dropdown-cleared-hide-clear-icon"

    Scenario: the current selected dropdown option can be cleared when show clear is set to true
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Dropdown type: nullable int, with callback, show clear and disabled options applied"
        And the current selected dropdown option has the text "2"
        And the Dropdown component image matches the base image "dropdown-show-clear-icon"
        When the user clicks on the dropdown clear icon
        Then the current selected dropdown option has the text ""
        And the dropdown clear icon is no longer in view
        And the Dropdown component image matches the base image "dropdown-cleared-hide-clear-icon"

    Scenario: the current selected dropdown option can be empty when show clear is set to false
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Dropdown type: Person class, null initial value, do not show clear"
        Then the current selected dropdown option has the text ""
        And the Dropdown component image matches the base image "dropdown-cleared-no-clear-icon"