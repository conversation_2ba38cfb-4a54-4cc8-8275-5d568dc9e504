@component @infoicon @infoicon_2
Feature: the info icon can be a variety of sizes of images and icons
    Scenario: the info icon can be a small blue circle image
        Given the user is at the home page
        And the user selects the "Info icon" component
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "InfoType: Tooltip, Size: XSmall, image source (svg), InfoPlacement: Right, with OnClick callback"
        When the info icon component image is "images/info_bsinfo_24dp.svg"
        Then the infoicon component image matches the base image "small blue circle"
        And the info icon height is "16.0px" and width is "16.0px"

    Scenario: the info icon can be a small white circle image
        Given the user is at the home page
        And the user selects the "Info icon" component
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "InfoType: Tooltip, Size: Small, image source (svg), InfoPlacement: Right, overridden background color via Style"
        When the info icon component image is "images/info_white_24dp.svg"
        Then the infoicon component image matches the base image "blue and white circle"
        And the info icon height is "19.2px" and width is "19.2px"

    Scenario: the info icon can be a question mark icon
        Given the user is at the home page
        And the user selects the "Info icon" component
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "InfoType: Tooltip, Size: Medium, image icon (material icon), InfoPlacement: Top, overridden background color via CssClass"
        When the info icon component icon is "help_center"
        Then the infoicon component image matches the base image "white and blue question mark"
        And the info icon height is "24.0px" and width is "24.0px"