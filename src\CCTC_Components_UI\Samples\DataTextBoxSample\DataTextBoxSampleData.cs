﻿using CCTC_Components_UI.Helpers.Models;
using CCTC_Components_UI.Helpers.Services;

namespace CCTC_Components_UI.Samples.DataTextBoxSample;

public static class DataTextBoxSampleData
{
    //Creates a treenode of dummy data for the purpose of the demo
    public static TreeNode<NamedNodeIdentifier> CreateDataNode()
    {
        var init = NamedNodeIdentifier.Create("_Base", "Theme", Guid.NewGuid(), new[] { "CCTU_CT" });
        var tree = new TreeNode<NamedNodeIdentifier>(init);

        var aero = tree.AddChild(NamedNodeIdentifier.Create("_Base", "Team", Guid.NewGuid(), new[] { "AeroDigestive" }));
        var womens = tree.AddChild(NamedNodeIdentifier.Create("_Base", "Team", Guid.NewGuid(), new[] { "Womens" }));

        var leader1 = tree.AddChild(NamedNodeIdentifier.Create("_Base", "Leader", Guid.NewGuid(), new[] { "<PERSON>" }));
        var leader2 = tree.AddChild(NamedNodeIdentifier.Create("_Base", "Leader", Guid.NewGuid(), new[] { "<PERSON>" }));
        var leader3 = tree.AddChild(NamedNodeIdentifier.Create("_Base", "Leader", Guid.NewGuid(), new[] { "Andy" }));
        var leader4 = tree.AddChild(NamedNodeIdentifier.Create("_Base", "Leader", Guid.NewGuid(), new[] { "Mike" }));

        var cancerType1 = tree.AddChild(NamedNodeIdentifier.Create("_Base", "CancerType", Guid.NewGuid(), new[] { "Ovarian" }));
        var cancerType2 = tree.AddChild(NamedNodeIdentifier.Create("_Base", "CancerType", Guid.NewGuid(), new[] { "Lung" }));
        var cancerType3 = tree.AddChild(NamedNodeIdentifier.Create("_Base", "CancerType", Guid.NewGuid(), new[] { "Prostate" }));

        var trial1 = aero.AddChild(NamedNodeIdentifier.Create("_Base", "Trial", Guid.NewGuid(), new[] { "Aero", "Trial 1" }));
        var trial2 = aero.AddChild(NamedNodeIdentifier.Create("_Base", "Trial", Guid.NewGuid(), new[] { "Aero", "Trial 2" }));
        var trial3 = aero.AddChild(NamedNodeIdentifier.Create("_Base", "Trial", Guid.NewGuid(), new[] { "Aero", "Trial 3" }));
        var trial4 = aero.AddChild(NamedNodeIdentifier.Create("_Base", "Trial", Guid.NewGuid(), new[] { "Aero", "Trial 4" }));


        var trial11 = womens.AddChild(NamedNodeIdentifier.Create("_Base", "Trial", Guid.NewGuid(), new[] { "womens", "Trial 1" }));
        var trial12 = womens.AddChild(NamedNodeIdentifier.Create("_Base", "Trial", Guid.NewGuid(), new[] { "womens", "Trial 2" }));
        var trial13 = womens.AddChild(NamedNodeIdentifier.Create("_Base", "Trial", Guid.NewGuid(), new[] { "womens", "Trial 3" }));
        var trial14 = womens.AddChild(NamedNodeIdentifier.Create("_Base", "Trial", Guid.NewGuid(), new[] { "womens", "Trial 4" }));


        var patient1_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 1" }));
        var patient2_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 2" }));
        var patient3_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 3" }));
        var patient4_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 4" }));
        var patient5_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 5" }));
        var patient6_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 6" }));
        var patient7_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 7" }));
        var patient8_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 8" }));
        var patient9_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 9" }));
        var patient10_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 10" }));
        var patient11_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 11" }));
        var patient12_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 12" }));
        var patient13_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 13" }));
        var patient14_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 14" }));
        var patient15_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 15" }));
        var patient16_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 16" }));
        var patient17_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 17" }));
        var patient18_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 18" }));
        var patient19_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 19" }));
        var patient20_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 20" }));
        var patient21_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 21" }));
        var patient22_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 22" }));
        var patient23_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 23" }));
        var patient24_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 24" }));
        var patient25_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 25" }));
        var patient26_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 26" }));
        var patient27_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 27" }));
        var patient28_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 28" }));
        var patient29_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 29" }));
        var patient30_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 30" }));
        var patient31_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 31" }));
        var patient32_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 32" }));
        var patient33_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 33" }));
        var patient34_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 34" }));
        var patient35_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 35" }));
        var patient36_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 36" }));
        var patient37_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 37" }));
        var patient38_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 38" }));
        var patient39_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 39" }));
        var patient40_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 40" }));
        var patient41_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 41" }));
        var patient42_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 42" }));
        var patient43_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 43" }));
        var patient44_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 44" }));
        var patient45_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 45" }));
        var patient46_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 46" }));
        var patient47_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 47" }));
        var patient48_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 48" }));
        var patient49_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 49" }));
        var patient50_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 1", "Patient 50" }));

        var patient1_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 2", "Patient 1" }));
        var patient2_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 2", "Patient 2" }));
        var patient3_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 2", "Patient 3" }));
        var patient4_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 2", "Patient 4" }));
        var patient5_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 2", "Patient 5" }));
        var patient6_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 2", "Patient 6" }));
        var patient7_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 2", "Patient 7" }));
        var patient8_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 2", "Patient 8" }));
        var patient9_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 2", "Patient 9" }));
        var patient10_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 2", "Patient 10" }));

        var patient1_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 3", "Patient 1" }));
        var patient2_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 3", "Patient 2" }));
        var patient3_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 3", "Patient 3" }));
        var patient4_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 3", "Patient 4" }));
        var patient5_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 3", "Patient 5" }));
        var patient6_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 3", "Patient 6" }));
        var patient7_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 3", "Patient 7" }));
        var patient8_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 3", "Patient 8" }));
        var patient9_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 3", "Patient 9" }));
        var patient10_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 3", "Patient 10" }));
        var patient11_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 3", "Patient 11" }));
        var patient12_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 3", "Patient 12" }));
        var patient13_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 3", "Patient 13" }));
        var patient14_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 3", "Patient 14" }));
        var patient15_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 3", "Patient 15" }));
        var patient16_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 3", "Patient 16" }));
        var patient17_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 3", "Patient 17" }));
        var patient18_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 3", "Patient 18" }));
        var patient19_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 3", "Patient 19" }));
        var patient20_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 3", "Patient 20" }));


        var patient1_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 1" }));
        var patient2_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 2" }));
        var patient3_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 3" }));
        var patient4_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 4" }));
        var patient5_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 5" }));
        var patient6_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 6" }));
        var patient7_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 7" }));
        var patient8_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 8" }));
        var patient9_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 9" }));
        var patient10_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 10" }));
        var patient11_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 11" }));
        var patient12_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 12" }));
        var patient13_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 13" }));
        var patient14_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 14" }));
        var patient15_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 15" }));
        var patient16_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 16" }));
        var patient17_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 17" }));
        var patient18_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 18" }));
        var patient19_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 19" }));
        var patient20_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 20" }));
        var patient21_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 21" }));
        var patient22_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 22" }));
        var patient23_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 23" }));
        var patient24_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 24" }));
        var patient25_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 25" }));
        var patient26_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 26" }));
        var patient27_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 27" }));
        var patient28_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 28" }));
        var patient29_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 29" }));
        var patient30_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 30" }));
        var patient31_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 31" }));
        var patient32_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 32" }));
        var patient33_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 33" }));
        var patient34_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 34" }));
        var patient35_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 35" }));
        var patient36_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 36" }));
        var patient37_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 37" }));
        var patient38_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 38" }));
        var patient39_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 39" }));
        var patient40_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 40" }));
        var patient41_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 41" }));
        var patient42_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 42" }));
        var patient43_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 43" }));
        var patient44_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 44" }));
        var patient45_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 45" }));
        var patient46_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 46" }));
        var patient47_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 47" }));
        var patient48_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 48" }));
        var patient49_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 49" }));
        var patient50_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 11", "Patient 50" }));

        var patient1_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 1" }));
        var patient2_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 2" }));
        var patient3_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 3" }));
        var patient4_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 4" }));
        var patient5_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 5" }));
        var patient6_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 6" }));
        var patient7_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 7" }));
        var patient8_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 8" }));
        var patient9_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 9" }));
        var patient10_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 10" }));
        var patient11_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 11" }));
        var patient12_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 12" }));
        var patient13_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 13" }));
        var patient14_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 14" }));
        var patient15_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 15" }));
        var patient16_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 16" }));
        var patient17_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 17" }));
        var patient18_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 18" }));
        var patient19_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 19" }));
        var patient20_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 20" }));
        var patient21_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 21" }));
        var patient22_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 22" }));
        var patient23_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 23" }));
        var patient24_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 24" }));
        var patient25_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 25" }));
        var patient26_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 26" }));
        var patient27_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 27" }));
        var patient28_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 28" }));
        var patient29_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 29" }));
        var patient30_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 30" }));
        var patient31_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 31" }));
        var patient32_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 32" }));
        var patient33_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 33" }));
        var patient34_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 34" }));
        var patient35_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 12", "Patient 35" }));

        var patient1_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 1" }));
        var patient2_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 2" }));
        var patient3_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 3" }));
        var patient4_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 4" }));
        var patient5_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 5" }));
        var patient6_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 6" }));
        var patient7_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 7" }));
        var patient8_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 8" }));
        var patient9_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 9" }));
        var patient10_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 10" }));
        var patient11_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 11" }));
        var patient12_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 12" }));
        var patient13_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 13" }));
        var patient14_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 14" }));
        var patient15_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 15" }));
        var patient16_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 16" }));
        var patient17_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 17" }));
        var patient18_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 18" }));
        var patient19_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 19" }));
        var patient20_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 20" }));
        var patient21_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 21" }));
        var patient22_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 22" }));
        var patient23_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 23" }));
        var patient24_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 24" }));
        var patient25_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 25" }));
        var patient26_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 26" }));
        var patient27_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 27" }));
        var patient28_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 28" }));
        var patient29_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 29" }));
        var patient30_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 30" }));
        var patient31_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 31" }));
        var patient32_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 32" }));
        var patient33_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 33" }));
        var patient34_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 34" }));
        var patient35_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 35" }));
        var patient36_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 36" }));
        var patient37_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 37" }));
        var patient38_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 38" }));
        var patient39_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 39" }));
        var patient40_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 40" }));
        var patient41_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 41" }));
        var patient42_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 42" }));
        var patient43_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 43" }));
        var patient44_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 44" }));
        var patient45_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 45" }));
        var patient46_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 46" }));
        var patient47_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 47" }));
        var patient48_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 48" }));
        var patient49_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 49" }));
        var patient50_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Patient", Guid.NewGuid(), new[] { "Trial 13", "Patient 50" }));

        var site1_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 1" }));
        var site2_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 2" }));
        var site3_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 3" }));
        var site4_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 4" }));
        var site5_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 5" }));
        var site6_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 6" }));
        var site7_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 7" }));
        var site8_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 8" }));
        var site9_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 9" }));
        var site10_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 10" }));
        var site11_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 11" }));
        var site12_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 12" }));
        var site13_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 13" }));
        var site14_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 14" }));
        var site15_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 15" }));
        var site16_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 16" }));
        var site17_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 17" }));
        var site18_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 18" }));
        var site19_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 19" }));
        var site20_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 20" }));
        var site21_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 21" }));
        var site22_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 22" }));
        var site23_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 23" }));
        var site24_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 24" }));
        var site25_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 25" }));
        var site26_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 26" }));
        var site27_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 27" }));
        var site28_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 28" }));
        var site29_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 29" }));
        var site30_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 30" }));
        var site31_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 31" }));
        var site32_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 32" }));
        var site33_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 33" }));
        var site34_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 34" }));
        var site35_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 35" }));
        var site36_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 36" }));
        var site37_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 37" }));
        var site38_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 38" }));
        var site39_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 39" }));
        var site40_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 40" }));
        var site41_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 41" }));
        var site42_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 42" }));
        var site43_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 43" }));
        var site44_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 44" }));
        var site45_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 45" }));
        var site46_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 46" }));
        var site47_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 47" }));
        var site48_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 48" }));
        var site49_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 49" }));
        var site50_trial1 = trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 1", "Site 50" }));

        var site1_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 1" }));
        var site2_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 2" }));
        var site3_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 3" }));
        var site4_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 4" }));
        var site5_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 5" }));
        var site6_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 6" }));
        var site7_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 7" }));
        var site8_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 8" }));
        var site9_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 9" }));
        var site10_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 10" }));
        var site11_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 11" }));
        var site12_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 12" }));
        var site13_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 13" }));
        var site14_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 14" }));
        var site15_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 15" }));
        var site16_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 16" }));
        var site17_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 17" }));
        var site18_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 18" }));
        var site19_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 19" }));
        var site20_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 20" }));
        var site21_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 21" }));
        var site22_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 22" }));
        var site23_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 23" }));
        var site24_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 24" }));
        var site25_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 25" }));
        var site26_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 26" }));
        var site27_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 27" }));
        var site28_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 28" }));
        var site29_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 29" }));
        var site30_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 30" }));
        var site31_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 31" }));
        var site32_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 32" }));
        var site33_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 33" }));
        var site34_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 34" }));
        var site35_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 35" }));
        var site36_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 36" }));
        var site37_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 37" }));
        var site38_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 38" }));
        var site39_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 39" }));
        var site40_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 40" }));
        var site41_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 41" }));
        var site42_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 42" }));
        var site43_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 43" }));
        var site44_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 44" }));
        var site45_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 45" }));
        var site46_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 46" }));
        var site47_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 47" }));
        var site48_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 48" }));
        var site49_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 49" }));
        var site50_trial2 = trial2.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 2", "Site 50" }));

        var site1_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 1" }));
        var site2_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 2" }));
        var site3_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 3" }));
        var site4_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 4" }));
        var site5_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 5" }));
        var site6_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 6" }));
        var site7_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 7" }));
        var site8_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 8" }));
        var site9_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 9" }));
        var site10_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 10" }));
        var site11_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 11" }));
        var site12_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 12" }));
        var site13_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 13" }));
        var site14_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 14" }));
        var site15_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 15" }));
        var site16_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 16" }));
        var site17_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 17" }));
        var site18_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 18" }));
        var site19_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 19" }));
        var site20_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 20" }));
        var site21_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 21" }));
        var site22_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 22" }));
        var site23_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 23" }));
        var site24_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 24" }));
        var site25_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 25" }));
        var site26_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 26" }));
        var site27_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 27" }));
        var site28_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 28" }));
        var site29_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 29" }));
        var site30_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 30" }));
        var site31_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 31" }));
        var site32_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 32" }));
        var site33_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 33" }));
        var site34_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 34" }));
        var site35_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 35" }));
        var site36_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 36" }));
        var site37_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 37" }));
        var site38_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 38" }));
        var site39_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 39" }));
        var site40_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 40" }));
        var site41_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 41" }));
        var site42_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 42" }));
        var site43_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 43" }));
        var site44_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 44" }));
        var site45_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 45" }));
        var site46_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 46" }));
        var site47_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 47" }));
        var site48_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 48" }));
        var site49_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 49" }));
        var site50_trial3 = trial3.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 3", "Site 50" }));

        var site1_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 1" }));
        var site2_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 2" }));
        var site3_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 3" }));
        var site4_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 4" }));
        var site5_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 5" }));
        var site6_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 6" }));
        var site7_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 7" }));
        var site8_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 8" }));
        var site9_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 9" }));
        var site10_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 10" }));
        var site11_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 11" }));
        var site12_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 12" }));
        var site13_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 13" }));
        var site14_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 14" }));
        var site15_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 15" }));
        var site16_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 16" }));
        var site17_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 17" }));
        var site18_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 18" }));
        var site19_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 19" }));
        var site20_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 20" }));
        var site21_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 21" }));
        var site22_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 22" }));
        var site23_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 23" }));
        var site24_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 24" }));
        var site25_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 25" }));
        var site26_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 26" }));
        var site27_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 27" }));
        var site28_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 28" }));
        var site29_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 29" }));
        var site30_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 30" }));
        var site31_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 31" }));
        var site32_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 32" }));
        var site33_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 33" }));
        var site34_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 34" }));
        var site35_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 35" }));
        var site36_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 36" }));
        var site37_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 37" }));
        var site38_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 38" }));
        var site39_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 39" }));
        var site40_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 40" }));
        var site41_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 41" }));
        var site42_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 42" }));
        var site43_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 43" }));
        var site44_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 44" }));
        var site45_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 45" }));
        var site46_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 46" }));
        var site47_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 47" }));
        var site48_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 48" }));
        var site49_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 49" }));
        var site50_trial11 = trial11.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 11", "Site 50" }));

        var site1_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 1" }));
        var site2_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 2" }));
        var site3_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 3" }));
        var site4_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 4" }));
        var site5_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 5" }));
        var site6_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 6" }));
        var site7_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 7" }));
        var site8_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 8" }));
        var site9_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 9" }));
        var site10_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 10" }));
        var site11_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 11" }));
        var site12_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 12" }));
        var site13_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 13" }));
        var site14_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 14" }));
        var site15_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 15" }));
        var site16_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 16" }));
        var site17_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 17" }));
        var site18_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 18" }));
        var site19_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 19" }));
        var site20_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 20" }));
        var site21_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 21" }));
        var site22_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 22" }));
        var site23_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 23" }));
        var site24_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 24" }));
        var site25_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 25" }));
        var site26_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 26" }));
        var site27_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 27" }));
        var site28_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 28" }));
        var site29_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 29" }));
        var site30_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 30" }));
        var site31_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 31" }));
        var site32_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 32" }));
        var site33_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 33" }));
        var site34_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 34" }));
        var site35_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 35" }));
        var site36_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 36" }));
        var site37_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 37" }));
        var site38_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 38" }));
        var site39_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 39" }));
        var site40_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 40" }));
        var site41_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 41" }));
        var site42_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 42" }));
        var site43_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 43" }));
        var site44_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 44" }));
        var site45_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 45" }));
        var site46_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 46" }));
        var site47_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 47" }));
        var site48_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 48" }));
        var site49_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 49" }));
        var site50_trial12 = trial12.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 12", "Site 50" }));

        var site1_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 1" }));
        var site2_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 2" }));
        var site3_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 3" }));
        var site4_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 4" }));
        var site5_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 5" }));
        var site6_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 6" }));
        var site7_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 7" }));
        var site8_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 8" }));
        var site9_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 9" }));
        var site10_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 10" }));
        var site11_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 11" }));
        var site12_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 12" }));
        var site13_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 13" }));
        var site14_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 14" }));
        var site15_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 15" }));
        var site16_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 16" }));
        var site17_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 17" }));
        var site18_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 18" }));
        var site19_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 19" }));
        var site20_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 20" }));
        var site21_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 21" }));
        var site22_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 22" }));
        var site23_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 23" }));
        var site24_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 24" }));
        var site25_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 25" }));
        var site26_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 26" }));
        var site27_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 27" }));
        var site28_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 28" }));
        var site29_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 29" }));
        var site30_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 30" }));
        var site31_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 31" }));
        var site32_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 32" }));
        var site33_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 33" }));
        var site34_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 34" }));
        var site35_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 35" }));
        var site36_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 36" }));
        var site37_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 37" }));
        var site38_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 38" }));
        var site39_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 39" }));
        var site40_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 40" }));
        var site41_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 41" }));
        var site42_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 42" }));
        var site43_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 43" }));
        var site44_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 44" }));
        var site45_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 45" }));
        var site46_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 46" }));
        var site47_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 47" }));
        var site48_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 48" }));
        var site49_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 49" }));
        var site50_trial13 = trial13.AddChild(NamedNodeIdentifier.Create("_Base", "Site", Guid.NewGuid(), new[] { "Trial 13", "Site 50" }));


        var crf1_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 1" }));
        var crf2_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 2" }));
        var crf3_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 3" }));
        var crf4_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 4" }));
        var crf5_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 5" }));
        var crf6_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 6" }));
        var crf7_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 7" }));
        var crf8_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 8" }));
        var crf9_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 9" }));
        var crf10_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 10" }));
        var crf11_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 11" }));
        var crf12_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 12" }));
        var crf13_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 13" }));
        var crf14_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 14" }));
        var crf15_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 15" }));
        var crf16_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 16" }));
        var crf17_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 17" }));
        var crf18_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 18" }));
        var crf19_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 19" }));
        var crf20_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 20" }));
        var crf21_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 21" }));
        var crf22_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 22" }));
        var crf23_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 23" }));
        var crf24_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 24" }));
        var crf25_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 25" }));
        var crf26_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 26" }));
        var crf27_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 27" }));
        var crf28_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 28" }));
        var crf29_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 29" }));
        var crf30_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 30" }));
        var crf31_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 31" }));
        var crf32_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 32" }));
        var crf33_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 33" }));
        var crf34_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 34" }));
        var crf35_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 35" }));
        var crf36_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 36" }));
        var crf37_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 37" }));
        var crf38_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 38" }));
        var crf39_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 39" }));
        var crf40_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 40" }));
        var crf41_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 41" }));
        var crf42_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 42" }));
        var crf43_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 43" }));
        var crf44_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 44" }));
        var crf45_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 45" }));
        var crf46_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 46" }));
        var crf47_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 47" }));
        var crf48_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 48" }));
        var crf49_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 49" }));
        var crf50_patient1_trial1 = patient1_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient1_Trial1", "Crf 50" }));

        var crf1_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 1" }));
        var crf2_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 2" }));
        var crf3_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 3" }));
        var crf4_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 4" }));
        var crf5_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 5" }));
        var crf6_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 6" }));
        var crf7_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 7" }));
        var crf8_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 8" }));
        var crf9_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 9" }));
        var crf10_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 10" }));
        var crf11_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 11" }));
        var crf12_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 12" }));
        var crf13_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 13" }));
        var crf14_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 14" }));
        var crf15_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 15" }));
        var crf16_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 16" }));
        var crf17_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 17" }));
        var crf18_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 18" }));
        var crf19_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 19" }));
        var crf20_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 20" }));
        var crf21_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 21" }));
        var crf22_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 22" }));
        var crf23_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 23" }));
        var crf24_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 24" }));
        var crf25_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 25" }));
        var crf26_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 26" }));
        var crf27_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 27" }));
        var crf28_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 28" }));
        var crf29_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 29" }));
        var crf30_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 30" }));
        var crf31_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 31" }));
        var crf32_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 32" }));
        var crf33_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 33" }));
        var crf34_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 34" }));
        var crf35_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 35" }));
        var crf36_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 36" }));
        var crf37_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 37" }));
        var crf38_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 38" }));
        var crf39_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 39" }));
        var crf40_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 40" }));
        var crf41_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 41" }));
        var crf42_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 42" }));
        var crf43_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 43" }));
        var crf44_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 44" }));
        var crf45_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 45" }));
        var crf46_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 46" }));
        var crf47_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 47" }));
        var crf48_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 48" }));
        var crf49_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 49" }));
        var crf50_patient2_trial1 = patient2_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient2_Trial1", "Crf 50" }));

        var crf1_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 1" }));
        var crf2_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 2" }));
        var crf3_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 3" }));
        var crf4_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 4" }));
        var crf5_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 5" }));
        var crf6_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 6" }));
        var crf7_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 7" }));
        var crf8_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 8" }));
        var crf9_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 9" }));
        var crf10_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 10" }));
        var crf11_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 11" }));
        var crf12_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 12" }));
        var crf13_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 13" }));
        var crf14_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 14" }));
        var crf15_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 15" }));
        var crf16_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 16" }));
        var crf17_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 17" }));
        var crf18_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 18" }));
        var crf19_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 19" }));
        var crf20_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 20" }));
        var crf21_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 21" }));
        var crf22_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 22" }));
        var crf23_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 23" }));
        var crf24_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 24" }));
        var crf25_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 25" }));
        var crf26_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 26" }));
        var crf27_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 27" }));
        var crf28_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 28" }));
        var crf29_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 29" }));
        var crf30_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 30" }));
        var crf31_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 31" }));
        var crf32_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 32" }));
        var crf33_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 33" }));
        var crf34_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 34" }));
        var crf35_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 35" }));
        var crf36_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 36" }));
        var crf37_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 37" }));
        var crf38_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 38" }));
        var crf39_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 39" }));
        var crf40_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 40" }));
        var crf41_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 41" }));
        var crf42_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 42" }));
        var crf43_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 43" }));
        var crf44_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 44" }));
        var crf45_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 45" }));
        var crf46_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 46" }));
        var crf47_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 47" }));
        var crf48_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 48" }));
        var crf49_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 49" }));
        var crf50_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 50" }));


        var crf51_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 51" }));
        var crf52_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 52" }));
        var crf53_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 53" }));
        var crf54_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 54" }));
        var crf55_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 55" }));
        var crf56_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 56" }));
        var crf57_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 57" }));
        var crf58_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 58" }));
        var crf59_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 59" }));
        var crf60_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 60" }));
        var crf61_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 61" }));
        var crf62_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 62" }));
        var crf63_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 63" }));
        var crf64_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 64" }));
        var crf65_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 65" }));
        var crf66_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 66" }));
        var crf67_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 67" }));
        var crf68_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 68" }));
        var crf69_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 69" }));
        var crf70_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 70" }));
        var crf71_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 71" }));
        var crf72_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 72" }));
        var crf73_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 73" }));
        var crf74_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 74" }));
        var crf75_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 75" }));
        var crf76_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 76" }));
        var crf77_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 77" }));
        var crf78_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 78" }));
        var crf79_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 79" }));
        var crf80_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 80" }));
        var crf81_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 81" }));
        var crf82_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 82" }));
        var crf83_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 83" }));
        var crf84_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 84" }));
        var crf85_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 85" }));
        var crf86_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 86" }));
        var crf87_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 87" }));
        var crf88_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 88" }));
        var crf89_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 89" }));
        var crf90_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 90" }));
        var crf91_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 91" }));
        var crf92_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 92" }));
        var crf93_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 93" }));
        var crf94_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 94" }));
        var crf95_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 95" }));
        var crf96_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 96" }));
        var crf97_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 97" }));
        var crf98_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 98" }));
        var crf99_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 99" }));
        var crf100_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 100" }));
        var crf101_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 101" }));
        var crf102_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 102" }));
        var crf103_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 103" }));
        var crf104_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 104" }));
        var crf105_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 105" }));
        var crf106_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 106" }));
        var crf107_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 107" }));
        var crf108_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 108" }));
        var crf109_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 109" }));
        var crf110_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 110" }));
        var crf111_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 111" }));
        var crf112_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 112" }));
        var crf113_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 113" }));
        var crf114_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 114" }));
        var crf115_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 115" }));
        var crf116_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 116" }));
        var crf117_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 117" }));
        var crf118_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 118" }));
        var crf119_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 119" }));
        var crf120_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 120" }));
        var crf121_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 121" }));
        var crf122_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 122" }));
        var crf123_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 123" }));
        var crf124_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 124" }));
        var crf125_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 125" }));
        var crf126_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 126" }));
        var crf127_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 127" }));
        var crf128_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 128" }));
        var crf129_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 129" }));
        var crf130_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 130" }));
        var crf131_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 131" }));
        var crf132_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 132" }));
        var crf133_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 133" }));
        var crf134_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 134" }));
        var crf135_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 135" }));
        var crf136_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 136" }));
        var crf137_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 137" }));
        var crf138_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 138" }));
        var crf139_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 139" }));
        var crf140_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 140" }));
        var crf141_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 141" }));
        var crf142_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 142" }));
        var crf143_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 143" }));
        var crf144_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 144" }));
        var crf145_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 145" }));
        var crf146_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 146" }));
        var crf147_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 147" }));
        var crf148_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 148" }));
        var crf149_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 149" }));
        var crf150_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 150" }));
        var crf151_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 151" }));
        var crf152_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 152" }));
        var crf153_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 153" }));
        var crf154_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 154" }));
        var crf155_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 155" }));
        var crf156_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 156" }));
        var crf157_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 157" }));
        var crf158_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 158" }));
        var crf159_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 159" }));
        var crf160_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 160" }));
        var crf161_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 161" }));
        var crf162_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 162" }));
        var crf163_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 163" }));
        var crf164_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 164" }));
        var crf165_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 165" }));
        var crf166_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 166" }));
        var crf167_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 167" }));
        var crf168_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 168" }));
        var crf169_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 169" }));
        var crf170_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 170" }));
        var crf171_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 171" }));
        var crf172_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 172" }));
        var crf173_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 173" }));
        var crf174_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 174" }));
        var crf175_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 175" }));
        var crf176_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 176" }));
        var crf177_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 177" }));
        var crf178_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 178" }));
        var crf179_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 179" }));
        var crf180_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 180" }));
        var crf181_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 181" }));
        var crf182_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 182" }));
        var crf183_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 183" }));
        var crf184_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 184" }));
        var crf185_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 185" }));
        var crf186_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 186" }));
        var crf187_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 187" }));
        var crf188_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 188" }));
        var crf189_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 189" }));
        var crf190_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 190" }));
        var crf191_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 191" }));
        var crf192_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 192" }));
        var crf193_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 193" }));
        var crf194_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 194" }));
        var crf195_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 195" }));
        var crf196_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 196" }));
        var crf197_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 197" }));
        var crf198_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 198" }));
        var crf199_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 199" }));
        var crf200_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 200" }));
        var crf201_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 201" }));
        var crf202_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 202" }));
        var crf203_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 203" }));
        var crf204_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 204" }));
        var crf205_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 205" }));
        var crf206_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 206" }));
        var crf207_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 207" }));
        var crf208_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 208" }));
        var crf209_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 209" }));
        var crf210_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 210" }));
        var crf211_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 211" }));
        var crf212_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 212" }));
        var crf213_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 213" }));
        var crf214_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 214" }));
        var crf215_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 215" }));
        var crf216_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 216" }));
        var crf217_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 217" }));
        var crf218_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 218" }));
        var crf219_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 219" }));
        var crf220_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 220" }));
        var crf221_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 221" }));
        var crf222_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 222" }));
        var crf223_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 223" }));
        var crf224_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 224" }));
        var crf225_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 225" }));
        var crf226_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 226" }));
        var crf227_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 227" }));
        var crf228_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 228" }));
        var crf229_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 229" }));
        var crf230_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 230" }));
        var crf231_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 231" }));
        var crf232_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 232" }));
        var crf233_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 233" }));
        var crf234_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 234" }));
        var crf235_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 235" }));
        var crf236_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 236" }));
        var crf237_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 237" }));
        var crf238_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 238" }));
        var crf239_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 239" }));
        var crf240_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 240" }));
        var crf241_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 241" }));
        var crf242_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 242" }));
        var crf243_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 243" }));
        var crf244_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 244" }));
        var crf245_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 245" }));
        var crf246_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 246" }));
        var crf247_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 247" }));
        var crf248_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 248" }));
        var crf249_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 249" }));
        var crf250_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 250" }));
        var crf251_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 251" }));
        var crf252_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 252" }));
        var crf253_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 253" }));
        var crf254_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 254" }));
        var crf255_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 255" }));
        var crf256_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 256" }));
        var crf257_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 257" }));
        var crf258_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 258" }));
        var crf259_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 259" }));
        var crf260_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 260" }));
        var crf261_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 261" }));
        var crf262_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 262" }));
        var crf263_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 263" }));
        var crf264_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 264" }));
        var crf265_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 265" }));
        var crf266_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 266" }));
        var crf267_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 267" }));
        var crf268_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 268" }));
        var crf269_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 269" }));
        var crf270_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 270" }));
        var crf271_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 271" }));
        var crf272_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 272" }));
        var crf273_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 273" }));
        var crf274_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 274" }));
        var crf275_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 275" }));
        var crf276_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 276" }));
        var crf277_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 277" }));
        var crf278_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 278" }));
        var crf279_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 279" }));
        var crf280_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 280" }));
        var crf281_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 281" }));
        var crf282_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 282" }));
        var crf283_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 283" }));
        var crf284_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 284" }));
        var crf285_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 285" }));
        var crf286_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 286" }));
        var crf287_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 287" }));
        var crf288_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 288" }));
        var crf289_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 289" }));
        var crf290_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 290" }));
        var crf291_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 291" }));
        var crf292_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 292" }));
        var crf293_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 293" }));
        var crf294_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 294" }));
        var crf295_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 295" }));
        var crf296_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 296" }));
        var crf297_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 297" }));
        var crf298_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 298" }));
        var crf299_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 299" }));
        var crf300_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 300" }));
        var crf301_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 301" }));
        var crf302_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 302" }));
        var crf303_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 303" }));
        var crf304_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 304" }));
        var crf305_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 305" }));
        var crf306_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 306" }));
        var crf307_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 307" }));
        var crf308_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 308" }));
        var crf309_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 309" }));
        var crf310_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 310" }));
        var crf311_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 311" }));
        var crf312_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 312" }));
        var crf313_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 313" }));
        var crf314_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 314" }));
        var crf315_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 315" }));
        var crf316_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 316" }));
        var crf317_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 317" }));
        var crf318_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 318" }));
        var crf319_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 319" }));
        var crf320_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 320" }));
        var crf321_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 321" }));
        var crf322_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 322" }));
        var crf323_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 323" }));
        var crf324_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 324" }));
        var crf325_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 325" }));
        var crf326_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 326" }));
        var crf327_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 327" }));
        var crf328_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 328" }));
        var crf329_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 329" }));
        var crf330_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 330" }));
        var crf331_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 331" }));
        var crf332_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 332" }));
        var crf333_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 333" }));
        var crf334_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 334" }));
        var crf335_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 335" }));
        var crf336_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 336" }));
        var crf337_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 337" }));
        var crf338_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 338" }));
        var crf339_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 339" }));
        var crf340_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 340" }));
        var crf341_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 341" }));
        var crf342_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 342" }));
        var crf343_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 343" }));
        var crf344_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 344" }));
        var crf345_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 345" }));
        var crf346_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 346" }));
        var crf347_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 347" }));
        var crf348_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 348" }));
        var crf349_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 349" }));
        var crf350_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 350" }));
        var crf351_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 351" }));
        var crf352_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 352" }));
        var crf353_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 353" }));
        var crf354_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 354" }));
        var crf355_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 355" }));
        var crf356_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 356" }));
        var crf357_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 357" }));
        var crf358_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 358" }));
        var crf359_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 359" }));
        var crf360_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 360" }));
        var crf361_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 361" }));
        var crf362_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 362" }));
        var crf363_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 363" }));
        var crf364_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 364" }));
        var crf365_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 365" }));
        var crf366_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 366" }));
        var crf367_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 367" }));
        var crf368_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 368" }));
        var crf369_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 369" }));
        var crf370_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 370" }));
        var crf371_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 371" }));
        var crf372_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 372" }));
        var crf373_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 373" }));
        var crf374_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 374" }));
        var crf375_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 375" }));
        var crf376_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 376" }));
        var crf377_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 377" }));
        var crf378_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 378" }));
        var crf379_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 379" }));
        var crf380_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 380" }));
        var crf381_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 381" }));
        var crf382_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 382" }));
        var crf383_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 383" }));
        var crf384_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 384" }));
        var crf385_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 385" }));
        var crf386_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 386" }));
        var crf387_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 387" }));
        var crf388_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 388" }));
        var crf389_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 389" }));
        var crf390_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 390" }));
        var crf391_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 391" }));
        var crf392_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 392" }));
        var crf393_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 393" }));
        var crf394_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 394" }));
        var crf395_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 395" }));
        var crf396_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 396" }));
        var crf397_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 397" }));
        var crf398_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 398" }));
        var crf399_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 399" }));
        var crf400_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 400" }));
        var crf401_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 401" }));
        var crf402_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 402" }));
        var crf403_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 403" }));
        var crf404_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 404" }));
        var crf405_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 405" }));
        var crf406_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 406" }));
        var crf407_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 407" }));
        var crf408_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 408" }));
        var crf409_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 409" }));
        var crf410_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 410" }));
        var crf411_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 411" }));
        var crf412_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 412" }));
        var crf413_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 413" }));
        var crf414_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 414" }));
        var crf415_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 415" }));
        var crf416_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 416" }));
        var crf417_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 417" }));
        var crf418_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 418" }));
        var crf419_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 419" }));
        var crf420_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 420" }));
        var crf421_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 421" }));
        var crf422_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 422" }));
        var crf423_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 423" }));
        var crf424_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 424" }));
        var crf425_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 425" }));
        var crf426_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 426" }));
        var crf427_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 427" }));
        var crf428_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 428" }));
        var crf429_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 429" }));
        var crf430_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 430" }));
        var crf431_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 431" }));
        var crf432_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 432" }));
        var crf433_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 433" }));
        var crf434_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 434" }));
        var crf435_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 435" }));
        var crf436_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 436" }));
        var crf437_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 437" }));
        var crf438_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 438" }));
        var crf439_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 439" }));
        var crf440_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 440" }));
        var crf441_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 441" }));
        var crf442_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 442" }));
        var crf443_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 443" }));
        var crf444_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 444" }));
        var crf445_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 445" }));
        var crf446_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 446" }));
        var crf447_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 447" }));
        var crf448_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 448" }));
        var crf449_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 449" }));
        var crf450_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 450" }));
        var crf451_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 451" }));
        var crf452_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 452" }));
        var crf453_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 453" }));
        var crf454_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 454" }));
        var crf455_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 455" }));
        var crf456_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 456" }));
        var crf457_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 457" }));
        var crf458_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 458" }));
        var crf459_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 459" }));
        var crf460_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 460" }));
        var crf461_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 461" }));
        var crf462_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 462" }));
        var crf463_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 463" }));
        var crf464_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 464" }));
        var crf465_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 465" }));
        var crf466_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 466" }));
        var crf467_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 467" }));
        var crf468_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 468" }));
        var crf469_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 469" }));
        var crf470_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 470" }));
        var crf471_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 471" }));
        var crf472_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 472" }));
        var crf473_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 473" }));
        var crf474_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 474" }));
        var crf475_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 475" }));
        var crf476_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 476" }));
        var crf477_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 477" }));
        var crf478_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 478" }));
        var crf479_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 479" }));
        var crf480_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 480" }));
        var crf481_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 481" }));
        var crf482_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 482" }));
        var crf483_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 483" }));
        var crf484_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 484" }));
        var crf485_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 485" }));
        var crf486_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 486" }));
        var crf487_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 487" }));
        var crf488_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 488" }));
        var crf489_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 489" }));
        var crf490_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 490" }));
        var crf491_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 491" }));
        var crf492_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 492" }));
        var crf493_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 493" }));
        var crf494_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 494" }));
        var crf495_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 495" }));
        var crf496_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 496" }));
        var crf497_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 497" }));
        var crf498_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 498" }));
        var crf499_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 499" }));
        var crf500_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 500" }));
        var crf501_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 501" }));
        var crf502_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 502" }));
        var crf503_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 503" }));
        var crf504_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 504" }));
        var crf505_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 505" }));
        var crf506_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 506" }));
        var crf507_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 507" }));
        var crf508_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 508" }));
        var crf509_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 509" }));
        var crf510_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 510" }));
        var crf511_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 511" }));
        var crf512_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 512" }));
        var crf513_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 513" }));
        var crf514_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 514" }));
        var crf515_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 515" }));
        var crf516_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 516" }));
        var crf517_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 517" }));
        var crf518_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 518" }));
        var crf519_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 519" }));
        var crf520_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 520" }));
        var crf521_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 521" }));
        var crf522_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 522" }));
        var crf523_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 523" }));
        var crf524_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 524" }));
        var crf525_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 525" }));
        var crf526_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 526" }));
        var crf527_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 527" }));
        var crf528_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 528" }));
        var crf529_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 529" }));
        var crf530_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 530" }));
        var crf531_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 531" }));
        var crf532_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 532" }));
        var crf533_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 533" }));
        var crf534_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 534" }));
        var crf535_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 535" }));
        var crf536_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 536" }));
        var crf537_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 537" }));
        var crf538_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 538" }));
        var crf539_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 539" }));
        var crf540_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 540" }));
        var crf541_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 541" }));
        var crf542_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 542" }));
        var crf543_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 543" }));
        var crf544_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 544" }));
        var crf545_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 545" }));
        var crf546_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 546" }));
        var crf547_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 547" }));
        var crf548_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 548" }));
        var crf549_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 549" }));
        var crf550_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 550" }));
        var crf551_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 551" }));
        var crf552_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 552" }));
        var crf553_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 553" }));
        var crf554_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 554" }));
        var crf555_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 555" }));
        var crf556_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 556" }));
        var crf557_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 557" }));
        var crf558_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 558" }));
        var crf559_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 559" }));
        var crf560_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 560" }));
        var crf561_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 561" }));
        var crf562_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 562" }));
        var crf563_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 563" }));
        var crf564_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 564" }));
        var crf565_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 565" }));
        var crf566_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 566" }));
        var crf567_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 567" }));
        var crf568_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 568" }));
        var crf569_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 569" }));
        var crf570_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 570" }));
        var crf571_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 571" }));
        var crf572_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 572" }));
        var crf573_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 573" }));
        var crf574_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 574" }));
        var crf575_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 575" }));
        var crf576_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 576" }));
        var crf577_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 577" }));
        var crf578_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 578" }));
        var crf579_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 579" }));
        var crf580_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 580" }));
        var crf581_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 581" }));
        var crf582_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 582" }));
        var crf583_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 583" }));
        var crf584_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 584" }));
        var crf585_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 585" }));
        var crf586_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 586" }));
        var crf587_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 587" }));
        var crf588_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 588" }));
        var crf589_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 589" }));
        var crf590_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 590" }));
        var crf591_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 591" }));
        var crf592_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 592" }));
        var crf593_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 593" }));
        var crf594_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 594" }));
        var crf595_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 595" }));
        var crf596_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 596" }));
        var crf597_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 597" }));
        var crf598_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 598" }));
        var crf599_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 599" }));
        var crf600_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 600" }));
        var crf601_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 601" }));
        var crf602_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 602" }));
        var crf603_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 603" }));
        var crf604_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 604" }));
        var crf605_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 605" }));
        var crf606_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 606" }));
        var crf607_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 607" }));
        var crf608_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 608" }));
        var crf609_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 609" }));
        var crf610_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 610" }));
        var crf611_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 611" }));
        var crf612_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 612" }));
        var crf613_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 613" }));
        var crf614_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 614" }));
        var crf615_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 615" }));
        var crf616_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 616" }));
        var crf617_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 617" }));
        var crf618_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 618" }));
        var crf619_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 619" }));
        var crf620_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 620" }));
        var crf621_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 621" }));
        var crf622_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 622" }));
        var crf623_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 623" }));
        var crf624_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 624" }));
        var crf625_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 625" }));
        var crf626_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 626" }));
        var crf627_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 627" }));
        var crf628_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 628" }));
        var crf629_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 629" }));
        var crf630_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 630" }));
        var crf631_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 631" }));
        var crf632_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 632" }));
        var crf633_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 633" }));
        var crf634_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 634" }));
        var crf635_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 635" }));
        var crf636_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 636" }));
        var crf637_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 637" }));
        var crf638_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 638" }));
        var crf639_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 639" }));
        var crf640_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 640" }));
        var crf641_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 641" }));
        var crf642_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 642" }));
        var crf643_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 643" }));
        var crf644_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 644" }));
        var crf645_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 645" }));
        var crf646_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 646" }));
        var crf647_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 647" }));
        var crf648_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 648" }));
        var crf649_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 649" }));
        var crf650_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 650" }));
        var crf651_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 651" }));
        var crf652_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 652" }));
        var crf653_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 653" }));
        var crf654_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 654" }));
        var crf655_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 655" }));
        var crf656_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 656" }));
        var crf657_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 657" }));
        var crf658_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 658" }));
        var crf659_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 659" }));
        var crf660_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 660" }));
        var crf661_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 661" }));
        var crf662_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 662" }));
        var crf663_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 663" }));
        var crf664_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 664" }));
        var crf665_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 665" }));
        var crf666_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 666" }));
        var crf667_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 667" }));
        var crf668_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 668" }));
        var crf669_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 669" }));
        var crf670_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 670" }));
        var crf671_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 671" }));
        var crf672_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 672" }));
        var crf673_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 673" }));
        var crf674_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 674" }));
        var crf675_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 675" }));
        var crf676_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 676" }));
        var crf677_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 677" }));
        var crf678_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 678" }));
        var crf679_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 679" }));
        var crf680_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 680" }));
        var crf681_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 681" }));
        var crf682_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 682" }));
        var crf683_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 683" }));
        var crf684_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 684" }));
        var crf685_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 685" }));
        var crf686_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 686" }));
        var crf687_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 687" }));
        var crf688_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 688" }));
        var crf689_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 689" }));
        var crf690_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 690" }));
        var crf691_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 691" }));
        var crf692_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 692" }));
        var crf693_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 693" }));
        var crf694_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 694" }));
        var crf695_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 695" }));
        var crf696_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 696" }));
        var crf697_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 697" }));
        var crf698_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 698" }));
        var crf699_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 699" }));
        var crf700_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 700" }));
        var crf701_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 701" }));
        var crf702_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 702" }));
        var crf703_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 703" }));
        var crf704_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 704" }));
        var crf705_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 705" }));
        var crf706_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 706" }));
        var crf707_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 707" }));
        var crf708_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 708" }));
        var crf709_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 709" }));
        var crf710_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 710" }));
        var crf711_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 711" }));
        var crf712_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 712" }));
        var crf713_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 713" }));
        var crf714_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 714" }));
        var crf715_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 715" }));
        var crf716_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 716" }));
        var crf717_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 717" }));
        var crf718_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 718" }));
        var crf719_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 719" }));
        var crf720_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 720" }));
        var crf721_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 721" }));
        var crf722_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 722" }));
        var crf723_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 723" }));
        var crf724_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 724" }));
        var crf725_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 725" }));
        var crf726_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 726" }));
        var crf727_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 727" }));
        var crf728_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 728" }));
        var crf729_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 729" }));
        var crf730_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 730" }));
        var crf731_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 731" }));
        var crf732_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 732" }));
        var crf733_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 733" }));
        var crf734_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 734" }));
        var crf735_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 735" }));
        var crf736_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 736" }));
        var crf737_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 737" }));
        var crf738_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 738" }));
        var crf739_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 739" }));
        var crf740_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 740" }));
        var crf741_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 741" }));
        var crf742_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 742" }));
        var crf743_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 743" }));
        var crf744_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 744" }));
        var crf745_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 745" }));
        var crf746_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 746" }));
        var crf747_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 747" }));
        var crf748_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 748" }));
        var crf749_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 749" }));
        var crf750_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 750" }));
        var crf751_patient3_trial1 = patient3_trial1.AddChild(NamedNodeIdentifier.Create("_Base", "Crf", Guid.NewGuid(), new[] { "Patient3_Trial1", "Crf 751" }));


        return tree;
    }
}