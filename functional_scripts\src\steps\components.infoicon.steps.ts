import { ICustomWorld } from '../support/custom-world';
import { compareToBaseImage } from '../utils/compareImages';
import * as Helpers from '../support/helper-functions';
import { When, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';

Then('the infoicon has a tooltip', async function (this: ICustomWorld) {
  await expect(
    Helpers.getSamplerTabsSelectedContent(this).locator('cctc-infoicon [data-bs-toggle="tooltip"]')
  ).toHaveCount(1);
});

Then('the infoicon has no tooltip', async function (this: ICustomWorld) {
  await expect(
    Helpers.getSamplerTabsSelectedContent(this).locator('cctc-infoicon [data-bs-toggle="tooltip"]')
  ).toHaveCount(0);
});

Then('the infoicon has a popover', async function (this: ICustomWorld) {
  await expect(
    Helpers.getSamplerTabsSelectedContent(this).locator('cctc-infoicon [data-bs-toggle="popover"]')
  ).toHaveCount(1);
});

Then('the infoicon has no popover', async function (this: ICustomWorld) {
  await expect(
    Helpers.getSamplerTabsSelectedContent(this).locator('cctc-infoicon [data-bs-toggle="popover"]')
  ).toHaveCount(0);
});

When('the user clicks the infoicon component', async function (this: ICustomWorld) {
  await Helpers.getSamplerTabsSelectedContent(this).locator('cctc-infoicon').click();
});

Then(
  'the info icon button counter number is {int}',
  async function (this: ICustomWorld, iconButtonCount: number) {
    const confirmResultOutcome = Helpers.getSamplerTabsSelectedContent(this).getByText('Counter: ');
    await expect(confirmResultOutcome).toHaveText(`Counter: ${iconButtonCount}`);
  }
);

Then(
  'the infoicon component image matches the base image {string}',
  async function (this: ICustomWorld, name: string) {
    const screenshot = await Helpers.tryGetStableScreenshot(
      Helpers.getSamplerTabsSelectedContent(this).locator('cctc-infoicon')
    );
    await compareToBaseImage(this, name, screenshot);
  }
);

Then(
  'the infoicon and its attached component matches the base image {string}',
  async function (this: ICustomWorld, name: string) {
    const screenshot = await Helpers.tryGetStableScreenshot(
      Helpers.getSamplerTabsSelectedContent(this).locator('cctc-concertinaitem-content .fragment')
    );
    await compareToBaseImage(this, name, screenshot);
  }
);

Then(
  'the info icon component image is {string}',
  async function (this: ICustomWorld, infoIconImage: string) {
    await expect(
      Helpers.getSamplerTabsSelectedContent(this).locator(
        'cctc-infoicon div cctc-infoicon-image-container img'
      )
    ).toHaveAttribute('src', infoIconImage);
  }
);

Then(
  'the info icon component icon is {string}',
  async function (this: ICustomWorld, infoIcon: string) {
    await expect(
      Helpers.getSamplerTabsSelectedContent(this).locator(
        'cctc-infoicon div cctc-infoicon-image-container .material-icons'
      )
    ).toHaveText(infoIcon);
  }
);

/**
 * Verifies the height and width of the info icon component match the expected values.
 * The height and width parameters should be provided in px and rounded to 1 decimal place (e.g. "24.0px").
 * For example:
 * the info icon height is "24.0px" and width is "24.0px"
 * @param {string} infoIconHeight - The expected height in px
 * @param {string} infoIconWidth - The expected width in px
 */
Then(
  'the info icon height is {string} and width is {string}',
  async function (this: ICustomWorld, infoIconHeight: string, infoIconWidth: string) {
    const infoIconSizeLocator = Helpers.getSamplerTabsSelectedContent(this).locator(
      'cctc-infoicon cctc-infoicon-image-container'
    );

    const computedHeight = await infoIconSizeLocator.evaluate((element) => {
      return window.getComputedStyle(element).height;
    });
    const computedWidth = await infoIconSizeLocator.evaluate((element) => {
      return window.getComputedStyle(element).width;
    });

    const roundedHeight = parseFloat(computedHeight).toFixed(1) + 'px';
    const roundedWidth = parseFloat(computedWidth).toFixed(1) + 'px';

    expect(roundedHeight).toBe(infoIconHeight);
    expect(roundedWidth).toBe(infoIconWidth);
  }
);

Then(
  'the info icon has tooltip or popver in position {string}',
  async function (this: ICustomWorld, infoIconTooltipPosition: string) {
    const infoIconTooltipPositionLocator = Helpers.getSamplerTabsSelectedContent(this)
      .locator('cctc-infoicon div')
      .first();
    await expect(infoIconTooltipPositionLocator).toHaveAttribute(
      'data-bs-placement',
      infoIconTooltipPosition
    );
  }
);

Then(
  'the info icon tooltip has text {string}',
  async function (this: ICustomWorld, infoIconTooltipContent: string) {
    const infoIconTooltipStringLocator = Helpers.getSamplerTabsSelectedContent(this)
      .locator('cctc-infoicon div')
      .first();
    await expect(infoIconTooltipStringLocator).toHaveAttribute(
      'data-bs-original-title',
      infoIconTooltipContent
    );
  }
);

Then(
  'the info icon popover has text {string}',
  async function (this: ICustomWorld, infoIconPopverContent: string) {
    const infoIconPopoverStringLocator
      = Helpers.getSamplerTabsSelectedContent(this).locator('cctc-infoicon a');
    await expect(infoIconPopoverStringLocator).toHaveAttribute(
      'data-bs-content',
      infoIconPopverContent
    );
  }
);

Then(
  'the info icon popover has text {string} with the header {string}',
  async function (
    this: ICustomWorld,
    infoIconPopoverContent: string,
    popoverHeaderContent: string
  ) {
    const infoIconPopoverLocator
      = Helpers.getSamplerTabsSelectedContent(this).locator('cctc-infoicon a');
    await expect(infoIconPopoverLocator).toHaveAttribute('data-bs-content', infoIconPopoverContent);
    await expect(infoIconPopoverLocator).toHaveAttribute(
      'data-bs-original-title',
      popoverHeaderContent
    );
  }
);
