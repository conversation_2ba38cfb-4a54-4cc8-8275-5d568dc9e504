@component @confirmmodal @confirmmodal_5

Feature: the confirm modal responses and icons can be customisable
    Scenario: the confirm modal responses can be customisable
        Given the user is at the home page
        And the user selects the "Confirm modal" component in the container "Modals"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Customised positive and negative responses, Modal options classes: UI theme plus custom class defining a max width"
        When the confirm modal button is clicked
        And the confirm modal ok response is "Confirm"
        And the confirm modal cancel response is "Go back"
        Then the confirm modal ok response icon is "check_circle"
        And the confirm modal ok response icon component image matches the base image "Tick customised"
        And the confirm modal cancel response icon is "cancel"
        And the confirm modal cancel response icon component image matches the base image "Cross in Box customised"


