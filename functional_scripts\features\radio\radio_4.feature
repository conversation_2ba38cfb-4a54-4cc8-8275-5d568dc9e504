@component @radio @radio_4
Feature: the radio component handles options that have a large amount of text and can display tooltips when required
    Scenario: long radio options can wrap after three lines
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "RadioOrientation: VerticalLeft, RadioLabelOverflow: Wrap, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, with callback"
        When the user clicks on the radio option button with the associated label text "Option 3"
        And the user presses the down arrow key once
        Then the Radio component image matches the base image "radio-option-wrap"

    Scenario: long radio options can truncate on one line
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "RadioOrientation: VerticalLeft, RadioLabelOverflow: NoWrap, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, with callback"
        When the user clicks on the radio option button with the associated label text "One"
        And the user presses the down arrow key 2 times
        Then the Radio component image matches the base image "radio-option-nowrap"

    Scenario: long radio options can scroll on one line
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "RadioOrientation: VerticalLeft, RadioLabelOverflow: Scroll, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: Disabled, with callback"
        When the user clicks on the radio option button with the associated label text "One"
        And the user presses the down arrow key 2 times
        Then the radio options can scroll
        And the Radio component image matches the base image "radio-option-scroll"

    Scenario: long radio options can be displayed in full
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "RadioOrientation: VerticalLeft, RadioLabelOverflow: None, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: Disabled, with callback, input height modified via Style parameter"
        When the user clicks on the radio option button with the associated label text "Option 3"
        And the user presses the down arrow key once
        And the Radio component image matches the base image "radio-option-full"

    Scenario: radio options can have a tooltip
        Given the user is at the home page
        When the user selects the "Radio" component in the container "Input"
        Then the radio options have tooltips enabled containing the full option text

    Scenario: radio options can not have a tooltip
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "RadioOrientation: VerticalLeft, RadioLabelOverflow: Scroll, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: Disabled, with callback"
        Then the radio options do not have tooltips enabled