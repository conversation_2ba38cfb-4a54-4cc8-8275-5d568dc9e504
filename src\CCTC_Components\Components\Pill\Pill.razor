﻿@using PillColorStyle = CCTC_Lib.Enums.UI.FillStyle
@using Size = CCTC_Lib.Enums.UI.Size
@inherits CCTC_Components.Components.__CCTC.CCTCBase

<cctc-pill id="@Id" class="@CssClass" style="@Style" data-author="cctc">
    <cctc-pill-content-container class="@_contentContainerClass" style="@_contentContainerStyle" @onclick="PillClicked">
@{
    RenderFragment pillContent =
        @<Text>
        @if (Icon is not null)
        {
            <div class="pill-icon material-icons" style="@_iconStyle">
                @Icon
            </div>
        }
            <div id="@_contentWrapperId" class="pill-content" style="@_contentStyle">
                @Content
            </div>
        </Text>;

    if (PillTooltipBehaviour != PillTooltipBehaviour.Disabled)
    {
        <Tooltip
            Id="@($"{Id}-pill-tooltip")"
            Content="@Content"
            TooltipPlacement="PillTooltipPlacement"
            TooltipBehaviour="@_tooltipBehaviour">
            @pillContent
        </Tooltip>
    }
    else
    {
        @pillContent
    }
}
    </cctc-pill-content-container>
</cctc-pill>

@code {

    /// <summary>
    /// The content to display in the pill
    /// </summary>
    [Parameter, EditorRequired]
    public required string Content { get; set; }

    /// <summary>
    /// The pill context
    /// </summary>
    [Parameter]
    public PillContext? PillContext { get; set; }

    /// <summary>
    /// The pill icon
    /// </summary>
    [Parameter]
    public string? Icon { get; set; }

    /// <summary>
    /// The pill size
    /// </summary>
    [Parameter]
    public Size Size { get; set; } = Size.Medium;

    /// <summary>
    /// The maximum pill width
    /// </summary>
    [Parameter]
    public string? MaxWidth { get; set; }

    /// <summary>
    /// The pill color style
    /// </summary>
    [Parameter]
    public PillColorStyle ColorStyle { get; set; } = PillColorStyle.Fill;

    /// <summary>
    /// A callback which fires when the pill is clicked
    /// </summary>
    [Parameter]
    public EventCallback OnClick { get; set; }

    /// <summary>
    /// Configure the pill tooltip placement
    /// </summary>
    [Parameter]
    public TooltipPlacement PillTooltipPlacement { get; set; }

    /// <summary>
    /// Configure the pill tooltip behaviour
    /// </summary>
    [Parameter]
    public PillTooltipBehaviour PillTooltipBehaviour { get; set; }

    string? _contentContainerClass;
    string? _contentContainerStyle;
    string? _iconStyle;
    string? _contentStyle;
    string? _size;
    string? _contentWrapperId;
    TooltipBehaviour? _tooltipBehaviour;

    TooltipBehaviour GetTooltipBehaviour(PillTooltipBehaviour pillTooltipBehaviour, string contentWrapperSelector)
    {
        return pillTooltipBehaviour switch
        {
            PillTooltipBehaviour.EnabledOnTextOverflow => new TooltipBehaviour.EnabledOnOverflow(contentWrapperSelector),
            PillTooltipBehaviour.Enabled => new TooltipBehaviour.Enabled(),
            PillTooltipBehaviour.Disabled => new TooltipBehaviour.Disabled(),
            _ => throw new ArgumentException("case not handled", nameof(pillTooltipBehaviour))
        };
    }

    string GetSize()
    {
        return Size switch
        {
            Size.XXSmall => "0.688rem",
            Size.XSmall => "0.75rem",
            Size.Small => "1rem",
            Size.Medium => "1.25rem",
            Size.Large => "1.5rem",
            Size.XLarge => "2rem",
            Size.XXLarge => "2.75rem",
            _ => throw new ArgumentOutOfRangeException()
        };
    }

    Task PillClicked()
    {
        return OnClick.InvokeAsync();
    }

    /// <inheritdoc />
    protected override void OnInitialized()
    {
        _contentWrapperId = $"{Id}-content-wrapper";
        _tooltipBehaviour = GetTooltipBehaviour(PillTooltipBehaviour, $"#{_contentWrapperId}");
    }

    /// <inheritdoc />
    protected override void OnParametersSet()
    {
        _contentContainerClass =
            new CssBuilder()
                .AddClass($"pill-{PillContext}".ToLower(), PillContext is not null)
                .AddClass("pill-outline", ColorStyle == PillColorStyle.Outline)
                .Build();

        _contentContainerStyle =
            new StyleBuilder()
                .AddStyle("max-width", MaxWidth, MaxWidth is not null)
                .AddStyle("cursor", OnClick.HasDelegate ? "pointer" : "default")
                .Build();

        _size = GetSize();

        _iconStyle =
            new StyleBuilder()
                .AddStyle("font-size", _size)
                .Build();

        _contentStyle =
            new StyleBuilder()
                .AddStyle("font-size", _size)
                .Build();
    }
}