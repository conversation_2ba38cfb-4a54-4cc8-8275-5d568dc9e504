1 - the numeric component can receive numeric input
2 - the numeric component has the facility to redact text and / or add a placeholder
3 - the numeric component input can be constrained by preventing whitespace (with a configurable response delay) or setting a max length
4 - the numeric component input can be constrained by applying an optional number format and the mathematical rounding method can be specified
5 - the numeric component can be made read-only and / or disabled. The read-only icon is optional
