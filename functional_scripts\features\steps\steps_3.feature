@component @steps @steps_3
Feature: the header can have an updateable name or a fixed name
    <PERSON><PERSON><PERSON>: the header names are updatable
        Given the user is at the home page
        And the user selects the "Steps" component in the container "Steps"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Updateable name, without wiping data when moving back"
        And the "Job Type" step is selected
        And the "Application Submission" step is not selected
        And the user clicks on the current selected dropdown option
        When the user clicks on the dropdown option with the text "Full-time"
        And the "Job Type" step is selected
        Then the "Share your CV" step is not selected
        And the steps headers image matches the base image "share your CV visible"
        And the user clicks on the current selected dropdown option
        When the user clicks on the dropdown option with the text "Freelance"
        And the "Job Type" step is selected
        Then the "Share your Portfolio" step is not selected
        And the steps headers image matches the base image "share your porfolio visible"


    Scenario: the header names are fixed
        Given the user is at the home page
        And the user selects the "Steps" component in the container "Steps"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Required data"
        And the "Case" step is selected
        And the "Motherboard" step is not selected
        And the "CPU" step is not selected
        And the steps headers image matches the base image "fixed headers"
        And the user clicks on the current selected dropdown option
        When the user clicks on the dropdown option with the text "ASUS Prime AP201 - Black"
        And the "Case" step is selected
        Then the "Motherboard" step is not selected
        And the "CPU" step is not selected
        And the steps headers image matches the base image "fixed headers"
        And the user clicks on the current selected dropdown option
        When the user clicks on the dropdown option with the text "Fractal Design Focus 2 - White"
        And the "Case" step is selected
        Then the "Motherboard" step is not selected
        And the "CPU" step is not selected
        And the steps headers image matches the base image "fixed headers"

