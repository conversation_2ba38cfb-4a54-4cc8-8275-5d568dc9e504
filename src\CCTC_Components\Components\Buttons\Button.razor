﻿@using CCTC_Lib.Enums.UI
@inherits CCTC_Components.Components.Buttons.ButtonBase

<cctc-button data-cctc-button-type="button" id="@Id" class="@CssClass" style="@Style" data-author="cctc">
    <div class="@_buttonWrapperClass" @onclick="OnButtonClick">
    @if (!string.IsNullOrEmpty(ButtonIcon))
    {
        <div class="button-icon material-icons">@ButtonIcon</div>
    }
    @if (!string.IsNullOrEmpty(ButtonText))
    {
        <div class="button-text">@ButtonText</div>
    }
    </div>
</cctc-button>

@code {

}