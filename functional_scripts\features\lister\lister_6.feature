@component @lister @lister_6
Feature: data loading within the lister can be cancelled and restarted
    Scenario: the lister component can have its data loading cancelled and then restarted
        Given the user is at the home page
        And the user selects the "Lister" component
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "IItemsService"
        And the user clicks the cancel button in the lister
        Then the lister display shows "cancelled"
        And the lister contains 0 items
        And the lister component image matches the base image "empty cancelled lister"
        When the user clicks the refresh button in the lister
        Then the lister contains 108 items

