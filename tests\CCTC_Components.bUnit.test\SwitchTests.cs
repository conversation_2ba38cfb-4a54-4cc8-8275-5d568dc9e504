﻿using CCTC_Components.Components.Switch;
using CCTC_Lib.Enums.UI;
using Microsoft.AspNetCore.Components;

namespace CCTC_Components.bUnit.test
{
    public class SwitchTests : TestContext
    {
        [Fact]
        public void SwitchClickInvokesCallback()
        {
            var mockDummyService = new Mock<IDummyService>();
            bool initialValue = false;
            bool valueChangedNewValue = initialValue;
            ChangeEventArgs switchChangedNewValue = new() { Value = initialValue};

            var cut = RenderComponent<Switch<bool>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.ValueChanged, async args => { valueChangedNewValue = args; await mockDummyService.Object.MethodOneAsync(); })
                .Add(p => p.SwitchChanged, async args => { switchChangedNewValue = args; await mockDummyService.Object.MethodTwoAsync(); })
                .Add(p => p.Value, initialValue)
            );

            var componentWrapper = cut.Find(".component-wrapper");
            //turn on
            componentWrapper.Click();

            Assert.True(valueChangedNewValue);
            Assert.True((bool)switchChangedNewValue.Value!);

            //turn off
            componentWrapper.Click();

            Assert.False(valueChangedNewValue);
            Assert.False((bool)switchChangedNewValue.Value!);

            mockDummyService.Verify(m => m.MethodOneAsync(), Times.Exactly(2));
            mockDummyService.Verify(m => m.MethodTwoAsync(), Times.Exactly(2));
        }

        [Fact]
        public void SwitchClickWhenDisabledDoesNotInvokeCallback()
        {
            var mockDummyService = new Mock<IDummyService>();
            var cut = RenderComponent<Switch<bool>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.ValueChanged, async () => { await mockDummyService.Object.MethodOneAsync(); })
                .Add(p => p.SwitchChanged, async () => { await mockDummyService.Object.MethodTwoAsync(); })
                .Add(p => p.Disabled, true)
            );

            var componentWrapper = cut.Find(".component-wrapper");
            componentWrapper.Click();

            mockDummyService.Verify(m => m.MethodOneAsync(), Times.Never);
            mockDummyService.Verify(m => m.MethodTwoAsync(), Times.Never);
        }


        [Fact]
        public void LabelPositionClassesConfiguredCorrectly()
        {
            var cut = RenderComponent<Switch<bool>>(parameters => parameters
                .Add(p => p.Id, "test-id")
            );

            var componentWrapper = cut.Find(".component-wrapper");
            string leftPositionClass = "label-left";
            string rightPositionClass = "label-right";

            TestHelpers.AssertDoesNotHaveClass(componentWrapper, leftPositionClass);
            TestHelpers.AssertHasClass(componentWrapper, rightPositionClass);

            cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.LabelPosition, PositionX.Left)
            );

            TestHelpers.AssertDoesNotHaveClass(componentWrapper, rightPositionClass);
            TestHelpers.AssertHasClass(componentWrapper, leftPositionClass);
        }

        [Fact]
        public void InvalidTypeParameterThrowsArgumentException()
        {
            var cut = () => RenderComponent<Switch<int>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Value, 2)
            );

            var actual = Assert.Throws<ArgumentException>(cut);
            string expectedParamName = "TValue";
            string expectedMessage = $"Expected a boolean or nullable boolean type (Parameter 'TValue')";
            Assert.Equal(expectedParamName, actual.ParamName);
            Assert.Equal(expectedMessage, actual.Message);
        }

        [Fact]
        public void ValidTypeParameterDoesNotThrowArgumentException()
        {
            RenderComponent<Switch<bool>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Value, true)
            );

            RenderComponent<Switch<bool?>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Value, null)
            );
        }
    }
}
