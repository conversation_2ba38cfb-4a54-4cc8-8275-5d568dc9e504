@using BlazorComponentUtilities
@inherits CCTC_Components.Components.__CCTC.CCTCBase

<CascadingValue Value="this">

    <cctc-steps id="@Id" class="@CssClass" style="@Style" data-author="cctc">
        <div class="d-flex flex-column gap-3">
            @if (ShowProgressControlsAtTop)
            {
                @ProgressControls
            }

            <cctc-steps-headers>
                @ChildContent
            </cctc-steps-headers>
            <cctc-steps-current-content style="@_contentStyle">
                @GetCurrentContent()
            </cctc-steps-current-content>

            @if (!ShowProgressControlsAtTop)
            {
                @ProgressControls
            }
        </div>
    </cctc-steps>

</CascadingValue>

@code {

    List<ProgressStep> _children = new();

    #region parameters

    /// <summary>
    /// Populated by the related <see cref="ProgressStep"/> sub components
    /// </summary>
    [Parameter, EditorRequired]
    public required RenderFragment ChildContent { get; set; }

    /// <summary>
    /// The height of the content area
    /// </summary>
    /// <remarks>The control buttons and step markers are not included, so the total height will be this value
    /// plus any height required by those</remarks>
    [Parameter]
    public int ContentHeightPixels { get; set; } = 200;

    /// <summary>
    /// A callback invoked when a step completes. Provides the completed step.
    /// </summary>
    [Parameter, EditorRequired]
    public EventCallback<Step> OnStepCompleted { get; set; }

    /// <summary>
    /// A callback invoked when all steps are complete
    /// </summary>
    [Parameter, EditorRequired]
    public EventCallback<IEnumerable<Step>> OnProgressCompleted { get; set; }

    /// <summary>
    /// A callback invoked when the user steps backwards. Provides the step moved from.
    /// </summary>
    [Parameter]
    public EventCallback<Step> OnMovePrevious { get; set; }

    /// <summary>
    /// The radius of each step header
    /// </summary>
    /// <remarks>Note that setting this in css is not straightforward so easier to set here</remarks>
    [Parameter]
    public int CircleRadius { get; set; } = 30;

    /// <summary>
    /// The text to provide in the superscript if an item is required
    /// </summary>
    [Parameter]
    public string? RequiredText { get; set; }

    /// <summary>
    /// The text to provide in the superscript if an item is optional
    /// </summary>
    [Parameter]
    public string? OptionalText { get; set; }

    /// <summary>
    /// When true (default), wipes previous entered data
    /// </summary>
    [Parameter]
    public bool WipeOnPrevious { get; set; } = true;

    /// <summary>
    /// Shows the progress controls (previous, next) at the top when true. Otherwise
    /// they are shown below the content
    /// </summary>
    [Parameter]
    public bool ShowProgressControlsAtTop { get; set; }

    #endregion parameters

    /// <summary>
    /// Returns the children collection
    /// </summary>
    /// <returns>The list of <see cref="ProgressStep"/>s representing each step in steps</returns>
    public List<ProgressStep> GetChildren() => _children;

    Step _currentStep = Step.Empty();

    async Task CurrentItemGetFocus()
    {
        var scrollArgs = new { behavior = "smooth", block = "nearest", inline = "nearest", alignToTop = false };
        await ScrollIntoViewFromSelector($"#{Id}-step-{_currentStep.StepNum}", scrollArgs);
    }

    RenderFragment? GetCurrentContent() =>
        _children.Any() && _currentStep.StepNum >= 0
            ? _children.Find(x => x.Step.StepNum == _currentStep.StepNum)!.ChildContent
            : null;

    /// <summary>
    /// Replaces a step with an update one
    /// </summary>
    /// <param name="newStep">The new step</param>
    /// <exception cref="InvalidOperationException">Thrown if the new step num doesn't exist or the new step has the
    /// same step num as the current step</exception>
    public void ReplaceStep(Step newStep)
    {
        if (!_children.Select(x => x.Step.StepNum).Contains(newStep.StepNum))
        {
            throw new InvalidOperationException("An existing step with the same step num does not exist");
        }

        if (_currentStep.StepNum == newStep.StepNum)
        {
            throw new InvalidOperationException("You cannot replace the current step");
        }

        ClearCurrAndLaterResponses(newStep.StepNum);
        var curr = _children.Single(x => x.Step.StepNum == newStep.StepNum);
        curr.ReplaceStep(newStep);

        if (_currentStep.StepNum > newStep.StepNum)
        {
            _currentStep = curr.Step;
        }

        InvokeAsync(StateHasChanged);
    }

    /// <summary>
    /// Gets the current step number
    /// </summary>
    /// <returns>The step num of the current step</returns>
    public int GetCurrentStepNum() => _currentStep.StepNum;

    int FirstStepNum() => _children.MinBy(x => x.Step.StepNum)!.Step.StepNum;

    bool IsFirstStep(Step step) => step.StepNum == FirstStepNum();

    int LastStepNum() => _children.MaxBy(x => x.Step.StepNum)!.Step.StepNum;

    bool IsFinalStep() => GetCurrentStepNum() == LastStepNum();

    bool AllStepsComplete() => _children.All(x => x.Step.IsComplete());

    bool CanMoveNext() => _children.Any() && _currentStep.CanMoveNext();

    string GetNextText() => _children.Any() && IsFinalStep() ? "done" : "next";

    string GetNextIcon() => _children.Any() && IsFinalStep() ? "done" : "keyboard_arrow_right";

    async Task MovePrevious()
    {
        if (!CanMovePrev())
        {
            return;
        }

        await OnMovePrevious.InvokeAsync(_currentStep);

        _currentStep = _children.Single(x => x.Step.StepNum == GetCurrentStepNum() - 1).Step;
        if (WipeOnPrevious)
        {
            ClearCurrAndLaterResponses(_currentStep.StepNum);
        }

        await CurrentItemGetFocus();
        SetPrevNextCss();


        StateHasChanged();
    }

    async Task MoveNext()
    {
        if (!CanMoveNext())
        {
            return;
        }

        if (IsFinalStep())
        {
            await OnProgressCompleted.InvokeAsync(_children.Select(x => x.Step));
            return;
        }

        if (_currentStep is { Required: false, Data: null })
        {
            //complete the step if can move next but the step is not required
            await OnStepCompleted.InvokeAsync(_currentStep);
        }

        _currentStep = _children.Single(x => x.Step.StepNum == GetCurrentStepNum() + 1).Step;
        await CurrentItemGetFocus();
        SetPrevNextCss();

        StateHasChanged();
    }

    bool CanMovePrev() => _children.Any() && !IsFirstStep(_currentStep);

    /// <summary>
    /// Marks a step as completed
    /// </summary>
    /// <param name="step">The step to mark as done</param>
    /// <returns>True if the step was successfully marked as done</returns>
    /// <remarks>If data is required but is null, the marking fails and returns false</remarks>
    public bool MarkStepAsDone(Step step)
    {
        if (step is { Required: true, Data: null })
        {
            return false;
        }

        OnStepCompleted.InvokeAsync(step);
        return true;
    }

    /// <summary>
    /// Marks a step as completed and moves on immediately (without user input being required)
    /// </summary>
    /// <param name="step">The step to mark as done</param>
    /// <returns></returns>
    public async Task<bool> MarkStepAsDoneAndMoveNext(Step step)
    {
        if (MarkStepAsDone(step))
        {
            await MoveNext();
            return true;
        }

        return false;
    }

    /// <summary>
    /// Marks a step as not done
    /// </summary>
    /// <param name="step">The step to mark as not done</param>
    public void MarkStepAsNotDone(Step step)
    {
        step.Data = null;
        StateHasChanged();
    }

    void ClearStep(Step step)
    {
        step.Name = "";
        step.Value = null;
        step.Data = null;
    }

    void ClearCurrAndLaterResponses(int fromStepNumInclusive)
    {
        //on moving previous need to reset everything after and including the new step
        var laterSteps =
            _children
                .Select(x => x.Step)
                .Where(x => x.StepNum >= fromStepNumInclusive);

        foreach (var laterStep in laterSteps)
        {
            ClearStep(laterStep);
        }
    }

    void CheckStepsCollectionValid()
    {
        if (FirstStepNum() != 0)
        {
            throw new InvalidOperationException("The first step num should be 0");
        }

        foreach (var progStep in _children)
        {
            if (_children.IndexOf(progStep) != progStep.Step.StepNum)
            {
                throw new InvalidOperationException("Step nums should start and 0 and increment by one for each new step");
            }
        }
    }

    /// <summary>
    /// Registers a child progress step
    /// </summary>
    /// <param name="step">The <see cref="ProgressStep"/> to register</param>
    /// <exception cref="ArgumentException">Thrown if the step is invalid</exception>
    /// <exception cref="InvalidOperationException">Thrown if the collection of steps has become invalid</exception>
    public void Register(ProgressStep step)
    {
        if (step.Step.StepNum < 0)
        {
            throw new ArgumentException("A step num must be greater than 0", nameof(step.Step.StepNum));
        }

        var existingStep = _children.SingleOrDefault(x => x.Step.StepNum == step.Step.StepNum);
        if (existingStep is not null)
        {
            _children.Remove(existingStep);
        }

        _children.Add(step);

        if (_children.Count == 1)
        {
            _currentStep = _children[0].Step;
        }

        CheckStepsCollectionValid();
        SetPrevNextCss();

        StateHasChanged();
    }

    string? _contentStyle;
    string? _prevButtonCss;
    string? _nextButtonCss;

    void SetPrevNextCss()
    {
        var canMovePrev = CanMovePrev();
        _prevButtonCss =
            new CssBuilder()
                .AddClass("button-enabled", canMovePrev)
                .AddClass("button-disabled", !canMovePrev)
                .AddClass("prev-control", canMovePrev)
                .AddClass("prev-control-disabled", !canMovePrev)
                .Build();

        var canMoveNext = CanMoveNext();
        _nextButtonCss =
            new CssBuilder()
                .AddClass("button-enabled", canMoveNext)
                .AddClass("button-disabled", !canMoveNext)
                .AddClass("next-control", canMoveNext)
                .AddClass("next-control-disabled", !canMoveNext)
                .Build();
    }

    /// <inheritdoc />
    protected override void OnParametersSet()
    {
        _contentStyle =
            new StyleBuilder()
                .AddStyle("height", $"{ContentHeightPixels}px")
                .Build();

        SetPrevNextCss();
    }

    RenderFragment ProgressControls =>
        @<cctc-steps-controls>
            <div class="controls">
                <div @onclick="MovePrevious" class="prev-next-control prev-control @_prevButtonCss">
                    <div class="material-icons button">keyboard_arrow_left</div>
                    <h5 class="mx-2 mb-1">previous</h5>
                </div>
                <div @onclick="MoveNext" class="prev-next-control next-control @_nextButtonCss">
                    <h5 class="mx-2 mb-1">@GetNextText()</h5>
                    <div class="material-icons button">@GetNextIcon()</div>
    </div>
    </div>
    </cctc-steps-controls>;
}