@component @steps @steps_1
Feature: the step can be marked as required or not
    <PERSON>enario: the dropdown component sample page is available
        Given the user is at the home page
        When the user selects the "Steps" component in the container "Steps"
        Then the url ending is "stepssample"

    Scenario: the step can be marked as optional and the user can complete without this step
        Given the user is at the home page
        And the user selects the "Steps" component in the container "Steps"
        And the "Case" step is selected
        And the "Motherboard" step is not selected
        And the previous button is disabled
        And the next button is enabled
        When the user clicks on the next button
        Then the previous button is enabled
        And the next button is disabled
        And the "Case" step is not selected
        And the "Motherboard" step is selected


    Scena<PERSON>: the step can be marked as required and the user cannot complete without this step
        Given the user is at the home page
        And the user selects the "Steps" component in the container "Steps"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Required data"
        And the "Case" step is selected
        And the "Motherboard" step is not selected
        And the user clicks on the next button 
        And the "Case" step is not selected
        And the "Motherboard" step is selected
        And the "CPU" step is not selected
        And the next button is disabled
        When the user clicks on the current selected dropdown option 
        And the user clicks on the dropdown option with the text "MSI PRO B760M-A WIFI DDR4"
        And the user clicks on the next button
        Then the "Case" step is not selected
        And the "Motherboard" step is not selected
        And the "CPU" step is selected

