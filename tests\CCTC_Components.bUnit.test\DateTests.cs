﻿using AngleSharp.Html.Dom;
using CCTC_Components.Components.Temporal;
using CCTC_Components.Components.TextBox;
using Microsoft.AspNetCore.Components;

namespace CCTC_Components.bUnit.test
{
    public class DateTests : CCTCComponentsTestContext
    {
        [Theory]
        [MemberData(nameof(GetDateCallbackTestData))]
        public async Task Date_Raises_Correct_Callbacks(bool allowClear, string inputValue, bool invocationExpected, DateOnly? expectedValue = null)
        {
            var testSchedulers = new TestSchedulers();
            Services.AddTestSchedulers(sp => testSchedulers);
            AddJSAlert();

            int componentDefaultThrottleMs = 500;
            string dateFormat = "yyyy-MM-dd";
            var mockDummyService = new Mock<IDummyService>();

            var cut = RenderComponent<Date<DateOnly?>>(parameters => parameters
                .Add(p => p.DateFormat, dateFormat)
                .Add(p => p.ValueChanged, async args => await mockDummyService.Object.MethodSixAsync(args))
                .Add(p => p.DateChanged, async args => await mockDummyService.Object.MethodSevenAsync(args))
                .Add(p => p.MinDate, new DateOnly(2022, 5, 1))
                .Add(p => p.MaxDate, new DateOnly(2022, 6, 1))
                .Add(p => p.ThrottleMs, componentDefaultThrottleMs)
                .Add(p => p.AllowClear, allowClear)
            );

            var textComponent = cut.FindComponent<Text>();
            await textComponent.InvokeAsync(() =>
            {
                textComponent.Find("input").Input(inputValue);
                testSchedulers.Default.AdvanceBy(TimeSpan.FromMilliseconds(componentDefaultThrottleMs).Ticks);
            });

            var dateInput = cut.Find("input.date-input");
            dateInput.Change(inputValue);

            var expectedValueString = expectedValue?.ToString(dateFormat);
            mockDummyService.Verify(m => m.MethodSixAsync(expectedValue), invocationExpected ? Times.Exactly(2) : Times.Never());
            mockDummyService.Verify(m => m.MethodSevenAsync(It.Is<ChangeEventArgs>(args => (string?)args.Value == expectedValueString)), invocationExpected ? Times.Exactly(2) : Times.Never());
        }

        [Theory]
        [InlineData(false, "2023-02-04", "yyyy-MM-dd", "material-icons.check", 1, FeedbackIcon.Both)]
        [InlineData(false, "2023-02-04", "yyyy-MM-dd", "material-icons.check", 1, FeedbackIcon.Valid)]
        [InlineData(false, "2023-02-04", "yyyy-MM-dd", "material-icons.check", 0, FeedbackIcon.None)]
        [InlineData(false, "2023-02-04", "yyyy-MM-dd", "material-icons.check", 0, null)]
        [InlineData(false, "2023-02-03", "yyyy-MM-dd", "material-icons.priority_high", 1, FeedbackIcon.Both)]
        [InlineData(false, "2023-03-11", "yyyy-MM-dd", "material-icons.priority_high", 1, FeedbackIcon.Error)]
        [InlineData(false, "2023-03-11", "yyyy-MM-dd", "material-icons.priority_high", 0, FeedbackIcon.None)]
        [InlineData(false, "2023", "yyyy-MM-dd", "material-icons.priority_high", 1, null)]
        [InlineData(false, "2023-02-04", "dd/MM/yyyy", "material-icons.priority_high", 1, FeedbackIcon.Both)]
        [InlineData(false, "2023-02-04", "dd/MM/yyyy", "material-icons.priority_high", 1, FeedbackIcon.Error)]
        [InlineData(false, "2023-02-04", "dd/MM/yyyy", "material-icons.priority_high", 0, FeedbackIcon.None)]
        [InlineData(false, "", null, "material-icons.check", 0, FeedbackIcon.Both)]
        [InlineData(false, "", null, "material-icons.check", 0, FeedbackIcon.Valid)]
        [InlineData(false, "", null, "material-icons.check", 0, FeedbackIcon.None)]
        [InlineData(true, "", null, "material-icons.check", 0, FeedbackIcon.Both)]
        [InlineData(true, "", null, "material-icons.check", 0, FeedbackIcon.Valid)]
        [InlineData(true, "", null, "material-icons.check", 0, FeedbackIcon.None)]
        [InlineData(false, "", null, "material-icons.priority_high", 1, FeedbackIcon.Both)]
        [InlineData(false, "", null, "material-icons.priority_high", 0, FeedbackIcon.Valid)]
        [InlineData(false, "", null, "material-icons.priority_high", 0, FeedbackIcon.None)]
        [InlineData(true, "", null, "material-icons.priority_high", 0, FeedbackIcon.Both)]
        [InlineData(true, "", null, "material-icons.priority_high", 0, FeedbackIcon.Valid)]
        [InlineData(true, "", null, "material-icons.priority_high", 0, FeedbackIcon.None)]
        public async Task Date_Text_Displays_Correct_Validation_Icon(bool allowClear, string inputValue, string? dateFormat, string? expectedCssClass,
            int cssClassCount, FeedbackIcon? feedbackIcon)
        {
            var testSchedulers = new TestSchedulers();
            Services.AddTestSchedulers(sp => testSchedulers);

            int componentDefaultThrottleMs = 500;

            var cut = RenderComponent<Date<DateOnly?>>(parameters => parameters
                .Add(p => p.DateFormat, dateFormat)
                .Add(p => p.Value, new DateOnly(2021, 12, 1))
                .Add(p => p.MinDate, new DateOnly(2023, 2, 4))
                .Add(p => p.MaxDate, new DateOnly(2023, 3, 10))
                .Add(p => p.FeedbackIcon, feedbackIcon)
                .Add(p => p.AllowClear, allowClear)
            );

            var textComponent = cut.FindComponent<Text>();
            await textComponent.InvokeAsync(() =>
            {
                textComponent.Find("input").Input(inputValue);
                testSchedulers.Default.AdvanceBy(TimeSpan.FromMilliseconds(componentDefaultThrottleMs).Ticks);
            });

            var validationIcon = cut.FindAll($"i.{expectedCssClass}");
            Assert.Equal(cssClassCount, validationIcon.Count);
        }

        [Fact]
        public void DateActiveAttributesConfiguredCorrectly()
        {
            Services.AddTestSchedulers();

            var cut = RenderComponent<Date<DateOnly>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.CssClass, "w-50")
                .Add(p => p.Style, "color: green;")
                .Add(p => p.ReadOnly, false)
                .Add(p => p.Disabled, false)
                .Add(p => p.MinDate, new DateOnly(2022, 04, 01))
                .Add(p => p.MaxDate, new DateOnly(2022, 05, 01))
            );

            var dateWrapperElement = cut.Find("cctc-input[data-cctc-input-type=\"date\"]");
            var inputElement = (IHtmlInputElement)cut.FindAll("input.date-input")[0];

            var expectedDateWrapperAttributes = new Dictionary<string, string?>()
            {
                { "id", "test-id" },
                { "class", "w-50" },
                { "style", "color: green;" },
                { "data-author", "cctc" }
            };

            var actualDateWrapperAttributes = new Dictionary<string, string?>()
            {
                { "id", dateWrapperElement.Id },
                { "class", dateWrapperElement.ClassName },
                { "style", dateWrapperElement.GetAttribute("style") },
                { "data-author", dateWrapperElement.GetAttribute("data-author") }
            };

            var expectedInputAttributes = new Dictionary<string, string?>()
            {
                { "id", "test-id-date-picker" },
                { "min", "2022-04-01" },
                { "max", "2022-05-01" }
            };

            var actualInputAttributes = new Dictionary<string, string?>()
            {
                { "id", inputElement.Id },
                { "min", inputElement.Minimum },
                { "max", inputElement.Maximum }
            };

            Assert.Equal(expectedDateWrapperAttributes, actualDateWrapperAttributes);
            Assert.Equal(expectedInputAttributes, actualInputAttributes);
        }

        [Theory]
        [InlineData(true, true)]
        [InlineData(true, false)]
        [InlineData(false, true)]
        public void DateInactiveAttributesConfiguredCorrectly(bool disabled, bool readOnly)
        {
            Services.AddTestSchedulers();

            var cut = RenderComponent<Date<DateOnly>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.CssClass, "w-50")
                .Add(p => p.Style, "color: green;")
                .Add(p => p.ReadOnly, readOnly)
                .Add(p => p.Disabled, disabled)
            );

            var dateWrapperElement = cut.Find("cctc-input[data-cctc-input-type=\"date\"]");
            var inputElements = cut.FindAll("input.date-input");

            var expectedDateWrapperAttributes = new Dictionary<string, string?>()
            {
                { "id", "test-id" },
                { "class", "w-50" },
                { "style", "color: green;" },
                { "data-author", "cctc" }
            };

            var actualDateWrapperAttributes = new Dictionary<string, string?>()
            {
                { "id", dateWrapperElement.Id },
                { "class", dateWrapperElement.ClassName },
                { "style", dateWrapperElement.GetAttribute("style") },
                { "data-author", dateWrapperElement.GetAttribute("data-author") }
            };

            Assert.Equal(expectedDateWrapperAttributes, actualDateWrapperAttributes);
            Assert.Equal(0, inputElements.Count);
        }

        [Fact]
        public void InvalidTypeParameterThrowsArgumentException()
        {
            Services.AddTestSchedulers();

            var cut = () => RenderComponent<Date<int>>(parameters => parameters
                .Add(p => p.Id, "test-id")
            );

            var actual = Assert.Throws<ArgumentException>(cut);
            string expectedParamName = "TValue";
            string expectedMessage = $"Expected a dateonly or nullable dateonly type (Parameter 'TValue')";
            Assert.Equal(expectedParamName, actual.ParamName);
            Assert.Equal(expectedMessage, actual.Message);
        }

        [Fact]
        public void AllowClearWithNonNullableValueTypeThrowsArgumentException()
        {
            Services.AddTestSchedulers();

            var cut = () => RenderComponent<Date<DateOnly>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.AllowClear, true)
            );

            var actual = Assert.Throws<ArgumentException>(cut);
            string expectedParamName = "AllowClear";
            string expectedMessage = $"AllowClear should only be set to true when TValue is of type nullable dateonly (Parameter 'AllowClear')";
            Assert.Equal(expectedParamName, actual.ParamName);
            Assert.Equal(expectedMessage, actual.Message);
        }

        [Fact]
        public void AllowClearWithNullableValueTypeDoesNotThrowArgumentException()
        {
            Services.AddTestSchedulers();

            var cut = RenderComponent<Date<DateOnly?>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.AllowClear, true)
            );
        }

        [Fact]
        public void DatePickerClearShowsAlertWhenAllowClearFalse()
        {
            Services.AddTestSchedulers();
            var jsAlertHandler = AddJSAlert();

            var cut = RenderComponent<Date<DateOnly?>>(parameters => parameters
                .Add(p => p.AllowClear, false)
            );

            var dateInput = cut.Find("input.date-input");
            dateInput.Change(string.Empty);

            jsAlertHandler.VerifyInvoke("alert");
            Assert.Equal("The date is not permitted to be cleared", jsAlertHandler.Invocations.Single().Arguments.First());
        }

        /// <summary>
        /// Generates test data for callbacks triggered by changing date input
        /// </summary>
        /// <returns>An IEnumerable of object arrays, one array for each test, comprising of allowClear, inputValue, invocationExpected, expectedValue (optional)</returns>
        public static IEnumerable<object?[]> GetDateCallbackTestData()
        {
            yield return new object?[] { false, "2022-05-01", true, new DateOnly(2022, 5, 1) };
            yield return new object?[] { false, "not a date", false };
            yield return new object?[] { false, "2022-04-30", false };
            yield return new object?[] { false, "2022-06-02", false };
            yield return new object?[] { false, "", false };
            yield return new object?[] { true, "", true, null };
        }
    }
}
