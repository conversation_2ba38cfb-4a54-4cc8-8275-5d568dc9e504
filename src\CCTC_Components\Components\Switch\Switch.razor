﻿@using CCTC_Lib.Enums.UI
@inherits CCTC_Components.Components.__CCTC.CCTCBase
@typeparam TValue

<cctc-switch id="@Id" class="@CssClass" style="@Style" data-author="cctc">
    <div class="@_componentWrapperClass" @onclick="OnValueChanged">
        <div id="@Id-switch" role="switch" class="switch">
            <span class="slider"></span>
        </div>
    @if (!string.IsNullOrWhiteSpace(Label))
    {
        <label for="@Id-switch">@Label</label>
    }
    </div>
</cctc-switch>

@code {

    /// <summary>
    /// The switch value
    /// </summary>
    [Parameter]
    public TValue? Value { get; set; }

    /// <summary>
    /// A callback which fires when the switch value changes
    /// </summary>
    [Parameter]
    public EventCallback<TValue> ValueChanged { get; set; }

    /// <summary>
    /// A second callback which fires when the switch value changes. Useful when consuming using @bind-Value
    /// </summary>
    [Parameter]
    public EventCallback<ChangeEventArgs> SwitchChanged { get; set; }

    /// <summary>
    /// The switch label
    /// </summary>
    [Parameter]
    public string? Label { get; set; }

    /// <summary>
    /// The switch label position
    /// </summary>
    [Parameter]
    public PositionX LabelPosition { get; set; } = PositionX.Right;

    /// <summary>
    /// Disabled if true
    /// </summary>
    [Parameter]
    public bool Disabled { get; set; }

    bool _checked;
    string? _componentWrapperClass;

    async Task OnValueChanged()
    {
        if (!Disabled)
        {
            _checked = !_checked;
            await ValueChanged.InvokeAsync((TValue)(object)_checked);
            await SwitchChanged.InvokeAsync(new ChangeEventArgs { Value = _checked });
        }
    }

    ///<inheritdoc />
    protected override void OnInitialized()
    {
        if (typeof(TValue) != typeof(bool) && !typeof(bool).IsAssignableFrom(Nullable.GetUnderlyingType(typeof(TValue))))
        {
            throw new ArgumentException("Expected a boolean or nullable boolean type", nameof(TValue));
        }
    }

    ///<inheritdoc />
    protected override void OnParametersSet()
    {
        _checked = object.Equals(Value, true);
        _componentWrapperClass =
            new CssBuilder("component-wrapper")
                .AddClass("checked", _checked)
                .AddClass("label-left", LabelPosition == PositionX.Left)
                .AddClass("label-right", LabelPosition == PositionX.Right)
                .AddClass("disabled", Disabled)
                .Build();
    }
}
