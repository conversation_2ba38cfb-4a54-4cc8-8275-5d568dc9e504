.preview-banner {
    pointer-events: none;
    position: fixed;
    top: 2px;
    left: 1px;
    opacity: 0.6;
    z-index: 10;
}

.page {
    color: var(--cctc-color);
    background-color: var(--cctc-background-color);
}

.header {
    grid-area: header;
    padding: 0.5rem 1rem 0.5rem 0.5rem;
    background-color: var(--cctc-highlight-background-color);
    border-bottom: 1px solid var(--cctc-highlight-background-color);
    display: flex;
    align-items: center;
}

.header-section {
    margin-left: 1rem;
}

.component-package-version {
    margin-left: 2px;
}

.github-logo {
    height: 1.5rem;
    margin-left: 0.5rem
}

.github-logo:hover {
    cursor: pointer;
}

.menu {
    grid-area: menu;
    border-right: 1px solid var(--cctc-highlight-background-color);
}

.main {
    grid-area: main;
    overflow-x: auto;
    overflow-y: auto;
    scrollbar-gutter: stable;
    padding: 0.4rem 0.2rem 0.4rem 0.2rem;
}

.grid {
    gap: 0.1rem;
    height: 100dvh;
}

/*mobile*/
.grid-container-collapsed {
    display: grid;
    grid-template-areas:
        'header'
        'main';
    grid-template-columns: 100%;
    grid-template-rows: [header] 5% [main] 95%;
}

/*mobile*/
.grid-container {
    display: grid;
    grid-template-areas:
        'header'
        'menu';
    grid-template-columns: 100%;
    grid-template-rows: [header] 5% [menu] 95%;
}

.grid-container ::deep .nav-container {
    overflow-y: auto;
    margin-right: 0;
}

.grid-container .menu {
    border: none;
}

.grid-container-tablet-collapsed {
    display: grid;
    grid-template-areas:
        'header header header header header header'
        'menu main main main main main';
    grid-template-columns: [menu] 60px [main] auto;
    grid-template-rows: [header] 5% [main] 95%;
}

.grid-container-tablet {
    display: grid;
    grid-template-areas:
        'header header header header header header'
        'menu main main main main main';
    grid-template-columns: [menu] 120px [main] auto;
    grid-template-rows: [header] 5% [main] 95%;
}

.grid-container-desktop {
    display: grid;
    grid-template-areas:
        'header header header header header header'
        'menu main main main main main';
    grid-template-columns: [menu] 15% [main] auto;
    grid-template-rows: [header] 5% [main] auto;
}

.grid-container-desktop-collapsed {
    display: grid;
    grid-template-areas:
        'header header header header header header'
        'menu main main main main main';
    grid-template-columns: [menu] 60px [main] auto;
    grid-template-rows: [header] 5% [main] auto;
}

.show-menu-button {
    visibility: visible;
}

.hide-menu-button {
    visibility: hidden;
}

.main-desktop {
    padding: 1.4rem 1.4rem 1.4rem 1.2rem;
}

::deep #theme-selector {
    --cctc-input-height: 1.3rem;
    width: 4.7rem;
}

.toggler-icon-wrapper {
    display: flex;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    padding: 0;
}

.toggler-icon {
    display: block;
    background: var(--cctc-icon-color);
    height: 2px;
    width: 18px;
    border-radius: 1px;
    transition: opacity .25s ease-in-out, transform .25s ease-in-out;
}

.grid-container.collapsed .top-bar {
    transform: rotate(0deg);
}

.grid-container.collapsed .middle-bar {
    opacity: 1;
}

.grid-container.collapsed .bottom-bar {
    transform: rotate(0deg);
}

.grid-container .top-bar {
    transform: translate(0px, 6px) rotate(45deg);
}

.grid-container .middle-bar {
    opacity: 0;
}

.grid-container .bottom-bar {
    transform: translate(0px, -6px) rotate(-45deg);
}
