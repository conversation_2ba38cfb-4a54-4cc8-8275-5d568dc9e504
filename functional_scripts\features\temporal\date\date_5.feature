@component @temporal @date @date_5
Feature: the date component has a placeholder which matches the date format
    Scenario: the date component has a placeholder which matches the date format
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: yyyy-MM-dd, null initial value"
        Then the Date component has the placeholder "yyyy-MM-dd"
        And the Date component image matches the base image "date-placeholder"