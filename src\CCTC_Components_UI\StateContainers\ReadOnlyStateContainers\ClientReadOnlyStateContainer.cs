﻿using CCTC_Components_UI.Services;
using CCTC_Lib.Contracts.UI;

namespace CCTC_Components_UI.StateContainers.ReadOnlyStateContainers
{
    //no state container notify state changed event required (properties in this class are only set once)
    public class ClientReadOnlyStateContainer : IAsyncDisposable, IBrowserNameProvider
    {
        readonly IBrowserService _browserService;

        public ClientReadOnlyStateContainer(IBrowserService browserService)
        {
            _browserService = browserService;
            BrowserName = string.Empty;
            Init();
        }

        public string BrowserName { get; private set; }

        async void Init()
        {
            await _browserService.LoadJSModule();
            var browserName = await _browserService.GetBrowserName();
            BrowserName = browserName ?? string.Empty;
        }

        public async ValueTask DisposeAsync()
        {
            await _browserService.DisposeAsync();
        }
    }
}
