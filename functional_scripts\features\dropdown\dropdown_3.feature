@component @dropdown @dropdown_3
Feature: the dropdown component handles options that have a large amount of text and can display tooltips when required
    Scenario: the current selected option can be truncated
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        When the user clicks on the current selected dropdown option
        And the user moves the mouse relative to the main viewport with an x coordinate of 0 and y coordinate of 0
        And the dropdown is expanded
        And the user presses the down arrow key 5 times
        And the user presses the enter key once
        Then the Dropdown component image matches the base image "dropdown-truncated"

    Scenario: long dropdown options in the listbox can wrap after three lines
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Dropdown type: Person record, DropDownOptionOverflow: Wrap, TooltipPlacement: Right, DropDownTooltipBehaviour: EnabledOnOptionOverflow"
        When the user clicks on the current selected dropdown option
        And the user moves the mouse relative to the main viewport with an x coordinate of 0 and y coordinate of 0
        And the dropdown is expanded
        And the user presses the up arrow key once
        Then the Dropdown component listbox image matches the base image "dropdown-option-wrap"

    Scenario: long dropdown options in the listbox can truncate on one line
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Dropdown type: Person record, DropDownOptionOverflow: NoWrap, TooltipPlacement: Right, DropDownTooltipBehaviour: EnabledOnOptionOverflow"
        When the user clicks on the current selected dropdown option
        And the user moves the mouse relative to the main viewport with an x coordinate of 0 and y coordinate of 0
        And the dropdown is expanded
        And the user presses the up arrow key once
        Then the Dropdown component listbox image matches the base image "dropdown-option-nowrap"

    Scenario: long dropdown options in the listbox can scroll on one line
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Dropdown type: Person record, DropDownOptionOverflow: Scroll, DropDownTooltipBehaviour: Disabled"
        When the user clicks on the current selected dropdown option
        And the user moves the mouse relative to the main viewport with an x coordinate of 0 and y coordinate of 0
        And the dropdown is expanded
        And the user presses the up arrow key once
        Then the dropdown options can scroll
        And the Dropdown component listbox image matches the base image "dropdown-option-scroll"

    Scenario: long dropdown options in the listbox be displayed in full
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Dropdown type: Person record, DropDownOptionOverflow: None, TooltipPlacement: Right, DropDownTooltipBehaviour: EnabledOnOptionOverflow"
        When the user clicks on the current selected dropdown option
        And the user moves the mouse relative to the main viewport with an x coordinate of 0 and y coordinate of 0
        And the dropdown is expanded
        And the user presses the up arrow key once
        Then the Dropdown component listbox image matches the base image "dropdown-option-full"

    Scenario: the current selected option can have a tooltip
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        When the user clicks on the current selected dropdown option
        And the dropdown is expanded
        And the user presses the down arrow key 5 times
        And the user presses the enter key once
        Then the current selected dropdown option has a tooltip enabled containing the full option text

    Scenario: the current selected option can not have a tooltip
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Dropdown type: Person record, DropDownOptionOverflow: Scroll, DropDownTooltipBehaviour: Disabled"
        Then the current selected dropdown option does not have a tooltip enabled

    Scenario: dropdown options can have a tooltip
        Given the user is at the home page
        When the user selects the "Dropdown" component in the container "Input"
        Then the dropdown options have tooltips enabled containing the full option text

    Scenario: dropdown options can not have a tooltip
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Dropdown type: Person record, DropDownOptionOverflow: Scroll, DropDownTooltipBehaviour: Disabled"
        Then the dropdown options do not have tooltips enabled
