RESTRICTION: == net8.0
NUGET
  remote: https://api.nuget.org/v3/index.json
    AngleSharp (1.1.2)
      System.Text.Encoding.CodePages (>= 8.0)
    AngleSharp.Css (1.0.0-beta.151)
      AngleSharp (>= 1.0 < 2.0)
    AngleSharp.Diffing (1.0)
      AngleSharp (>= 1.1.2)
      AngleSharp.Css (>= 1.0.0-beta.144)
    BlazorComponentUtilities (1.8)
    Blazored.Modal (7.3.1)
      Microsoft.AspNetCore.Components (>= 8.0.2)
      Microsoft.AspNetCore.Components.Web (>= 8.0.2)
      Microsoft.Extensions.DependencyInjection.Abstractions (>= 8.0)
      Microsoft.JSInterop.WebAssembly (>= 8.0.2)
    BlazorPro.Spinkit (1.2)
      BlazorComponentUtilities (>= 1.6)
      Microsoft.AspNetCore.Components (>= 3.1.2)
      Microsoft.AspNetCore.Components.Web (>= 3.1.2)
    BouncyCastle.Cryptography (2.5.1)
    bunit (1.27.17)
      bunit.core (>= 1.27.17)
      bunit.web (>= 1.27.17)
    bunit.core (1.27.17)
      Microsoft.AspNetCore.Components (>= 8.0.2)
      Microsoft.Extensions.Logging (>= 8.0)
      Microsoft.Extensions.Logging.Abstractions (>= 8.0)
    bunit.web (1.27.17)
      AngleSharp (>= 1.1)
      AngleSharp.Css (>= 1.0.0-beta.139)
      AngleSharp.Diffing (>= 0.18.2)
      bunit.core (>= 1.27.17)
      Microsoft.AspNetCore.Components.Authorization (>= 8.0.2)
      Microsoft.AspNetCore.Components.Web (>= 8.0.2)
      Microsoft.AspNetCore.Components.WebAssembly (>= 8.0.2)
      Microsoft.AspNetCore.Components.WebAssembly.Authentication (>= 8.0.2)
      Microsoft.Extensions.Caching.Memory (>= 8.0)
      Microsoft.Extensions.Localization.Abstractions (>= 8.0.2)
    Castle.Core (5.1.1)
      System.Diagnostics.EventLog (>= 6.0)
    Expecto (10.2.2)
      FSharp.Core (>= 7.0.200)
      Mono.Cecil (>= 0.11.4 < 1.0)
    FSharp.Core (9.0.201)
    FSharp.Data (6.6)
      FSharp.Core (>= 6.0.1)
      FSharp.Data.Csv.Core (>= 6.6)
      FSharp.Data.Html.Core (>= 6.6)
      FSharp.Data.Http (>= 6.6)
      FSharp.Data.Json.Core (>= 6.6)
      FSharp.Data.Runtime.Utilities (>= 6.6)
      FSharp.Data.WorldBank.Core (>= 6.6)
      FSharp.Data.Xml.Core (>= 6.6)
    FSharp.Data.Csv.Core (6.6)
      FSharp.Core (>= 6.0.1)
      FSharp.Data.Runtime.Utilities (>= 6.6)
    FSharp.Data.Html.Core (6.6)
      FSharp.Core (>= 6.0.1)
      FSharp.Data.Csv.Core (>= 6.6)
      FSharp.Data.Runtime.Utilities (>= 6.6)
    FSharp.Data.Http (6.6)
      FSharp.Core (>= 6.0.1)
    FSharp.Data.Json.Core (6.6)
      FSharp.Core (>= 6.0.1)
      FSharp.Data.Http (>= 6.6)
      FSharp.Data.Runtime.Utilities (>= 6.6)
    FSharp.Data.Runtime.Utilities (6.6)
      FSharp.Core (>= 6.0.1)
      FSharp.Data.Http (>= 6.6)
    FSharp.Data.WorldBank.Core (6.6)
      FSharp.Core (>= 6.0.1)
      FSharp.Data.Http (>= 6.6)
      FSharp.Data.Json.Core (>= 6.6)
      FSharp.Data.Runtime.Utilities (>= 6.6)
    FSharp.Data.Xml.Core (6.6)
      FSharp.Core (>= 6.0.1)
      FSharp.Data.Http (>= 6.6)
      FSharp.Data.Json.Core (>= 6.6)
      FSharp.Data.Runtime.Utilities (>= 6.6)
    FSharp.SystemTextJson (1.3.13)
      FSharp.Core (>= 4.7)
      System.Text.Json (>= 6.0)
    FsToolkit.ErrorHandling (4.18)
      FSharp.Core (>= 7.0.300)
    FsToolkit.ErrorHandling.TaskResult (4.18)
      FsToolkit.ErrorHandling (>= 4.18)
    MailKit (4.11)
      MimeKit (>= 4.11)
      System.Formats.Asn1 (>= 8.0.1)
    Microsoft.AspNetCore.Authorization (8.0.4)
      Microsoft.AspNetCore.Metadata (>= 8.0.4)
      Microsoft.Extensions.Logging.Abstractions (>= 8.0.1)
      Microsoft.Extensions.Options (>= 8.0.2)
    Microsoft.AspNetCore.Components (8.0.4)
      Microsoft.AspNetCore.Authorization (>= 8.0.4)
      Microsoft.AspNetCore.Components.Analyzers (>= 8.0.4)
    Microsoft.AspNetCore.Components.Analyzers (8.0.4)
    Microsoft.AspNetCore.Components.Authorization (8.0.4)
      Microsoft.AspNetCore.Authorization (>= 8.0.4)
      Microsoft.AspNetCore.Components (>= 8.0.4)
    Microsoft.AspNetCore.Components.Forms (8.0.4)
      Microsoft.AspNetCore.Components (>= 8.0.4)
    Microsoft.AspNetCore.Components.Web (8.0.4)
      Microsoft.AspNetCore.Components (>= 8.0.4)
      Microsoft.AspNetCore.Components.Forms (>= 8.0.4)
      Microsoft.Extensions.DependencyInjection (>= 8.0)
      Microsoft.Extensions.Primitives (>= 8.0)
      Microsoft.JSInterop (>= 8.0.4)
      System.IO.Pipelines (>= 8.0)
    Microsoft.AspNetCore.Components.WebAssembly (8.0.4)
      Microsoft.AspNetCore.Components.Web (>= 8.0.4)
      Microsoft.Extensions.Configuration.Binder (>= 8.0.1)
      Microsoft.Extensions.Configuration.Json (>= 8.0)
      Microsoft.Extensions.Logging (>= 8.0)
      Microsoft.JSInterop.WebAssembly (>= 8.0.4)
    Microsoft.AspNetCore.Components.WebAssembly.Authentication (8.0.4)
      Microsoft.AspNetCore.Components.Authorization (>= 8.0.4)
      Microsoft.AspNetCore.Components.Web (>= 8.0.4)
    Microsoft.AspNetCore.Components.WebAssembly.DevServer (8.0.4)
    Microsoft.AspNetCore.Metadata (8.0.4)
    Microsoft.Bcl.AsyncInterfaces (9.0.3)
    Microsoft.Bcl.Cryptography (9.0.3)
      System.Formats.Asn1 (>= 9.0.3)
    Microsoft.CodeCoverage (17.9)
    Microsoft.Extensions.Caching.Abstractions (8.0)
      Microsoft.Extensions.Primitives (>= 8.0)
    Microsoft.Extensions.Caching.Memory (8.0)
      Microsoft.Extensions.Caching.Abstractions (>= 8.0)
      Microsoft.Extensions.DependencyInjection.Abstractions (>= 8.0)
      Microsoft.Extensions.Logging.Abstractions (>= 8.0)
      Microsoft.Extensions.Options (>= 8.0)
      Microsoft.Extensions.Primitives (>= 8.0)
    Microsoft.Extensions.Configuration (8.0)
      Microsoft.Extensions.Configuration.Abstractions (>= 8.0)
      Microsoft.Extensions.Primitives (>= 8.0)
    Microsoft.Extensions.Configuration.Abstractions (8.0)
      Microsoft.Extensions.Primitives (>= 8.0)
    Microsoft.Extensions.Configuration.Binder (8.0.1)
      Microsoft.Extensions.Configuration.Abstractions (>= 8.0)
    Microsoft.Extensions.Configuration.FileExtensions (8.0)
      Microsoft.Extensions.Configuration (>= 8.0)
      Microsoft.Extensions.Configuration.Abstractions (>= 8.0)
      Microsoft.Extensions.FileProviders.Abstractions (>= 8.0)
      Microsoft.Extensions.FileProviders.Physical (>= 8.0)
      Microsoft.Extensions.Primitives (>= 8.0)
    Microsoft.Extensions.Configuration.Json (8.0)
      Microsoft.Extensions.Configuration (>= 8.0)
      Microsoft.Extensions.Configuration.Abstractions (>= 8.0)
      Microsoft.Extensions.Configuration.FileExtensions (>= 8.0)
      Microsoft.Extensions.FileProviders.Abstractions (>= 8.0)
      System.Text.Json (>= 8.0)
    Microsoft.Extensions.DependencyInjection (8.0)
      Microsoft.Extensions.DependencyInjection.Abstractions (>= 8.0)
    Microsoft.Extensions.DependencyInjection.Abstractions (8.0.1)
    Microsoft.Extensions.FileProviders.Abstractions (8.0)
      Microsoft.Extensions.Primitives (>= 8.0)
    Microsoft.Extensions.FileProviders.Physical (8.0)
      Microsoft.Extensions.FileProviders.Abstractions (>= 8.0)
      Microsoft.Extensions.FileSystemGlobbing (>= 8.0)
      Microsoft.Extensions.Primitives (>= 8.0)
    Microsoft.Extensions.FileSystemGlobbing (8.0)
    Microsoft.Extensions.Localization.Abstractions (8.0.4)
    Microsoft.Extensions.Logging (8.0)
      Microsoft.Extensions.DependencyInjection (>= 8.0)
      Microsoft.Extensions.Logging.Abstractions (>= 8.0)
      Microsoft.Extensions.Options (>= 8.0)
    Microsoft.Extensions.Logging.Abstractions (8.0.1)
      Microsoft.Extensions.DependencyInjection.Abstractions (>= 8.0.1)
    Microsoft.Extensions.Options (8.0.2)
      Microsoft.Extensions.DependencyInjection.Abstractions (>= 8.0)
      Microsoft.Extensions.Primitives (>= 8.0)
    Microsoft.Extensions.Primitives (8.0)
    Microsoft.JSInterop (8.0.4)
    Microsoft.JSInterop.WebAssembly (8.0.4)
      Microsoft.JSInterop (>= 8.0.4)
    Microsoft.NET.Test.Sdk (17.9)
      Microsoft.CodeCoverage (>= 17.9)
      Microsoft.TestPlatform.TestHost (>= 17.9)
    Microsoft.Reactive.Testing (6.0)
      System.Reactive (>= 6.0)
    Microsoft.TestPlatform.ObjectModel (17.9)
      System.Reflection.Metadata (>= 1.6)
    Microsoft.TestPlatform.TestHost (17.9)
      Microsoft.TestPlatform.ObjectModel (>= 17.9)
      Newtonsoft.Json (>= 13.0.1)
    MimeKit (4.11)
      BouncyCastle.Cryptography (>= 2.5.1)
      System.Security.Cryptography.Pkcs (>= 8.0.1)
    Mono.Cecil (0.11.6)
    Moq (4.20.70)
      Castle.Core (>= 5.1.1)
    Neo4j.Driver (5.27)
      Microsoft.Bcl.AsyncInterfaces (>= 5.0)
      System.IO.Pipelines (>= 7.0)
      System.ValueTuple (>= 4.5)
    Newtonsoft.Json (13.0.3)
    NodaTime (3.2.2)
    NodaTime.Serialization.SystemTextJson (1.3)
      NodaTime (>= 3.0 < 4.0)
    System.Collections.Immutable (8.0)
    System.Diagnostics.EventLog (8.0)
    System.Formats.Asn1 (9.0.3)
    System.IO.Pipelines (8.0)
    System.Reactive (6.0.1)
    System.Reflection.Metadata (8.0)
      System.Collections.Immutable (>= 8.0)
    System.Security.Cryptography.Pkcs (9.0.3)
      Microsoft.Bcl.Cryptography (>= 9.0.3)
      System.Formats.Asn1 (>= 9.0.3)
    System.Text.Encoding.CodePages (8.0)
    System.Text.Encodings.Web (8.0)
    System.Text.Json (8.0.3)
      System.Text.Encodings.Web (>= 8.0)
    System.ValueTuple (4.6.1)
    TextCopy (6.2.1)
      Microsoft.Extensions.DependencyInjection.Abstractions (>= 7.0)
    xunit (2.7)
      xunit.analyzers (>= 1.11)
      xunit.assert (>= 2.7)
      xunit.core (2.7)
    xunit.abstractions (2.0.3)
    xunit.analyzers (1.11)
    xunit.assert (2.7)
    xunit.core (2.7)
      xunit.extensibility.core (2.7)
      xunit.extensibility.execution (2.7)
    xunit.extensibility.core (2.7)
      xunit.abstractions (>= 2.0.3)
    xunit.extensibility.execution (2.7)
      xunit.extensibility.core (2.7)
    xunit.runner.visualstudio (2.5.7)
  remote: https://nuget.pkg.github.com/CCTC-team/index.json
    CCTC_Lib (1.0.14)
      System.Reactive (>= 6.0.1)
    Lib (1.0.32)
      Expecto (>= 10.2.1)
      FSharp.Core (>= 8.0.300)
      FSharp.Data (>= 6.4)
      FSharp.SystemTextJson (>= 1.3.13)
      FsToolkit.ErrorHandling (>= 4.15.2)
      FsToolkit.ErrorHandling.TaskResult (>= 4.15.2)
      MailKit (>= 4.7.1)
      Neo4j.Driver (>= 5.20)
      NodaTime (>= 3.1.11)
      NodaTime.Serialization.SystemTextJson (>= 1.2)
