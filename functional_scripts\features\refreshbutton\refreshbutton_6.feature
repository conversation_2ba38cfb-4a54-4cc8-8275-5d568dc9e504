@component @refreshbutton @refreshbutton_7
Feature: the refresh button and refresh button text change colour when the user hovers over them 
    Scenario: the refresh button component icon is a different colour when the user hovers over
        Given the user is at the home page
        And the user selects the "Refresh Button" component in the container "Buttons"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "ButtonIcon: refresh, IconPosition: Left, styled with border"
        Then the button component icon has the RGB color model value "rgb(255, 255, 255)" when in normal state
        And the button icon has the RGB color model value "rgb(192, 192, 192)" when in the hover state

    Scenario: the button component tex is a different colour when the user hovers over
        Given the user is at the home page
        And the user selects the "Refresh Button" component in the container "Buttons"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "ButtonIcon: refresh, IconPosition: Left, styled with border"
        Then the button component text has the RGB color model value "rgb(255, 255, 255)" when in normal state
        And the button text has the RGB color model value "rgb(192, 192, 192)" when in the hover state

    Scenario: the button component border is a different colour when the user hovers over
        Given the user is at the home page
        And the user selects the "Refresh Button" component in the container "Buttons"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "ButtonIcon: refresh, IconPosition: Left, styled with border"
        Then the button border the RGB color model value "rgb(255, 255, 255)" when in normal state
        And the button border has the RGB color model value "rgb(192, 192, 192)" when in the hover state
