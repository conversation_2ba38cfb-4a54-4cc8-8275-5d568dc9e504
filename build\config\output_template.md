<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>

<style>

    * {
        font-family: monospace, Courier;
    }

    table {
        border-collapse: collapse;
    }

    th, td {
        /* border: dashed grey 1px; */
        padding: 0.5rem 1rem;
    }

    .feature-body > *, .js-file-line {
        font-size: 12px;
    }

    .spec-script > * {
        font-size: 14px;        
        font-family: Arial, Helvetica, sans-serif;
    }

    .comments {
        font-size: 12px;
    }

    .comment {
        padding-bottom: 0.2rem;
    }

    .comment-edit {
        font-size: 11px;        
    }

    .timeline {
        font-size: 11px;
    }

    .index-good {
        font-size: 12px;
    }

    .index-errors {
        font-size: 11px;
    }

    .sub-header {
        font-size: 10px;
    }

</style>

## |TARGETSOFTWARE| validation for |VERSION|

|SUBHEADER|

<div class="sub-header">
* Note that currently the Project related events are not being picked up see
    See https://github.com/orgs/community/discussions/57326
    The audit trail related to moving columns within a project are not being pulled by graphql
</div>

### Index

|INDEX|

<br/>

### Features

|FEATURES|



</body>
</html>