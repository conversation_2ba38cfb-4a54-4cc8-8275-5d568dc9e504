﻿using System.Runtime.InteropServices;
using AngleSharp.Diffing.Extensions;
using CCTC_Components.bUnit.test.Helpers.Models;
using CCTC_Components.Components.__CCTC.Models;
using CCTC_Components.Components.DataTextBox;
using CCTC_Components.Components.TextBox;
using CCTC_Lib.Models.UI;
using Microsoft.AspNetCore.Components.Web;
using Neo4j.Driver;
using Xunit.Abstractions;

namespace CCTC_Components.bUnit.test.DataTextBox;

public class DataTextBoxTests : CCTCComponentsTestContext
{
    public DataTextBoxTests(ITestOutputHelper output) : base(output)
    {
        //Services.AddTestSchedulers();
        // Services.AddGetElementBoundRect();
        // Services.AddGetElementBoundRectFromQuery();
        // Services.AddScrollIntoViewFromQuery();
    }


    #region testData

    static NamedNodeIdentifier InitialItem =>
        NamedNodeIdentifier.Create("_Base", "Theme", Guid.NewGuid(), "CCTU-CT");

    #endregion testData

    [Fact]
    public void OneItemInSelectionOnInit()
    {
        var testSchedulers = new TestSchedulers();
        Services.AddTestSchedulers(sp => testSchedulers);
        AddGetElementBoundRectFromQuery(new BoundingRect(0, 0, 0, 0, 0, 0, 0, 0));
        AddScrollIntoViewFromQuery();
        var mockDataService = new Mock<IDataTextBoxDataService<NamedNodeIdentifier, Node>>();

        const string id = "datatextbox";
        List<NamedNodeIdentifier>? pathsListed = null;

        var cut = RenderComponent<DataTextBox<NamedNodeIdentifier, Node>>
        (parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.InitialValue, InitialItem)
            .Add(p => p.DataTextBoxDataService, mockDataService.Object)
            .Add(p => p.DisplayFunc, node => node.DisplayAs())
            .Add(p => p.SelectedPath, paths => pathsListed = paths)
        );

        Assert.Equal(1, pathsListed!.Count);
    }


    string ChildrenListerSelector(string id) => $"#{id}-dropdown-children-lister";

    void ChildrenListerIsHidden(IRenderedFragment cut, string id) => TestHelpers.AssertNotFound(cut, ChildrenListerSelector(id));
    void ChildrenListerIsVisible(IRenderedFragment cut, string id) => TestHelpers.AssertFound(cut, ChildrenListerSelector(id));
    string ChildTypesListerSelector(string id) => $"#{id}-dropdown-childtypes-lister";
    void ChildTypesListerIsHidden(IRenderedFragment cut, string id) => TestHelpers.AssertNotFound(cut, ChildTypesListerSelector(id));
    void ChildTypesListerIsVisible(IRenderedFragment cut, string id) => TestHelpers.AssertFound(cut, ChildTypesListerSelector(id));

    void MakeDropDownVisibleByClick(IRenderedFragment cut, string id, TestSchedulers testSchedulers, int advanceBy)
    {
        var input = cut.Find($"#{id} cctc-input input");
        input.MouseUp();
        testSchedulers.Default.AdvanceBy(TimeSpan.FromMilliseconds(advanceBy * 2).Ticks);
    }

    static Guid MakeGuid(int i)
    {
        if (i is < 0 or > 9)
        {
            throw new ArgumentOutOfRangeException(nameof(i), "only works from 0 to 9 at present");
        }
        const string emptyMinusLast = "00000000-0000-0000-0000-00000000000";
        return new Guid($"{emptyMinusLast}{i}");
    }

    //with grouping
    Mock<IDataTextBoxDataService<NamedNodeIdentifier, Node>> SetupMockWhenGrouping()
    {
        var mockDataService = new Mock<IDataTextBoxDataService<NamedNodeIdentifier, Node>>();

        //calls to child types
        mockDataService.SetupSequence(x =>
                x.GetDistinctChildTypes(It.IsAny<NamedNodeIdentifier>()))

            // 1
            .ReturnsAsync(new List<Node>
            {
                new("_Base", "Team"),
                new("_Base", "Leader"),
                new("_Base", "CancerType")
            })

            // 3
            .ReturnsAsync(new List<Node>
            {
                new Node("_Base", "Trial"),
                new Node("_Base", "Senior"),
                new Node("_Base", "Junior"),
            });

        //calls to children with type
        mockDataService.SetupSequence(x =>
                x.GetChildren(It.IsAny<NamedNodeIdentifier>(), It.IsAny<Node>()))

            // 2
            .ReturnsAsync(new List<NamedNodeIdentifier>
            {
                NamedNodeIdentifier.Create("_Base", "Team", MakeGuid(1), "Aero"),
                NamedNodeIdentifier.Create("_Base", "Team", MakeGuid(2), "Lung"),
            })

            //4
            .ReturnsAsync(new List<NamedNodeIdentifier>
            {
                NamedNodeIdentifier.Create("_Base", "Senior", MakeGuid(3), "June"),
                NamedNodeIdentifier.Create("_Base", "Senior", MakeGuid(4), "Betty"),
            });

        return mockDataService;
    }

    //no grouping
    Mock<IDataTextBoxDataService<NamedNodeIdentifier, Node>> SetupMockWhenNoGrouping()
    {
        var mockDataService = new Mock<IDataTextBoxDataService<NamedNodeIdentifier, Node>>();

        //calls to children with type
        mockDataService.SetupSequence(x =>
                x.GetChildren(It.IsAny<NamedNodeIdentifier>()))

            // 1
            .ReturnsAsync(new List<NamedNodeIdentifier>
            {
                NamedNodeIdentifier.Create("_Base", "Team", MakeGuid(1), "Aero"),
                NamedNodeIdentifier.Create("_Base", "Team", MakeGuid(2), "Lung"),
                NamedNodeIdentifier.Create("_Base", "Team", MakeGuid(3), "Other"),
            })

            //2
            .ReturnsAsync(new List<NamedNodeIdentifier>
            {
                NamedNodeIdentifier.Create("_Base", "Senior", MakeGuid(4), "June"),
                NamedNodeIdentifier.Create("_Base", "Senior", MakeGuid(5), "Betty"),
            })

            //3
            .ReturnsAsync(new List<NamedNodeIdentifier>
            {
                NamedNodeIdentifier.Create("_Base", "Section", MakeGuid(6), "Section 1"),
                NamedNodeIdentifier.Create("_Base", "Section", MakeGuid(7), "Section 2"),
                NamedNodeIdentifier.Create("_Base", "Division", MakeGuid(8), "Div A"),
                NamedNodeIdentifier.Create("_Base", "Division", MakeGuid(9), "Div B"),
            });

        return mockDataService;
    }


    [Fact]
    public void FocusOnInputCausesInitialQueryToRunWhenIncludesGrouping()
    {
        var testSchedulers = new TestSchedulers();
        Services.AddTestSchedulers(sp => testSchedulers);
        AddGetCursorSelection(new CursorSelection(0, 0));

        AddGetElementBoundRectFromQuery(new BoundingRect(0, 0, 0, 0, 0, 0, 0, 0));
        AddScrollIntoViewFromQuery();

        var mockDataService = new Mock<IDataTextBoxDataService<NamedNodeIdentifier, Node>>();
        mockDataService.Setup(x =>
                x.GetDistinctChildTypes(It.IsAny<NamedNodeIdentifier>()))
            .ReturnsAsync(new List<Node>
            {
                new("_Base", "Team"),
                new("_Base", "Leader")
            });

        const string id = "datatextbox";
        const bool includeGrouping = true;
        var cut = RenderComponent<DataTextBox<NamedNodeIdentifier, Node>>
        (parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.InitialValue, InitialItem)
            .Add(p => p.ListerItemHeightPixels, 30)
            .Add(p => p.DisplayFunc, node => node.AsIdAndNameParts())
            .Add(p => p.DisplayChildTypesFunc, node => node.AsLabel())
            .Add(p => p.DataTextBoxDataService, mockDataService.Object)
            .Add(p => p.IncludeGroupingStep, includeGrouping)
        );

        MakeDropDownVisibleByClick(cut, id, testSchedulers, cut.Instance.ComponentDefaultThrottleMs);

        //verify that the initial data service request is made
        mockDataService.Verify(x =>
            x.GetDistinctChildTypes(It.IsAny<NamedNodeIdentifier>()), Times.Once);

        ChildTypesListerIsVisible(cut, id);
        ChildrenListerIsHidden(cut, id);
    }

    [Fact]
    public void FocusOnInputCausesInitialQueryToRunWhenNoGrouping()
    {
        var testSchedulers = new TestSchedulers();
        Services.AddTestSchedulers(sp => testSchedulers);
        AddGetCursorSelection(new CursorSelection(0, 0));
        AddGetElementBoundRectFromQuery(new BoundingRect(0, 0, 0, 0, 0, 0, 0, 0));
        AddScrollIntoViewFromQuery();

        var mockDataService = SetupMockWhenNoGrouping();

        const string id = "datatextbox";
        const bool includeGrouping = false;
        var cut = RenderComponent<DataTextBox<NamedNodeIdentifier, Node>>
        (parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.InitialValue, InitialItem)
            .Add(p => p.ListerItemHeightPixels, 30)
            .Add(p => p.DisplayFunc, node => node.DisplayAs())
            .Add(p => p.DataTextBoxDataService, mockDataService.Object)
            .Add(p => p.IncludeGroupingStep, includeGrouping)
        );

        MakeDropDownVisibleByClick(cut, id, testSchedulers, cut.Instance.ComponentDefaultThrottleMs);

        //verify that the initial data service request is made
        mockDataService.Verify(x =>
            x.GetChildren(It.IsAny<NamedNodeIdentifier>()), Times.Once);

        ChildrenListerIsVisible(cut, id);
        ChildTypesListerIsHidden(cut, id);
    }

    //confirms the query history has been correctly updated
    void AssertQueryHistory(
        List<(NamedNodeIdentifier sourceData, TypeOfQuery queryType, Node? childParams)>? queryHistory,
        int countOfItems, TypeOfQuery queryType, string schema, string nodeResource, string[] name, Node? childType = null)
    {
        Assert.Equal(countOfItems, queryHistory!.Count);
        var (namedNode, qType, childParams) = queryHistory.Last();
        Assert.Equal(schema, namedNode.Schema);
        Assert.Equal(nodeResource, namedNode.NodeResource);
        Assert.Equal(name, namedNode.Name);
        Assert.Equal(queryType, qType);
        if (childType is not null)
        {
            Assert.Equal(childType, childParams);
        }
        else
        {
            Assert.Null(childParams);
        }
    }

    //confirms the selected path has been correctly updated
    void AssertSelectedPath(List<NamedNodeIdentifier>? pathsListed, int countOfItems, string schema, string nodeResource, string[] name)
    {
        Assert.Equal(countOfItems, pathsListed!.Count);
        var node = pathsListed.Last();
        Assert.Equal(schema, node.Schema);
        Assert.Equal(nodeResource, node.NodeResource);
        Assert.Equal(name, node.Name);
    }


    [Fact]
    public async Task CorrectQueriesAreRunWhenIncludesGrouping()
    {
        //runs alternate grouping and children

        var testSchedulers = new TestSchedulers();
        Services.AddTestSchedulers(sp => testSchedulers);
        AddGetElementBoundRectFromQuery(new BoundingRect(0, 0, 0, 0, 0, 0, 0, 0));
        AddScrollIntoViewFromQuery();
        AddGetCursorSelection(new CursorSelection(1, 1));

        var mockDataService = SetupMockWhenGrouping();

        const string id = "datatextbox";
        const bool includeGrouping = true;
        List<NamedNodeIdentifier>? pathsListed = null;
        List<(NamedNodeIdentifier sourceData, TypeOfQuery queryType, Node? childParams)>? queryHistory = null;

        var cut = RenderComponent<DataTextBox<NamedNodeIdentifier, Node>>
        (parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.InitialValue, InitialItem)
            .Add(p => p.DataTextBoxDataService, mockDataService.Object)
            .Add(p => p.ListerItemHeightPixels, 30)
            .Add(p => p.DisplayFunc, node => node.AsIdAndNameParts())
            .Add(p => p.DisplayChildTypesFunc, node => node.AsLabel())
            .Add(p => p.IncludeGroupingStep, includeGrouping)
            .Add(p => p.SelectedPath, paths => pathsListed = paths)
            .Add(p => p.QueryHistory, qh => queryHistory = qh)
        );

        //check - init set up correctly
        AssertSelectedPath(pathsListed, 1, "_Base", "Theme", new[] { "CCTU-CT" });
        Assert.Empty(queryHistory!);

        //make dropdown visible
        MakeDropDownVisibleByClick(cut, id, testSchedulers, cut.Instance.ComponentDefaultThrottleMs);

        cut.WaitForElement($"{ChildTypesListerSelector(id)} [data-item-row='0']");

        //select the first child type
        await cut.InvokeAsync(() => cut.Find($"{ChildTypesListerSelector(id)} [data-item-row='0']").Click());

        //check - still shows only the first item as last selection was a child type selection
        //note should be CancerType as the items are sorted
        AssertSelectedPath(pathsListed, 1, "_Base", "Theme", new[] { "CCTU-CT" });
        AssertQueryHistory(queryHistory,
            1, TypeOfQuery.Children, "_Base", "Theme", new[] { "CCTU-CT" },
            new Node("_Base", "CancerType"));

        cut.WaitForElement($"{ChildrenListerSelector(id)} [data-item-row='1']");

        //select the child item
        await cut.InvokeAsync(() => cut.Find($"{ChildrenListerSelector(id)} [data-item-row='1']").Click());

        //check
        AssertSelectedPath(pathsListed, 2, "_Base", "Team", new[] { "Lung" });
        AssertQueryHistory(queryHistory,
            2, TypeOfQuery.ChildType, "_Base", "Team", new[] { "Lung" });

        cut.WaitForElement($"{ChildTypesListerSelector(id)} [data-item-row='1']");

        //select the second data row
        await cut.InvokeAsync(() => cut.Find($"{ChildTypesListerSelector(id)} [data-item-row='1']").Click());

        //check
        AssertSelectedPath(pathsListed, 2, "_Base", "Team", new[] { "Lung" });
        AssertQueryHistory(queryHistory,
            3, TypeOfQuery.Children, "_Base", "Team", new[] { "Lung" },
            new Node("_Base", "Senior"));
    }


    [Fact]
    public async Task CorrectQueriesAreRunWhenNoGrouping()
    {
        //runs only children queries

        var testSchedulers = new TestSchedulers();
        Services.AddTestSchedulers(sp => testSchedulers);

        AddGetElementBoundRectFromQuery(new BoundingRect(0, 0, 0, 0, 0, 0, 0, 0));
        AddScrollIntoViewFromQuery();
        AddGetCursorSelection(new CursorSelection(0, 0));

        var mockDataService = SetupMockWhenNoGrouping();

        const string id = "datatextbox";
        const bool includeGrouping = false;
        List<NamedNodeIdentifier>? pathsListed = null;
        List<(NamedNodeIdentifier sourceData, TypeOfQuery queryType, Node? childParams)>? queryHistory = null;

        var cut = RenderComponent<DataTextBox<NamedNodeIdentifier, Node>>
        (parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.InitialValue, InitialItem)
            .Add(p => p.DataTextBoxDataService, mockDataService.Object)
            .Add(p => p.ListerItemHeightPixels, 30)
            .Add(p => p.DisplayFunc, node => node.AsIdAndNameParts())
            .Add(p => p.IncludeGroupingStep, includeGrouping)
            .Add(p => p.SelectedPath, paths => pathsListed = paths)
            .Add(p => p.QueryHistory, qh => queryHistory = qh)
        );

        //check - init set up correctly
        AssertSelectedPath(pathsListed, 1, "_Base", "Theme", new[] { "CCTU-CT" });
        Assert.Empty(queryHistory!);

        //make dropdown visible
        MakeDropDownVisibleByClick(cut, id, testSchedulers, cut.Instance.ComponentDefaultThrottleMs);

        cut.WaitForElement($"{ChildrenListerSelector(id)} [data-item-row='0']");

        //select the first child - Team
        await cut.InvokeAsync(() => cut.Find($"{ChildrenListerSelector(id)} [data-item-row='0']").Click());

        //check -
        AssertSelectedPath(pathsListed, 2, "_Base", "Team", new[] { "Aero" });
        AssertQueryHistory(queryHistory,
            1, TypeOfQuery.Children, "_Base", "Team", new[] { "Aero" });

        cut.WaitForElement($"{ChildrenListerSelector(id)} [data-item-row='1']");

        //select the next child - 2nd item (note the items are sorted)
        await cut.InvokeAsync(() => cut.Find($"{ChildrenListerSelector(id)} [data-item-row='1']").Click());

        //check
        AssertSelectedPath(pathsListed, 3, "_Base", "Senior", new[] { "June" });
        AssertQueryHistory(queryHistory,
            2, TypeOfQuery.Children, "_Base", "Senior", new[] { "June" });
    }
}