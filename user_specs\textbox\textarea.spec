1 - the text area component can receive text input
2 - the text area component has the facility to add a placeholder and / or set an initial number of display rows
3 - the text area component input can be constrained by applying an input mask, preventing whitespace (with a configurable response delay) or setting a max length
4 - the text area component can be made read-only and / or disabled. The read-only icon is optional