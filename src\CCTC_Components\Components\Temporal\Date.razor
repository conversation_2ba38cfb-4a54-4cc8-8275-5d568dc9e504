﻿@using System.Globalization
@using CCTC_Components.Components.TextBox.TextInteraction
@using CCTC_Lib.Contracts.UI
@using static Common.Helpers.StringMask
@inherits CCTC_Components.Components.__CCTC.CCTCBase
@typeparam TValue

<cctc-input data-cctc-input-type="date" id="@Id" class="@CssClass" style="@Style" data-author="cctc">
    <div class="component-wrapper">
        <div class="date-text-box-wrapper">
            <Text
                Id="@($"{Id}-date-text")"
                Value="@_textDateValue"
                ValueChanged="OnTextValueChanged"
                Placeholder="@(DateFormat ?? _defaultDateFormat)"
                Mask="@(DateFormat is null ? _defaultDateMask : getDateMask(DateFormat))"
                ThrottleMs="@ThrottleMs"
                PreventWhitespace="@(!_canClear)"
                HideReadOnlyIcon="@HideReadOnlyIcon"
                Disabled="@Disabled"
                ReadOnly="@ReadOnly">
            </Text>
        </div>
    @if (!ReadOnly && !Disabled)
    {
        <div class="background-block">
            <div class="val-icon-wrapper">
            @if (_displayValidationIcon)
            {
                <i class="material-icons @_validationIcon">
                    @_validationIcon
                </i>
            }
            </div>
            <div class="date-input-wrapper">
                <input
                    id="@Id-date-picker"
                    type="date"
                    value="@_pickerDateValue"
                    class="date-input"
                    min="@GetDateValue(MinDate)"
                    max="@GetDateValue(MaxDate)"
                    @onchange="OnPickerValueChanged"
                    disabled="@Disabled"
                    readonly="@ReadOnly"
                    required>
            </div>
            <div class="cal-icon-wrapper">
                <i class="material-icons">
                    calendar_month
                </i>
            </div>
        </div>
    }
    </div>
</cctc-input>

@code {

    /// <summary>
    /// The input value
    /// </summary>
    [Parameter]
    public TValue? Value { get; set; }

    /// <summary>
    /// A callback which fires when the input value changes
    /// </summary>
    [Parameter]
    public EventCallback<TValue> ValueChanged { get; set; }

    /// <summary>
    /// A second callback which fires when the input value changes. Useful when consuming using @bind-Value
    /// </summary>
    [Parameter]
    public EventCallback<ChangeEventArgs> DateChanged { get; set; }

    /// <summary>
    /// The date format. See <see cref="Common.Services.Clock.availableDateFormats"/> for the supported formats
    /// </summary>
    [Parameter]
    public string? DateFormat { get; set; }

    /// <summary>
    /// Provide a minimum date
    /// </summary>
    [Parameter]
    public DateOnly? MinDate { get; set; }

    /// <summary>
    /// Provide a maximum date
    /// </summary>
    [Parameter]
    public DateOnly? MaxDate { get; set; }

    /// <summary>
    /// Disabled if true
    /// </summary>
    [Parameter]
    public bool Disabled { get; set; }

    /// <summary>
    /// Read-only if true
    /// </summary>
    [Parameter]
    public bool ReadOnly { get; set; }

    /// <summary>
    /// Hides the read-only icon when <see cref="ReadOnly"/> is true
    /// </summary>
    [Parameter]
    public bool HideReadOnlyIcon { get; set; }

    /// <summary>
    /// The Throttle speed
    /// </summary>
    [Parameter]
    public int ThrottleMs { get; set; }

    /// <summary>
    /// Control the feedback icon display
    /// </summary>
    [Parameter]
    public FeedbackIcon? FeedbackIcon { get; set; }

    /// <summary>
    /// Allows the current value to be cleared from the UI
    /// </summary>
    [Parameter]
    public bool AllowClear { get; set; }

    string? _textDateValue = string.Empty;
    string? _pickerDateValue = string.Empty;
    const string _defaultDateFormat = "yyyy-MM-dd";
    const string _defaultDateMask = "0000-00-00";
    bool _typeParamAllowsNull;
    bool _canClear;
    string? _validationIcon;
    bool _displayValidationIcon;
    FeedbackIcon _feedbackIcon;

    string GetDateValue(object? value, string? format = null)
    {
        string dateFormat = format is null ? _defaultDateFormat : format;
        var date = value as DateOnly?;

        if (date is not null)
        {
            return date.Value.ToString(dateFormat);
        }
        else if (value is not null)
        {
            return value.ToString()!;
        }
        else
        {
            return string.Empty;
        }
    }

    async Task OnTextValueChanged(string newValue)
    {
        bool dateCleared = string.IsNullOrEmpty(newValue);
        //note: update _textDateValue even if the changed value is not a valid date
        _textDateValue = newValue;

        if (dateCleared)
        {
            if (_canClear)
            {
                await OnValueChanged(null);
            }

            _displayValidationIcon = false;
            return;
        }

        bool result = DateOnly.TryParseExact(newValue, DateFormat ?? _defaultDateFormat, CultureInfo.InvariantCulture, DateTimeStyles.None, out DateOnly parsedDate);
        bool isDateValid = result && IsDateWithinLimits(parsedDate);
        ApplyValidation(parsedDate, isDateValid);

        if (isDateValid)
        {
            await OnValueChanged(parsedDate);
        }
    }

    async Task OnPickerValueChanged(ChangeEventArgs args)
    {
        string dateValue = GetDateValue(args.Value);
        bool dateCleared = string.IsNullOrEmpty(dateValue);
        bool result = DateOnly.TryParseExact(dateValue, _defaultDateFormat, CultureInfo.InvariantCulture, DateTimeStyles.None, out DateOnly parsedDate);
        _pickerDateValue = dateCleared ? string.Empty : GetDateValue(parsedDate);

        if (dateCleared)
        {
            if (_canClear)
            {
                await OnValueChanged(null);
            }
            else
            {
                await ShowAlert("The date is not permitted to be cleared");
            }

            return;
        }

        bool isDateValid = result && IsDateWithinLimits(parsedDate);
        ApplyValidation(parsedDate, isDateValid);

        if (isDateValid)
        {
            await OnValueChanged(parsedDate);
        }
    }

    async Task OnValueChanged(DateOnly? parsedDate)
    {
        await ValueChanged.InvokeAsync((TValue?)(object?)parsedDate);
        await DateChanged.InvokeAsync(new ChangeEventArgs() { Value = parsedDate is null ? null : GetDateValue(parsedDate, DateFormat) });
    }

    void DisplayPassValidation()
    {
        _validationIcon = "check";
        _displayValidationIcon = true;
    }

    void DisplayFailValidation()
    {
        _validationIcon = "priority_high";
        _displayValidationIcon = true;
    }

    bool IsDateWithinLimits(DateOnly? dateValue)
    {
        if (dateValue is null)
        {
            return true;
        }

        bool minResult = true;
        bool maxResult = true;

        if (MinDate is not null)
        {
            minResult = dateValue >= MinDate;
        }

        if (MaxDate is not null)
        {
            maxResult = dateValue <= MaxDate;
        }

        return minResult && maxResult;
    }

    void ApplyValidation(DateOnly? dateValue, bool isDateValid)
    {
        if (dateValue is null || _feedbackIcon == Temporal.FeedbackIcon.None)
        {
            _displayValidationIcon = false;
        }
        else if ((_feedbackIcon == Temporal.FeedbackIcon.Both || _feedbackIcon == Temporal.FeedbackIcon.Error) && !isDateValid)
        {
            DisplayFailValidation();
        }
        else if ((_feedbackIcon == Temporal.FeedbackIcon.Both || _feedbackIcon == Temporal.FeedbackIcon.Valid) && isDateValid)
        {
            DisplayPassValidation();
        }
        else
        {
            _displayValidationIcon = false;
        }
    }

    void CheckConfig(bool typeParamAllowsNull)
    {
        if (typeof(TValue) != typeof(DateOnly) && !typeof(DateOnly).IsAssignableFrom(Nullable.GetUnderlyingType(typeof(TValue))))
        {
            throw new ArgumentException("Expected a dateonly or nullable dateonly type", nameof(TValue));
        }

        if (AllowClear && !typeParamAllowsNull)
        {
            throw new ArgumentException($"{nameof(AllowClear)} should only be set to true when {nameof(TValue)} is of type nullable dateonly", nameof(AllowClear));
        }
    }

    ///<inheritdoc />
    protected override void OnInitialized()
    {
        _typeParamAllowsNull = default(TValue) == null;
        CheckConfig(_typeParamAllowsNull);
    }

    /// <inheritdoc/>
    protected override void OnParametersSet()
    {
        _canClear = _typeParamAllowsNull && AllowClear && !ReadOnly && !Disabled;
        _feedbackIcon = FeedbackIcon ?? Temporal.FeedbackIcon.Error;

        var dateValue = Value as DateOnly?;
        _textDateValue = GetDateValue(dateValue, DateFormat);
        _pickerDateValue = GetDateValue(dateValue);
        ApplyValidation(dateValue, IsDateWithinLimits(dateValue));
    }
}
