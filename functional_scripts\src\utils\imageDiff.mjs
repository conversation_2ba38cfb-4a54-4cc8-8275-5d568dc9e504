import pixelmatch from 'pixelmatch';
import { PNG } from 'pngjs';
import { readFileSync, writeFileSync } from 'fs';

const basePath = 'C:/temp/ImageDiffFiles';
const outputPath = `${basePath}/diff1.png`;
const threshold = 0.4;

const img1 = PNG.sync.read(readFileSync(`${basePath}/image1.png`));
const img2 = PNG.sync.read(readFileSync(`${basePath}/image2.png`));
const { width, height } = img1;
const diff = new PNG({ width, height });

const difference = pixelmatch(img1.data, img2.data, diff.data, width, height, {
  threshold: threshold
});
console.log(`Threshold: ${threshold}`);
console.log(`Mismatched pixels: ${difference}`);
console.log(`See ${outputPath} for image diff`);

writeFileSync(outputPath, PNG.sync.write(diff));
