﻿namespace CCTC_Components.bUnit.test.Helpers.Models;

using CCTC_Lib.Contracts.Data;
using CCTC_Lib.Enums.Data;

public record Node(string Schema, string NodeResource) : IUniqueIdentity<string>, ISortable, ISearchable
{
    public string Key => Guid.NewGuid().ToString();

    public static string SortBy { get; set; } = "ForSorting";

    public static SortDir SortDir { get; set; } = SortDir.Asc;

    //required for sorting purposes
    string ForSorting => $"{Schema} {NodeResource}";

    public string SearchText() => $"{Schema}{NodeResource}";

    public string AsLabel()
    {
        return $"{NodeResource} ({Schema})";
    }

    public Node Copy() => this with { };

    public static Node Empty => new("", "");
}