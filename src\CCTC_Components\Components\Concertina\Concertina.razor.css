cctc-concertina {
    position: relative;
    width: 100%;
}

cctc-concertina-header {
    display: flex;
    align-items: center;
}

.config-button {
    display: flex;
    align-items: center;
}

.config-button {
    color: var(--cctc-concertina-icon-color);
    cursor: pointer;
}

.config-button:hover, ::deep [id$=should-collapse-others-label]:hover {
    color: var(--cctc-concertina-icon-hover-color);
}

cctc-concertina-header {
    color: var(--cctc-concertina-header-color);
    background-color: var(--cctc-concertina-header-background-color);
}

cctc-concertina-subheader {
    color: var(--cctc-concertina-subheader-color);
    background-color: var(--cctc-concertina-subheader-background-color);
}

::deep [id$=should-collapse-others] {
    --cctc-input-height: 1.2rem;
}

::deep [id$=should-collapse-others] .checkbox-wrapper {
    --cctc-input-height: 1.2rem;
    --cctc-input-color: var(--cctc-color);
    --cctc-input-background-color: var(--cctc-concertina-header-background-color);
    border: none;
}

::deep [id$=should-collapse-others-label] {
    font-size: 0.7rem;
    user-select: none;
}
