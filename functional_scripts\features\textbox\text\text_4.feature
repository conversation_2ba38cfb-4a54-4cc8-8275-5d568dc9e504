@component @textbox @text @text_4
Feature: the text component can be made read-only and / or disabled. The read-only icon is optional
    Scenario: the text component can be made read-only
        Given the user is at the home page
        And the user selects the "Text" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Read-only"
        Then the Text component is not editable
        And the Text component image matches the base image "text-readonly"

    Scenario: the text component can be disabled
        Given the user is at the home page
        And the user selects the "Text" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Disabled"
        Then the Text component is disabled
        And the Text component image matches the base image "text-disabled"

    Scenario: the text component can be made read-only and disabled
        Given the user is at the home page
        And the user selects the "Text" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Disabled and read-only"
        Then the Text component is not editable
        And the Text component is disabled
        And the Text component image matches the base image "text-readonly-disabled"

    Scenario: the text component can be made read-only without a read-only icon
        Given the user is at the home page
        And the user selects the "Text" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Read-only long (hide read-only icon)"
        Then the Text component is not editable
        And the Text component image matches the base image "text-readonly-no-icon"

    Scenario: the text component can be made read-only and disabled without a read-only icon
        Given the user is at the home page
        And the user selects the "Text" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Disabled and read-only (hide read-only icon)"
        Then the Text component is not editable
        And the Text component is disabled
        And the Text component image matches the base image "text-readonly-disabled-no-icon"