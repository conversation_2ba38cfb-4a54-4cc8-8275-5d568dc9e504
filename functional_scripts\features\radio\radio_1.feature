@component @radio @radio_1
Feature: the radio component option can be changed using the mouse by clicking on a radio button or radio label
    Scenario: the radio component sample page is available
        Given the user is at the home page
        When the user selects the "Radio" component in the container "Input"
        Then the url ending is "radiosample"

    Scenario: the radio component option can be changed using the mouse by clicking on a radio button
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        And the current selected radio option has the text "Option 1"
        When the user clicks on the radio option button with the associated label text "Option 2"
        Then the current selected radio option has the text "Option 2"

    Scenario: the radio component option can be changed using the mouse by clicking on a radio label
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        And the current selected radio option has the text "Option 1"
        When the user clicks on the radio option label with the text "Option 2"
        Then the current selected radio option has the text "Option 2"