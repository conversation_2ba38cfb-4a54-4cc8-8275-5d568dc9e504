@component @lister @lister_7
Feature: items in the lister can be deleted and the list reordered
        Scenario: the lister component can have items deleted from it and they then re-order
        Given the user is at the home page
        And the user selects the "Lister" component
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Preloaded data with deletion"
        And the row with content "000002 : TRT | 4 | PaclitaxTrt | 1 | 09/08/2016 653e4782" has row index 2
        When the user clicks the delete icon for row with content "000002 : TRT | 4 | PaclitaxTrt | 1 | 09/08/2016 653e4782"
        Then the row with content "000003 : TRT | 1 | NeuroQuest | 1 | 02/11/2016 99079ff0" has row index 2
        And the row with content "000002 : TRT | 4 | PaclitaxTrt | 1 | 09/08/2016 653e4782" is not visible 

