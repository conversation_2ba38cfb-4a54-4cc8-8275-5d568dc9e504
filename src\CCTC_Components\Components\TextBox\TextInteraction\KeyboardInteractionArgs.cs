﻿using CCTC_Components.Components.__CCTC.Models;
using Microsoft.AspNetCore.Components.Web;

namespace CCTC_Components.Components.TextBox.TextInteraction;

/// <summary>
/// A payload for interaction via the keyboard
/// </summary>
/// <param name="KeyboardEventArgs">The <see cref="KeyboardEventArgs"/> associated with the event</param>
/// <param name="CursorSelection">The cursor positioning as a <see cref="CursorSelection"/></param>
public record KeyboardInteractionArgs(KeyboardEventArgs KeyboardEventArgs, CursorSelection CursorSelection)
    : InteractionArgs(CursorSelection);