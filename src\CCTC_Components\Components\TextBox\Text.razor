﻿@using CCTC_Lib.Enums.UI
@using CCTC_Components.Components.__CCTC.Models
@using CCTC_Components.Components.TextBox.TextInteraction
@using Microsoft.JSInterop
@inject IJSRuntime JSRuntime
@inherits TextBase

@namespace CCTC_Components.Components.TextBox

<cctc-input data-cctc-input-type="text" id="@Id" class="@CssClass" style="@Style" data-author="cctc">
    <div class="component-wrapper">
        <input
            @attributes="InputAttributes"
            id="@Id-input"
            type="@(RedactText ? "password" : "text")"
            @ref="ElementRef"
            value="@TextValue"
            @onchange="@(args => OnValueChanged(args, BindEvent.OnChange))"
            @oninput="@(args => OnValueChanged(args, BindEvent.OnInput))"
            @onfocus="OnFocus"
            @onblur="OnBlur"
            @onkeyup="@(keyArgs => OnInteraction(keyboardEventArgs: keyArgs))"
            @onmouseup="@(mouseArgs => OnInteraction(mouseEventArgs: mouseArgs))"

            disabled="@Disabled"
            readonly="@ReadOnly"
            maxlength="@(MaxLength > 0 ? MaxLength : null)"
            placeholder="@Placeholder"
            autocomplete="off"/>
        @if (ReadOnly && !HideReadOnlyIcon)
        {
            <div class="icon-wrapper">
                <span class="material-icons">
                    lock
                </span>
            </div>
        }
    </div>
</cctc-input>

@code {

    /*
     * TODO: removed the touch events for now, but this will need adding later
     * @ontouchend="@(touchArgs => OnInteraction(touchEventArgs: touchArgs))"
     * @ontouchcancel="@(touchArgs => OnInteraction(touchEventArgs: touchArgs))"
     */



    /// <summary>
    /// Redacts the text when true
    /// </summary>
    [Parameter]
    public bool RedactText { get; set; }

    /// <summary>
    /// A callback that provides <see cref="InteractionArgs"/> for the type of interaction that occurred
    /// </summary>
    /// <remarks>The end property on <see cref="CursorSelection"/> can be used to find the current cursor position</remarks>
    [Parameter]
    public Func<InteractionArgs, Task>? Interaction { get; set; }

    async Task OnInteraction(KeyboardEventArgs? keyboardEventArgs = null, MouseEventArgs? mouseEventArgs = null, TouchEventArgs? touchEventArgs = null)
    {
        if (Interaction is not null)
        {
            var selection = await GetCursorSelection($"#{Id}-input");
            var args =
                (keyboardEventArgs, mouseEventArgs, touchEventArgs) switch
                {
                    (not null, null, null) => (InteractionArgs)new KeyboardInteractionArgs(keyboardEventArgs, selection),
                    (null, not null, null) => new MouseInteractionArgs(mouseEventArgs, selection),
                    (null, null, not null) => new TouchInteractionArgs(touchEventArgs, selection),
                    _ => throw new InvalidOperationException()
                };

            await InvokeAsync(() => Interaction(args));
        }
    }

    List<string> ReservedInputAttributes =>
        new()
        {
            "onkeyup", "onmouseup", "ontouchend", "ontouchcancel"
        };

    /// <inheritdoc />
    protected override void OnInitialized()
    {
        base.OnInitialized();

        if(Interaction is not null)
        {
            var (valid, disallowedAttributes) = CheckInputAttributesAreValid(ReservedInputAttributes);
            if(!valid)
            {
                throw new ArgumentException($"Using the Interaction implementation limits you to which attributes you can use." +
                                            $"The following reserved attributes are required for Interaction: " +
                                            $"{string.Join(", ", disallowedAttributes!)}", nameof(InputAttributes));
            }
        }
    }

}