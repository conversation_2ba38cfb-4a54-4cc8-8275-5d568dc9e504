﻿@using Microsoft.JSInterop;
@using TextCopy
@inject IClipboard Clipboard
@inject IJSRuntime JSRuntime
@inherits CCTC_Components.Components.__CCTC.CCTCBase
@implements IAsyncDisposable

<cctc-infotext id="@Id" class="@CssClass" style="@Style" data-author="cctc">
    <div class="component-wrapper">
@{
    RenderFragment content = @<div id="@_contentWrapperId" class="@_textOverflowClass">@Content</div>;
    if (InfoTextTooltipBehaviour != InfoTextTooltipBehaviour.Disabled)
    {
        <Tooltip
            Id="@($"{Id}-infotext-tooltip")"
            Content="@Content"
            TooltipPlacement="InfoTextTooltipPlacement"
            TooltipBehaviour="_tooltipBehaviour">
            @content
        </Tooltip>
    }
    else
    {
        @content
    }
        <div class="@_iconWrapperClass">
            <a id="@_popoverId" tabindex="0" role="button" data-bs-toggle="popover" data-bs-trigger="focus" data-bs-content="@TextCopyMessage" data-bs-custom-class="custom-popover"
                data-bs-animation="true" @onclick="CopyContent" data-bs-container="@($"#{_popoverId}")" data-bs-placement="@_popoverPlacement">
                <i id="@($"{Id}-infotext-copy")" class="material-icons">
                    content_copy
                </i>
            </a>
        </div>
}
    </div>
</cctc-infotext>

@code {

    /// <summary>
    /// The content to display in the info text tooltip
    /// </summary>
    [Parameter, EditorRequired]
    public required string Content { get; set; }

    /// <summary>
    /// The text copy message
    /// </summary>
    [Parameter]
    public string? TextCopyMessage { get; set; } = "Copied to clipboard";

    /// <summary>
    /// Only show copy icon on hover when true
    /// </summary>
    [Parameter]
    public bool OnlyShowCopyIconOnHover { get; set; } = true;

    /// <summary>
    /// Configure the info text overflow
    /// </summary>
    [Parameter]
    public InfoTextOverflow InfoTextOverflow { get; set; }

    /// <summary>
    /// Configure the info text tooltip placement
    /// </summary>
    [Parameter]
    public TooltipPlacement InfoTextTooltipPlacement { get; set; }

    /// <summary>
    /// Configure the info text popover placement
    /// </summary>
    [Parameter]
    public InfoTextPopoverPlacement InfoTextPopoverPlacement { get; set; } = InfoTextPopoverPlacement.Bottom;

    /// <summary>
    /// Configure the info text tooltip behavior
    /// </summary>
    [Parameter]
    public InfoTextTooltipBehaviour InfoTextTooltipBehaviour { get; set; }

    string? _contentWrapperId;
    TooltipBehaviour? _tooltipBehaviour;
    string? _popoverPlacement;
    string? _popoverId;
    string? _textOverflowClass;
    string? _iconWrapperClass;
    IJSObjectReference? _jsModulePopover;

    TooltipBehaviour GetTooltipBehaviour(InfoTextTooltipBehaviour infoTextTooltipBehaviour, string contentWrapperSelector)
    {
        return infoTextTooltipBehaviour switch
        {
            InfoTextTooltipBehaviour.EnabledOnTextOverflow => new TooltipBehaviour.EnabledOnOverflow(contentWrapperSelector),
            InfoTextTooltipBehaviour.Enabled => new TooltipBehaviour.Enabled(),
            InfoTextTooltipBehaviour.Disabled => new TooltipBehaviour.Disabled(),
            _ => throw new ArgumentException("case not handled", nameof(infoTextTooltipBehaviour))
        };
    }

    Task CopyContent()
    {
        return Clipboard.SetTextAsync(Content);
    }

    ///<inheritdoc />
    protected override void OnInitialized()
    {
        _contentWrapperId = $"{Id}-content-wrapper";
        _tooltipBehaviour = GetTooltipBehaviour(InfoTextTooltipBehaviour, $"#{_contentWrapperId}");
        _popoverPlacement = InfoTextPopoverPlacement.ToString().ToLower();
        _popoverId = $"{Id}-infotext-popover";
    }

    ///<inheritdoc />
    protected override void OnParametersSet()
    {
        _iconWrapperClass =
            new CssBuilder("icon-wrapper")
                .AddClass("show-on-hover", OnlyShowCopyIconOnHover)
                .Build();

        _textOverflowClass =
            new CssBuilder()
                .AddClass("infotext-wrap", InfoTextOverflow == InfoTextOverflow.Wrap)
                .AddClass("infotext-nowrap", InfoTextOverflow == InfoTextOverflow.NoWrap)
                .AddClass("infotext-scroll", InfoTextOverflow == InfoTextOverflow.Scroll)
                .Build();
    }

    ///<inheritdoc />
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            _jsModulePopover = await JSRuntime.InvokeAsync<IJSObjectReference>("import", "./_content/CCTC_Components/scripts/bootstrap_popover.js");
            await _jsModulePopover.InvokeVoidAsync("addPopover", _popoverId);
        }
    }

    /// <inheritdoc />
    public override async ValueTask DisposeAsync()
    {
        if (_jsModulePopover is not null)
        {
            await _jsModulePopover.InvokeVoidAsync("disposePopover", _popoverId);
            await _jsModulePopover.DisposeAsync();
        }
    }
}