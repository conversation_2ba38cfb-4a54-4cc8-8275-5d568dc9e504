﻿using CCTC_Components.Components.Tabs;
using Xunit.Abstractions;

namespace CCTC_Components.bUnit.test;

public class TabsTests : CCTCComponentsTestContext
{
    readonly ITestOutputHelper _output;

    public TabsTests(ITestOutputHelper output)
    {
        _output = output;
    }

    const string TabId = "tab-example";
    const string TabItem1 = "tabitem1";
    const string TabItem2 = "tabitem2";
    const string TabItem3 = "tabitem3";

    IRenderedComponent<Tabs> Init(string? preSelectTab = null)
    {
        var paramBuilder = new ComponentParameterCollectionBuilder<Tabs>();

        //add Tabs required parameters
        paramBuilder.Add(p => p.Id, TabId);

        //add Tabs optional parameters
        if (preSelectTab is not null)
        {
            paramBuilder.Add(p => p.PreSelectTabId, preSelectTab);
        }

        //add children
        paramBuilder.AddChildContent<TabItem>(alertParameters => alertParameters
            .Add(p => p.Id, TabItem1)
            .Add(p => p.<PERSON><PERSON>, TabItem1)
            .AddChildContent("<div id='tabitem1-content'></div>"));
        paramBuilder.AddChildContent<TabItem>(alertParameters => alertParameters
            .Add(p => p.Id, TabItem2)
            .Add(p => p.Header, TabItem2)
            .AddChildContent("<div id='tabitem2-content'></div>"));
        paramBuilder.AddChildContent<TabItem>(alertParameters => alertParameters
            .Add(p => p.Id, TabItem3)
            .Add(p => p.Header, TabItem3)
            .AddChildContent("<div id='tabitem3-content'></div>"));

        return
            Render<Tabs>(paramBuilder.Build().ToRenderFragment<Tabs>());
    }

    [Fact]
    public void CountOfRegisteredItemsCorrect()
    {
        AddAddTooltip();
        AddUpdateTooltipTitle();

        var cut = Init();
        Assert.Equal(3, cut.Instance.Children.Count);
    }

    [Fact]
    public void GetTabItemsByIndexFailsWithInvalidIndex()
    {
        AddAddTooltip();
        AddUpdateTooltipTitle();

        var cut = Init();
        Assert.Throws<IndexOutOfRangeException>(() => cut.Instance.GetTabItemByIndex(-1));
    }

    [Fact]
    public void GetTabItemsByIndexFailsWithInvalidIndexTooFew()
    {
        AddAddTooltip();
        AddUpdateTooltipTitle();

        var cut = Init();
        Assert.Throws<IndexOutOfRangeException>(() => cut.Instance.GetTabItemByIndex(3));
    }

    [Fact]
    public void CanGetTabItemsByIndex()
    {
        AddAddTooltip();
        AddUpdateTooltipTitle();

        var cut = Init();

        var first = cut.Instance.GetTabItemByIndex(0);
        Assert.Equal(TabItem1, first.Id);
        Assert.Equal(TabItem1, first.Header);

        var second = cut.Instance.GetTabItemByIndex(1);
        Assert.Equal(TabItem2, second.Id);
        Assert.Equal(TabItem2, second.Header);
    }

    [Fact]
    public void PreSelectingATabIdMakesItSelected()
    {
        AddAddTooltip();
        AddUpdateTooltipTitle();

        var cut = Init(TabItem2);

        //content is shown in the selected id
        TestHelpers.AssertFound(() => cut.Find($"#{TabId}-tabs-selected-content > #{TabItem2}-content"));
        //the selected tab item has the selected class
        TestHelpers.AssertFound(() => cut.Find($"#{TabItem2} cctc-tabitem-header.tab-item-header-selected"));
    }

    [Fact]
    public void PreSelectingANonExistentTabIdMakesTheFirstTabItemSelected()
    {
        AddAddTooltip();
        AddUpdateTooltipTitle();

        var cut = Init("somemadeupid");

        //content of first item is shown in the selected id
        TestHelpers.AssertFound(() => cut.Find($"#{TabId}-tabs-selected-content > #{TabItem1}-content"));
        //the selected tab item has the selected class
        TestHelpers.AssertFound(() => cut.Find($"#{TabItem1} cctc-tabitem-header.tab-item-header-selected"));
    }

    [Fact]
    public void NoPreselectionRendersFirstTabItem()
    {
        AddAddTooltip();
        AddUpdateTooltipTitle();

        var cut = Init();

        //content of first item is shown in the selected id
        TestHelpers.AssertFound(() => cut.Find($"#{TabId}-tabs-selected-content > #{TabItem1}-content"));
        //the selected tab item has the selected class
        TestHelpers.AssertFound(() => cut.Find($"#{TabItem1} cctc-tabitem-header.tab-item-header-selected"));
    }

}