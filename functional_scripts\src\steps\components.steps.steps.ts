import { ICustomWorld } from '../support/custom-world';
import { compareToBaseImage } from '../utils/compareImages';
import * as Helpers from '../support/helper-functions';
import { When, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';

Then('the {string} step is selected', async function (this: ICustomWorld, step: string) {
  const stepSelector = Helpers.getSamplerTabsSelectedContent(this)
    .locator('cctc-steps')
    .locator('cctc-steps-headers')
    .locator('cctc-progressstep.selected');
  await expect(stepSelector).toContainText(step);
});

Then('the {string} step is not selected', async function (this: ICustomWorld, step: string) {
  const stepSelector = Helpers.getSamplerTabsSelectedContent(this)
    .locator('cctc-steps')
    .locator('cctc-steps-headers')
    .locator('cctc-progressstep')
    .filter({ hasText: step });
  await expect(stepSelector).not.toHaveClass('selected');
});

Then('the previous button is disabled', async function (this: ICustomWorld) {
  const prevButton = Helpers.getSamplerTabsSelectedContent(this)
    .locator('cctc-steps')
    .locator('cctc-steps-controls')
    .locator('.prev-control');
  await expect(prevButton).toHaveClass(/button-disabled/);
});

Then('the next button is disabled', async function (this: ICustomWorld) {
  const nextButton = Helpers.getSamplerTabsSelectedContent(this)
    .locator('cctc-steps')
    .locator('cctc-steps-controls')
    .locator('.next-control');
  await expect(nextButton).toHaveClass(/button-disabled/);
});

When('the previous button is enabled', async function (this: ICustomWorld) {
  const prevButton = Helpers.getSamplerTabsSelectedContent(this)
    .locator('cctc-steps')
    .locator('cctc-steps-controls')
    .locator('.prev-control');
  await expect(prevButton).toHaveClass(/button-enabled/);
});

When('the next button is enabled', async function (this: ICustomWorld) {
  const nextButton = Helpers.getSamplerTabsSelectedContent(this)
    .locator('cctc-steps')
    .locator('cctc-steps-controls')
    .locator('.next-control');
  await expect(nextButton).toHaveClass(/button-enabled/);
});

When('the user clicks on the next/done button', async function (this: ICustomWorld) {
  await Helpers.getSamplerTabsSelectedContent(this).locator('cctc-steps')
    .locator('cctc-steps-controls')
    .locator('.next-control')
    .click();
});

When('the user clicks on the previous button', async function (this: ICustomWorld) {
  await Helpers.getSamplerTabsSelectedContent(this).locator('cctc-steps')
    .locator('cctc-steps-controls')
    .locator('.prev-control')
    .click();
});

Then('the progress step content contains {string}', async function (this: ICustomWorld, status: string) {
  const selectedElement = Helpers.getSamplerTabsSelectedContent(this)
    .locator('cctc-steps')
    .locator('cctc-steps-current-content');
  await expect(selectedElement).toContainText(status);
});

Then(
  'the steps headers image matches the base image {string}',
  async function (this: ICustomWorld, name: string) {
    const screenshot = await Helpers.tryGetStableScreenshot(
      Helpers.getSamplerTabsSelectedContent(this).locator('cctc-steps')
        .locator('cctc-steps-headers')
    );
    await compareToBaseImage(this, name, screenshot);
  }
);

Then(
  'the steps component image matches the base image {string}',
  async function (this: ICustomWorld, name: string) {
    const screenshot = await Helpers.tryGetStableScreenshot(
      Helpers.getSamplerTabsSelectedContent(this).locator('cctc-steps')
    );
    await compareToBaseImage(this, name, screenshot);
  }
);

Then('the header font size is {string}', async function (this: ICustomWorld, stepsHeaderFontSize: string) {
  const headerFontSize = Helpers.getSamplerTabsSelectedContent(this).locator('cctc-steps cctc-steps-headers cctc-progressstep'
  ).locator('.selected').first();
  await expect(headerFontSize).toHaveCSS('font-size', stepsHeaderFontSize);
});

Then('the header superscript font size is {string}', async function (this: ICustomWorld, stepsHeaderFontSize: string) {
  const headerFontSize = Helpers.getSamplerTabsSelectedContent(this).locator('cctc-steps cctc-steps-headers cctc-progressstep sup.selected'
  );
  await expect(headerFontSize).toHaveCSS('font-size', stepsHeaderFontSize);
});

Then('the selected header superscript is {string}', async function (this: ICustomWorld, superscriptText: string) {
  const headerSuperscriptText = Helpers.getSamplerTabsSelectedContent(this).locator('cctc-steps cctc-steps-headers cctc-progressstep sup.selected'
  );
  await expect(headerSuperscriptText).toHaveText(superscriptText);
});

Then('the steps buttons are at the top',
  async function (this: ICustomWorld) {
    const stepsLocator = Helpers.getSamplerTabsSelectedContent(this).locator('cctc-steps');
    await expect(stepsLocator.locator('cctc-steps-controls:first-child')).toHaveCount(1);
  }
);

Then('the steps buttons are at the bottom',
  async function (this: ICustomWorld) {
    const stepsLocator = Helpers.getSamplerTabsSelectedContent(this).locator('cctc-steps');
    await expect(stepsLocator.locator('cctc-steps-controls:last-child')).toHaveCount(1);
  }
);
