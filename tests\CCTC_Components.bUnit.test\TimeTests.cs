﻿using CCTC_Components.Components.Temporal;
using CCTC_Components.Components.TextBox;
using Microsoft.AspNetCore.Components;

namespace CCTC_Components.bUnit.test
{
    public class TimeTests : TestContext
    {
        [Theory]
        [MemberData(nameof(GetTimeCallbackTestData))]
        public async Task Time_Raises_Correct_callbacks(bool allowClear, string inputValue, bool invocationExpected, TimeOnly? expectedValue = null)
        {
            var testSchedulers = new TestSchedulers();
            Services.AddTestSchedulers(sp => testSchedulers);

            int componentDefaultThrottleMs = 500;
            string dateFormat = "HH:mm:ss";
            var mockDummyService = new Mock<IDummyService>();

            var cut = RenderComponent<Time<TimeOnly?>>(parameters => parameters
                .Add(p => p.DateFormat, dateFormat)
                .Add(p => p.ValueChanged, async args => await mockDummyService.Object.MethodSixAsync(args))
                .Add(p => p.TimeChanged, async args => await mockDummyService.Object.MethodSevenAsync(args))
                .Add(p => p.MinTime, new TimeOnly(13, 15, 22))
                .Add(p => p.MaxTime, new TimeOnly(14, 20, 30))
                .Add(p => p.ThrottleMs, componentDefaultThrottleMs)
                .Add(p => p.AllowClear, allowClear)
            );

            var textComponent = cut.FindComponent<Text>();
            await textComponent.InvokeAsync(() =>
            {
                textComponent.Find("input").Input(inputValue);
                testSchedulers.Default.AdvanceBy(TimeSpan.FromMilliseconds(componentDefaultThrottleMs).Ticks);
            });

            var expectedValueString = expectedValue?.ToString(dateFormat);
            mockDummyService.Verify(m => m.MethodSixAsync(expectedValue), invocationExpected ? Times.Once() : Times.Never());
            mockDummyService.Verify(m => m.MethodSevenAsync(It.Is<ChangeEventArgs>(args => (string?)args.Value == expectedValueString)), invocationExpected ? Times.Once() : Times.Never());
        }

        [Theory]
        [InlineData(false, "11:05:31", "HH:mm:ss", "material-icons.check", 1, FeedbackIcon.Both)]
        [InlineData(false, "11:05:31", "HH:mm:ss", "material-icons.check", 1, FeedbackIcon.Valid)]
        [InlineData(false, "11:05:31", "HH:mm:ss", "material-icons.check", 0, FeedbackIcon.None)]
        [InlineData(false, "11:05:31", "HH:mm:ss", "material-icons.check", 0, null)]
        [InlineData(false, "11:05:29", "HH:mm:ss", "material-icons.priority_high", 1, FeedbackIcon.Both)]
        [InlineData(false, "13:10:51", "HH:mm:ss", "material-icons.priority_high", 1, FeedbackIcon.Error)]
        [InlineData(false, "13:10:51", "HH:mm:ss", "material-icons.priority_high", 0, FeedbackIcon.None)]
        [InlineData(false, "13", "HH:mm:ss", "material-icons.priority_high", 1, null)]
        [InlineData(false, "11:05:23", "h:mm tt", "material-icons.priority_high", 1, FeedbackIcon.Both)]
        [InlineData(false, "11:05:23", "h:mm tt", "material-icons.priority_high", 1, FeedbackIcon.Error)]
        [InlineData(false, "11:05:23", "h:mm tt", "material-icons.priority_high", 0, FeedbackIcon.None)]
        [InlineData(false, "", null, "material-icons.check", 0, FeedbackIcon.Both)]
        [InlineData(false, "", null, "material-icons.check", 0, FeedbackIcon.Valid)]
        [InlineData(false, "", null, "material-icons.check", 0, FeedbackIcon.None)]
        [InlineData(true, "", null, "material-icons.check", 0, FeedbackIcon.Both)]
        [InlineData(true, "", null, "material-icons.check", 0, FeedbackIcon.Valid)]
        [InlineData(true, "", null, "material-icons.check", 0, FeedbackIcon.None)]
        [InlineData(false, "", null, "material-icons.priority_high", 1, FeedbackIcon.Both)]
        [InlineData(false, "", null, "material-icons.priority_high", 0, FeedbackIcon.Valid)]
        [InlineData(false, "", null, "material-icons.priority_high", 0, FeedbackIcon.None)]
        [InlineData(true, "", null, "material-icons.priority_high", 0, FeedbackIcon.Both)]
        [InlineData(true, "", null, "material-icons.priority_high", 0, FeedbackIcon.Valid)]
        [InlineData(true, "", null, "material-icons.priority_high", 0, FeedbackIcon.None)]
        public async Task Time_Displays_Correct_Validation_Icon(bool allowClear, string inputValue, string? dateFormat, string? expectedCssClass,
            int cssClassCount, FeedbackIcon? feedbackIcon)
        {
            var testSchedulers = new TestSchedulers();
            Services.AddTestSchedulers(sp => testSchedulers);

            int componentDefaultThrottleMs = 500;

            var cut = RenderComponent<Time<TimeOnly?>>(parameters => parameters
                .Add(p => p.DateFormat, dateFormat)
                .Add(p => p.Value, new TimeOnly(1, 5, 14))
                .Add(p => p.MinTime, new TimeOnly(11, 5, 30))
                .Add(p => p.MaxTime, new TimeOnly(13, 10, 50))
                .Add(p => p.FeedbackIcon, feedbackIcon)
                .Add(p => p.AllowClear, allowClear)
            );

            var textComponent = cut.FindComponent<Text>();
            await textComponent.InvokeAsync(() =>
            {
                textComponent.Find("input").Input(inputValue);
                testSchedulers.Default.AdvanceBy(TimeSpan.FromMilliseconds(componentDefaultThrottleMs).Ticks);
            });


            var validationIcon = cut.FindAll($"i.{expectedCssClass}");
            Assert.Equal(cssClassCount, validationIcon.Count);
        }

        [Fact]
        public void TimeActiveAttributesConfiguredCorrectly()
        {
            Services.AddTestSchedulers();

            var cut = RenderComponent<Time<TimeOnly>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.CssClass, "w-50")
                .Add(p => p.Style, "color: green;")
                .Add(p => p.ReadOnly, false)
                .Add(p => p.Disabled, false)
                .Add(p => p.MinTime, new TimeOnly(13, 5, 30))
                .Add(p => p.MaxTime, new TimeOnly(15, 10, 50))
            );

            var timeWrapperElement = cut.Find("cctc-input[data-cctc-input-type=\"time\"]");

            var expectedTimeWrapperAttributes = new Dictionary<string, string?>()
            {
                { "id", "test-id" },
                { "class", "w-50" },
                { "style", "color: green;" },
                { "data-author", "cctc" }
            };

            var actualTimeWrapperAttributes = new Dictionary<string, string?>()
            {
                { "id", timeWrapperElement.Id },
                { "class", timeWrapperElement.ClassName },
                { "style", timeWrapperElement.GetAttribute("style") },
                { "data-author", timeWrapperElement.GetAttribute("data-author") }
            };

            Assert.Equal(expectedTimeWrapperAttributes, actualTimeWrapperAttributes);
        }

        [Theory]
        [InlineData(true, true)]
        [InlineData(true, false)]
        [InlineData(false, true)]
        public void TimeInactiveAttributesConfiguredCorrectly(bool disabled, bool readOnly)
        {
            Services.AddTestSchedulers();

            var cut = RenderComponent<Time<TimeOnly>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.CssClass, "w-50")
                .Add(p => p.Style, "color: green;")
                .Add(p => p.ReadOnly, readOnly)
                .Add(p => p.Disabled, disabled)
            );

            var timeWrapperElement = cut.Find("cctc-input[data-cctc-input-type=\"time\"]");
            var validationWrapper = cut.FindAll(".validation-wrapper");

            var expectedTimeWrapperAttributes = new Dictionary<string, string?>()
            {
                { "id", "test-id" },
                { "class", "w-50" },
                { "style", "color: green;" },
                { "data-author", "cctc" }
            };

            var actualTimeWrapperAttributes = new Dictionary<string, string?>()
            {
                { "id", timeWrapperElement.Id },
                { "class", timeWrapperElement.ClassName },
                { "style", timeWrapperElement.GetAttribute("style") },
                { "data-author", timeWrapperElement.GetAttribute("data-author") }
            };

            Assert.Equal(expectedTimeWrapperAttributes, actualTimeWrapperAttributes);
            Assert.Equal(0, validationWrapper.Count);
        }

        [Fact]
        public void InvalidTypeParameterThrowsArgumentException()
        {
            Services.AddTestSchedulers();

            var cut = () => RenderComponent<Time<int>>(parameters => parameters
                .Add(p => p.Id, "test-id")
            );

            var actual = Assert.Throws<ArgumentException>(cut);
            string expectedParamName = "TValue";
            string expectedMessage = $"Expected a timeonly or nullable timeonly type (Parameter 'TValue')";
            Assert.Equal(expectedParamName, actual.ParamName);
            Assert.Equal(expectedMessage, actual.Message);
        }

        [Fact]
        public void AllowClearWithNonNullableValueTypeThrowsArgumentException()
        {
            Services.AddTestSchedulers();

            var cut = () => RenderComponent<Time<TimeOnly>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.AllowClear, true)
            );

            var actual = Assert.Throws<ArgumentException>(cut);
            string expectedParamName = "AllowClear";
            string expectedMessage = $"AllowClear should only be set to true when TValue is of type nullable timeonly (Parameter 'AllowClear')";
            Assert.Equal(expectedParamName, actual.ParamName);
            Assert.Equal(expectedMessage, actual.Message);
        }

        [Fact]
        public void AllowClearWithNullableValueTypeDoesNotThrowArgumentException()
        {
            Services.AddTestSchedulers();

            var cut = RenderComponent<Time<TimeOnly?>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.AllowClear, true)
            );
        }

        /// <summary>
        /// Generates test data for callbacks triggered by changing time input
        /// </summary>
        /// <returns>An IEnumerable of object arrays, one array for each test, comprising of allowClear, inputValue, invocationExpected, expectedValue (optional)</returns>
        public static IEnumerable<object?[]> GetTimeCallbackTestData()
        {
            yield return new object?[] { false, "13:15:22", true, new TimeOnly(13, 15, 22) };
            yield return new object?[] { false, "not a time", false };
            yield return new object?[] { false, "13:15:21", false };
            yield return new object?[] { false, "14:20:31", false };
            yield return new object?[] { false, "", false };
            yield return new object?[] { true, "", true, null };
        }
    }
}
