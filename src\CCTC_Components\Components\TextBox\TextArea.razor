﻿@using CCTC_Lib.Enums.UI
@inherits TextBase

@namespace CCTC_Components.Components.TextBox

<cctc-input data-cctc-input-type="textarea" id="@Id" class="@CssClass" style="@Style" data-author="cctc">
    <div class="component-wrapper">
        <textarea
            @attributes="InputAttributes"
            id="@Id-input"
            @ref=ElementRef
            value="@TextValue"
            @onchange="@(args => OnValueChanged(args, BindEvent.OnChange))"
            @oninput="@(args => OnValueChanged(args, BindEvent.OnInput))"
            @onfocus="OnFocus"
            @onblur="OnBlur"
            disabled="@Disabled"
            readonly="@ReadOnly"
            maxlength="@(MaxLength > 0 ? MaxLength : null)"
            placeholder="@Placeholder"
            autocomplete="off"
            rows="@Rows">
        </textarea>
    @if (ReadOnly && !HideReadOnlyIcon)
    {
        <div class="icon-wrapper">
            <span class="material-icons">
                lock
            </span>
        </div>
    }
    </div>
</cctc-input>

@code {

    /// <summary>
    /// Set the initial number of display rows
    /// </summary>
    [Parameter]
    public int Rows { get; set; } = 2;

    //ignoring the cols property as doesn't really improve anything
    //whilst height can be set in css, using rows is a quick and easy way of setting a default height
}