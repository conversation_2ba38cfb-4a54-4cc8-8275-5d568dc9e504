@component @confirmmodal @confirmmodal_3

Feature: the confirm modal window has named text that can wrap
    <PERSON>enario: the confirm modal window has text that can wrap
        Given the user is at the home page
        And the user selects the "Confirm modal" component in the container "Modals"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Customised positive and negative responses, Modal options classes: UI theme plus custom class defining a max width"
        When the confirm modal button is clicked
        Then the confirm modal text is "Are you sure you want to make this change? This question is quite long too to see what it looks like"
        And the confirm modal component image matches the base image "Confirm Modal Box with wrapped header and text"
    
