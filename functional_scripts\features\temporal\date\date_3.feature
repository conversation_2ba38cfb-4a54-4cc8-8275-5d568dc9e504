@component @temporal @date @date_3
Feature: the date component date is validated (date format, min and / or max date) and there is feedback provided to the user via an icon
    Scenario: an incomplete date is shown as an error
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: default, FeedbackIcon.Error"
        When the user enters "2021010" into the Date component
        Then the Date component has the value "2021-01-0"
        And the Date component displays a red exclamation mark feedback icon
        And the Date component image matches the base image "date-error"

    Scenario: a complete date is shown as valid
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: dd MMM yy, FeedbackIcon.Valid"
        When the user enters "06May22" into the Date component
        Then the Date component has the value "06 May 22"
        And the Date component displays a green tick feedback icon
        And the Date component image matches the base image "date-valid"

    Scenario: a date earlier than the minimum date is shown as an error
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: dd/MM/yyyy, Min (11/12/2023) and Max (16/12/2023), FeedbackIcon.Both"
        When the user enters "10122023" into the Date component
        Then the Date component has the value "10/12/2023"
        And the Date component displays a red exclamation mark feedback icon

    Scenario: a date later than the maximum date is shown as an error
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: dd/MM/yyyy, Min (11/12/2023) and Max (16/12/2023), FeedbackIcon.Both"
        When the user enters "17122023" into the Date component
        Then the Date component has the value "17/12/2023"
        And the Date component displays a red exclamation mark feedback icon
