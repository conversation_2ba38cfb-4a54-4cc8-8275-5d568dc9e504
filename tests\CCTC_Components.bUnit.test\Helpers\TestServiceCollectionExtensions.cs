﻿using Blazored.Modal;
using CCTC_Lib.Contracts.Interaction;
using CCTC_Lib.Contracts.Reactive;
using CCTC_Lib.Contracts.UI;
using Microsoft.Extensions.DependencyInjection;
using TextCopy;

namespace CCTC_Components.bUnit.test.Helpers
{
    /// <summary>
    /// Used for adding service extensions only. For including js interop, use the
    /// CCTCComponentsTestContext class as the base text context
    /// </summary>
    public static class TestServiceCollectionExtensions
    {
        public static IServiceCollection AddTestContextServices(this IServiceCollection services)
        {
            if (services is null)
            {
                throw new ArgumentNullException(nameof(services));
            }

            services.AddScoped<IDelayService, NoDelayService>();
            services.AddScoped<IClipboard, Clipboard>();
            services.AddBlazoredModal();

            return services;
        }

        public static IServiceCollection AddTestSchedulers(this IServiceCollection services)
        {
            services.AddScoped<ISchedulerProvider, TestSchedulers>();
            return services;
        }

        public static IServiceCollection AddTestSchedulers(this IServiceCollection services, Func<IServiceProvider, TestSchedulers> testSchedulers)
        {
            services.AddScoped<ISchedulerProvider>(testSchedulers);
            return services;
        }

        public static IServiceCollection AddBrowserName(this IServiceCollection services, string browserName)
        {
            var mockBrowserNameProvider = new Mock<IBrowserNameProvider>();
            mockBrowserNameProvider.Setup(m => m.BrowserName).Returns(browserName);
            services.AddSingleton(mockBrowserNameProvider.Object);

            return services;
        }
    }
}