﻿using Microsoft.AspNetCore.Components;

namespace CCTC_Components.Components.PanelMenu;

/// <summary>
/// Used by the PanelMenu to determine how the panel menu items are matched to the uri
/// </summary>
/// <remarks>Location is the uri, path is the path of the panel menu item</remarks>
public static class PanelMenuMatches
{
    static NavigationManager? _navigationManager;

    /// <summary>
    /// Initialises the class
    /// </summary>
    /// <param name="navigationManager">The navigation manager</param>
    /// <remarks>Throws an exception if attempting to use the StartsWithMatchPred and this method is not called</remarks>
    public static void Init(NavigationManager navigationManager)
    {
        _navigationManager = navigationManager;
    }

    /// <summary>
    /// Default match
    /// </summary>
    public static Predicate<(string location, string path)> Default => ContainsMatchPred;

    /// <summary>
    /// Any part of the location contains the path
    /// </summary>
    public static readonly Predicate<(string location, string path)> ContainsMatchPred = tup => tup.location.Contains(tup.path);

    /// <summary>
    /// The location ends with the path
    /// </summary>
    public static readonly Predicate<(string location, string path)> EndsWithMatchPred = tup => tup.location.EndsWith(tup.path);

    /// <summary>
    /// The location starts with the path
    /// </summary>
    /// <param name="tup">The location, path for the predicate</param>
    /// <returns>true if matches</returns>
    /// <exception cref="InvalidOperationException">Throws if the class is not initialised</exception>
    public static bool StartsWithMatchPred((string location, string path) tup)
    {
        if (_navigationManager is null)
        {
            throw new InvalidOperationException("you need to initialise first");
        }

        var targetLocation = tup.location;

        var replacedBase = targetLocation.Replace(_navigationManager.BaseUri, string.Empty);
        var cleanTargetLocation =
            targetLocation == "/" || targetLocation == "" || targetLocation == _navigationManager.BaseUri
                ? "/"
                : replacedBase.StartsWith("/")
                    ? replacedBase[1..]
                    : replacedBase;

        return cleanTargetLocation.StartsWith(tup.path);
    }
}