@component @tabs @tabs_4
Feature: the tab header tooltips show when the tab header text is truncated
    Scenario: the content box can be different sizes in each tab
        Given the user is at the home page
        When the user selects the "Tabs" component in the container "Tabs"
        And the user clicks the "Example" tab
        And the user clicks the "5 Another longer header that will truncate" tab  
        Then the tab with text "5 Another longer header that will truncate" has a tooltip containing the full header text

