﻿namespace CCTC_Components.bUnit.test.Helpers
{
    public class DummyService : IDummyService
    {
        public async Task MethodOneAsync()
        {
            await Task.CompletedTask;
        }

        public async Task MethodTwoAsync()
        {
            await Task.CompletedTask;
        }

        public async Task MethodThreeAsync()
        {
            await Task.CompletedTask;
        }

        public async Task MethodFourAsync()
        {
            await Task.CompletedTask;
        }

        public async Task MethodFiveAsync()
        {
            await Task.CompletedTask;
        }

        public async Task MethodSixAsync(object? args)
        {
            await Task.CompletedTask;
        }

        public async Task MethodSevenAsync(object? args)
        {
            await Task.CompletedTask;
        }

        public async Task MethodEightAsync(object? args1, object? args2)
        {
            await Task.CompletedTask;
        }
    }
}
