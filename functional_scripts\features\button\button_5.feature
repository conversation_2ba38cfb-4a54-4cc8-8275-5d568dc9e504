@component @button @button_5
Feature: the button can have a border or no border
    Scenario: the button component can have a border
        Given the user is at the home page
        And the user selects the "Button" component in the container "Buttons"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "ButtonIcon: circle, IconPosition: Left, styled with border"
        Then the button component has a border
        And the button component image matches the base image "button with a border"

    Scenario: the button component can have no border
        Given the user is at the home page
        And the user selects the "Button" component in the container "Buttons"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "ButtonIcon: light_mode, IconPosition: Right, overridden icon color via Style"
        Then the button component has no border 
        And the button component image matches the base image "button with no border"
