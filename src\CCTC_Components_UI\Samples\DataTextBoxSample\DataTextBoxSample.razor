﻿@page "/datatextboxsample"
@using CCTC_Lib.Contracts.Data
@using CCTC_Lib.Enums.Data
@using CCTC_Components_UI.Helpers.Models
@using CCTC_Components_UI.Helpers.Services
@using Common.Services.Neo4jDataAccess

@{
    var description = new List<string>
    {
        "The DataTextBox can be used to tunnel into a tree-like collection by selecting items and presenting the user a further selection based on the item chosen. " +
        "This allows a user to follow a 'path' through the data and home in on their final selection. Progress can be controlled by the <code>IncludeGroupingStep</code>, " +
        "option; this optionally allows a preselection step where child items are initially grouped by 'type'. ",
        "As items are presented, they can be filtered to further refine the items to select from.",
    };


    var features = new List<(string, string)>
    {
        ("Use of Lister component", "The component provides a drop down of options using the Lister component. This provides much of the navigation functionality."),
        ("IncludeGroupingStep", "When true, the user is presented with a distinct list of the 'types' of children in order to pre-refine their options. " +
                                "When false, all children are shown and the 'types' step is omitted."),
        ("Keyboard interaction", "Refer to the Keyboard controls tab"),
    };

    var gotchas = new List<(string, string)>
    {
        ("Keyboard navigation in Lister:", "There are known issues when navigating with the keyboard for the lister component"),
    };

    var usageCode = new List<(string title, string description, string code, RenderFragment fragment)>
    {
        ("Including grouping", "",
            @"<DataTextBox
    Id=""datatextbox-example""
    TData=""NamedNodeIdentifier""
    TKey=""Node""
    @ref=""_dataTextBox""
    DataTextBoxDataService=""@_demoDataService""
    InitialValue=""@_demoDataService!.Data!.Data""
    IncludeGroupingStep=""true""
    DisplayFunc=""@(node => node.AsIdAndNameParts())""
    DisplayChildTypesFunc=""@(node => node.AsLabel())""
    ListerItemHeightPixels=""30""
    OnSelectionComplete=""@(node => _finalSelectedItem = node)""
    QueryHistory=""@GetQueryHistory"">
</DataTextBox>

@code {
    //this implementation will use the local demo data. A real implementation will call into the db
    class DemoDataTextBoxDataService : IDataTextBoxDataService<NamedNodeIdentifier, Node>
    {
        List<Node>? _currentLoadedChildTypes;
        List<NamedNodeIdentifier>? _currentLoadedChildren;

        public TreeNode<NamedNodeIdentifier>? Data { get; }

        public DemoDataTextBoxDataService(TreeNode<NamedNodeIdentifier>? data)
        {
            Data ??=
                data
                ?? throw new ArgumentNullException(nameof(data), ""need some data for the demo!"");
        }

        public Node GetTypeKey(NamedNodeIdentifier item) => item.Node;

        public Task<List<Node>> GetDistinctChildTypes(NamedNodeIdentifier parent)
        {
            using var enumerator = Data!.GetEnumerator();
            while (enumerator.MoveNext())
            {
                if (enumerator.Current.Data.Key() == parent.Key())
                {
                    _currentLoadedChildTypes =
                        enumerator.Current
                            .Children
                            .Select(x => GetTypeKey(x.Data))
                            .Distinct()
                            .ToList();
                    return Task.FromResult(_currentLoadedChildTypes);
                }
            }

            return Task.FromResult(new List<Node>());
        }

        public Task<List<NamedNodeIdentifier>> GetChildren(NamedNodeIdentifier parent)
        {
            return GetChildren(parent, null);
        }

        public Task<List<NamedNodeIdentifier>> GetChildren(NamedNodeIdentifier parent, Node? childType)
        {
            using var enumerator = Data!.GetEnumerator();
            while (enumerator.MoveNext())
            {
                if (enumerator.Current.Data.Key() == parent.Key())
                {
                    _currentLoadedChildren =
                        enumerator.Current
                            .Children
                            .Where(x => childType is null || GetTypeKey(x.Data).Equals(childType))
                            .Select(x => x.Data)
                            .ToList();

                    return Task.FromResult(_currentLoadedChildren);
                }
            }

            return Task.FromResult(new List<NamedNodeIdentifier>());
        }

        public Task<List<Node>> FilterChildTypes(Predicate<Node> filter)
        {
            return
                Task.FromResult(_currentLoadedChildTypes is null
                    ? new List<Node>()
                    : _currentLoadedChildTypes
                        .Where(x => filter(x))
                        .ToList());
        }

        public Task<List<NamedNodeIdentifier>> FilterChildren(Predicate<Node> filter)
        {
            throw new NotImplementedException();
        }
    }

    protected override void OnInitialized()
    {
        _demoDataService = new DemoDataTextBoxDataService(DataTextBoxSampleData.CreateDataNode());
    }
}",
            @<DataTextBox
                Id="datatextbox-example"
                TData="NamedNodeIdentifier"
                TKey="Node"
                @ref="_dataTextBox"
                DataTextBoxDataService="@_demoDataService"
                InitialValue="@_demoDataService!.Data!.Data"
                IncludeGroupingStep="true"
                DisplayFunc="@(node => node.AsIdAndNameParts())"
                DisplayChildTypesFunc="@(node => node.AsLabel())"
                ListerItemHeightPixels="30"
                OnSelectionComplete="@(node => _finalSelectedItem = node)"
                QueryHistory="@GetQueryHistory">
           </DataTextBox>),

        ("Without grouping", "",
            @"<DataTextBox
    Id=""datatextbox-example""
    TData=""NamedNodeIdentifier""
    TKey=""Node""
    @ref=""_dataTextBox""
    DataTextBoxDataService=""@_demoDataService""
    InitialValue=""@_demoDataService!.Data!.Data""
    IncludeGroupingStep=""false""
    DisplayFunc=""@(node => node.AsIdAndNameParts())""
    DisplayChildTypesFunc=""@(node => node.AsLabel())""
    ListerItemHeightPixels=""30""
    OnSelectionComplete=""@(node => _finalSelectedItem = node)""
    QueryHistory=""@GetQueryHistory"">
</DataTextBox>

@code {
    //this implementation will use the local demo data. A real implementation will call into the db
    class DemoDataTextBoxDataService : IDataTextBoxDataService<NamedNodeIdentifier, Node>
    {
        List<Node>? _currentLoadedChildTypes;
        List<NamedNodeIdentifier>? _currentLoadedChildren;

        public TreeNode<NamedNodeIdentifier>? Data { get; }

        public DemoDataTextBoxDataService(TreeNode<NamedNodeIdentifier>? data)
        {
            Data ??=
                data
                ?? throw new ArgumentNullException(nameof(data), ""need some data for the demo!"");
        }

        public Node GetTypeKey(NamedNodeIdentifier item) => item.Node;

        public Task<List<Node>> GetDistinctChildTypes(NamedNodeIdentifier parent)
        {
            using var enumerator = Data!.GetEnumerator();
            while (enumerator.MoveNext())
            {
                if (enumerator.Current.Data.Key() == parent.Key())
                {
                    _currentLoadedChildTypes =
                        enumerator.Current
                            .Children
                            .Select(x => GetTypeKey(x.Data))
                            .Distinct()
                            .ToList();
                    return Task.FromResult(_currentLoadedChildTypes);
                }
            }

            return Task.FromResult(new List<Node>());
        }

        public Task<List<NamedNodeIdentifier>> GetChildren(NamedNodeIdentifier parent)
        {
            return GetChildren(parent, null);
        }

        public Task<List<NamedNodeIdentifier>> GetChildren(NamedNodeIdentifier parent, Node? childType)
        {
            using var enumerator = Data!.GetEnumerator();
            while (enumerator.MoveNext())
            {
                if (enumerator.Current.Data.Key() == parent.Key())
                {
                    _currentLoadedChildren =
                        enumerator.Current
                            .Children
                            .Where(x => childType is null || GetTypeKey(x.Data).Equals(childType))
                            .Select(x => x.Data)
                            .ToList();

                    return Task.FromResult(_currentLoadedChildren);
                }
            }

            return Task.FromResult(new List<NamedNodeIdentifier>());
        }

        public Task<List<Node>> FilterChildTypes(Predicate<Node> filter)
        {
            return
                Task.FromResult(_currentLoadedChildTypes is null
                    ? new List<Node>()
                    : _currentLoadedChildTypes
                        .Where(x => filter(x))
                        .ToList());
        }

        public Task<List<NamedNodeIdentifier>> FilterChildren(Predicate<Node> filter)
        {
            throw new NotImplementedException();
        }
    }

    protected override void OnInitialized()
    {
        _demoDataService = new DemoDataTextBoxDataService(DataTextBoxSampleData.CreateDataNode());
    }
}",
            @<DataTextBox
                Id="datatextbox-example"
                TData="NamedNodeIdentifier"
                TKey="Node"
                @ref="_dataTextBox"
                DataTextBoxDataService="@_demoDataService"
                InitialValue="@_demoDataService!.Data!.Data"
                IncludeGroupingStep="false"
                DisplayFunc="@(node => node.AsIdAndNameParts())"
                DisplayChildTypesFunc="@(node => node.AsLabel())"
                ListerItemHeightPixels="30"
                OnSelectionComplete="@(node => _finalSelectedItem = node)"
                QueryHistory="@GetQueryHistory">
           </DataTextBox>)
    };

    var subParts = new List<string>()
    {
        "-selected-item-@i",
        "-text-input",
        "-dropdown-childtypes-lister",
        "-dropdown-children-lister"
    };

    var keyboardControls = new List<string>
    {
        KeyboardOptions
    };
}

<div required-so-deep-works>
    @if (_demoDataService is not null)
    {
        <Sampler
            ComponentName="DataTextBox"
            ComponentCssName="datatextbox"
            Description="@description"
            UsageText="Typical usages of the <code>DataTextBox</code> component are shown below"
            UsageCodeList="@usageCode"
            SubParts="subParts"
            KeyboardControls="@keyboardControls"
            ContentHeightPixels="500"
            Features="@features"
            Gotchas="@gotchas">
            <ExampleTemplate>
                <div class="d-flex gap-2">
                    <div>Include grouping:</div>
                    <DropDown
                        TData="string"
                        Id="lister-sampler-sortby"
                        Style="width: 4rem;"
                        Data="@(new List<string> { "Yes", "No" })"
                        Value="@_includeGrouping"
                        ValueChanged="@(newVal => ChangeIncludeGrouping(newVal))">
                    </DropDown>
                </div>
                <hr/>
                <div>Selected item: <em>@(_finalSelectedItem is null ? "no selection made" : _finalSelectedItem)</em></div>
                <hr/>
                <div class="tab-item-content">
                    <DataTextBox
                        Id="datatextbox-example"
                        TData="NamedNodeIdentifier"
                        TKey="Node"
                        @ref="_dataTextBox"
                        DataTextBoxDataService="@_demoDataService"
                        InitialValue="@_demoDataService!.Data!.Data"
                        IncludeGroupingStep="@(_includeGrouping == "Yes")"
                        DisplayFunc="@(node => node.AsIdAndNameParts())"
                        DisplayChildTypesFunc="@(node => node.AsLabel())"
                        ListerItemHeightPixels="30"
                        OnSelectionComplete="@(node => _finalSelectedItem = node)"
                        QueryHistory="@(args => GetQueryHistory(args))">
                    </DataTextBox>
                </div>
            </ExampleTemplate>
        </Sampler>
    }
</div>

@code {

    const string KeyboardOptions =
    "<div class=\"d-flex flex-column\">" +
       "<small class=\"mt-1\"><strong>With focus on a selected item</strong></small>" +
       "<small>-- Arrow left | Tab - <em>move to previous selection item</em></small>" +
       "<small>-- Arrow right | Shift-Tab - <em>move to the next selection item or focus on Text input when last selection item</em></small>" +
       "<small>-- Arrow down | Enter - <em>focus on items list for this selected item</em></small>" +
       "<small>-- Backspace | Delete - <em>deleted selected items back to the select item in focus</em></small>" +
       "<small class=\"mt-1\"><strong>With focus on text input and no filtering applied</strong></small>" +
       "<small>-- Arrow left | Tab- <em>move to the latest selected item</em></small>" +
       "<small>-- Backspace - <em>remove the latest selected item and focus on the new latest item</em></small>" +
       "<small>-- Arrow down - <em>focus on the items list ready for keyboard navigation</em></small>" +
       "<small>-- Shift-Enter | Alt-Enter - <em>mark the latest selected items as the final item and return the value</em></small>" +
       "<small>-- Alt-0-9 - <em>jump to the selected item with the given index</em></small>" +
       "<small class=\"mt-1\"><strong>With focus on the listed items</strong></small>" +
       "<small>-- Enter | Tab (or mouse click) - <em>add the item in focus to the selection</em></small>" +
       "<small>-- Arrow up when on first listed item - <em>move focus to the text input</em></small>" +
       "</div>";

    NamedNodeIdentifier? _finalSelectedItem;
    DemoDataTextBoxDataService? _demoDataService;
    DataTextBox<NamedNodeIdentifier, Node>? _dataTextBox;
    string _includeGrouping = "Yes";

    async Task ChangeIncludeGrouping(string newVal)
    {
        if (newVal != _includeGrouping)
        {
            _includeGrouping = newVal;
            await _dataTextBox!.Initialise(_includeGrouping == "Yes");
        }

    }

    void GetQueryHistory(List<(NamedNodeIdentifier sourceData, TypeOfQuery queryType, Node? childParams)>? history)
    {
        Console.WriteLine("------------------------------------");
        if (history is not null)
        {
            foreach (var (sourceData, queryType, childParams) in history)
            {
                Console.WriteLine($"src: {sourceData}, qtype: {queryType}, child: {childParams}");
            }
        }
    }

    protected override void OnInitialized()
    {
        _demoDataService = new DemoDataTextBoxDataService(DataTextBoxSampleData.CreateDataNode());
    }

    //this implementation will use the local demo data. A real implementation will call into the db
    class DemoDataTextBoxDataService : IDataTextBoxDataService<NamedNodeIdentifier, Node>
    {
        List<Node>? _currentLoadedChildTypes;
        List<NamedNodeIdentifier>? _currentLoadedChildren;

        public TreeNode<NamedNodeIdentifier>? Data { get; }

        public DemoDataTextBoxDataService(TreeNode<NamedNodeIdentifier>? data)
        {
            Data ??=
                data
                ?? throw new ArgumentNullException(nameof(data), "need some data for the demo!");
        }

        public Node GetTypeKey(NamedNodeIdentifier item) => item.Node;

        public Task<List<Node>> GetDistinctChildTypes(NamedNodeIdentifier parent)
        {
            using var enumerator = Data!.GetEnumerator();
            while (enumerator.MoveNext())
            {
                if (enumerator.Current.Data.Key == parent.Key)
                {
                    _currentLoadedChildTypes =
                        enumerator.Current
                            .Children
                            .Select(x => GetTypeKey(x.Data))
                            .Distinct()
                            .ToList();
                    return Task.FromResult(_currentLoadedChildTypes);
                }
            }

            return Task.FromResult(new List<Node>());
        }

        public Task<List<NamedNodeIdentifier>> GetChildren(NamedNodeIdentifier parent)
        {
            return GetChildren(parent, null);
        }

        public Task<List<NamedNodeIdentifier>> GetChildren(NamedNodeIdentifier parent, Node? childType)
        {
            using var enumerator = Data!.GetEnumerator();
            while (enumerator.MoveNext())
            {
                if (enumerator.Current.Data.Key == parent.Key)
                {
                    _currentLoadedChildren =
                        enumerator.Current
                            .Children
                            .Where(x => childType is null || GetTypeKey(x.Data).Equals(childType))
                            .Select(x => x.Data)
                            .ToList();

                    return Task.FromResult(_currentLoadedChildren);
                }
            }

            return Task.FromResult(new List<NamedNodeIdentifier>());
        }

        public Task<List<Node>> FilterChildTypes(Predicate<Node> filter)
        {
            return
                Task.FromResult(_currentLoadedChildTypes is null
                    ? new List<Node>()
                    : _currentLoadedChildTypes
                        .Where(x => filter(x))
                        .ToList());
        }

        public Task<List<NamedNodeIdentifier>> FilterChildren(Predicate<Node> filter)
        {
            throw new NotImplementedException();
        }
    }
}