import { config } from '../support/config';
import { ICustomWorld } from '../support/custom-world';
import { Given, When, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';
import { Page } from '@playwright/test';

// Declare global type for our app readiness API
declare global {
  interface Window {
    CCTCAppReadiness?: {
      waitForAppReady: () => Promise<void>;
      setAppReady: () => void;
    };
  }
}

// TODO: use this for common, reusable steps that can be used throughout
// shouldn't just be one massive file though, can be broken down into multiple files as needed

/**
 * Shared navigation function that handles app readiness detection
 */
async function navigateAndWaitForAppReady(page: Page, url: string) {
  await page.goto(url);

  // Wait for app readiness signal
  console.log(`Waiting for app readiness signal...`);
  try {
    await page.evaluate(async (timeout) => {
      console.log('[Browser Console] Checking if app readiness API is available');

      // Calculate max iterations based on polling interval and timeout
      const pollingInterval = 250;
      const maxIterations = timeout === -1 ? Number.MAX_SAFE_INTEGER : Math.ceil(timeout / pollingInterval);

      // Poll for app readiness API with iteration counter
      let iterations = 0;
      while (!window.CCTCAppReadiness && iterations < maxIterations) {
        console.log('[Browser Console] Waiting for app readiness API to load... (attempt ' + (iterations + 1) + ')');
        await new Promise(resolve => setTimeout(resolve, pollingInterval));
        iterations++;
      }

      if (!window.CCTCAppReadiness) {
        console.log(`[Browser Console] App readiness API not found after ${iterations} attempts (${iterations * pollingInterval}ms)`);
        return false;
      }

      console.log('[Browser Console] App readiness API found, waiting for app ready signal');
      try {
        await window.CCTCAppReadiness.waitForAppReady();
        console.log('[Browser Console] App ready signal received');
        return true;
      } catch (error) {
        console.log(`[Browser Console] Error waiting for app ready signal: ${String(error)}`);
        return false;
      }
    }, config.DEFAULT_TIMEOUT);

    console.log('✅ App ready signal received successfully');
  } catch (err) {
    console.log(`⚠️ Error in app readiness check: ${String(err)}`);
    console.log('Falling back to DOM availability check');

    // Emergency fallback if script fails completely
    await page.waitForSelector('.main-section');
    console.log('✅ DOM availability check passed');
  }
}

Given('the page is {string}', async function (this: ICustomWorld, url: string) {
  const page = this.page!;
  await navigateAndWaitForAppReady(page, url);
});

Given('the user is at the home page', async function (this: ICustomWorld) {
  const page = this.page!;
  await navigateAndWaitForAppReady(page, config.BASE_URL);
});

When(
  'the user selects the {string} component',
  async function (this: ICustomWorld, menuItem: string) {
    const page = this.page!;
    await page.locator('cctc-panelmenuitem').getByText(menuItem, { exact: true }).click();
  }
);

When(
  'the user selects the {string} component in the container {string}',
  async function (this: ICustomWorld, menuItem: string, menuHeader: string) {
    const page = this.page!;
    await page.locator('.panelmenu-header-title').getByText(menuHeader).click();
    await page.locator('cctc-panelmenuitem').getByText(menuItem, { exact: true }).click();
  }
);

/**
 * Set the viewport dimensions. Should be set before navigating to the page
 * @param viewportWidth - The viewport width
 * @param viewportHeight - The viewport height
 */
When(
  'the viewport has a width of {int} and a height of {int}',
  async function (this: ICustomWorld, viewportWidth: number, viewportHeight: number) {
    await this.page!.setViewportSize({ width: viewportWidth, height: viewportHeight });
  }
);

Then('the page url is {string}', async function (this: ICustomWorld, url: string) {
  const page = this.page!;
  await expect(page).toHaveURL(url);
});

Then('the url ending is {string}', async function (this: ICustomWorld, urlEnding: string) {
  const page = this.page!;
  await expect(page).toHaveURL(config.BASE_URL + '/' + urlEnding);
});
