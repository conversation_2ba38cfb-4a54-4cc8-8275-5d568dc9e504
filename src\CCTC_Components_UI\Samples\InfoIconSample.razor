﻿@page "/infoiconsample"

@{
    var description = new List<string>
    {
        "A component for displaying an info icon"
    };

    var features = new List<(string, string)>
    {
        ("Info type", "Clicking the icon shows either a tooltip or a popover which can have an <code>OnClick</code> callback registered. The tooltip / popover placement is configurable"),
        ("Icon", "Provide an <code>ImageSource</code> (e.g. .svg, .png) or an <code>ImageIcon</code> (material icon e.g. circle). The icon size is configurable via the <code>Size</code> parameter")
    };

    var usageCode = new List<(string title, string description, string code, RenderFragment fragment)>
    {
        ("InfoType: Tooltip, Size: XSmall, image source (svg), InfoPlacement: Right, with OnClick callback", "",
@"<div class=""usage-wrapper"">
    <label for=""usage1-field"">Text input with info icon</label>
    <input type=""text"" id=""usage1-field"">
    <InfoIcon
        Id=""usage1""
        InfoType=""@InfoType.Tooltip""
        Content=""@TooltipMessage""
        ImageSource=""images/info_bsinfo_24dp.svg""
        Size=""Size.XSmall""
        InfoPlacement=""InfoPlacement.Right""
        OnClick=""@(() => Console.WriteLine(""usage 1 tooltip clicked""))"">
    </InfoIcon>
</div>

@code {

    string TooltipMessage { get; set; } = ""Tooltip message"";
}",
        @<div class="usage-wrapper">
        <label for="usage1-field">Text input with info icon</label>
            <input type="text" id="usage1-field">
            <InfoIcon
                Id="usage1"
                InfoType="@InfoType.Tooltip"
                Content="@TooltipMessage"
                ImageSource="images/info_bsinfo_24dp.svg"
                Size="Size.XSmall"
                InfoPlacement="InfoPlacement.Right"
                OnClick="@(() => Console.WriteLine("usage 1 tooltip clicked"))">
            </InfoIcon>
        </div>),
        ("InfoType: Popover, Size: XSmall, image source (svg), InfoPlacement: Right, with OnClick callback", "",
@"<div class=""usage-wrapper"">
    <label for=""usage2-field"">Text input with info icon</label>
    <input type=""text"" id=""usage2-field"">
    <InfoIcon
        Id=""usage2""
        InfoType=""@InfoType.Popover""
        Content=""@PopoverMessage""
        ImageSource=""images/info_bsinfo_24dp.svg""
        Size=""Size.XSmall""
        InfoPlacement=""InfoPlacement.Right""
        OnClick=""@(() => Console.WriteLine(""usage 2 popover clicked""))"">
    </InfoIcon>
</div>

@code {

    string PopoverMessage { get; set; } = ""Popover message"";
}",
        @<div class="usage-wrapper">
            <label for="usage2-field">Text input with info icon</label>
            <input type="text" id="usage2-field">
            <InfoIcon
                Id="usage2"
                InfoType="@InfoType.Popover"
                Content="@PopoverMessage"
                ImageSource="images/info_bsinfo_24dp.svg"
                Size="Size.XSmall"
                InfoPlacement="InfoPlacement.Right"
                OnClick="@(() => Console.WriteLine("usage 2 popover clicked"))">
            </InfoIcon>
        </div>),
        ("InfoType: Popover, Size: XSmall, image source (svg), InfoPlacement: Right, with title", "",
@"<div class=""usage-wrapper"">
    <label for=""usage3-field"">Text input with info icon</label>
    <input type=""text"" id=""usage3-field"">
    <InfoIcon
        Id=""usage3""
        InfoType=""@InfoType.Popover""
        Content=""@PopoverMessage""
        Title=""@PopoverTitle""
        ImageSource=""images/info_bsinfo_24dp.svg""
        Size=""Size.XSmall""
        InfoPlacement=""InfoPlacement.Right"">
    </InfoIcon>
</div>

@code {
    
    string PopoverMessage { get; set; } = ""Popover message"";

    string PopoverTitle { get; set; } = ""Popover title"";
}",
        @<div class="usage-wrapper">
            <label for="usage3-field">Text input with info icon</label>
            <input type="text" id="usage3-field">
            <InfoIcon
                Id="usage3"
                InfoType="@InfoType.Popover"
                Content="@PopoverMessage"
                Title="@PopoverTitle"
                ImageSource="images/info_bsinfo_24dp.svg"
                Size="Size.XSmall"
                InfoPlacement="InfoPlacement.Right">
            </InfoIcon>
        </div>),
        ("InfoType: Tooltip, Size: Small, image source (svg), InfoPlacement: Right, overridden background color via Style", "",
@"<div class=""usage-wrapper"">
    <label for=""usage4-field"">Text input with info icon</label>
    <input type=""text"" id=""usage4-field"">
    <InfoIcon
        Id=""usage4""
        InfoType=""@InfoType.Tooltip""
        Content=""@TooltipMessage""
        ImageSource=""images/info_white_24dp.svg""
        Size=""Size.Small""
        InfoPlacement=""InfoPlacement.Right""
        Style=""--cctc-infoicon-image-container-background-color: var(--bs-info);"">
    </InfoIcon>
</div>

@code {
    
    string TooltipMessage { get; set; } = ""Tooltip message"";
}",
        @<div class="usage-wrapper">
            <label for="usage4-field">Text input with info icon</label>
            <input type="text" id="usage4-field">
            <InfoIcon
                Id="usage4"
                InfoType="@InfoType.Tooltip"
                Content="@TooltipMessage"
                ImageSource="images/info_white_24dp.svg"
                Size="Size.Small"
                InfoPlacement="InfoPlacement.Right"
                Style="--cctc-infoicon-image-container-background-color: var(--bs-info);">
            </InfoIcon>
        </div>),
        ("InfoType: Tooltip, Size: Medium, image icon (material icon), InfoPlacement: Top, overridden background color via CssClass", "",
@"<div class=""usage-wrapper"">
    <label for=""usage5-field"">Text input with info icon</label>
    <input type=""text"" id=""usage5-field"">
    <InfoIcon
        Id=""usage5""
        InfoType=""@InfoType.Tooltip""
        Content=""@TooltipMessage""
        ImageIcon=""help_center""
        Size=""Size.Medium""
        InfoPlacement=""InfoPlacement.Top""
        CssClass=""info-background"">
    </InfoIcon>
</div>

@code {

    string TooltipMessage { get; set; } = ""Tooltip message"";
}",
        @<div class="usage-wrapper">
            <label for="usage5-field">Text input with info icon</label>
            <input type="text" id="usage5-field">
            <InfoIcon
                Id="usage5"
                InfoType="@InfoType.Tooltip"
                Content="@TooltipMessage"
                ImageIcon="help_center"
                Size="Size.Medium"
                InfoPlacement="InfoPlacement.Top"
                CssClass="info-background">
            </InfoIcon>
        </div>),
        ("InfoType: Tooltip, Size: Large, image icon (material icon), InfoPlacement: Right, CssClass applied", "",
@"<InfoIcon
    Id=""usage6""
    InfoType=""@InfoType.Tooltip""
    Content=""@TooltipMessage""
    ImageIcon=""circle""
    CssClass=""test-pink""
    Size=""@Size.Large""
    InfoPlacement=""@InfoPlacement.Right"">
</InfoIcon>

@code {

    string TooltipMessage { get; set; } = ""Tooltip message"";
}",
        @<InfoIcon
            Id="usage6"
            InfoType="@InfoType.Tooltip"
            Content="@TooltipMessage"
            ImageIcon="circle"
            CssClass="test-pink"
            Size="@Size.Large"
            InfoPlacement="@InfoPlacement.Right">
        </InfoIcon>),
        ("InfoType: Tooltip, Size: XXLarge, image source (png), InfoPlacement: Right, CssClass applied", "",
@"<InfoIcon
    Id=""usage7""
    InfoType=""@InfoType.Tooltip""
    Content=""@TooltipMessage""
    ImageSource=""images/info.png""
    Size=""@Size.XXLarge""
    InfoPlacement=""@(InfoPlacement.Right)""
    CssClass=""info-background"">
</InfoIcon>

@code {
    
    string TooltipMessage { get; set; } = ""Tooltip message"";
}",
        @<InfoIcon
            Id="usage7"
            InfoType="@InfoType.Tooltip"
            Content="@TooltipMessage"
            ImageSource="images/info.png"
            Size="@Size.XXLarge"
            InfoPlacement="@(InfoPlacement.Right)"
            CssClass="info-background">
        </InfoIcon>)
    };
}

@* The wrapper div is necessary here for ::deep isolated css rules to be applied to the InfoIcon component *@
<div>
    <Sampler
        ComponentName="InfoIcon"
        ComponentCssName="infoicon"
        Description="@description"
        Features="@features"
        UsageText="Typical usages of the <code>InfoIcon</code> component are shown below"
        UsageCodeList="@usageCode"
        ContentHeightPixels="490">
        <ExampleTemplate>
            <div class="example-wrapper">
                <label for="example1-field">Text input with info icon</label>
                <input type="text" id="example1-field">
                <InfoIcon
                    Id="example1"
                    InfoType="@(InfoType.Tooltip)"
                    Content="some tooltip message"
                    ImageSource="images/info_bsinfo_24dp.svg"
                    Size="Size.XSmall"
                    InfoPlacement="InfoPlacement.Right"
                    OnClick="@(() => _counter++)">
                </InfoIcon>
            </div>
            <p class="mt-3">Counter: @_counter</p>
        </ExampleTemplate>
    </Sampler>
</div>   

@code {

    int _counter;

    string TooltipMessage { get; set; } = "Tooltip message";

    string PopoverMessage { get; set; } = "Popover message";

    string PopoverTitle { get; set; } = "Popover title";
}
