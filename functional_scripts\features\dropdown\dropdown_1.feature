@component @dropdown @dropdown_1
Feature: the dropdown component option can be changed using the mouse or the keyboard
    Scenario: the dropdown component sample page is available
        Given the user is at the home page
        When the user selects the "Dropdown" component in the container "Input"
        Then the url ending is "dropdownsample"

    Scenario: the dropdown component option can be changed using the mouse
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        And the current selected dropdown option has the text "One"
        When the user clicks on the current selected dropdown option
        And the dropdown is expanded
        And the user clicks on the dropdown option with the text "Two"
        Then the current selected dropdown option has the text "Two"

    Scenario: the dropdown component option can be changed using the keyboard
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        And the current selected dropdown option has the text "One"
        When the user clicks on the current selected dropdown option
        And the dropdown is expanded
        And the user presses the down arrow key 2 times
        And the user presses the up arrow key once
        Then the current selected dropdown option has the text "Two"