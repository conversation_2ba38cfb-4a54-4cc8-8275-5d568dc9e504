﻿@using BlazorComponentUtilities
@using System.Timers
@inherits CCTC_Components.Components.__CCTC.CCTCBase
@implements IDisposable

<cctc-timeout id="@Id" class="@CssClass" style="@Style" data-author="cctc">
    <div class="@($"{_displayCss} {_visibleCss} {_fadeCss}".Trim())" style="@_visibleStyle">
        @ChildContent
    </div>
</cctc-timeout>

@code {

    #region parameters

    /// <summary>
    /// The content of the animation
    /// </summary>
    [Parameter]
    public RenderFragment? ChildContent { get; set; }

    /// <summary>
    /// TimeSpan representing how long the fading process should take
    /// </summary>
    [Parameter]
    public TimeSpan FadeFor { get; set; } = TimeSpan.FromSeconds(3);

    /// <summary>
    /// TimeSpan representing how long before the fading should begin.
    /// AfterShowContentForElapsed is called after this time elapses
    /// </summary>
    [Parameter]
    public TimeSpan ShowContentFor { get; set; } = TimeSpan.FromSeconds(5);

    /// <summary>
    /// Reserve space for the content of timeout even when hidden - defaults to true
    /// </summary>
    [Parameter]
    public bool ReserveSpace { get; set; } = true;

    /// <summary>
    /// The callback to invoke once the ShowContentFor timespan has elapsed
    /// </summary>
    [Parameter]
    public EventCallback AfterShowContent { get; set; }

    /// <summary>
    /// The callback to invoke once the ShowContentFor timespan + AnimateFor timespan has elapsed
    /// </summary>
    [Parameter]
    public EventCallback AfterFadeFinished { get; set; }

    /// <summary>
    /// If true, when ReserveSpace is false, collapse the space
    /// </summary>
    [Parameter]
    public bool CollapseOnFadeComplete { get; set; }

    /// <summary>
    /// Automatically restarts the timeout if the timeout is initiated when already running
    /// </summary>
    /// <remarks>When false, use the Stop method to stop the timeout manually</remarks>
    [Parameter]
    public bool AutoRestart { get; set; }

    /// <summary>
    /// The callback to invoke when the timeout is stopped. Passes true stopped by AutoRestart
    /// </summary>
    [Parameter]
    public EventCallback<bool> WhenStopped { get; set; }

    #endregion parameters

    Timer _timer = new();
    bool _isRunning;

    void OnShowContentForElapsed(object source, ElapsedEventArgs e)
    {
        AfterShowContent.InvokeAsync();
        _timer = new Timer(FadeFor);
        _timer.AutoReset = false;
        _timer.Elapsed += OnFullTimerElapsed!;

        _timer.Start();
        StartFadeWithDelay();
    }

    void OnFullTimerElapsed(object source, ElapsedEventArgs e)
    {
        AfterFadeFinished.InvokeAsync();

        InitializeTimer();
        _isRunning = false;
        _fadeComplete = true;
        _visibleStyle = null;
        _fadeCss = null;

        UpdateDisplay();
    }

    /// <summary>
    /// Starts the TimeOut
    /// </summary>
    public void Initiate()
    {
        if (AutoRestart)
        {
            DoStop();
            WhenStopped.InvokeAsync(true);
        }

        _fadeComplete = false;
        _timer.Start();
        _timer.AutoReset = false;
        _isRunning = true;

        UpdateDisplay();
    }

    /// <summary>
    /// Stops the currently running timeout and resets
    /// </summary>
    public void Stop()
    {
        DoStop();
        WhenStopped.InvokeAsync(false);
    }

    void DoStop()
    {
        _isRunning = false;
        _fadeComplete = true;
        _visibleStyle = null;
        _fadeCss = null;
        UpdateDisplay();
        _timer.Stop();
        _timer.Dispose();
        InvokeAsync(StateHasChanged);
        InitializeTimer();
    }

    /// <summary>
    /// True if the animation is running
    /// </summary>
    public bool IsRunning => _isRunning;

    bool _fadeComplete;
    string? _displayCss;
    string? _visibleCss;
    string? _fadeCss;
    string? _visibleStyle;

    void UpdateDisplay()
    {
        (_displayCss, _visibleCss) = (_isRunning, ReserveSpace, CollapseOnFadeComplete, _fadeComplete) switch
        {
            (true, _, _, _) => ("reserve", "visible"),
            (_, true, false, _) => ("reserve", "invisible"),
            (_, false, false, true) => ("reserve", "invisible"),
            (_, false, false, false) => ("no-reserve", "invisible"),
            (_, false, true, false) => ("no-reserve", "invisible"),
            (_, true, true, false) => ("reserve", "invisible"),
            (_, false, true, true) => ("no-reserve", "invisible"),
            (_, true, true,true) => ("no-reserve", "invisible"),
        };

        InvokeAsync(StateHasChanged);
    }

    // Root Cause:
    // The StartFade method updates _fadeCss and _visibleStyle to initiate the CSS animation. However, in Blazor Server, due to the round-trip nature of updates:
    //
    // The state change (e.g., applying the timeout-fade class) may not be rendered in the DOM at the expected moment.
    //     The animation starts but isn't visually noticeable, as the client-side rendering doesn't align perfectly with the timing.
    //     Solution:
    // You can address this issue by ensuring that the CSS class responsible for the animation is applied in a way that avoids dependency on Blazor's state synchronization delays.
    //
    //     Updated StartFade Method Using InvokeAsync with a Delay:
    // Introduce a slight delay before applying the timeout-fade class to ensure the DOM updates correctly.
    void StartFadeWithDelay()
    {
        _visibleStyle = $"animation-duration: {FadeFor.TotalMilliseconds}ms;";

        // Ensure a small delay to allow Blazor Server to apply the class
        InvokeAsync(async () =>
        {
            await Task.Delay(50); // Small delay to ensure DOM updates
            _fadeCss = "timeout-fade";
            StateHasChanged();
        });
    }

    // void StartFade()
    // {
    //     _fadeCss = "timeout-fade";
    //     _visibleStyle = $"animation-duration: {FadeFor.TotalMilliseconds}ms;";
    //
    //     StateHasChanged();
    // }

    /// <inheritdoc />
    protected override void OnParametersSet()
    {
        UpdateDisplay();
    }

    void InitializeTimer()
    {
        _timer = new Timer(ShowContentFor);
        _timer.Elapsed += OnShowContentForElapsed!;
    }

    /// <inheritdoc />
    protected override void OnInitialized()
    {
        //start with show content timer, then update it for the fade for timer
        InitializeTimer();
    }

    /// <inheritdoc />
    public void Dispose()
    {
        _timer.Elapsed -= OnShowContentForElapsed!;
        _timer.Elapsed -= OnFullTimerElapsed!;
        _timer.Dispose();
    }
}