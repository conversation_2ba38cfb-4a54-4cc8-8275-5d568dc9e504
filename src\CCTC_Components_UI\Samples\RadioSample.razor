﻿@page "/radiosample"

@{
    var description = new List<string>
    {
        "A component for radio input"
    };

    var features = new List<(string, string)>
    {
        ("Selected option", "Can be made read-only. The read-only icon is optional"),
        ("Options", "Options can be individually disabled or set as visible. The value text and display text can be configured. Option overflow can be set to Wrap (default), NoWrap, Scroll or None"),
        ("Orientation", "The orientation of the radio collection. Can be set to VerticalLeft (default), VerticalRight or Horizontal"),
        ("Clear selection", "Allows the current selection to be cleared from the UI (depends on the data type - see gotchas). Not enabled by default"),
        ("Tooltip", "Tooltip behaviour can be set to EnabledOnOptionOverflow (default), Enabled or Disabled. Tooltip placement is configurable")
    };

    var gotchas = new List<(string, string)>
    {
        ("ShowClear", "Can clear only if TData is a reference type or nullable value type")
    };

    var tips = new List<(string, string)>
    {
        ("Sizing", "The Radio component adapts to the size of its parent container. RadioOrientation.Horizontal - radio buttons wrap if wider than the parent container and scroll if taller than the parent container. " +
            "RadioOrientation.VerticalLeft and RadioOrientation.VerticalRight - radio buttons scroll if taller than the parent container. The css variable --cctc-input-height sets the minimum height of each radio option. " +
            "The radio option height can be greater than this depending on the length of the provided label. " +
            "The input radio button scales according to the value of --cctc-input-height"),
        ("Label wrapping", "When the label is set to wrap, the --cctc-input-webkit-line-clamp css variable sets the maximum number of lines before truncation is applied"),
        ("Target the cctc-input component(s) on a page ignoring any child cctc-input components. For example, setting a uniform input component width adjusting according to screen size",
@"
<code>
    <pre>

    ::deep cctc-input:not(cctc-input cctc-input) {
        width: 100%;
    }

    @media (min-width: 1200px) {
        ::deep cctc-input:not(cctc-input cctc-input) {
            width: 35%;
        }
    }
    </pre>
</code>")
    };

    var usageCode = new List<(string title, string description, string code, RenderFragment fragment)>
    {
        ("RadioOrientation: VerticalLeft, RadioLabelOverflow: Wrap, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, with callback", "",
@"<Radio
    TData=""(int value, string display)""
    Data=""RadioData2""
    Id=""usage1""
    @bind-Value=""@RadioValue2""
    ValueSelector=""@(item => item.value.ToString())""
    DisplaySelector=""@(item => item.display)""
    RadioOrientation=""@RadioOrientation.VerticalLeft""
    RadioLabelOverflow=""RadioLabelOverflow.Wrap""
    RadioLabelTooltipPlacement=""TooltipPlacement.Right""
    RadioLabelTooltipBehaviour=""RadioLabelTooltipBehaviour.EnabledOnLabelOverflow""
    SelectionChanged=""@(args => Console.WriteLine(args.Value?.ToString()))"">
</Radio>

@code {

    List<(int value, string display)> RadioData2 = new() { (1, ""Option 1""), (2, ""Option 2""), (3, ""Option 3""), (4, ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.""), (5, ""Option 5"") };

    (int value, string display) RadioValue2 { get; set; } = (3, ""Option 3"");
}",
        @<Radio
            TData="(int value, string display)"
            Data="RadioData2"
            Id="usage1"
            @bind-Value="@RadioValue2"
            ValueSelector="@(item => item.value.ToString())"
            DisplaySelector="@(item => item.display)"
            RadioOrientation="@RadioOrientation.VerticalLeft"
            RadioLabelOverflow="RadioLabelOverflow.Wrap"
            RadioLabelTooltipPlacement="TooltipPlacement.Right"
            RadioLabelTooltipBehaviour="RadioLabelTooltipBehaviour.EnabledOnLabelOverflow"
            SelectionChanged="@(args => Console.WriteLine(args.Value?.ToString()))">
        </Radio>),
("RadioOrientation: VerticalRight, RadioLabelOverflow: Wrap, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, with callback", "",
@"<Radio
    TData=""(int value, string display)""
    Data=""RadioData2""
    Id=""usage2""
    @bind-Value=""@RadioValue2""
    ValueSelector=""@(item => item.value.ToString())""
    DisplaySelector=""@(item => item.display)""
    RadioOrientation=""@RadioOrientation.VerticalRight""
    RadioLabelOverflow=""RadioLabelOverflow.Wrap""
    RadioLabelTooltipPlacement=""TooltipPlacement.Right""
    RadioLabelTooltipBehaviour=""RadioLabelTooltipBehaviour.EnabledOnLabelOverflow""
    SelectionChanged=""@(args => Console.WriteLine(args.Value?.ToString()))"">
</Radio>

@code {

    List<(int value, string display)> RadioData2 = new() { (1, ""Option 1""), (2, ""Option 2""), (3, ""Option 3""), (4, ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.""), (5, ""Option 5"") };

    (int value, string display) RadioValue2 { get; set; } = (3, ""Option 3"");
}",
        @<Radio
            TData="(int value, string display)"
            Data="RadioData2"
            Id="usage2"
            @bind-Value="@RadioValue2"
            ValueSelector="@(item => item.value.ToString())"
            DisplaySelector="@(item => item.display)"
            RadioOrientation="@RadioOrientation.VerticalRight"
            RadioLabelOverflow="RadioLabelOverflow.Wrap"
            RadioLabelTooltipPlacement="TooltipPlacement.Right"
            RadioLabelTooltipBehaviour="RadioLabelTooltipBehaviour.EnabledOnLabelOverflow"
            SelectionChanged="@(args => Console.WriteLine(args.Value?.ToString()))">
        </Radio>),
        ("RadioOrientation: VerticalLeft, RadioLabelOverflow: NoWrap, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, with callback", "",
@"<Radio
    TData=""string""
    Data=""RadioData4""
    Id=""usage3""
    @bind-Value=""@RadioValue4""
    RadioOrientation=""@RadioOrientation.VerticalLeft""
    RadioLabelOverflow=""RadioLabelOverflow.NoWrap""
    RadioLabelTooltipPlacement=""TooltipPlacement.Right""
    RadioLabelTooltipBehaviour=""RadioLabelTooltipBehaviour.EnabledOnLabelOverflow""
    SelectionChanged=""@(args => Console.WriteLine(args.Value?.ToString()))"">
</Radio>

@code {

    List<string> RadioData4 = new() { ""One"", ""Two"", ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."", ""Four"" };

    string RadioValue4 = ""One"";
}",
        @<Radio
            TData="string"
            Data="RadioData4"
            Id="usage3"
            @bind-Value="@RadioValue4"
            RadioOrientation="@RadioOrientation.VerticalLeft"
            RadioLabelOverflow="RadioLabelOverflow.NoWrap"
            RadioLabelTooltipPlacement="TooltipPlacement.Right"
            RadioLabelTooltipBehaviour="RadioLabelTooltipBehaviour.EnabledOnLabelOverflow"
            SelectionChanged="@(args => Console.WriteLine(args.Value?.ToString()))">
        </Radio>),
        ("RadioOrientation: VerticalLeft, RadioLabelOverflow: Scroll, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: Disabled, with callback", "",
@"<Radio
    TData=""string""
    Data=""RadioData4""
    Id=""usage4""
    @bind-Value=""@RadioValue4""
    RadioOrientation=""@RadioOrientation.VerticalLeft""
    RadioLabelOverflow=""RadioLabelOverflow.Scroll""
    RadioLabelTooltipPlacement=""TooltipPlacement.Right""
    RadioLabelTooltipBehaviour=""RadioLabelTooltipBehaviour.Disabled""
    SelectionChanged=""@(args => Console.WriteLine(args.Value?.ToString()))"">
</Radio>

@code {

    List<string> RadioData4 = new() { ""One"", ""Two"", ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."", ""Four"" };

    string RadioValue4 = ""One"";
}",
        @<Radio
            TData="string"
            Data="RadioData4"
            Id="usage4"
            @bind-Value="@RadioValue4"
            RadioOrientation="@RadioOrientation.VerticalLeft"
            RadioLabelOverflow="RadioLabelOverflow.Scroll"
            RadioLabelTooltipPlacement="TooltipPlacement.Right"
            RadioLabelTooltipBehaviour="RadioLabelTooltipBehaviour.Disabled"
            SelectionChanged="@(args => Console.WriteLine(args.Value?.ToString()))">
        </Radio>),
        ("RadioOrientation: VerticalLeft, RadioLabelOverflow: None, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: Disabled, with callback, input height modified via Style parameter", "",
@"<Radio
    TData=""(int value, string display)""
    Data=""RadioData2""
    Id=""usage5""
    Style=""--cctc-input-height: 2.2rem;""
    @bind-Value=""@RadioValue2""
    ValueSelector=""@(item => item.value.ToString())""
    DisplaySelector=""@(item => item.display)""
    RadioOrientation=""@RadioOrientation.VerticalLeft""
    RadioLabelOverflow=""RadioLabelOverflow.None""
    RadioLabelTooltipPlacement=""TooltipPlacement.Right""
    RadioLabelTooltipBehaviour=""RadioLabelTooltipBehaviour.Disabled""
    SelectionChanged=""@(args => Console.WriteLine(args.Value?.ToString()))"">
</Radio>

@code {

    List<(int value, string display)> RadioData2 = new() { (1, ""Option 1""), (2, ""Option 2""), (3, ""Option 3""), (4, ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.""), (5, ""Option 5"") };

    (int value, string display) RadioValue2 { get; set; } = (3, ""Option 3"");
}",
        @<Radio
            TData="(int value, string display)"
            Data="RadioData2"
            Id="usage5"
            Style="--cctc-input-height: 2.2rem;"
            @bind-Value="@RadioValue2"
            ValueSelector="@(item => item.value.ToString())"
            DisplaySelector="@(item => item.display)"
            RadioOrientation="@RadioOrientation.VerticalLeft"
            RadioLabelOverflow="RadioLabelOverflow.None"
            RadioLabelTooltipPlacement="TooltipPlacement.Right"
            RadioLabelTooltipBehaviour="RadioLabelTooltipBehaviour.Disabled"
            SelectionChanged="@(args => Console.WriteLine(args.Value?.ToString()))">
        </Radio>),
        ("RadioOrientation: VerticalLeft, RadioLabelOverflow: Wrap, RadioLabelTooltipPlacement: Top, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, read-only", "",
@"<Radio
    TData=""(int value, string display)""
    Data=""RadioData2""
    Id=""usage6""
    @bind-Value=""@RadioValue2""
    ValueSelector=""@(item => item.value.ToString())""
    DisplaySelector=""@(item => item.display)""
    RadioOrientation=""@RadioOrientation.VerticalLeft""
    RadioLabelOverflow=""RadioLabelOverflow.Wrap""
    RadioLabelTooltipPlacement=""TooltipPlacement.Top""
    RadioLabelTooltipBehaviour=""RadioLabelTooltipBehaviour.EnabledOnLabelOverflow""
    ReadOnly=""true"">
</Radio>

@code {

    List<(int value, string display)> RadioData2 = new() { (1, ""Option 1""), (2, ""Option 2""), (3, ""Option 3""), (4, ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.""), (5, ""Option 5"") };

    (int value, string display) RadioValue2 { get; set; } = (3, ""Option 3"");
}",
        @<Radio
            TData="(int value, string display)"
            Data="RadioData2"
            Id="usage6"
            @bind-Value="@RadioValue2"
            ValueSelector="@(item => item.value.ToString())"
            DisplaySelector="@(item => item.display)"
            RadioOrientation="@RadioOrientation.VerticalLeft"
            RadioLabelOverflow="RadioLabelOverflow.Wrap"
            RadioLabelTooltipPlacement="TooltipPlacement.Top"
            RadioLabelTooltipBehaviour="RadioLabelTooltipBehaviour.EnabledOnLabelOverflow"
            ReadOnly="true">
        </Radio>),
        ("RadioOrientation: VerticalLeft, RadioLabelOverflow: Scroll, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: Disabled, read-only, scroll items due to restricted height", "",
@"<Radio
    TData=""(int value, string display)""
    Data=""RadioData3""
    Id=""usage7""
    @bind-Value=""@RadioValue3""
    Style=""height: calc(var(--cctc-input-height) * 3);""
    ValueSelector=""@(item => item.value.ToString())""
    DisplaySelector=""@(item => item.display)""
    RadioOrientation=""@RadioOrientation.VerticalLeft""
    RadioLabelOverflow=""RadioLabelOverflow.Scroll""
    RadioLabelTooltipPlacement=""TooltipPlacement.Right""
    RadioLabelTooltipBehaviour=""RadioLabelTooltipBehaviour.Disabled""
    ReadOnly=""true"">
</Radio>

@code {

    List<(int value, string display)> RadioData3 = new() { (1, ""Option 1""), (2, ""Option 2""), (3, ""Option 3""), (4, ""Option 4""), (5, ""Option 5""), (6, ""Option 6""), (7, ""Option 7""),
        (8, ""Option 8""), (9, ""Option 9""), (10, ""Option 10""), (11, ""Option 11""), (12, ""Option 12""), (13, ""Option 13""), (14, ""Option 14""), (15, ""Option 15""),
            (16, ""Option 16 containing some long description to demonstrate truncation containing some long description to demonstrate truncation"") };

    (int value, string display) RadioValue3 { get; set; } = (2, ""Option 2"");
}",
        @<Radio
            TData="(int value, string display)"
            Data="RadioData3"
            Id="usage7"
            @bind-Value="@RadioValue3"
            Style="height: calc(var(--cctc-input-height) * 3);"
            ValueSelector="@(item => item.value.ToString())"
            DisplaySelector="@(item => item.display)"
            RadioOrientation="@RadioOrientation.VerticalLeft"
            RadioLabelOverflow="RadioLabelOverflow.Scroll"
            RadioLabelTooltipPlacement="TooltipPlacement.Right"
            RadioLabelTooltipBehaviour="RadioLabelTooltipBehaviour.Disabled"
            ReadOnly="true">
        </Radio>),
("RadioOrientation: VerticalLeft, RadioLabelOverflow: NoWrap, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, read-only, hide read-only icon, scroll items due to restricted height", "",
@"<Radio
    TData=""(int value, string display)""
    Data=""RadioData3""
    Id=""usage8""
    @bind-Value=""@RadioValue3""
    Style=""height: calc(var(--cctc-input-height) * 3);""
    ValueSelector=""@(item => item.value.ToString())""
    DisplaySelector=""@(item => item.display)""
    RadioOrientation=""@RadioOrientation.VerticalLeft""
    RadioLabelOverflow=""RadioLabelOverflow.NoWrap""
    RadioLabelTooltipPlacement=""TooltipPlacement.Right""
    RadioLabelTooltipBehaviour=""RadioLabelTooltipBehaviour.EnabledOnLabelOverflow""
    ReadOnly=""true""
    HideReadOnlyIcon=""true"">
</Radio>

@code {

    List<(int value, string display)> RadioData3 = new() { (1, ""Option 1""), (2, ""Option 2""), (3, ""Option 3""), (4, ""Option 4""), (5, ""Option 5""), (6, ""Option 6""), (7, ""Option 7""),
        (8, ""Option 8""), (9, ""Option 9""), (10, ""Option 10""), (11, ""Option 11""), (12, ""Option 12""), (13, ""Option 13""), (14, ""Option 14""), (15, ""Option 15""),
            (16, ""Option 16 containing some long description to demonstrate truncation containing some long description to demonstrate truncation"") };

    (int value, string display) RadioValue3 { get; set; } = (2, ""Option 2"");
}",
        @<Radio
            TData="(int value, string display)"
            Data="RadioData3"
            Id="usage8"
            @bind-Value="@RadioValue3"
            Style="height: calc(var(--cctc-input-height) * 3);"
            ValueSelector="@(item => item.value.ToString())"
            DisplaySelector="@(item => item.display)"
            RadioOrientation="@RadioOrientation.VerticalLeft"
            RadioLabelOverflow="RadioLabelOverflow.NoWrap"
            RadioLabelTooltipPlacement="TooltipPlacement.Right"
            RadioLabelTooltipBehaviour="RadioLabelTooltipBehaviour.EnabledOnLabelOverflow"
            ReadOnly="true"
            HideReadOnlyIcon="true">
        </Radio>),
        ("RadioOrientation: VerticalLeft, RadioLabelOverflow: NoWrap, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, read-only and all radio options disabled, scroll items due to restricted height", "",
@"<Radio
    TData=""(int value, string display)""
    Data=""RadioData3""
    Id=""usage9""
    @bind-Value=""@RadioValue3""
    Style=""height: calc(var(--cctc-input-height) * 3);""
    ValueSelector=""@(item => item.value.ToString())""
    DisplaySelector=""@(item => item.display)""
    RadioOrientation=""@RadioOrientation.VerticalLeft""
    RadioLabelOverflow=""RadioLabelOverflow.NoWrap""
    RadioLabelTooltipPlacement=""TooltipPlacement.Right""
    RadioLabelTooltipBehaviour=""RadioLabelTooltipBehaviour.EnabledOnLabelOverflow""
    ReadOnly=""true""
    Disabled=""@(_ => true)"">
</Radio>

@code {

    List<(int value, string display)> RadioData3 = new() { (1, ""Option 1""), (2, ""Option 2""), (3, ""Option 3""), (4, ""Option 4""), (5, ""Option 5""), (6, ""Option 6""), (7, ""Option 7""),
        (8, ""Option 8""), (9, ""Option 9""), (10, ""Option 10""), (11, ""Option 11""), (12, ""Option 12""), (13, ""Option 13""), (14, ""Option 14""), (15, ""Option 15""),
            (16, ""Option 16 containing some long description to demonstrate truncation containing some long description to demonstrate truncation"") };

    (int value, string display) RadioValue3 { get; set; } = (2, ""Option 2"");
}",
        @<Radio
            TData="(int value, string display)"
            Data="RadioData3"
            Id="usage9"
            @bind-Value="@RadioValue3"
            Style="height: calc(var(--cctc-input-height) * 3);"
            ValueSelector="@(item => item.value.ToString())"
            DisplaySelector="@(item => item.display)"
            RadioOrientation="@RadioOrientation.VerticalLeft"
            RadioLabelOverflow="RadioLabelOverflow.NoWrap"
            RadioLabelTooltipPlacement="TooltipPlacement.Right"
            RadioLabelTooltipBehaviour="RadioLabelTooltipBehaviour.EnabledOnLabelOverflow"
            ReadOnly="true"
            Disabled="@(_ => true)">
        </Radio>),
        ("RadioOrientation: VerticalLeft, RadioLabelOverflow: NoWrap, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, second radio option disabled, fifth radio option not visible", "",
@"<Radio
    TData=""(int value, string display)""
    Data=""RadioData2""
    Id=""usage10""
    @bind-Value=""@RadioValue2""
    ValueSelector=""@(item => item.value.ToString())""
    DisplaySelector=""@(item => item.display)""
    RadioOrientation=""@RadioOrientation.VerticalLeft""
    RadioLabelOverflow=""RadioLabelOverflow.NoWrap""
    RadioLabelTooltipPlacement=""TooltipPlacement.Right""
    RadioLabelTooltipBehaviour=""RadioLabelTooltipBehaviour.EnabledOnLabelOverflow""
    Disabled=""@(item => item.value == 2)""
    Visible=""@(item => item.value != 5)"">
</Radio>

@code {

    List<(int value, string display)> RadioData2 = new() { (1, ""Option 1""), (2, ""Option 2""), (3, ""Option 3""), (4, ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.""), (5, ""Option 5"") };

    (int value, string display) RadioValue2 { get; set; } = (3, ""Option 3"");
}",
        @<Radio
            TData="(int value, string display)"
            Data="RadioData2"
            Id="usage10"
            @bind-Value="@RadioValue2"
            ValueSelector="@(item => item.value.ToString())"
            DisplaySelector="@(item => item.display)"
            RadioOrientation="@RadioOrientation.VerticalLeft"
            RadioLabelOverflow="RadioLabelOverflow.NoWrap"
            RadioLabelTooltipPlacement="TooltipPlacement.Right"
            RadioLabelTooltipBehaviour="RadioLabelTooltipBehaviour.EnabledOnLabelOverflow"
            Disabled="@(item => item.value == 2)"
            Visible="@(item => item.value != 5)">
        </Radio>),
        ("RadioOrientation: VerticalLeft, nullable tuple data with show clear", "",
@"<Radio
    TData=""(int value, string display)?""
    Data=""RadioData6""
    Id=""usage11""
    @bind-Value=""@RadioValue6""
    ValueSelector=""@(item => item is null ? item.ToString() : item.Value.value.ToString())""
    DisplaySelector=""@(item => item is null ? item.ToString() : item.Value.display)""
    RadioOrientation=""@RadioOrientation.VerticalLeft""
    ShowClear=""true"">
</Radio>

@code {

    List<(int value, string display)?> RadioData6 = new() { (0, ""No""), (1, ""Yes"") };

    (int value, string display)? RadioValue6 = (1, ""Yes"");
}",
        @<Radio
            TData="(int value, string display)?"
            Data="RadioData6"
            Id="usage11"
            @bind-Value="@RadioValue6"
            ValueSelector="@(item => item is null ? item.ToString() : item.Value.value.ToString())"
            DisplaySelector="@(item => item is null ? item.ToString() : item.Value.display)"
            RadioOrientation="@RadioOrientation.VerticalLeft"
            ShowClear="true">
        </Radio>),
("RadioOrientation: VerticalLeft, null initial value, CssClass applied", "",
@"<Radio
    TData=""string""
    Data=""RadioData4""
    Id=""usage12""
    CssClass=""red-border""
    @bind-Value=""@RadioValue5""
    SelectionChanged=""@(args => Console.WriteLine(args.Value?.ToString()))"">
</Radio>

@code {

    List<string> RadioData4 = new() { ""One"", ""Two"", ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."", ""Four"" };

    string? RadioValue5;
}",
        @<Radio
            TData="string"
            Data="RadioData4"
            Id="usage12"
            CssClass="red-border"
            @bind-Value="@RadioValue5"
            SelectionChanged="@(args => Console.WriteLine(args.Value?.ToString()))">
        </Radio>),
        ("RadioOrientation: Horizontal, RadioLabelOverflow: Wrap, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, with callback", "",
@"<Radio
    TData=""(int value, string display)""
    Data=""RadioData2""
    Id=""usage13""
    @bind-Value=""@RadioValue2""
    ValueSelector=""@(item => item.value.ToString())""
    DisplaySelector=""@(item => item.display)""
    RadioOrientation=""@RadioOrientation.Horizontal""
    RadioLabelOverflow=""RadioLabelOverflow.Wrap""
    RadioLabelTooltipPlacement=""TooltipPlacement.Right""
    RadioLabelTooltipBehaviour=""RadioLabelTooltipBehaviour.EnabledOnLabelOverflow""
    SelectionChanged=""@(args => Console.WriteLine(args.Value?.ToString()))"">
</Radio>

@code {

    List<(int value, string display)> RadioData2 = new() { (1, ""Option 1""), (2, ""Option 2""), (3, ""Option 3""), (4, ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.""), (5, ""Option 5"") };

    (int value, string display) RadioValue2 { get; set; } = (3, ""Option 3"");
}",
        @<Radio
            TData="(int value, string display)"
            Data="RadioData2"
            Id="usage13"
            @bind-Value="@RadioValue2"
            ValueSelector="@(item => item.value.ToString())"
            DisplaySelector="@(item => item.display)"
            RadioOrientation="@RadioOrientation.Horizontal"
            RadioLabelOverflow="RadioLabelOverflow.Wrap"
            RadioLabelTooltipPlacement="TooltipPlacement.Right"
            RadioLabelTooltipBehaviour="RadioLabelTooltipBehaviour.EnabledOnLabelOverflow"
            SelectionChanged="@(args => Console.WriteLine(args.Value?.ToString()))">
        </Radio>),
        ("RadioOrientation: Horizontal, RadioLabelOverflow: NoWrap, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, with callback", "",
@"<Radio
    TData=""string""
    Data=""RadioData4""
    Id=""usage14""
    @bind-Value=""@RadioValue4""
    RadioOrientation=""@RadioOrientation.Horizontal""
    RadioLabelOverflow=""RadioLabelOverflow.NoWrap""
    RadioLabelTooltipPlacement=""TooltipPlacement.Right""
    RadioLabelTooltipBehaviour=""RadioLabelTooltipBehaviour.EnabledOnLabelOverflow""
    SelectionChanged=""@(args => Console.WriteLine(args.Value?.ToString()))"">
</Radio>

@code {

    List<string> RadioData4 = new() { ""One"", ""Two"", ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."", ""Four"" };

    string RadioValue4 = ""One"";
}",
        @<Radio
            TData="string"
            Data="RadioData4"
            Id="usage14"
            @bind-Value="@RadioValue4"
            RadioOrientation="@RadioOrientation.Horizontal"
            RadioLabelOverflow="RadioLabelOverflow.NoWrap"
            RadioLabelTooltipPlacement="TooltipPlacement.Right"
            RadioLabelTooltipBehaviour="RadioLabelTooltipBehaviour.EnabledOnLabelOverflow"
            SelectionChanged="@(args => Console.WriteLine(args.Value?.ToString()))">
        </Radio>),
        ("RadioOrientation: Horizontal, RadioLabelOverflow: Scroll, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: Disabled, with callback", "",
@"<Radio
    TData=""string""
    Data=""RadioData4""
    Id=""usage15""
    @bind-Value=""@RadioValue4""
    RadioOrientation=""@RadioOrientation.Horizontal""
    RadioLabelOverflow=""RadioLabelOverflow.Scroll""
    RadioLabelTooltipPlacement=""TooltipPlacement.Right""
    RadioLabelTooltipBehaviour=""RadioLabelTooltipBehaviour.Disabled""
    SelectionChanged=""@(args => Console.WriteLine(args.Value?.ToString()))"">
</Radio>

@code {

    List<string> RadioData4 = new() { ""One"", ""Two"", ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."", ""Four"" };

    string RadioValue4 = ""One"";
}",
        @<Radio
            TData="string"
            Data="RadioData4"
            Id="usage15"
            @bind-Value="@RadioValue4"
            RadioOrientation="@RadioOrientation.Horizontal"
            RadioLabelOverflow="RadioLabelOverflow.Scroll"
            RadioLabelTooltipPlacement="TooltipPlacement.Right"
            RadioLabelTooltipBehaviour="RadioLabelTooltipBehaviour.Disabled"
            SelectionChanged="@(args => Console.WriteLine(args.Value?.ToString()))">
        </Radio>),
        ("RadioOrientation: Horizontal, RadioLabelOverflow: None, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: Disabled, with callback, input height modified via Style parameter", "",
@"<Radio
    TData=""(int value, string display)""
    Data=""RadioData2""
    Id=""usage16""
    Style=""--cctc-input-height: 2.2rem;""
    @bind-Value=""@RadioValue2""
    ValueSelector=""@(item => item.value.ToString())""
    DisplaySelector=""@(item => item.display)""
    RadioOrientation=""@RadioOrientation.Horizontal""
    RadioLabelOverflow=""RadioLabelOverflow.None""
    RadioLabelTooltipPlacement=""TooltipPlacement.Right""
    RadioLabelTooltipBehaviour=""RadioLabelTooltipBehaviour.Disabled""
    SelectionChanged=""@(args => Console.WriteLine(args.Value?.ToString()))"">
</Radio>

@code {

    List<(int value, string display)> RadioData2 = new() { (1, ""Option 1""), (2, ""Option 2""), (3, ""Option 3""), (4, ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.""), (5, ""Option 5"") };

    (int value, string display) RadioValue2 { get; set; } = (3, ""Option 3"");
}",
        @<Radio
            TData="(int value, string display)"
            Data="RadioData2"
            Id="usage16"
            Style="--cctc-input-height: 2.2rem;"
            @bind-Value="@RadioValue2"
            ValueSelector="@(item => item.value.ToString())"
            DisplaySelector="@(item => item.display)"
            RadioOrientation="@RadioOrientation.Horizontal"
            RadioLabelOverflow="RadioLabelOverflow.None"
            RadioLabelTooltipPlacement="TooltipPlacement.Right"
            RadioLabelTooltipBehaviour="RadioLabelTooltipBehaviour.Disabled"
            SelectionChanged="@(args => Console.WriteLine(args.Value?.ToString()))">
        </Radio>),
        ("RadioOrientation: Horizontal, RadioLabelOverflow: Wrap, RadioLabelTooltipPlacement: Top, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, read-only", "",
@"<Radio
    TData=""(int value, string display)""
    Data=""RadioData2""
    Id=""usage17""
    @bind-Value=""@RadioValue2""
    ValueSelector=""@(item => item.value.ToString())""
    DisplaySelector=""@(item => item.display)""
    RadioOrientation=""@RadioOrientation.Horizontal""
    RadioLabelOverflow=""RadioLabelOverflow.Wrap""
    RadioLabelTooltipPlacement=""TooltipPlacement.Top""
    RadioLabelTooltipBehaviour=""RadioLabelTooltipBehaviour.EnabledOnLabelOverflow""
    ReadOnly=""true"">
</Radio>

@code {

    List<(int value, string display)> RadioData2 = new() { (1, ""Option 1""), (2, ""Option 2""), (3, ""Option 3""), (4, ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.""), (5, ""Option 5"") };

    (int value, string display) RadioValue2 { get; set; } = (3, ""Option 3"");
}",
        @<Radio
            TData="(int value, string display)"
            Data="RadioData2"
            Id="usage17"
            @bind-Value="@RadioValue2"
            ValueSelector="@(item => item.value.ToString())"
            DisplaySelector="@(item => item.display)"
            RadioOrientation="@RadioOrientation.Horizontal"
            RadioLabelOverflow="RadioLabelOverflow.Wrap"
            RadioLabelTooltipPlacement="TooltipPlacement.Top"
            RadioLabelTooltipBehaviour="RadioLabelTooltipBehaviour.EnabledOnLabelOverflow"
            ReadOnly="true">
        </Radio>),
        ("RadioOrientation: Horizontal, RadioLabelOverflow: Scroll, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: Disabled, read-only, scroll items due to restricted height", "",
@"<Radio
    TData=""(int value, string display)""
    Data=""RadioData3""
    Id=""usage18""
    @bind-Value=""@RadioValue3""
    Style=""height: calc(var(--cctc-input-height) * 3);""
    ValueSelector=""@(item => item.value.ToString())""
    DisplaySelector=""@(item => item.display)""
    RadioOrientation=""@RadioOrientation.Horizontal""
    RadioLabelOverflow=""RadioLabelOverflow.Scroll""
    RadioLabelTooltipPlacement=""TooltipPlacement.Right""
    RadioLabelTooltipBehaviour=""RadioLabelTooltipBehaviour.Disabled""
    ReadOnly=""true"">
</Radio>

@code {

    List<(int value, string display)> RadioData3 = new() { (1, ""Option 1""), (2, ""Option 2""), (3, ""Option 3""), (4, ""Option 4""), (5, ""Option 5""), (6, ""Option 6""), (7, ""Option 7""),
        (8, ""Option 8""), (9, ""Option 9""), (10, ""Option 10""), (11, ""Option 11""), (12, ""Option 12""), (13, ""Option 13""), (14, ""Option 14""), (15, ""Option 15""),
            (16, ""Option 16 containing some long description to demonstrate truncation containing some long description to demonstrate truncation"") };

    (int value, string display) RadioValue3 { get; set; } = (2, ""Option 2"");
}",
        @<Radio
            TData="(int value, string display)"
            Data="RadioData3"
            Id="usage18"
            @bind-Value="@RadioValue3"
            Style="height: calc(var(--cctc-input-height) * 3);"
            ValueSelector="@(item => item.value.ToString())"
            DisplaySelector="@(item => item.display)"
            RadioOrientation="@RadioOrientation.Horizontal"
            RadioLabelOverflow="RadioLabelOverflow.Scroll"
            RadioLabelTooltipPlacement="TooltipPlacement.Right"
            RadioLabelTooltipBehaviour="RadioLabelTooltipBehaviour.Disabled"
            ReadOnly="true">
        </Radio>),
("RadioOrientation: Horizontal, RadioLabelOverflow: NoWrap, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, read-only, hide read-only icon, scroll items due to restricted height", "",
@"<Radio
    TData=""(int value, string display)""
    Data=""RadioData3""
    Id=""usage19""
    @bind-Value=""@RadioValue3""
    Style=""height: calc(var(--cctc-input-height) * 3);""
    ValueSelector=""@(item => item.value.ToString())""
    DisplaySelector=""@(item => item.display)""
    RadioOrientation=""@RadioOrientation.Horizontal""
    RadioLabelOverflow=""RadioLabelOverflow.NoWrap""
    RadioLabelTooltipPlacement=""TooltipPlacement.Right""
    RadioLabelTooltipBehaviour=""RadioLabelTooltipBehaviour.EnabledOnLabelOverflow""
    ReadOnly=""true""
    HideReadOnlyIcon=""true"">
</Radio>

@code {

    List<(int value, string display)> RadioData3 = new() { (1, ""Option 1""), (2, ""Option 2""), (3, ""Option 3""), (4, ""Option 4""), (5, ""Option 5""), (6, ""Option 6""), (7, ""Option 7""),
        (8, ""Option 8""), (9, ""Option 9""), (10, ""Option 10""), (11, ""Option 11""), (12, ""Option 12""), (13, ""Option 13""), (14, ""Option 14""), (15, ""Option 15""),
            (16, ""Option 16 containing some long description to demonstrate truncation containing some long description to demonstrate truncation"") };

    (int value, string display) RadioValue3 { get; set; } = (2, ""Option 2"");
}",
        @<Radio
            TData="(int value, string display)"
            Data="RadioData3"
            Id="usage19"
            @bind-Value="@RadioValue3"
            Style="height: calc(var(--cctc-input-height) * 3);"
            ValueSelector="@(item => item.value.ToString())"
            DisplaySelector="@(item => item.display)"
            RadioOrientation="@RadioOrientation.Horizontal"
            RadioLabelOverflow="RadioLabelOverflow.NoWrap"
            RadioLabelTooltipPlacement="TooltipPlacement.Right"
            RadioLabelTooltipBehaviour="RadioLabelTooltipBehaviour.EnabledOnLabelOverflow"
            ReadOnly="true"
            HideReadOnlyIcon="true">
        </Radio>),
        ("RadioOrientation: Horizontal, RadioLabelOverflow: NoWrap, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, read-only and all radio options disabled, scroll items due to restricted height", "",
@"<Radio
    TData=""(int value, string display)""
    Data=""RadioData3""
    Id=""usage20""
    @bind-Value=""@RadioValue3""
    Style=""height: calc(var(--cctc-input-height) * 3);""
    ValueSelector=""@(item => item.value.ToString())""
    DisplaySelector=""@(item => item.display)""
    RadioOrientation=""@RadioOrientation.Horizontal""
    RadioLabelOverflow=""RadioLabelOverflow.NoWrap""
    RadioLabelTooltipPlacement=""TooltipPlacement.Right""
    RadioLabelTooltipBehaviour=""RadioLabelTooltipBehaviour.EnabledOnLabelOverflow""
    ReadOnly=""true""
    Disabled=""@(_ => true)"">
</Radio>

@code {

    List<(int value, string display)> RadioData3 = new() { (1, ""Option 1""), (2, ""Option 2""), (3, ""Option 3""), (4, ""Option 4""), (5, ""Option 5""), (6, ""Option 6""), (7, ""Option 7""),
        (8, ""Option 8""), (9, ""Option 9""), (10, ""Option 10""), (11, ""Option 11""), (12, ""Option 12""), (13, ""Option 13""), (14, ""Option 14""), (15, ""Option 15""),
            (16, ""Option 16 containing some long description to demonstrate truncation containing some long description to demonstrate truncation"") };

    (int value, string display) RadioValue3 { get; set; } = (2, ""Option 2"");
}",
        @<Radio
            TData="(int value, string display)"
            Data="RadioData3"
            Id="usage20"
            @bind-Value="@RadioValue3"
            Style="height: calc(var(--cctc-input-height) * 3);"
            ValueSelector="@(item => item.value.ToString())"
            DisplaySelector="@(item => item.display)"
            RadioOrientation="@RadioOrientation.Horizontal"
            RadioLabelOverflow="RadioLabelOverflow.NoWrap"
            RadioLabelTooltipPlacement="TooltipPlacement.Right"
            RadioLabelTooltipBehaviour="RadioLabelTooltipBehaviour.EnabledOnLabelOverflow"
            ReadOnly="true"
            Disabled="@(_ => true)">
        </Radio>),
        ("RadioOrientation: Horizontal, RadioLabelOverflow: NoWrap, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, second radio option disabled, fifth radio option not visible", "",
@"<Radio
    TData=""(int value, string display)""
    Data=""RadioData2""
    Id=""usage21""
    @bind-Value=""@RadioValue2""
    ValueSelector=""@(item => item.value.ToString())""
    DisplaySelector=""@(item => item.display)""
    RadioOrientation=""@RadioOrientation.Horizontal""
    RadioLabelOverflow=""RadioLabelOverflow.NoWrap""
    RadioLabelTooltipPlacement=""TooltipPlacement.Right""
    RadioLabelTooltipBehaviour=""RadioLabelTooltipBehaviour.EnabledOnLabelOverflow""
    Disabled=""@(item => item.value == 2)""
    Visible=""@(item => item.value != 5)"">
</Radio>

@code {

    List<(int value, string display)> RadioData2 = new() { (1, ""Option 1""), (2, ""Option 2""), (3, ""Option 3""), (4, ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.""), (5, ""Option 5"") };

    (int value, string display) RadioValue2 { get; set; } = (3, ""Option 3"");
}",
        @<Radio
            TData="(int value, string display)"
            Data="RadioData2"
            Id="usage21"
            @bind-Value="@RadioValue2"
            ValueSelector="@(item => item.value.ToString())"
            DisplaySelector="@(item => item.display)"
            RadioOrientation="@RadioOrientation.Horizontal"
            RadioLabelOverflow="RadioLabelOverflow.NoWrap"
            RadioLabelTooltipPlacement="TooltipPlacement.Right"
            RadioLabelTooltipBehaviour="RadioLabelTooltipBehaviour.EnabledOnLabelOverflow"
            Disabled="@(item => item.value == 2)"
            Visible="@(item => item.value != 5)">
        </Radio>),
        ("RadioOrientation: Horizontal, nullable tuple data with show clear", "",
@"<Radio
    TData=""(int value, string display)?""
    Data=""RadioData6""
    Id=""usage22""
    @bind-Value=""@RadioValue6""
    ValueSelector=""@(item => item is null ? item.ToString() : item.Value.value.ToString())""
    DisplaySelector=""@(item => item is null ? item.ToString() : item.Value.display)""
    RadioOrientation=""@RadioOrientation.Horizontal""
    ShowClear=""true"">
</Radio>

@code {

    List<(int value, string display)?> RadioData6 = new() { (0, ""No""), (1, ""Yes"") };

    (int value, string display)? RadioValue6 = (1, ""Yes"");
}",
        @<Radio
            TData="(int value, string display)?"
            Data="RadioData6"
            Id="usage22"
            @bind-Value="@RadioValue6"
            ValueSelector="@(item => item is null ? item.ToString() : item.Value.value.ToString())"
            DisplaySelector="@(item => item is null ? item.ToString() : item.Value.display)"
            RadioOrientation="@RadioOrientation.Horizontal"
            ShowClear="true">
        </Radio>),
        ("RadioOrientation: Horizontal, null initial value, CssClass applied", "",
@"<Radio
    TData=""string""
    Data=""RadioData4""
    Id=""usage23""
    CssClass=""red-border""
    @bind-Value=""@RadioValue5""
    RadioOrientation=""RadioOrientation.Horizontal""
    SelectionChanged=""@(args => Console.WriteLine(args.Value?.ToString()))"">
</Radio>

@code {

    List<string> RadioData4 = new() { ""One"", ""Two"", ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."", ""Four"" };

    string? RadioValue5;
}",
        @<Radio
            TData="string"
            Data="RadioData4"
            Id="usage23"
            CssClass="red-border"
            @bind-Value="@RadioValue5"
            RadioOrientation="RadioOrientation.Horizontal"
            SelectionChanged="@(args => Console.WriteLine(args.Value?.ToString()))">
        </Radio>),
        ("RadioOrientation: Horizontal, null initial value with show clear", "",
@"<Radio
    TData=""string""
    Data=""RadioData4""
    Id=""usage24""
    @bind-Value=""@RadioValue5""
    RadioOrientation=""RadioOrientation.Horizontal""
    SelectionChanged=""@(args => Console.WriteLine(args.Value?.ToString()))""
    ShowClear=""true"">
</Radio>

@code {

    List<string> RadioData4 = new() { ""One"", ""Two"", ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."", ""Four"" };

    string? RadioValue5;
}",
    @<Radio
        TData="string"
        Data="RadioData4"
        Id="usage24"
        @bind-Value="@RadioValue5"
        RadioOrientation="RadioOrientation.Horizontal"
        SelectionChanged="@(args => Console.WriteLine(args.Value?.ToString()))"
        ShowClear="true">
    </Radio>)
    };
}

@* The wrapper div is necessary here for ::deep isolated css rules to be applied to the Radio component *@
<div>
    <Sampler
        ComponentName="Radio"
        ComponentCssName="input"
        ComponentTypeName="radio"
        Description="@description"
        Features="@features"
        UsageText="Typical usages of the <code>Radio</code> component are shown below"
        UsageCodeList="@usageCode"
        Gotchas="@gotchas"
        Tips="@tips"
        ContentHeightPixels="450">
        <ExampleTemplate>
            <Radio
                TData="(int value, string display)"
                Data="RadioData"
                Id="Example1"
                @bind-Value="@RadioValue"
                ValueSelector="@(item => item.value.ToString())"
                DisplaySelector="@(item => item.display)"
                RadioOrientation="@RadioOrientation.VerticalLeft"
                SelectionChanged="@(args => Console.WriteLine(args.Value?.ToString()))">
            </Radio>
        </ExampleTemplate>
    </Sampler>
</div>

@code {

    List<(int value, string display)> RadioData = new() { (1, "Option 1"), (2, "Option 2"), (3, "Option 3"), (4, "Option 4") };

    (int value, string display) RadioValue { get; set; } = (1, "Option 1");

    List<(int value, string display)> RadioData2 = new() { (1, "Option 1"), (2, "Option 2"), (3, "Option 3"), (4, "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur."), (5, "Option 5") };

    (int value, string display) RadioValue2 { get; set; } = (3, "Option 3");

    List<(int value, string display)> RadioData3 = new() { (1, "Option 1"), (2, "Option 2"), (3, "Option 3"), (4, "Option 4"), (5, "Option 5"), (6, "Option 6"), (7, "Option 7"),
        (8, "Option 8"), (9, "Option 9"), (10, "Option 10"), (11, "Option 11"), (12, "Option 12"), (13, "Option 13"), (14, "Option 14"), (15, "Option 15"),
            (16, "Option 16 containing some long description to demonstrate truncation") };

    (int value, string display) RadioValue3 { get; set; } = (2, "Option 2");

    List<string> RadioData4 = new() { "One", "Two", "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.", "Four" };

    string RadioValue4 = "One";

    string? RadioValue5;

    List<(int value, string display)?> RadioData6 = new() { (0, "No"), (1, "Yes") };

    (int value, string display)? RadioValue6 = (1, "Yes");
}
