﻿@using System.Net.Http
@using System.Net.Http.Json
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using Microsoft.AspNetCore.Components.WebAssembly.Http
@using Microsoft.JSInterop
@using CCTC_Lib.Enums.UI
@using Common.Services
@using BlazorPro.Spinkit
@using Blazored.Modal
@using Blazored.Modal.Services
@using CCTC_Components_UI
@using CCTC_Components_UI.Shared
@using CCTC_Components_UI.Helpers
@using CCTC_Components.Components
@using CCTC_Components.Components.TextBox
@using CCTC_Components.Components.Temporal
@using CCTC_Components.Components.Lister
@using CCTC_Components.Components.Tooltip
@using CCTC_Components.Components.Radio
@using CCTC_Components.Components.InfoIcon
@using CCTC_Components.Components.InfoText
@using CCTC_Components.Components.Buttons
@using CCTC_Components.Components.CheckBox
@using CCTC_Components.Components.Concertina
@using CCTC_Components.Components.DataTextBox
@using CCTC_Components.Components.DropDown
@using CCTC_Components.Components.Modals
@using CCTC_Components.Components.PanelMenu
@using CCTC_Components.Components.Pill
@using CCTC_Components.Components.Steps
@using CCTC_Components.Components.Tabs
@using CCTC_Components.Components.Switch
@using CCTC_Components.Components.Charts