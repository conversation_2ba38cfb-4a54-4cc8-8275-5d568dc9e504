import { ICustomWorld } from '../support/custom-world';
import { compareToBaseImage } from '../utils/compareImages';
import * as Helpers from '../support/helper-functions';
import { When, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';

When('the user clicks the Switch within the Switch component', async function (this: ICustomWorld) {
  await Helpers.getSamplerTabsSelectedContent(this).getByRole('switch').click();
});

When('the user clicks the Label within the Switch component', async function (this: ICustomWorld) {
  await Helpers.getSamplerTabsSelectedContent(this).locator('cctc-switch label').click();
});

Then('the Switch component is turned off', async function (this: ICustomWorld) {
  await expect(
    Helpers.getSamplerTabsSelectedContent(this)
      .locator('cctc-switch')
      .locator('.component-wrapper')
  ).not.toHaveClass(/^(?=.*checked).*$/);
});

Then('the Switch component is turned on', async function (this: ICustomWorld) {
  await expect(
    Helpers.getSamplerTabsSelectedContent(this)
      .locator('cctc-switch')
      .locator('.component-wrapper')
  ).toHaveClass(/^(?=.*checked).*$/);
});

Then(
  'the Switch component image matches the base image {string}',
  async function (this: ICustomWorld, name: string) {
    const screenshot = await Helpers.tryGetStableScreenshot(
      Helpers.getSamplerTabsSelectedContent(this).locator('cctc-switch'),
      { threshold: 0.3 }
    );
    await compareToBaseImage(this, name, screenshot);
  }
);

Then('the Switch component is disabled', async function (this: ICustomWorld) {
  await expect(
    Helpers.getSamplerTabsSelectedContent(this)
      .locator('cctc-switch')
      .locator('.component-wrapper')
  ).toHaveClass(/^(?=.*disabled).*$/);
});
