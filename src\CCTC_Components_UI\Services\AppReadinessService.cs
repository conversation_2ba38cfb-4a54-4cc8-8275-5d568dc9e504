using Microsoft.JSInterop;

namespace CCTC_Components_UI.Services
{
    /// <summary>
    /// Service to handle application readiness state
    /// </summary>
    public class AppReadinessService : IAppReadinessService
    {
        private readonly IJSRuntime _jsRuntime;

        public AppReadinessService(IJSRuntime jsRuntime)
        {
            _jsRuntime = jsRuntime;
        }

        /// <summary>
        /// Signal that the application is fully initialized
        /// </summary>
        public async Task SignalAppReadyAsync()
        {
            await _jsRuntime.InvokeVoidAsync("CCTCAppReadiness.setAppReady");
        }
    }
} 