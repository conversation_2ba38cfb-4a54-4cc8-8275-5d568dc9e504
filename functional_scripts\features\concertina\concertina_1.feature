@component @concertina @concertina_1
Feature: the concertina has the ability to collapse or enable all concertina items

Scenario: The concertina component sample page is available
    Given the user is at the home page
    When the user selects the "Concertina" component in the container "Concertina"
    Then the url ending is "concertinasample"

Scenario: The component has the correct header
    Given the user is at the home page
    And the user selects the "Concertina" component in the container "Concertina"
    Then the header is "This is the header"

Scenario: The content is expanded by default and can be collapsed and expanded individually
    Given the user is at the home page
    And the user selects the "Concertina" component in the container "Concertina"
    And the user clicks the "Usage" tab
    When the user expands the concertina by clicking on the header with the text "Collapse others"
    Then concertina content "this is the content 1" is visible
    And concertina content "some content 2" is visible
    And concertina content "this is the content 3" is visible
    And the Concertina component image matches the base image "Concertina content expanded"

Scenario: Other text is hidden when the user collapses the item
    Given the user is at the home page
    And the user selects the "Concertina" component in the container "Concertina"
    And the user clicks the "Usage" tab
    When the user expands the concertina by clicking on the header with the text "Collapse others"
    And the user presses the collapse or expand button of header "this is header 1"
    Then concertina content "this is the content 1" is hidden
    And the Concertina component image matches the base image "Concertina content collapsed"

Scenario: The content is expanded by default and can be collapsed and expanded
    Given the user is at the home page
    And the user selects the "Concertina" component in the container "Concertina"
    And the user clicks the "Usage" tab
    And the user expands the concertina by clicking on the header with the text "Includes collapse/expand all"
    And concertina content "this is the content 1" is visible
    And concertina content "this is the content 3" is visible
    And the concertina collapse or expand all button is clicked
    And concertina content "this is the content 1" is hidden
    And concertina content "this is the content 3" is hidden
    When the concertina collapse or expand all button is clicked
    Then concertina content "this is the content 1" is visible
    And concertina content "this is the content 3" is visible



