﻿namespace CCTC_Components.Components.CheckBox
{
    /// <summary>
    /// Overflow of the checkbox label
    /// </summary>
    public enum CheckBoxLabelOverflow
    {
        /// <summary>
        /// Wrap the checkbox label
        /// </summary>
        Wrap,
        /// <summary>
        /// Do not wrap the checkbox label
        /// </summary>
        NoWrap,
        /// <summary>
        /// Scroll the checkbox label
        /// </summary>
        Scroll,
        /// <summary>
        /// Display the full checkbox label
        /// </summary>
        None
    }
}
