﻿cctc-input {
    position: relative;
    cursor: default;
    width: 100%;
}

cctc-input-dropdown-selected {
    display: flex;
    align-items: center;
    width: 100%;
    height: var(--cctc-input-height);
    padding-left: var(--cctc-input-padding-left);
    padding-right: var(--cctc-input-padding-right);
    color: var(--cctc-input-color);
    background-color: var(--cctc-input-background-color);
    border-radius: var(--cctc-input-border-radius);
    border-color: var(--cctc-input-border-color);
    border-width: var(--cctc-input-border-width);
    border-style: var(--cctc-input-border-style);
}

cctc-input-dropdown-selected ::deep cctc-tooltip {
    flex-grow: 1;
    min-width: 0;
}

cctc-input-dropdown-selected.disabled {
    color: var(--cctc-input-disabled-color);
    border-color: var(--cctc-input-disabled-background-color);
    background-color: var(--cctc-input-disabled-background-color);
}

cctc-input-dropdown-selected[aria-expanded] {
    border-radius: var(--cctc-input-border-radius) var(--cctc-input-border-radius) 0 0;
}

cctc-input-dropdown-selected p {
    flex-grow: 1;
    height: calc(var(--cctc-input-height) - 2 * var(--cctc-input-border-width));
    line-height: calc(var(--cctc-input-height) - 2 * var(--cctc-input-border-width));
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

cctc-input-dropdown {
    position: absolute;
    top: var(--cctc-input-height);
    left: 0;
    width: 100%;
    color: var(--cctc-input-color);
    background-color: var(--cctc-input-background-color);
    border-radius: 0 0 var(--cctc-input-border-radius) var(--cctc-input-border-radius);
    border-color: var(--cctc-input-border-color);
    border-width: 0 var(--cctc-input-border-width) var(--cctc-input-border-width) var(--cctc-input-border-width);
    border-style: var(--cctc-input-border-style);
    z-index: 5;
}

cctc-input-dropdown-options {
    padding: var(--cctc-input-padding-left) 0 var(--cctc-input-padding-left) 0;
    max-height: calc(var(--cctc-input-height) * 4);
    overflow-x: hidden;
    overflow-y: auto;
}

cctc-input-dropdown-options ::deep cctc-tooltip {
    width: 100%;
}

cctc-input-dropdown-option {
    padding: 0.25rem var(--cctc-input-padding-left) 0.25rem var(--cctc-input-padding-left);
}

cctc-input-dropdown-options.option-wrap cctc-input-dropdown-option {
    display: -webkit-box;
    -webkit-line-clamp: var(--cctc-input-webkit-line-clamp);
    -webkit-box-orient: vertical;
    overflow: hidden;
}

cctc-input-dropdown-options.option-nowrap cctc-input-dropdown-option {
    white-space: nowrap;
    overflow-x: hidden;
    text-overflow: ellipsis;
}

cctc-input-dropdown-options.option-scroll cctc-input-dropdown-option {
    white-space: nowrap;
    overflow-x: auto;
    scrollbar-gutter: stable;
}

cctc-input-dropdown-option[data-option-selected] {
    color: var(--cctc-color);
    background-color: var(--cctc-highlight-background-color);
}

cctc-input-dropdown-option[data-option-disabled] {
    color: var(--cctc-input-disabled-color);
    background-color: var(--cctc-input-disabled-background-color);
}

.dropdown-visible {
    opacity: 1;
}

.dropdown-hidden {
    opacity: 0;
    pointer-events: none;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar {
    width: 0.4rem;
    height: 0.4rem;
}

.icon-wrapper {
    display: flex;
    align-items: center;
    user-select: none;
}

.icon-wrapper .expand {
    color: var(--cctc-input-icon-color);
    font-size: 1rem;
}

.icon-wrapper .lock {
    color: var(--cctc-input-readonly-icon-color);
    margin-left: 0.1rem;
    margin-right: -0.1rem;
}

.icon-wrapper .backspace {
    color: var(--cctc-input-icon-color);
    margin-left: 0.2rem;
}

cctc-input-dropdown-selected:not(.disabled, .read-only) .icon-wrapper .expand:hover, .backspace:hover {
    color: var(--cctc-input-icon-hover-color);
}

.material-icons.backspace.hide-clear-icon {
    visibility: hidden;
    opacity: 0;
    transition: opacity 0.5s ease-out, visibility 0s linear 0.5s;
}

.material-icons.backspace.show-clear-icon {
    visibility: visible;
    opacity: 1;
    transition: opacity 0.5s ease-out, visibility 0s linear 0s;
}