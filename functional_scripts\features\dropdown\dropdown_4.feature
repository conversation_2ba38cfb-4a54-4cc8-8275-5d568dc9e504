@component @dropdown @dropdown_4
Feature: the dropdown component can be made read-only and individual options can be disabled or set as visible. The read-only icon is optional
    Scenario: the dropdown component can be made read-only
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Dropdown type: Tuple data, read-only, TooltipPlacement.Bottom"
        When the user clicks on the current selected dropdown option
        And the user moves the mouse relative to the main viewport with an x coordinate of 0 and y coordinate of 0
        Then the dropdown is not expanded
        And the Dropdown component image matches the base image "dropdown-readonly"

    Scenario: the dropdown component can be disabled
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Dropdown type: Tuple data, disabled, TooltipPlacement: Right"
        When the user clicks on the current selected dropdown option
        And the user moves the mouse relative to the main viewport with an x coordinate of 0 and y coordinate of 0
        Then the dropdown is not expanded
        And the Dropdown component image matches the base image "dropdown-disabled"

    Scenario: the dropdown component can be made read-only and disabled
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Dropdown type: Tuple data, read-only, disabled, TooltipPlacement: Right"
        When the user clicks on the current selected dropdown option
        And the user moves the mouse relative to the main viewport with an x coordinate of 0 and y coordinate of 0
        Then the dropdown is not expanded
        And the Dropdown component image matches the base image "dropdown-readonly-disabled"

    Scenario: the dropdown component can be made read-only without a read-only icon
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Dropdown type: Tuple data, read-only, hide read-only icon, TooltipPlacement: Top"
        When the user clicks on the current selected dropdown option
        And the user moves the mouse relative to the main viewport with an x coordinate of 0 and y coordinate of 0
        Then the dropdown is not expanded
        And the Dropdown component image matches the base image "dropdown-readonly-no-icon"

    Scenario: the dropdown component can be made read-only and disabled without a read-only icon
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Dropdown type: Tuple data, read-only, disabled, hide read-only icon, TooltipPlacement: Bottom"
        When the user clicks on the current selected dropdown option
        And the user moves the mouse relative to the main viewport with an x coordinate of 0 and y coordinate of 0
        Then the dropdown is not expanded
        And the Dropdown component image matches the base image "dropdown-readonly-disabled-no-icon"

    Scenario: the dropdown component individual options can be disabled
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Dropdown type: nullable int, with callback, show clear and disabled options applied"
        And the current selected dropdown option has the text "2"
        When the user clicks on the current selected dropdown option
        And the user moves the mouse relative to the main viewport with an x coordinate of 0 and y coordinate of 0
        And the dropdown is expanded
        And the Dropdown component listbox image matches the base image "dropdown-option-disabled"
        And the user clicks on the dropdown option with the text "4"
        Then the current selected dropdown option has the text "2"