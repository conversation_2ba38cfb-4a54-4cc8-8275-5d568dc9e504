import { ICustomWorld } from '../../support/custom-world';
import * as TextboxRoleFunctions from '../../support/step-functions/textbox-role-functions';
import { Then } from '@cucumber/cucumber';

Then(
  'the Text component image matches the base image {string}',
  async function (this: ICustomWorld, name: string) {
    await TextboxRoleFunctions.assertImageMatchesBaseImage(
      this,

      'cctc-input[data-cctc-input-type="text"]',
      name
    );
  }
);
