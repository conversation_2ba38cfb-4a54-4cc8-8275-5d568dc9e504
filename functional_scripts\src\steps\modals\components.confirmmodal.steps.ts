import { ICustomWorld } from '../../support/custom-world';
import { compareToBaseImage } from '../../utils/compareImages';
import * as Helpers from '../../support/helper-functions';
import { expect } from '@playwright/test';
import { When, Then } from '@cucumber/cucumber';

When('the confirm modal button is clicked', async function (this: ICustomWorld) {
  await Helpers.getSamplerTabsSelectedContent(this).getByRole('button').click();
});

When('the confirm modal ok response is clicked', async function (this: ICustomWorld) {
  await this.page!.locator(
    '.bm-container cctc-confirmmodal cctc-confirmmodal-responses .ok-response'
  ).click();
});

When('the confirm modal cancel response is clicked', async function (this: ICustomWorld) {
  await this.page!.locator(
    '.bm-container cctc-confirmmodal cctc-confirmmodal-responses .cancel-response'
  ).click();
});

Then(
  'the confirm modal component image matches the base image {string}', async function (this: ICustomWorld, name: string) {
    const page = this.page!;
    await Helpers.waitForAnimationCompletion(page, '.bm-container');
    const screenshot = await Helpers.tryGetStableScreenshot(page.locator('.bm-container cctc-confirmmodal'));
    await compareToBaseImage(this, name, screenshot);
  }
);

Then(
  'the confirm modal header is {string}',
  async function (this: ICustomWorld, headerContent: string) {
    const header = this.page!.locator(
      '.bm-container cctc-confirmmodal cctc-confirmmodal-header h4'
    );
    await expect(header).toHaveText(headerContent);
  }
);

Then(
  'the confirm modal text is {string}',
  async function (this: ICustomWorld, textContent: string) {
    const modalText = this.page!.locator(
      '.bm-container cctc-confirmmodal cctc-confirmmodal-container h5'
    );
    await expect(modalText).toHaveText(textContent);
  }
);

Then('the confirm modal header has a question mark', async function (this: ICustomWorld) {
  const questionMark = this.page!.locator(
    '.bm-container cctc-confirmmodal cctc-confirmmodal-header'
  ).getByText('question_mark');
  await expect(questionMark).toHaveCount(1);
});

Then('the confirm result is {string}', async function (this: ICustomWorld, confirmResult: string) {
  const confirmResultOutcome = Helpers.getSamplerTabsSelectedContent(this)
    .getByText(`confirm result: ${confirmResult}`, { exact: true })
    .first();
  await expect(confirmResultOutcome).toHaveText(`confirm result: ${confirmResult}`);
});

Then(
  'the confirm result image in confirm matches the base image {string}',
  async function (this: ICustomWorld, name: string) {
    const screenshot = await Helpers.tryGetStableScreenshot(
      Helpers.getSamplerTabsSelectedContent(this).getByText('confirm result:').first()
    );
    await compareToBaseImage(this, name, screenshot);
  }
);

Then(
  'the confirm modal ok response is {string}',
  async function (this: ICustomWorld, okResponse: string) {
    const okResponseLocator = this.page!.locator(
      '.bm-container cctc-confirmmodal cctc-confirmmodal-responses .ok-response'
    ).getByText(okResponse, { exact: true });
    await expect(okResponseLocator).toHaveText(okResponse);
  }
);

Then(
  'the confirm modal cancel response is {string}',
  async function (this: ICustomWorld, cancelResponse: string) {
    const cancelResponseLocator = this.page!.locator(
      '.bm-container cctc-confirmmodal cctc-confirmmodal-responses .cancel-response'
    ).getByText(cancelResponse, { exact: true });
    await expect(cancelResponseLocator).toHaveText(cancelResponse);
  }
);

Then(
  'the confirm modal ok response icon component image matches the base image {string}',
  async function (this: ICustomWorld, name: string) {
    const screenshot = await Helpers.tryGetStableScreenshot(
      this.page!.locator(
        '.bm-container cctc-confirmmodal cctc-confirmmodal-responses .ok-response .material-icons'
      )
    );
    await compareToBaseImage(this, name, screenshot);
  }
);

Then(
  'the confirm modal cancel response icon component image matches the base image {string}',
  async function (this: ICustomWorld, name: string) {
    const screenshot = await Helpers.tryGetStableScreenshot(
      this.page!.locator(
        '.bm-container cctc-confirmmodal cctc-confirmmodal-responses .cancel-response .material-icons'
      )
    );
    await compareToBaseImage(this, name, screenshot);
  }
);

Then(
  'the confirm modal cancel response icon is {string}',
  async function (this: ICustomWorld, cancelResponseIcon: string) {
    await expect(
      this.page!.locator(
        '.bm-container cctc-confirmmodal cctc-confirmmodal-responses .cancel-response .material-icons'
      )
    ).toHaveText(cancelResponseIcon);
  }
);

Then(
  'the confirm modal ok response icon is {string}',
  async function (this: ICustomWorld, okResponseIcon: string) {
    await expect(
      this.page!.locator(
        '.bm-container cctc-confirmmodal cctc-confirmmodal-responses .ok-response .material-icons'
      )
    ).toHaveText(okResponseIcon);
  }
);

Then(
  'the ok response button has the color {string}',
  async function (this: ICustomWorld, expectedColor: string) {
    const okResponseLocator = this.page!.locator(
      '.bm-container cctc-confirmmodal cctc-confirmmodal-responses .ok-response'
    );
    await expect(okResponseLocator).toHaveCSS('color', `${expectedColor}`);
  }
);

Then(
  'the cancel response button has the color {string}',
  async function (this: ICustomWorld, expectedColor: string) {
    const cancelResponseLocator = this.page!.locator(
      '.bm-container cctc-confirmmodal cctc-confirmmodal-responses .cancel-response'
    );
    await expect(cancelResponseLocator).toHaveCSS('color', `${expectedColor}`);
  }
);

Then(
  'the ok response button has the RGB color model value {string} when in the hover state',
  async function (this: ICustomWorld, expectedColor: string) {
    const okResponseLocator = this.page!.locator(
      '.bm-container cctc-confirmmodal cctc-confirmmodal-responses .ok-response'
    );
    await okResponseLocator.hover();
    await expect(okResponseLocator).toHaveCSS('color', expectedColor);
  }
);

Then(
  'the cancel response button has the RGB color model value {string} when in the hover state',
  async function (this: ICustomWorld, expectedColor: string) {
    const cancelResponseLocator = this.page!.locator(
      '.bm-container cctc-confirmmodal cctc-confirmmodal-responses .cancel-response'
    );
    await cancelResponseLocator.hover();
    await expect(cancelResponseLocator).toHaveCSS('color', expectedColor);
  }
);
