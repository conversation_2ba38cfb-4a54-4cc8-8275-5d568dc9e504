﻿using AngleSharp.Dom;
using Microsoft.AspNetCore.Components;
using TextCopy;

namespace CCTC_Components.bUnit.test.Helpers
{
    public static class TestHelpers
    {
        public static void AssertChecked(Func<IElement> find)
        {
            var ele = find.Invoke();
            Assert.True(ele.Has<PERSON>ri<PERSON>e("checked"));
        }

        public static void AssertNotChecked(Func<IElement> find)
        {
            var ele = find.Invoke();
            Assert.False(ele.Has<PERSON>ttribute("checked"));
        }

        public static void AssertReadonly(Func<IElement> find)
        {
            var ele = find.Invoke();
            Assert.True(ele.<PERSON>bute("readonly"));
        }

        public static void AssertNotReadonly(Func<IElement> find)
        {
            var ele = find.Invoke();
            Assert.False(ele.<PERSON>ttribute("readonly"));
        }

        public static void AssertNotFound(Func<IElement> find)
        {
            Assert.False(ElementIsFound(find));
        }

        public static void AssertFound(Func<IElement> find)
        {
            Assert.True(ElementIsFound(find));
        }

        public static void AssertFound(IRenderedFragment cut, string selector)
        {
            Assert.True(ElementIsFound(cut, selector));
        }

        public static void AssertNotFound(IRenderedFragment cut, string selector)
        {
            Assert.False(ElementIsFound(cut, selector));
        }

        public static bool ElementIsFound<T>(Func<T> find)
        {
            try
            {
                find.Invoke();
                return true;
            }
            catch (ElementNotFoundException)
            {
                return false;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public static bool ElementIsFound(IRenderedFragment cut, string selector)
        {
            try
            {
                cut.Find(selector);
                return true;
            }
            catch (ElementNotFoundException)
            {
                return false;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public static ComponentParameter[] GetComponentParameterArray(ComponentParameterCollection componentParameters)
        {
            List<ComponentParameter> parameters = new();
            foreach (var componentParameter in componentParameters)
            {
                parameters.Add(componentParameter);
            }

            return parameters.ToArray();
        }

        public static void AssertHasClass(Func<IElement?> find, string className)
        {
            Assert.True(find.Invoke()?.ClassList.Contains(className) ?? false);
        }

        public static void AssertHasClass(IElement element, string className)
        {
            Assert.True(element.ClassList.Contains(className));
        }

        public static void AssertDoesNotHaveClass(Func<IElement?> find, string className)
        {
            Assert.False(find.Invoke()?.ClassList.Contains(className) ?? false);
        }

        public static void AssertDoesNotHaveClass(IElement element, string className)
        {
            Assert.False(element.ClassList.Contains(className));
        }

        public static IElement? GetElementAt<TComponent>(this IRenderedComponent<TComponent> cut, string cssSelector, int index) where TComponent : IComponent
        {
            return cut.FindAll(cssSelector)[index];
        }

        public static void ClearClipboardText()
        {
            new Clipboard().SetText(string.Empty);
        }

        public static string? GetClipboardText()
        {
            return new Clipboard().GetText();
        }
    }
}
