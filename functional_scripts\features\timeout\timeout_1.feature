@component @timeout @timeout_1
Feature: timeout shows content on screen for predefined period of time before fading out

    Scenario: the timeout component sample page is available
        Given the user is at the home page
        When the user selects the "Time out" component
        Then the url ending is "timeoutsample"

    Scenario: the timeout component displays content then fades out
        Given the user is at the home page
        And the user selects the "Time out" component
        And the timeout component does not reserve space
        When the user clicks the timeout component initiate button
        Then the timeout component displays content
        And the timeout component reserves space
        And the timeout component content is visible
        When the user waits for 1200 milliseconds
        Then the timeout component is fading
        When the user waits for 2500 milliseconds
        Then the timeout component is not fading
        And the timeout component content is not visible
        And the timeout component does not reserve space

    Scenario: the timeout component can be initiated multiple times
        Given the user is at the home page
        And the user selects the "Time out" component
        When the user clicks the timeout component initiate button
        Then the timeout component displays content
        When the user waits for 4000 milliseconds
        And the timeout component is not fading
        When the user clicks the timeout component initiate button
        Then the timeout component displays content
        And the timeout component content is visible