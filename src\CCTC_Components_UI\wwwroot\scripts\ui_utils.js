export function getWindowSize() {
    return {
        width: window.innerWidth,
        height: window.innerHeight
    };
}

let blazor;
let resizeHandler;

window.WindowInterop = {
    init: function (helper) {
        blazor = helper;
        
        resizeHandler = () => {
            if (blazor) {
                blazor.invokeMethodAsync('GetWindowsDimensions', window.innerWidth, window.innerHeight);
            }
        };
        
        window.addEventListener('resize', resizeHandler);
        
        return blazor;
    },

    destroy: function () {
        // Clean up by removing the event listener
        if (resizeHandler) {
            window.removeEventListener('resize', resizeHandler);
        }
        
        blazor = null;
        resizeHandler = null;
    }
}