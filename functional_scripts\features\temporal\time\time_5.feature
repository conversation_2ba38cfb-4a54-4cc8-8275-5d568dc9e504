@component @temporal @time @time_5
Feature: the time component has a placeholder which matches the time format
    Scenario: the time component has a placeholder which matches the time format
        Given the user is at the home page
        And the user selects the "Time" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: h:mm tt, null initial value, FeedbackIcon.Valid"
        Then the Time component has the placeholder "h:mm am/pm"
        And the Time component image matches the base image "time-placeholder"