import { ICustomWorld } from '../support/custom-world';
import { compareToBaseImage } from '../utils/compareImages';
import * as Helpers from '../support/helper-functions';
import { When, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';

Then('the tab content contains {string}', async function (this: ICustomWorld, tabContent: string) {
  const tabContentElement = Helpers.getSamplerTabsSelectedContent(this).locator('cctc-tabs-selected-content');
  await expect(tabContentElement).toBeVisible();

  await expect(tabContentElement).toHaveText(tabContent);
});

When('the user clicks the {string} tab', async function (this: ICustomWorld, tabName: string) {
  await this.page!.getByRole('tab').getByText(tabName).click();
});

Then(
  'the Tabs component image matches the base image {string}',
  async function (this: ICustomWorld, name: string) {
    const screenshot = await Helpers.tryGetStableScreenshot(
      Helpers.getSamplerTabsSelectedContent(this).locator('cctc-tabs')
    );
    await compareToBaseImage(this, name, screenshot);
  }
);

Then('the tab background colour should be {string}', async function (
  this: ICustomWorld,
  expectedColor: string
) {
  const element = Helpers.getSamplerTabsSelectedContent(this).locator('.tab-item-content-margin');
  await expect(element).toBeVisible();
  const backgroundColor = await element.evaluate((el) => {
    return window.getComputedStyle(el).backgroundColor;
  });
  console.log('Tab background color:', backgroundColor);
  expect(backgroundColor).toBe(expectedColor);
});

Then('the tab content font size should be {string}', async function (this: ICustomWorld, fontSize: string) {
  const tabFontSize = Helpers.getSamplerTabsSelectedContent(this).locator('.tab-item-content-margin');
  await expect(tabFontSize).toHaveCSS('font-size', fontSize);
});

Then('the tab content box height should be {string}', async function (this: ICustomWorld, expected: string) {
  const content = Helpers.getSamplerTabsSelectedContent(this).locator('cctc-tabs-selected-content');
  const style = await content.getAttribute('style');
  expect(style).toMatch(new RegExp(`height:\\s*${expected}`));
});

Then('the tab with text {string} has a tooltip containing the full header text',
  async function (this: ICustomWorld, tabHeaderText: string) {
    const tabTooltip = Helpers.getSamplerTabsSelectedContent(this).locator('cctc-tooltip').filter({ hasText: tabHeaderText });
    const headerText = await tabTooltip.locator('cctc-tabitem-header').innerText();
    await expect(tabTooltip).toHaveAttribute('data-bs-original-title', headerText);
  }
);

When('the user selects {string} from the dropdown showing {string}', async function (this: ICustomWorld, optionText: string, currentValue: string) {
  const dropdown = this.page!.locator(`cctc-input-dropdown-selected p:text("${currentValue}")`).first();
  await expect(dropdown).toBeVisible({ timeout: 5000 });
  await dropdown.click();
  await this.page!.getByRole('option')
    .getByText(optionText)
    .click();
});

Then('the {string} tab has position {string}', async function (this: ICustomWorld, tabHeaderText: string, positionClass: string) {
  const validPositions = ['top-item-header', 'left-item-header', 'right-item-header', 'bottom-item-header'];
  if (!validPositions.includes(positionClass)) {
    throw new Error(`"${positionClass}" is not a valid tab position.`);
  }
  const tabHeader = this.page!.locator('cctc-tabitem-header', { hasText: tabHeaderText }).first();
  await expect(tabHeader).toHaveClass(new RegExp(`\\b${positionClass}\\b`));
});
