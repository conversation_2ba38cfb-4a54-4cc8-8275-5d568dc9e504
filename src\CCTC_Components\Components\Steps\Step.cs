namespace CCTC_Components.Components.Steps;

/// <summary>
/// Represents an individual Step for use with the <see cref="Steps"/> component
/// </summary>
public class Step
{
    const int EmptyStepNum = -1;

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="stepNum">The step number</param>
    /// <param name="required">True if required</param>
    /// <param name="name">The name for the step</param>
    /// <param name="fixedName">The fixed, non-mutable name for the step</param>
    /// <param name="data">Data associated with the step</param>
    /// <param name="value">The value of the step</param>
    public Step(int stepNum, bool required, string? name, string? fixedName, object? data, string? value)
    {
        StepNum = stepNum;
        Required = required;
        Name = name;
        FixedName = fixedName;
        Data = data;
        Value = value;
    }

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="stepNum">The step number</param>
    /// <param name="required">True if required</param>
    /// <param name="name">The name for the step</param>
    /// <param name="fixedName">The fixed, non-mutable name for the step</param>
    public Step(int stepNum, bool required, string? name, string? fixedName)
    {
        StepNum = stepNum;
        Required = required;
        Name = name;
        FixedName = fixedName;
        Data = null;
        Value = null;
    }

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="stepNum">The step number</param>
    /// <param name="required">True if required</param>
    /// <param name="name">The name for the step</param>
    public Step(int stepNum, bool required, string? name)
    {
        StepNum = stepNum;
        Required = required;
        Name = name;
        FixedName = null;
        Data = null;
        Value = null;
    }

    /// <summary>
    /// Gets the step number
    /// </summary>
    public int StepNum { get; }

    /// <summary>
    /// Gets whether the step is required
    /// </summary>
    public bool Required { get; }

    /// <summary>
    /// Name can be updated according to previous step. This will be wiped in the event of stepping back
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Once given, the fixed name is set and not changed even when stepping back. Fixed name is selected first.
    /// </summary>
    public string? FixedName { get; set; }

    /// <summary>
    /// Data associated with the step
    /// </summary>
    /// <remarks>This property can be used to store any data associated with a progress step. Common usages would
    /// be where a step contains more than a simple string response</remarks>
    public object? Data { get; set; }

    /// <summary>
    /// The value of the step
    /// </summary>
    /// <remarks>This is a convenience property. For example, it can be used to store a single value for a
    /// progress step with a single value</remarks>
    public string? Value { get; set; }

    /// <summary>
    /// When true, automatically moves to the next step when a value is given
    /// </summary>
    public bool AutoMoveOnWhenMarkedComplete { get; set; }

    /// <inheritdoc />
    public override string ToString()
    {
        return $"- {StepNum} - name: {FixedName} ({Name}) - req: {Required} -> data: {Data}";
    }

    /// <summary>
    /// Create an empty Step with the given number
    /// </summary>
    /// <returns>A <see cref="Step"/></returns>
    public static Step Empty()
    {
        return new Step(EmptyStepNum, false, null, null, null, null);
    }

    /// <summary>
    /// Determines if the step is complete
    /// </summary>
    /// <returns>True if the step is complete</returns>
    public bool IsComplete() => CanMoveNext();

    /// <summary>
    /// The current step can move on
    /// </summary>
    /// <returns>True if the step can move on</returns>
    public bool CanMoveNext() => !Required || Required && Data is not null;

    /// <summary>
    /// Determines whether the step is an empty step
    /// </summary>
    /// <param name="step">The step to check</param>
    /// <returns>True if an empty step</returns>
    public bool IsEmptyStep(Step step) => step.StepNum == EmptyStepNum;

    /// <summary>
    /// Gets the display name
    /// </summary>
    /// <returns>The display name</returns>
    /// <remarks>Uses the fixed name first</remarks>
    public string? DisplayName()
    {
        return FixedName ?? Name;
    }
}
