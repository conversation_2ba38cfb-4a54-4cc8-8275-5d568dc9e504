@component @confirmmodal @confirmmodal_2

Feature: the confirm modal window has a heading that can wrap and features an icon
    Scenario: the confirm modal window has a named heading that can wrap
        Given the user is at the home page
        And the user selects the "Confirm modal" component in the container "Modals"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Customised positive and negative responses, Modal options classes: UI theme plus custom class defining a max width"
        When the confirm modal button is clicked
        Then the confirm modal header is "Heading which is quite long so wraps"
        And the confirm modal component image matches the base image "Confirm Modal Box with wrapped header and text"
    
    Scenario: the confirm modal window has a named heading that does not wrap and features a question mark
        Given the user is at the home page
        And the user selects the "Confirm modal" component in the container "Modals"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Default positive and negative responses, Modal options classes: UI theme plus custom class defining a max width"
        When the confirm modal button is clicked
        Then the confirm modal header is "Submit changes"
        And the confirm modal header has a question mark
        And the confirm modal component image matches the base image "Confirm Modal Box with header that is not wrapped with question mark"
