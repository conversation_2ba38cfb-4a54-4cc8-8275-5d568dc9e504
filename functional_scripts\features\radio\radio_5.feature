@component @radio @radio_5
Feature: the radio component can be made read-only and individual options can be disabled or set as visible. The read-only icon is optional
    Scenario: the radio component can be made read-only
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "RadioOrientation: VerticalLeft, RadioLabelOverflow: Wrap, RadioLabelTooltipPlacement: Top, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, read-only"
        And the current selected radio option has the text "Option 3"
        When the user clicks on the radio option button with the associated label text "Option 2"
        Then the current selected radio option has the text "Option 3"
        Then the Radio component image matches the base image "radio-readonly"

    Scenario: the radio component individual options can be disabled
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "RadioOrientation: VerticalLeft, RadioLabelOverflow: NoWrap, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, second radio option disabled, fifth radio option not visible"
        Then the radio option button with the associated label text "Option 2" is disabled
        And the Radio component image matches the base image "radio-option-disabled"

    Scenario: the radio component can be made read-only and disabled
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "RadioOrientation: VerticalLeft, RadioLabelOverflow: NoWrap, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, read-only and all radio options disabled, scroll items due to restricted height"
        Then the radio option buttons are all disabled
        And the Radio component image matches the base image "radio-readonly-disabled"

    Scenario: the radio component can be made read-only without a read-only icon
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "RadioOrientation: VerticalLeft, RadioLabelOverflow: NoWrap, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, read-only, hide read-only icon, scroll items due to restricted height"
        And the current selected radio option has the text "Option 2"
        When the user clicks on the radio option button with the associated label text "Option 1"
        Then the current selected radio option has the text "Option 2"
        Then the Radio component image matches the base image "radio-readonly-no-icon"
