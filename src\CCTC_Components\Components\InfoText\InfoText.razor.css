﻿.component-wrapper {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.component-wrapper .icon-wrapper.show-on-hover {
    visibility: hidden;
}

.component-wrapper:hover .icon-wrapper {
    visibility: visible;
}

.component-wrapper:hover .icon-wrapper i {
    cursor: pointer;
    color: var(--cctc-infotext-icon-color);
}

.component-wrapper .icon-wrapper i:hover {
    color: var(--cctc-infotext-icon-hover-color);
}

cctc-infotext ::deep cctc-tooltip {
    min-width: 0;
}

cctc-infotext .infotext-wrap {
    display: -webkit-box;
    -webkit-line-clamp: var(--cctc-infotext-webkit-line-clamp);
    -webkit-box-orient: vertical;
    overflow: hidden;
}

cctc-infotext .infotext-nowrap {
    white-space: nowrap;
    overflow-x: hidden;
    text-overflow: ellipsis;
}

cctc-infotext .infotext-scroll {
    white-space: nowrap;
    overflow-x: auto;
    scrollbar-gutter: stable;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar {
    width: 0.4rem;
    height: 0.4rem;
}

::deep .custom-popover {
    --bs-popover-body-color: var(--cctc-infotext-popover-color);
    --bs-popover-bg: var(--cctc-infotext-popover-background-color);
    --bs-popover-border-color: var(--cctc-infotext-popover-background-color);
    --bs-popover-body-padding-x: 0.75rem;
    --bs-popover-body-padding-y: 0.5rem;
}
