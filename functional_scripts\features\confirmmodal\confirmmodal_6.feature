@component @confirmmodal @confirmmodal_6

Feature: the confirm modal responses and icons can be default
    Scenario: the confirm modal responses and icons can be default
        Given the user is at the home page
        And the user selects the "Confirm modal" component in the container "Modals"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Default positive and negative responses, Modal options classes: UI theme plus custom class defining a max width"
        When the confirm modal button is clicked
        And the confirm modal ok response is "Ok"
        And the confirm modal cancel response is "Cancel"
        Then the confirm modal ok response icon is "done"
        And the confirm modal ok response icon component image matches the base image "Tick default"
        And the confirm modal cancel response icon is "close"
        And the confirm modal cancel response icon component image matches the base image "Cross default"