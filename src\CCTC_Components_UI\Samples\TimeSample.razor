﻿@page "/timesample"

@{
    var description = new List<string>
    {
        "A component for time input. Support Reactive input and format validation"
    };

    var features = new List<(string, string)>
    {
        ("Interaction", "Can be made read-only and / or disabled. The read-only icon is optional"),
        ("Constrained input", "Apply a date format (see <code>Lib.Common.Services.Clock.availableDateFormats</code> for the supported formats), set a min and / or max time"),
        ("Binding", "The component throttle speed can be changed"),
        ("Feedback", "Provides configurable feedback on the inputted time"),
        ("Clear value", "Allows the current value to be cleared from the UI (depends on the data type - see gotchas). Not enabled by default"),
    };

    var gotchas = new List<(string, string)>
    {
        ("ThrottleMs", "500 ms is the minimum <code>ThrottleMs</code> and will be applied automatically unless a higher value is provided"),
        ("AllowClear", "Can clear only if TValue is of type nullable timeonly"),
        ("--cctc-input-webkit-line-clamp", "CSS variable not used by this component")
    };

    var tips = new List<(string, string)>
    {
        ("Target the cctc-input component(s) on a page ignoring any child cctc-input components. For example, setting a uniform input component width adjusting according to screen size",
@"
<code>
    <pre>

    ::deep cctc-input:not(cctc-input cctc-input) {
        width: 100%;
    }

    @media (min-width: 1200px) {
        ::deep cctc-input:not(cctc-input cctc-input) {
            width: 35%;
        }
    }
    </pre>
</code>")
    };

    var usageCode = new List<(string title, string description, string code, RenderFragment fragment)>
    {
        ("Format: default, FeedbackIcon.None", "",
@"<Time
    Id=""usage1""
    TValue=""TimeOnly""
    Value=""@TimeValue""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    ValueChanged=""@(args => TimeChanged(args, ""HH:mm:ss""))""
    TimeChanged=""@(args => Console.WriteLine(args.Value))""
    Disabled=""false""
    FeedbackIcon=""FeedbackIcon.None"">
</Time>

@code {

    TimeOnly TimeValue { get; set; } = new TimeOnly(13, 46, 22);

    void TimeChanged(TimeOnly newValue, string format)
    {
        TimeValue = newValue;
        Console.WriteLine($""Time new value is: {newValue.ToString(format)}"");
    }
}",
        @<Time
            Id="usage1"
            TValue="TimeOnly"
            Value="@TimeValue"
            ThrottleMs="@Constants.DefaultThrottleMs"
            ValueChanged="@(args => TimeChanged(args, "HH:mm:ss"))"
            TimeChanged="@(args => Console.WriteLine(args.Value))"
            Disabled="false"
            FeedbackIcon="FeedbackIcon.None">
        </Time>),
        ("Format: h:mm tt, Min (11:05 am) and max (1:10 pm), FeedbackIcon.Both, CssClass applied", "",
@"<Time
    Id=""usage2""
    DateFormat=""h:mm tt""
    MinTime=""@(new TimeOnly(11, 5, 00))""
    MaxTime=""@(new TimeOnly(13, 10, 00))""
    @bind-Value=""@TimeValue""
    CssClass=""red-border""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    TimeChanged=""@(args => Console.WriteLine(args.Value))""
    FeedbackIcon=""FeedbackIcon.Both"">
</Time>

@code {

    TimeOnly TimeValue { get; set; } = new TimeOnly(13, 46, 22);
}",
        @<Time
            Id="usage2"
            DateFormat="h:mm tt"
            MinTime="@(new TimeOnly(11, 5, 00))"
            MaxTime="@(new TimeOnly(13, 10, 00))"
            @bind-Value="@TimeValue"
            CssClass="red-border"
            ThrottleMs="@Constants.DefaultThrottleMs"
            TimeChanged="@(args => Console.WriteLine(args.Value))"
            FeedbackIcon="FeedbackIcon.Both">
        </Time>),
        ("Format: HH:mm:ss, FeedbackIcon.Error", "",
@"<Time
    Id=""usage3""
    DateFormat=""HH:mm:ss""
    @bind-Value=""@TimeValue""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    TimeChanged=""@(args => Console.WriteLine(args.Value))""
    FeedbackIcon=""FeedbackIcon.Error"">
</Time>

@code {
    
    TimeOnly TimeValue { get; set; } = new TimeOnly(13, 46, 22);
}",
        @<Time
            Id="usage3"
            DateFormat="HH:mm:ss"
            @bind-Value="@TimeValue"
            ThrottleMs="@Constants.DefaultThrottleMs"
            TimeChanged="@(args => Console.WriteLine(args.Value))"
            FeedbackIcon="FeedbackIcon.Error">
        </Time>),
        ("Format: h:mm tt, null initial value, FeedbackIcon.Valid", "",
@"<Time
    Id=""usage4""
    DateFormat=""h:mm tt""
    @bind-Value=""@TimeValueNullable""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    TimeChanged=""@(args => Console.WriteLine(args.Value))""
    FeedbackIcon=""FeedbackIcon.Valid"">
</Time>

@code {

    TimeOnly? TimeValueNullable { get; set; }
}",
        @<Time
            Id="usage4"
            DateFormat="h:mm tt"
            @bind-Value="@TimeValueNullable"
            ThrottleMs="@Constants.DefaultThrottleMs"
            TimeChanged="@(args => Console.WriteLine(args.Value))"
            FeedbackIcon="FeedbackIcon.Valid">
        </Time>),
        ("Format: h:mm tt, FeedbackIcon.Valid, read-only", "",
@"<Time
    Id=""usage5""
    DateFormat=""h:mm tt""
    @bind-Value=""@TimeValue""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    TimeChanged=""@(args => Console.WriteLine(args.Value))""
    FeedbackIcon=""FeedbackIcon.Valid""
    ReadOnly=""true"">
</Time>

@code {
    
    TimeOnly TimeValue { get; set; } = new TimeOnly(13, 46, 22);
}",
        @<Time
            Id="usage5"
            DateFormat="h:mm tt"
            @bind-Value="@TimeValue"
            ThrottleMs="@Constants.DefaultThrottleMs"
            TimeChanged="@(args => Console.WriteLine(args.Value))"
            FeedbackIcon="FeedbackIcon.Valid"
            ReadOnly="true">
        </Time>),
        ("Format: h:mm tt, FeedbackIcon.Valid, read-only and disabled", "",
@"<Time
    Id=""usage6""
    DateFormat=""h:mm tt""
    @bind-Value=""@TimeValue""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    TimeChanged=""@(args => Console.WriteLine(args.Value))""
    FeedbackIcon=""FeedbackIcon.Valid""
    ReadOnly=""true""
    Disabled=""true"">
</Time>

@code {
    
    TimeOnly TimeValue { get; set; } = new TimeOnly(13, 46, 22);
}",
        @<Time
            Id="usage6"
            DateFormat="h:mm tt"
            @bind-Value="@TimeValue"
            ThrottleMs="@Constants.DefaultThrottleMs"
            TimeChanged="@(args => Console.WriteLine(args.Value))"
            FeedbackIcon="FeedbackIcon.Valid"
            ReadOnly="true"
            Disabled="true">
        </Time>),
        ("Format: h:mm tt, FeedbackIcon.Valid, read-only, disabled and read-only icon hidden", "",
@"<Time
    Id=""usage7""
    DateFormat=""h:mm tt""
    @bind-Value=""@TimeValue""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    TimeChanged=""@(args => Console.WriteLine(args.Value))""
    FeedbackIcon=""FeedbackIcon.Valid""
    ReadOnly=""true""
    Disabled=""true""
    HideReadOnlyIcon=""true"">
</Time>

@code {

    TimeOnly TimeValue { get; set; } = new TimeOnly(13, 46, 22);
}",
        @<Time
            Id="usage7"
            DateFormat="h:mm tt"
            @bind-Value="@TimeValue"
            ThrottleMs="@Constants.DefaultThrottleMs"
            TimeChanged="@(args => Console.WriteLine(args.Value))"
            FeedbackIcon="FeedbackIcon.Valid"
            ReadOnly="true"
            Disabled="true"
            HideReadOnlyIcon="true">
        </Time>),
        ("Format: h:mm tt, FeedbackIcon.Valid, read-only and read-only icon hidden", "",
@"<Time
    Id=""usage8""
    DateFormat=""h:mm tt""
    @bind-Value=""@TimeValue""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    TimeChanged=""@(args => Console.WriteLine(args.Value))""
    FeedbackIcon=""FeedbackIcon.Valid""
    ReadOnly=""true""
    HideReadOnlyIcon=""true"">
</Time>

@code {

    TimeOnly TimeValue { get; set; } = new TimeOnly(13, 46, 22);
}",
        @<Time
            Id="usage8"
            DateFormat="h:mm tt"
            @bind-Value="@TimeValue"
            ThrottleMs="@Constants.DefaultThrottleMs"
            TimeChanged="@(args => Console.WriteLine(args.Value))"
            FeedbackIcon="FeedbackIcon.Valid"
            ReadOnly="true"
            HideReadOnlyIcon="true">
        </Time>),
        ("Format: h:mm tt, FeedbackIcon.Valid, disabled", "",
@"<Time
    Id=""usage9""
    DateFormat=""h:mm tt""
    @bind-Value=""@TimeValue""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    TimeChanged=""@(args => Console.WriteLine(args.Value))""
    FeedbackIcon=""FeedbackIcon.Valid""
    Disabled=""true"">
</Time>

@code {
    
    TimeOnly TimeValue { get; set; } = new TimeOnly(13, 46, 22);
}",
        @<Time
            Id="usage9"
            DateFormat="h:mm tt"
            @bind-Value="@TimeValue"
            ThrottleMs="@Constants.DefaultThrottleMs"
            TimeChanged="@(args => Console.WriteLine(args.Value))"
            FeedbackIcon="FeedbackIcon.Valid"
            Disabled="true">
        </Time>),
        ("Format: default, allow clear, FeedbackIcon.Error", "",
@"<Time
    Id=""usage10""
    Value=""@TimeValueNullable2""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    TimeChanged=""@(args => Console.WriteLine(args.Value ?? ""value cleared""))""
    AllowClear=""true""
    FeedbackIcon=""FeedbackIcon.Error"">        
</Time>

@code {

    TimeOnly? TimeValueNullable2 { get; set; } = new TimeOnly(14, 12, 46);
}",     
        @<Time
            Id="usage10"
            @bind-Value="@TimeValueNullable2"
            ThrottleMs="@Constants.DefaultThrottleMs"
            TimeChanged="@(args => Console.WriteLine(args.Value ?? "value cleared"))"
            AllowClear="true"
            FeedbackIcon="FeedbackIcon.Error">        
        </Time>),
        ("Format: default, allow clear, null initial value, FeedbackIcon.Error", "",
@"<Time
    Id=""usage11""
    Value=""@TimeValueNullable""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    TimeChanged=""@(args => Console.WriteLine(args.Value ?? ""value cleared""))""
    AllowClear=""true""
    FeedbackIcon=""FeedbackIcon.Error"">
</Time>

@code {

    TimeOnly? TimeValueNullable { get; set; }
}",
        @<Time
            Id="usage11"
            @bind-Value="@TimeValueNullable"
            ThrottleMs="@Constants.DefaultThrottleMs"
            TimeChanged="@(args => Console.WriteLine(args.Value ?? "value cleared"))"
            AllowClear="true"
            FeedbackIcon="FeedbackIcon.Error">
    </Time>)
    };
}

@* The wrapper div is necessary here for ::deep isolated css rules to be applied to the Time component *@
<div>
    <Sampler
        ComponentName="Time"
        ComponentCssName="input"
        ComponentTypeName="time"
        Description="@description"
        Features="@features"
        UsageText="Typical usages of the <code>Time</code> component are shown below"
        UsageCodeList="@usageCode"
        Gotchas="@gotchas"
        Tips="@tips"
        ContentHeightPixels="450">
        <ExampleTemplate>
            <Time
                Id="example1"
                @bind-Value="@TimeValue"
                ThrottleMs="@Constants.DefaultThrottleMs"
                TimeChanged="@(args => Console.WriteLine(args.Value))"
                FeedbackIcon="FeedbackIcon.Error">
            </Time>
        </ExampleTemplate>
    </Sampler>
</div>

@code {

    TimeOnly TimeValue { get; set; } = new TimeOnly(13, 46, 22);

    TimeOnly? TimeValueNullable { get; set; }

    TimeOnly? TimeValueNullable2 { get; set; } = new TimeOnly(14, 12, 46);

    void TimeChanged(TimeOnly newValue, string format)
    {
        TimeValue = newValue;
        Console.WriteLine($"Time new value is: {newValue.ToString(format)}");
    }
}
