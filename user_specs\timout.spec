1 - timeout shows content on screen for predefined period of time before fading out
2 - the timeout component can reserve space initially and not collapse after displaying content
3 - the timeout component can reserve space initially and collapse after displaying content
4 - the timeout component can start without reserving space and not collapse after displaying content
5 - the timeout component can start without reserving space and collapse after displaying content
6 - the timeout component can automatically restart when initiated while already running
7 - the timeout component can be manually stopped before completion