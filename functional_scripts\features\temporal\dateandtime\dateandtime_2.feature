@component @temporal @dateandtime @dateandtime_2
Feature: the date and time component can allow an empty value and an entered value can be optionally cleared
    Scenario: the date and time component can allow an empty date and time value when allow clear is set to true
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "Format: default, allow clear, null initial value, FeedbackIcon.Error"
        Then the Date part of the Date and Time component has the value ""
        And the Time part of the Date and Time component has the value ""

    Scenario: the date and time component date and time value can be cleared when allow clear is set to true
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: default, allow clear, FeedbackIcon.Error"
        When the user enters "" into the Date part of the Date and Time component
        And the user enters "" into the Time part of the Date and Time component
        Then the Date part of the Date and Time component has the value ""
        And the Time part of the Date and Time component has the value ""

    Scenario: the date and time component date value can be cleared via the date picker when allow clear is set to true
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: default, allow clear, FeedbackIcon.Error"
        When the user enters "" into the Date part of the Date and Time component via the date picker
        Then the Date part of the Date and Time component has the value ""

    Scenario: the date and time component can allow an empty date and time value when allow clear is set to false
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "Format: default, null initial value, min (2022-01-05 14:33:20) and max (2023-01-05 14:33:22), FeedbackIcon.Both"
        Then the Date part of the Date and Time component has the value ""
        And the Time part of the Date and Time component has the value ""

    Scenario: the date and time component date and time value can not be cleared when allow clear is set to false
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        When the user enters "" into the Date part of the Date and Time component
        And the user enters "" into the Time part of the Date and Time component
        Then the Date part of the Date and Time component has the value "04 October 2022"
        And the Time part of the Date and Time component has the value "12:13 pm"
