import { ICustomWorld } from '../../support/custom-world';
import * as TextboxRoleFunctions from '../../support/step-functions/textbox-role-functions';
import { When, Then } from '@cucumber/cucumber';

When('the user focusses on the Text( area) component', async function (this: ICustomWorld) {
  await TextboxRoleFunctions.focus(this);
});

When(
  'the user enters {string} into the Text( area) component',
  async function (this: ICustomWorld, text: string) {
    await TextboxRoleFunctions.inputText(this, text);
  }
);

Then(
  'the Text( area) component has the value {string}',
  async function (this: ICustomWorld, expectedText: string) {
    await TextboxRoleFunctions.assertHasValue(this, expectedText);
  }
);

Then(
  'the Text( area) component has the placeholder {string}',
  async function (this: ICustomWorld, placeholderText: string) {
    await TextboxRoleFunctions.assertHasPlaceholder(this, placeholderText);
  }
);

Then('the Text( area) component is disabled', async function (this: ICustomWorld) {
  await TextboxRoleFunctions.assertIsDisabled(this);
});

Then('the Text( area) component is not editable', async function (this: ICustomWorld) {
  await TextboxRoleFunctions.assertIsNotEditable(this);
});
