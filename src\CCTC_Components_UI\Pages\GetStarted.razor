@page "/get-started"

<h4>Get Started</h4>
<div class="guidance-wrapper">
    <p>The following guidance on using the CCTC_Components Blazor library relates to a .NET8 Blazor Web App. Note that components require the RenderMode to be set to InteractiveServer or InteractiveWebAssembly</p>
    <ol>
        <li>
            <p>Install the NuGet package</p>
            <p>Install CCTC_Components via the CCTC-team private NuGet feed</p>
            <code>dotnet add package CCTC_Components --version 1.0.62</code>
        </li>
        <li>
            <p>Import component namespaces by adding to _Imports.razor as appropriate</p>
        </li>
        <li>
            <p>Reference the component css by adding the following stylesheet link into the html head section of App.razor. Ensure that the link is added before any custom css stylesheets containing overridden variables</p>
            <code>_content/CCTC_Components/cctc-components.css</code>
        </li>
        <li>
            <p>Reference Material Icons by adding the following stylesheet link into the html head section of App.razor</p>
            <code>https://fonts.googleapis.com/icon?family=Material+Icons</code>
        </li>
        <li>
            <p>Reference Bootstrap css by adding the following stylesheet link into the html head section of App.razor</p>
            <code>https://cdn.jsdelivr.net/npm/bootstrap@@5.3.2/dist/css/bootstrap.min.css</code>
            <p>Alternatively create a node project in the server www folder and import Bootstrap as a dependency. Compile Bootstrap css from a scss file using sass and then add a link in the html head section referencing the css file</p>
        </li>
        <li>
            <p>Reference Bootstrap js by adding a script in the html body section of App.razor (via cdn.jsdelivr.net). Ensure that the Bootstrap js version matches the Bootstrap css version or the version of Bootstrap referenced in package.json</p>
            <code>https://cdn.jsdelivr.net/npm/bootstrap@@5.3.2/dist/js/bootstrap.bundle.min.js</code>
        </li>
        <li>
            <p>Reference jQuery js by adding a script in the html body section of App.razor (via jquery.com)</p>
            <code>https://code.jquery.com/jquery-3.5.1.min.js</code>
        </li>
        <li>
            <p>If required, apply a custom theme to modify the default component styling by overriding css variables</p>
        </li>
        <li>
            <p>In order to use the Modal components install Blazored Modal (see https://blazored.github.io/Modal/)</p>
        </li>
        <li>
            <p>In order to use Chart components add the following script in the html body section of App.razor (via cdn.jsdelivr.net)</p>
            <code>https://cdn.jsdelivr.net/npm/chart.js@@4.4.3/dist/chart.umd.min.js</code>
        </li>
        <li>
            <p>Add the required services by calling the <code>AddCCTCComponents()</code> extension method in Program.cs. This will add a default
                implementation of IScheduleProvider</p>
            <code>builder.Services.AddCCTCComponents();</code>
        </li>
    </ol>
</div>

@code {

}
