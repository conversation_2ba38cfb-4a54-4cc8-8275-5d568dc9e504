﻿@page "/dropdownsample"

@{
    var description = new List<string>
    {
        "A component for dropdown input"
    };

    var features = new List<(string, string)>
    {
        ("Selected option", "Can be made read-only. The read-only icon is optional"),
        ("Options", "Options can be individually disabled or set as visible. The value text and display text can be configured. Option overflow can be set to Wrap (default), NoWrap, Scroll or None"),
        ("Clear selection", "Allows the current selection to be cleared from the UI (depends on the data type - see gotchas). Not enabled by default"),
        ("Tooltip", "Tooltip behaviour can be set to EnabledOnOptionOverflow (default), Enabled or Disabled. Tooltip placement is configurable"),
        ("Keyboard interaction", "Refer to the Keyboard controls tab")
    };

    var gotchas = new List<(string, string)>
    {
        ("Selected option", "There is no check for duplicate options and therefore an exception will be thrown if a duplicate option is selected. The client should ensure that options are unique"),
        ("Option equality", "If the data type is a class it should implement IEquatable, or there should be a ValueSelector or DisplaySelector function provided"),
        ("ShowClear", "Can clear only if TData is a reference type or nullable value type")
    };

    var tips = new List<(string, string)>
    {
        ("Option wrapping", "When the option is set to wrap, the --cctc-input-webkit-line-clamp css variable sets the maximum number of lines before truncation is applied"),
        ("Target the cctc-input component(s) on a page ignoring any child cctc-input components. For example, setting a uniform input component width adjusting according to screen size",
@"
<code>
    <pre>

    ::deep cctc-input:not(cctc-input cctc-input) {
        width: 100%;
    }

    @media (min-width: 1200px) {
        ::deep cctc-input:not(cctc-input cctc-input) {
            width: 35%;
        }
    }
    </pre>
</code>")
    };

    var usageCode = new List<(string title, string description, string code, RenderFragment fragment)>
    {

        ("Dropdown type: string, DropDownOptionOverflow: Wrap, TooltipPlacement: Right, DropDownTooltipBehaviour: EnabledOnOptionOverflow", "",
@"<DropDown
    TData=""string""
    Id=""usage1""
    Data=""@(new List<string> { ""One"", ""Two"", ""Three"", LongOption })""
    @bind-Value=""@DropDownStringValue""
    DropDownOptionOverflow=""DropDownOptionOverflow.Wrap""
    DropDownTooltipPlacement=""TooltipPlacement.Right""
    DropDownTooltipBehaviour=""DropDownTooltipBehaviour.EnabledOnOptionOverflow"">
</DropDown>

@code {

    string LongOption { get; set; } = ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat."";
    string DropDownStringValue { get; set; } = ""One"";

}",
        @<DropDown
            TData="string"
            Id="usage1"
            Data="@(new List<string> { "One", "Two", "Three", LongOption })"
            @bind-Value="@DropDownStringValue"
            DropDownOptionOverflow="DropDownOptionOverflow.Wrap"
            DropDownTooltipPlacement="TooltipPlacement.Right"
            DropDownTooltipBehaviour="DropDownTooltipBehaviour.EnabledOnOptionOverflow">
        </DropDown>),
        ("Dropdown type: nullable int, with callback, show clear and disabled options applied", "",
@"<DropDown
    TData=""int?""
    Id=""usage2""
    Data=""@(new List<int?> { 1, 2, 3, 4, 5 })""
    @bind-Value=""@DropDownIntValue""
    Disabled=""@(item => item == 1 || item == 4)""
    SelectionChanged=""@(args => Console.WriteLine(args.Value?.ToString() ?? ""No selection chosen""))""
    ShowClear=""true"">
</DropDown>

@code {

    int? DropDownIntValue { get; set; } = 2;
}",
        @<DropDown
            TData="int?"
            Id="usage2"
            Data="@(new List<int?> { 1, 2, 3, 4, 5 })"
            @bind-Value="@DropDownIntValue"
            Disabled="@(item => item == 1 || item == 4)"
            SelectionChanged="@(args => Console.WriteLine(args.Value?.ToString() ?? "No selection chosen"))"
            ShowClear="true">
        </DropDown>),
        ("Dropdown type: Person class, with CssClass applied", "",
@"<DropDown
    TData=""Person""
    Id=""usage3""
    Data=""@People""
    @bind-Value=""@SelectedPerson""
    ValueSelector=""@(item => item.FirstName)""
    DisplaySelector=""@(item => $""{item.FirstName} {item.LastName}"")""
    CssClass=""info-background"">
</DropDown>

@code {

    public class Person : IEquatable<Person>
    {
        public string? FirstName { get; set; }
        public string? LastName { get; set; }

        public override bool Equals(object? obj) => Equals(obj as Person);

        public bool Equals(Person? person)
        {
            if (person is null)
            {
                return false;
            }

            if (ReferenceEquals(this, person))
            {
                return true;
            }

            return FirstName == person.FirstName
                && LastName == person.LastName;
        }

        public override int GetHashCode() => (FirstName, LastName).GetHashCode();

        public override string ToString()
        {
            return $""{FirstName} {LastName}"";
        }
    }

    List<Person> People { get; set; } = new();
    Person? SelectedPerson { get; set; }

    protected override void OnInitialized()
    {
        var p1 = new Person {FirstName = ""Paul"", LastName = ""McCartney""};
        var people = new List<Person>
        {
            p1,
            new Person {FirstName = ""John"", LastName = ""Lennon""},
            new Person {FirstName = ""George"", LastName = ""Harrison""},
        };

        People = people;
        SelectedPerson = People.Find(p => p.FirstName == ""Paul"")!;
    }
}",
        @<DropDown
            TData="Person"
            Id="usage3"
            Data="@People"
            @bind-Value="@SelectedPerson"
            ValueSelector="@(item => item.FirstName)"
            DisplaySelector="@(item => $"{item.FirstName} {item.LastName}")"
            CssClass="info-background">
        </DropDown>),
        ("Dropdown type: Person class, with callback and show clear", "",
@"<DropDown
    TData=""Person""
    Id=""usage4""
    Data=""@People""
    Value=""@SelectedPerson""
    ValueChanged=""@(item => PersonValueChanged(item))""
    SelectionChanged=""@(args => Console.WriteLine(args.Value?.ToString() ?? ""No selection chosen""))""
    ValueSelector=""@(item => item.FirstName)""
    DisplaySelector=""@(item => $""{item.FirstName} {item.LastName}"")""
    ShowClear=""true"">
</DropDown>

@code {

    public class Person : IEquatable<Person>
    {
        public string? FirstName { get; set; }
        public string? LastName { get; set; }

        public override bool Equals(object? obj) => Equals(obj as Person);

        public bool Equals(Person? person)
        {
            if (person is null)
            {
                return false;
            }

            if (ReferenceEquals(this, person))
            {
                return true;
            }

            return FirstName == person.FirstName
                && LastName == person.LastName;
        }

        public override int GetHashCode() => (FirstName, LastName).GetHashCode();

        public override string ToString()
        {
            return $""{FirstName} {LastName}"";
        }
    }

    List<Person> People { get; set; } = new();
    Person? SelectedPerson { get; set; }

    protected override void OnInitialized()
    {
        var p1 = new Person {FirstName = ""Paul"", LastName = ""McCartney""};
        var people = new List<Person>
        {
            p1,
            new Person {FirstName = ""John"", LastName = ""Lennon""},
            new Person {FirstName = ""George"", LastName = ""Harrison""},
        };

        People = people;
        SelectedPerson = People.Find(p => p.FirstName == ""Paul"")!;
    }

    void PersonValueChanged(Person newValue)
    {
        SelectedPerson = newValue;
    }
}",
        @<DropDown
            TData="Person"
            Id="usage4"
            Data="@People"
            Value="@SelectedPerson"
            ValueChanged="@(item => PersonValueChanged(item))"
            SelectionChanged="@(args => Console.WriteLine(args.Value?.ToString() ?? "No selection chosen"))"
            ValueSelector="@(item => item.FirstName)"
            DisplaySelector="@(item => $"{item.FirstName} {item.LastName}")"
            ShowClear="true">
        </DropDown>),
        ("Dropdown type: Tuple data without ValueSelector, second item not visible", "",
@"<DropDown
    TData=""(string first, string second)""
    Id=""usage5""
    Data=""@TupCollection""
    Value=""@TupValue2""
    ValueChanged=""@(item => SelectionChanged2(item))""
    DisplaySelector=""@(item => $""{item.first}, {item.second}"")""
    Visible=""@(item => item.first != ""2"")"">
</DropDown>

@code {

    protected override void OnInitialized()
    {
        TupValue2 = TupCollection.Skip(2).First();
    }

    List<(string first, string second)> TupCollection => new List<(string first, string second)>()
    {
        (""1"", ""One""),
        (""2"", ""Et harum quidem rerum facilis est et expedita distinctio.""),
        (""3"", ""Pellentesque sit amet tincidunt massa. Vivamus eget ex ullamcorper, pellentesque elit at, maximus erat. Etiam vel condimentum lectus."")
    };

    (string first, string second) TupValue2 { get; set; }

    void SelectionChanged2((string first, string second) tup)
    {
        TupValue2 = tup;
        Console.WriteLine($""SelectionChanged2 called with new value {tup}"");
    }
}",
        @<DropDown
            TData="(string first, string second)"
            Id="usage5"
            Data="@TupCollection"
            Value="@TupValue2"
            ValueChanged="@(item => SelectionChanged2(item))"
            DisplaySelector="@(item => $"{item.first}, {item.second}")"
            Visible="@(item => item.first != "2")">
        </DropDown>),
        ("Dropdown type: Person record, DropDownOptionOverflow: Wrap, TooltipPlacement: Right, DropDownTooltipBehaviour: EnabledOnOptionOverflow", "",
@"<DropDown
    TData=""Person2""
    Id=""usage6""
    Data=""@People2""
    Value=""@SelectedPerson2""
    ValueChanged=""@(item => PersonValueChanged2(item))""
    ValueSelector=""@(item => item.FirstName)""
    DisplaySelector=""@(item => $""{item.FirstName} {item.LastName}"")""
    DropDownOptionOverflow=""DropDownOptionOverflow.Wrap""
    DropDownTooltipPlacement=""TooltipPlacement.Right"">
</DropDown>

@code {

    public record Person2(string FirstName, string LastName);

    List<Person2> People2 = new()
    {
        new ( ""Timothy"", ""Long Surname - Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo."" ),
        new ( ""Catherine"", ""Hollingsworth"" )
    };

    Person2? SelectedPerson2 { get; set; }

    protected override void OnInitialized()
    {
        SelectedPerson2 = People2.Find(p => p.FirstName == ""Catherine"");
    }

    void PersonValueChanged2(Person2 newValue)
    {
        SelectedPerson2 = newValue;
        Console.WriteLine($""PersonValueChanged2 called with new value {newValue}"");
    }
}",
        @<DropDown
            TData="Person2"
            Id="usage6"
            Data="@People2"
            Value="@SelectedPerson2"
            ValueChanged="@(item => PersonValueChanged2(item))"
            ValueSelector="@(item => item.FirstName)"
            DisplaySelector="@(item => $"{item.FirstName} {item.LastName}")"
            DropDownOptionOverflow="DropDownOptionOverflow.Wrap"
            DropDownTooltipPlacement="TooltipPlacement.Right">
        </DropDown>),
        ("Dropdown type: Person record, DropDownOptionOverflow: NoWrap, TooltipPlacement: Right, DropDownTooltipBehaviour: EnabledOnOptionOverflow", "",
@"<DropDown
    TData=""Person2""
    Id=""usage7""
    Data=""@People2""
    Value=""@SelectedPerson2""
    ValueChanged=""@(item => PersonValueChanged2(item))""
    ValueSelector=""@(item => item.FirstName)""
    DisplaySelector=""@(item => $""{item.FirstName} {item.LastName}"")""
    DropDownOptionOverflow=""DropDownOptionOverflow.NoWrap""
    DropDownTooltipPlacement=""TooltipPlacement.Right"">
</DropDown>

@code {

    public record Person2(string FirstName, string LastName);

    List<Person2> People2 = new()
    {
        new ( ""Timothy"", ""Long Surname - Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo."" ),
        new ( ""Catherine"", ""Hollingsworth"" )
    };

    Person2? SelectedPerson2 { get; set; }

    protected override void OnInitialized()
    {
        SelectedPerson2 = People2.Find(p => p.FirstName == ""Catherine"");
    }

    void PersonValueChanged2(Person2 newValue)
    {
        SelectedPerson2 = newValue;
        Console.WriteLine($""PersonValueChanged2 called with new value {newValue}"");
    }
}",
        @<DropDown
            TData="Person2"
            Id="usage7"
            Data="@People2"
            Value="@SelectedPerson2"
            ValueChanged="@(item => PersonValueChanged2(item))"
            ValueSelector="@(item => item.FirstName)"
            DisplaySelector="@(item => $"{item.FirstName} {item.LastName}")"
            DropDownOptionOverflow="DropDownOptionOverflow.NoWrap"
            DropDownTooltipPlacement="TooltipPlacement.Right">
        </DropDown>),
        ("Dropdown type: Person record, DropDownOptionOverflow: Scroll, DropDownTooltipBehaviour: Disabled", "",
@"<DropDown
    TData=""Person2""
    Id=""usage8""
    Data=""@People2""
    Value=""@SelectedPerson2""
    ValueChanged=""@(item => PersonValueChanged2(item))""
    ValueSelector=""@(item => item.FirstName)""
    DisplaySelector=""@(item => $""{item.FirstName} {item.LastName}"")""
    DropDownOptionOverflow=""DropDownOptionOverflow.Scroll""
    DropDownTooltipBehaviour=""DropDownTooltipBehaviour.Disabled"">
</DropDown>

@code {

    public record Person2(string FirstName, string LastName);

    List<Person2> People2 = new()
    {
        new ( ""Timothy"", ""Long Surname - Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo."" ),
        new ( ""Catherine"", ""Hollingsworth"" )
    };

    Person2? SelectedPerson2 { get; set; }

    protected override void OnInitialized()
    {
        SelectedPerson2 = People2.Find(p => p.FirstName == ""Catherine"");
    }

    void PersonValueChanged2(Person2 newValue)
    {
        SelectedPerson2 = newValue;
        Console.WriteLine($""PersonValueChanged2 called with new value {newValue}"");
    }
}",
        @<DropDown
            TData="Person2"
            Id="usage8"
            Data="@People2"
            Value="@SelectedPerson2"
            ValueChanged="@(item => PersonValueChanged2(item))"
            ValueSelector="@(item => item.FirstName)"
            DisplaySelector="@(item => $"{item.FirstName} {item.LastName}")"
            DropDownOptionOverflow="DropDownOptionOverflow.Scroll"
            DropDownTooltipBehaviour="DropDownTooltipBehaviour.Disabled">
        </DropDown>),
        ("Dropdown type: Person record, DropDownOptionOverflow: None, TooltipPlacement: Right, DropDownTooltipBehaviour: EnabledOnOptionOverflow", "",
@"<DropDown
    TData=""Person2""
    Id=""usage9""
    Data=""@People2""
    Value=""@SelectedPerson2""
    ValueChanged=""@(item => PersonValueChanged2(item))""
    ValueSelector=""@(item => item.FirstName)""
    DisplaySelector=""@(item => $""{item.FirstName} {item.LastName}"")""
    DropDownOptionOverflow=""DropDownOptionOverflow.None""
    DropDownTooltipPlacement=""TooltipPlacement.Right""
    DropDownTooltipBehaviour=""DropDownTooltipBehaviour.EnabledOnOptionOverflow"">
</DropDown>

@code {

    public record Person2(string FirstName, string LastName);

    List<Person2> People2 = new()
    {
        new ( ""Timothy"", ""Long Surname - Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo."" ),
        new ( ""Catherine"", ""Hollingsworth"" )
    };

    Person2? SelectedPerson2 { get; set; }

    protected override void OnInitialized()
    {
        SelectedPerson2 = People2.Find(p => p.FirstName == ""Catherine"");
    }

    void PersonValueChanged2(Person2 newValue)
    {
        SelectedPerson2 = newValue;
        Console.WriteLine($""PersonValueChanged2 called with new value {newValue}"");
    }
}",
        @<DropDown
            TData="Person2"
            Id="usage9"
            Data="@People2"
            Value="@SelectedPerson2"
            ValueChanged="@(item => PersonValueChanged2(item))"
            ValueSelector="@(item => item.FirstName)"
            DisplaySelector="@(item => $"{item.FirstName} {item.LastName}")"
            DropDownOptionOverflow="DropDownOptionOverflow.None"
            DropDownTooltipPlacement="TooltipPlacement.Right"
            DropDownTooltipBehaviour="DropDownTooltipBehaviour.EnabledOnOptionOverflow">
        </DropDown>),
        ("Dropdown type: Tuple data, disabled, TooltipPlacement: Right", "",
@"<DropDown
    TData=""(string first, string second)""
    Id=""usage10""
    Data=""@TupCollection""
    Value=""@TupValue2""
    ValueChanged=""@(item => SelectionChanged2(item))""
    DisplaySelector=""@(item => $""{item.first}, {item.second}"")""
    Disabled=""@(_ => true)""
    DropDownTooltipPlacement=""@TooltipPlacement.Right"">
</DropDown>

@code {

    protected override void OnInitialized()
    {
        TupValue2 = TupCollection.Skip(2).First();
    }

    List<(string first, string second)> TupCollection => new List<(string first, string second)>()
    {
        (""1"", ""One""),
        (""2"", ""Et harum quidem rerum facilis est et expedita distinctio.""),
        (""3"", ""Pellentesque sit amet tincidunt massa. Vivamus eget ex ullamcorper, pellentesque elit at, maximus erat. Etiam vel condimentum lectus."")
    };

    (string first, string second) TupValue2 { get; set; }

    void SelectionChanged2((string first, string second) tup)
    {
        TupValue2 = tup;
        Console.WriteLine($""SelectionChanged2 called with new value {tup}"");
    }
}",
        @<DropDown
            TData="(string first, string second)"
            Id="usage10"
            Data="@TupCollection"
            Value="@TupValue2"
            ValueChanged="@(item => SelectionChanged2(item))"
            DisplaySelector="@(item => $"{item.first}, {item.second}")"
            Disabled="@(_ => true)"
            DropDownTooltipPlacement="@TooltipPlacement.Right">
        </DropDown>),
        ("Dropdown type: Tuple data, read-only, TooltipPlacement.Bottom", "",
@"<DropDown
    TData=""(string first, string second)""
    Id=""usage11""
    Data=""@TupCollection""
    Value=""@TupValue""
    ValueChanged=""@(item => SelectionChanged(item))""
    DisplaySelector=""@(item => $""{item.first}, {item.second}"")""
    ReadOnly=""true""
    DropDownTooltipPlacement=""@TooltipPlacement.Bottom"">
</DropDown>

@code {

    protected override void OnInitialized()
    {
        TupValue = TupCollection.Skip(1).First();
    }

    List<(string first, string second)> TupCollection => new List<(string first, string second)>()
    {
        (""1"", ""One""),
        (""2"", ""Et harum quidem rerum facilis est et expedita distinctio.""),
        (""3"", ""Pellentesque sit amet tincidunt massa. Vivamus eget ex ullamcorper, pellentesque elit at, maximus erat. Etiam vel condimentum lectus."")
    };

    (string first, string second) TupValue { get; set; }

    void SelectionChanged((string first, string second) tup)
    {
        TupValue = tup;
        Console.WriteLine($""SelectionChanged called with new value {tup}"");
    }
}",
        @<DropDown
            TData="(string first, string second)"
            Id="usage11"
            Data="@TupCollection"
            Value="@TupValue"
            ValueChanged="@(item => SelectionChanged(item))"
            DisplaySelector="@(item => $"{item.first}, {item.second}")"
            ReadOnly="true"
            DropDownTooltipPlacement="@TooltipPlacement.Bottom">
        </DropDown>),
        ("Dropdown type: Tuple data, read-only, hide read-only icon, TooltipPlacement: Top", "",
@"<DropDown
    TData=""(string first, string second)""
    Id=""usage12""
    Data=""@TupCollection""
    Value=""@TupValue2""
    ValueChanged=""@(item => SelectionChanged2(item))""
    DisplaySelector=""@(item => $""{item.first}, {item.second}"")""
    ReadOnly=""true""
    HideReadOnlyIcon=""true""
    DropDownTooltipPlacement=""@TooltipPlacement.Top"">
</DropDown>

@code {

    protected override void OnInitialized()
    {
        TupValue2 = TupCollection.Skip(2).First();
    }

    List<(string first, string second)> TupCollection => new List<(string first, string second)>()
    {
        (""1"", ""One""),
        (""2"", ""Et harum quidem rerum facilis est et expedita distinctio.""),
        (""3"", ""Pellentesque sit amet tincidunt massa. Vivamus eget ex ullamcorper, pellentesque elit at, maximus erat. Etiam vel condimentum lectus."")
    };

    (string first, string second) TupValue2 { get; set; }

    void SelectionChanged2((string first, string second) tup)
    {
        TupValue2 = tup;
        Console.WriteLine($""SelectionChanged2 called with new value {tup}"");
    }
}",
        @<DropDown
            TData="(string first, string second)"
            Id="usage12"
            Data="@TupCollection"
            Value="@TupValue2"
            ValueChanged="@(item => SelectionChanged2(item))"
            DisplaySelector="@(item => $"{item.first}, {item.second}")"
            ReadOnly="true"
            HideReadOnlyIcon="true"
            DropDownTooltipPlacement="@TooltipPlacement.Top">
        </DropDown>),
        ("Dropdown type: Tuple data, read-only, disabled, TooltipPlacement: Right", "",
@"<DropDown
    TData=""(string first, string second)""
    Id=""usage13""
    Data=""@TupCollection""
    Value=""@TupValue""
    ValueChanged=""@(item => SelectionChanged(item))""
    DisplaySelector=""@(item => $""{item.first}, {item.second}"")""
    ReadOnly=""true""
    Disabled=""@(_ => true)""
    DropDownTooltipPlacement=""@TooltipPlacement.Right"">
</DropDown>

@code {

    protected override void OnInitialized()
    {
        TupValue = TupCollection.Skip(1).First();
    }

    List<(string first, string second)> TupCollection => new List<(string first, string second)>()
    {
        (""1"", ""One""),
        (""2"", ""Et harum quidem rerum facilis est et expedita distinctio.""),
        (""3"", ""Pellentesque sit amet tincidunt massa. Vivamus eget ex ullamcorper, pellentesque elit at, maximus erat. Etiam vel condimentum lectus."")
    };

    (string first, string second) TupValue { get; set; }

    void SelectionChanged((string first, string second) tup)
    {
        TupValue = tup;
        Console.WriteLine($""SelectionChanged called with new value {tup}"");
    }
}",
        @<DropDown
            TData="(string first, string second)"
            Id="usage13"
            Data="@TupCollection"
            Value="@TupValue"
            ValueChanged="@(item => SelectionChanged(item))"
            DisplaySelector="@(item => $"{item.first}, {item.second}")"
            ReadOnly="true"
            Disabled="@(_ => true)"
            DropDownTooltipPlacement="@TooltipPlacement.Right">
        </DropDown>),
        ("Dropdown type: Tuple data, read-only, disabled, hide read-only icon, TooltipPlacement: Bottom", "",
@"<DropDown
    TData=""(string first, string second)""
    Id=""usage14""
    Data=""@TupCollection""
    Value=""@TupValue2""
    ValueChanged=""@(item => SelectionChanged2(item))""
    DisplaySelector=""@(item => $""{item.first}, {item.second}"")""
    ReadOnly=""true""
    Disabled=""@(_ => true)""
    HideReadOnlyIcon=""true""
    DropDownTooltipPlacement=""@TooltipPlacement.Bottom"">
</DropDown>

@code {

    protected override void OnInitialized()
    {
        TupValue2 = TupCollection.Skip(2).First();
    }

    List<(string first, string second)> TupCollection => new List<(string first, string second)>()
    {
        (""1"", ""One""),
        (""2"", ""Et harum quidem rerum facilis est et expedita distinctio.""),
        (""3"", ""Pellentesque sit amet tincidunt massa. Vivamus eget ex ullamcorper, pellentesque elit at, maximus erat. Etiam vel condimentum lectus."")
    };

    (string first, string second) TupValue2 { get; set; }

    void SelectionChanged2((string first, string second) tup)
    {
        TupValue2 = tup;
        Console.WriteLine($""SelectionChanged2 called with new value {tup}"");
    }
}",
        @<DropDown
            TData="(string first, string second)"
            Id="usage14"
            Data="@TupCollection"
            Value="@TupValue2"
            ValueChanged="@(item => SelectionChanged2(item))"
            DisplaySelector="@(item => $"{item.first}, {item.second}")"
            ReadOnly="true"
            Disabled="@(_ => true)"
            HideReadOnlyIcon="true"
            DropDownTooltipPlacement="@TooltipPlacement.Bottom">
        </DropDown>),
        ("Dropdown type: Person class, null initial value with show clear", "",
@"<DropDown
    TData=""Person""
    Id=""usage15""
    Data=""@People""
    @bind-Value=""@SelectedPerson3""
    ValueSelector=""@(item => item.FirstName)""
    DisplaySelector=""@(item => $""{item.FirstName} {item.LastName}"")""
    SelectionChanged=""@(args => Console.WriteLine(args.Value?.ToString() ?? ""No selection chosen""))""
    ShowClear=""true"">
</DropDown>

@code {

    public class Person : IEquatable<Person>
    {
        public string? FirstName { get; set; }
        public string? LastName { get; set; }

        public override bool Equals(object? obj) => Equals(obj as Person);

        public bool Equals(Person? person)
        {
            if (person is null)
            {
                return false;
            }

            if (ReferenceEquals(this, person))
            {
                return true;
            }

            return FirstName == person.FirstName
                && LastName == person.LastName;
        }

        public override int GetHashCode() => (FirstName, LastName).GetHashCode();

        public override string ToString()
        {
            return $""{FirstName} {LastName}"";
        }
    }

    List<Person> People { get; set; } = new();
    Person? SelectedPerson3 { get; set; }

    protected override void OnInitialized()
    {
        var p1 = new Person {FirstName = ""Paul"", LastName = ""McCartney""};
        var people = new List<Person>
        {
            p1,
            new Person {FirstName = ""John"", LastName = ""Lennon""},
            new Person {FirstName = ""George"", LastName = ""Harrison""},
        };

        People = people;
    }
}",
        @<DropDown
            Id="usage15"
            Data="@People"
            @bind-Value="@SelectedPerson3"
            ValueSelector="@(item => item?.FirstName)"
            DisplaySelector="@(item => $"{item?.FirstName} {item?.LastName}")"
            SelectionChanged="@(args => Console.WriteLine(args.Value?.ToString() ?? "No selection chosen"))"
            ShowClear="true">
        </DropDown>),
        ("Dropdown type: Person class, null initial value, do not show clear", "",
@"<DropDown
    TData=""Person""
    Id=""usage16""
    Data=""@People""
    @bind-Value=""@SelectedPerson4""
    ValueSelector=""@(item => item.FirstName)""
    DisplaySelector=""@(item => $""{item.FirstName} {item.LastName}"")""
    SelectionChanged=""@(args => Console.WriteLine(args.Value?.ToString() ?? ""No selection chosen""))"">
</DropDown>

@code {

    public class Person : IEquatable<Person>
    {
        public string? FirstName { get; set; }
        public string? LastName { get; set; }

        public override bool Equals(object? obj) => Equals(obj as Person);

        public bool Equals(Person? person)
        {
            if (person is null)
            {
                return false;
            }

            if (ReferenceEquals(this, person))
            {
                return true;
            }

            return FirstName == person.FirstName
                && LastName == person.LastName;
        }

        public override int GetHashCode() => (FirstName, LastName).GetHashCode();

        public override string ToString()
        {
            return $""{FirstName} {LastName}"";
        }
    }

    List<Person> People { get; set; } = new();
    Person? SelectedPerson4 { get; set; }

    protected override void OnInitialized()
    {
        var p1 = new Person {FirstName = ""Paul"", LastName = ""McCartney""};
        var people = new List<Person>
        {
            p1,
            new Person {FirstName = ""John"", LastName = ""Lennon""},
            new Person {FirstName = ""George"", LastName = ""Harrison""},
        };

        People = people;
    }
}",
        @<DropDown
            TData="Person"
            Id="usage16"
            Data="@People"
            @bind-Value="@SelectedPerson4"
            ValueSelector="@(item => item.FirstName)"
            DisplaySelector="@(item => $"{item.FirstName} {item.LastName}")"
            SelectionChanged="@(args => Console.WriteLine(args.Value?.ToString() ?? "No selection chosen"))">
        </DropDown>),
        ("Dropdown type: Nullable Tuple data with show clear", "",
@"<DropDown
    TData=""(int key, string value)?""
    Id=""usage17""
    Data=""@NullableTupCollection""
    @bind-Value=""@NullableTupValue""
    SelectionChanged=""@(args => Console.WriteLine(args.Value?.ToString() ?? ""No selection chosen""))""
    ShowClear=""true"">
</DropDown>

@code {

    List<(int key, string value)?> NullableTupCollection => new List<(int key, string value)?>()
    {
        (1, ""One""),
        (2, ""Two""),
        (3, ""Three"")
    };

    (int key, string value)? NullableTupValue { get; set; }
}",
        @<DropDown
            TData="(int key, string value)?"
            Id="usage17"
            Data="@NullableTupCollection"
            @bind-Value="@NullableTupValue"
            ValueSelector="@(item => item is null ? item.ToString() : item.Value.key.ToString())"
            DisplaySelector="@(item => item is null ? item.ToString() : item.Value.value.ToString())"
            SelectionChanged="@(args => Console.WriteLine(args.Value?.ToString() ?? "No selection chosen"))"
            ShowClear="true">
        </DropDown>)
    };

    var keyboardControls = new List<string>
    {
        KeyboardOptions
    };
}

@* The wrapper div is necessary here for ::deep isolated css rules to be applied to the DropDown component *@
<div>
    <Sampler
        ComponentName="DropDown"
        ComponentCssName="input"
        ComponentTypeName="dropdown"
        Description="@description"
        Features="@features"
        UsageText="Typical usages of the <code>DropDown</code> component are shown below"
        UsageCodeList="@usageCode"
        KeyboardControls="@keyboardControls"
        Gotchas="@gotchas"
        Tips="@tips"
        ContentHeightPixels="450">
        <ExampleTemplate>
            <DropDown
                TData="string"
                Id="example1"
                Data="@(new List<string> { "One", "Two", "Three", "Four", "Five", LongOption })"
                @bind-Value="@DropDownExampleValue"
                SelectionChanged="@(args => Console.WriteLine(args.Value?.ToString()))">
            </DropDown>
        </ExampleTemplate>
    </Sampler>
</div>

@code {

    const string KeyboardOptions =
    "<div class=\"d-flex flex-column\">" +
        "<small class=\"mt-1\"><strong>With focus on a selected option</strong></small>" +
        "<small>-- Arrow down - <em>move to next option</em></small>" +
        "<small>-- Arrow up - <em>move to previous option</em></small>" +
        "<small>-- Enter | Escape - <em>close dropdown</em></small>" +
        "<small>-- Ctrl + c - <em>copy selected option</em></small>" +
    "</div>";

    public class Person : IEquatable<Person>
    {
        public string? FirstName { get; set; }
        public string? LastName { get; set; }

        public override bool Equals(object? obj) => Equals(obj as Person);

        public bool Equals(Person? person)
        {
            if (person is null)
            {
                return false;
            }

            if (ReferenceEquals(this, person))
            {
                return true;
            }

            return FirstName == person.FirstName
                && LastName == person.LastName;
        }

        public override int GetHashCode() => (FirstName, LastName).GetHashCode();

        public override string ToString()
        {
            return $"{FirstName} {LastName}";
        }
    }

    public record Person2(string FirstName, string LastName);

    List<Person> People { get; set; } = new();

    List<Person2> People2 = new()
    {
        new ( "Timothy", "Long Surname - Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo." ),
        new ( "Catherine", "Hollingsworth" )
    };

    protected override void OnInitialized()
    {
        var p1 = new Person {FirstName = "Paul", LastName = "McCartney"};
        var people = new List<Person>
        {
            p1,
            new Person {FirstName = "John", LastName = "Lennon"},
            new Person {FirstName = "George", LastName = "Harrison"},
        };

        People = people;

        SelectedPerson = People.Find(p => p.FirstName == "Paul")!;

        SelectedPerson2 = People2.Find(p => p.FirstName == "Catherine");

        TupValue = TupCollection.Skip(1).First();

        TupValue2 = TupCollection.Skip(2).First();
    }

    string LongOption { get; set; } = "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.";

    Person? SelectedPerson { get; set; }

    Person2? SelectedPerson2 { get; set; }

    Person? SelectedPerson3 { get; set; }

    Person? SelectedPerson4 { get; set; }

    string DropDownExampleValue { get; set; } = "One";

    string DropDownStringValue { get; set; } = "One";

    int? DropDownIntValue { get; set; } = 2;

    (string first, string second) TupValue { get; set; }

    (string first, string second) TupValue2 { get; set; }

    (int key, string value)? NullableTupValue { get; set; }

    void PersonValueChanged(Person newValue)
    {
        SelectedPerson = newValue;
    }

    void PersonValueChanged2(Person2 newValue)
    {
        SelectedPerson2 = newValue;
        Console.WriteLine($"PersonValueChanged2 called with new value {newValue}");
    }

    List<(string first, string second)> TupCollection => new List<(string first, string second)>()
    {
        ("1", "One"),
        ("2", "Et harum quidem rerum facilis est et expedita distinctio."),
        ("3", "Pellentesque sit amet tincidunt massa. Vivamus eget ex ullamcorper, pellentesque elit at, maximus erat. Etiam vel condimentum lectus.")
    };

    List<(int key, string value)?> NullableTupCollection => new List<(int key, string value)?>()
    {
        (1, "One"),
        (2, "Two"),
        (3, "Three")
    };

    void SelectionChanged((string first, string second) tup)
    {
        TupValue = tup;
        Console.WriteLine($"SelectionChanged called with new value {tup}");
    }

    void SelectionChanged2((string first, string second) tup)
    {
        TupValue2 = tup;
        Console.WriteLine($"SelectionChanged2 called with new value {tup}");
    }
}
