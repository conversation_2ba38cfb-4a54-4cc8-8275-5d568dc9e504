@component @refreshbutton @refreshbutton_6
Feature: the refresh button can have a border or no border
    Scenario: the refresh button component can have a border
        Given the user is at the home page
        And the user selects the "Refresh Button" component in the container "Buttons"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "ButtonIcon: refresh, IconPosition: Left, styled with border"
        Then the button component has a border
        And the button component image matches the base image "refresh button with a border"

    Scenario: the refresh button component can have no border
        Given the user is at the home page
        And the user selects the "Refresh Button" component in the container "Buttons"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "ButtonIcon: light_mode, IconPosition: Right, overridden icon color via Style"
        Then the button component has no border 
        And the button component image matches the base image "refresh button with no border"