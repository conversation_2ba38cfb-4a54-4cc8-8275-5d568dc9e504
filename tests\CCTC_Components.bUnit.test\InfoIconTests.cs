﻿using CCTC_Components.Components.InfoIcon;

namespace CCTC_Components.bUnit.test
{
    public class InfoIconTests : CCTCComponentsTestContext
    {
        [Fact]
        public void InfoIconPopoverClickInvokesCallback()
        {
            AddAddPopover();

            var mockDummyService = new Mock<IDummyService>();
            var cut = RenderComponent<InfoIcon>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.OnClick, async () => { await mockDummyService.Object.MethodOneAsync(); })
            );

            var image = cut.Find("cctc-infoicon-image-container > img");
            image.Click();

            mockDummyService.Verify(m => m.MethodOneAsync(), Times.Once);
        }

        [Fact]
        public void InfoIconTooltipClickInvokesCallback()
        {
            AddAddTooltip();
            AddUpdateTooltipTitle();

            var mockDummyService = new Mock<IDummyService>();
            var cut = RenderComponent<InfoIcon>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.InfoType, InfoType.Tooltip)
                .Add(p => p.OnClick, async () => { await mockDummyService.Object.MethodOneAsync(); })
            );

            var image = cut.Find("cctc-infoicon-image-container > img");
            image.Click();

            mockDummyService.Verify(m => m.MethodOneAsync(), Times.Once);
        }

        [Fact]
        public void InfoIconMissingImageUsesDefault()
        {
            AddAddPopover();

            var cut = RenderComponent<InfoIcon>(parameters => parameters
                .Add(p => p.Id, "test-id")
            );

            var image = cut.Find("cctc-infoicon-image-container > img");
            image.MarkupMatches("<img src=\"./_content/CCTC_Components/images/info_bsinfo_24dp.svg\" alt=\"info\">");
        }

        [Fact]
        public void InfoIconTooltipWithTitleThrowsArgumentException()
        {
            string title = "Some title";
            var cut = () => RenderComponent<InfoIcon>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.InfoType, InfoType.Tooltip)
                .Add(p => p.Title, title)
            );

            var actual = Assert.Throws<ArgumentException>(cut);
            Assert.Equal($"The title '{title}' cannot be used with a Tooltip. Consider using a Popover instead", actual.Message);
        }

        [Fact]
        public void AmbiguousIconThrowsArgumentException()
        {
            var cut = () => RenderComponent<InfoIcon>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.ImageSource, "images/info.png")
                .Add(p => p.ImageIcon, "circle")
            );

            var actual = Assert.Throws<ArgumentException>(cut);
            Assert.Equal($"Provide an ImageSource or an ImageIcon", actual.Message);
        }
    }
}
