1 - the time component can receive time input
2 - the time component can allow an empty value and an entered value can be optionally cleared
3 - the time component time is validated (time format, min and / or max time) and there is feedback provided to the user via an icon
4 - the time component can be made read-only and / or disabled. The read-only icon is optional
5 - the time component has a placeholder which matches the time format