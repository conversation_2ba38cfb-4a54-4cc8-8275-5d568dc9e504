﻿@inherits CCTC_Components.Components.__CCTC.CCTCBase

<cctc-animatedplaceholder id="@Id" class="@CssClass" style="@Style" data-author="cctc">
    <div style="@_componentWrapperStyle">
        <div class="load-wrapper">
            <div class="activity"></div>
        </div>
    </div>
</cctc-animatedplaceholder>

@code {

    /// <summary>
    /// The placeholder height
    /// </summary>
    [Parameter, EditorRequired]
    public required string Height { get; set; }

    /// <summary>
    /// The placeholder width
    /// </summary>
    [Parameter, EditorRequired]
    public required string Width { get; set; }

    /// <summary>
    /// The placeholder margin
    /// </summary>
    [Parameter]
    public string Margin { get; set; } = "0";

    string? _componentWrapperStyle;

    void CheckParam(string parameter, string value)
    {
        if (!ParamInputValidation.CssWidthOrHeightValid(value))
        {
            throw new ArgumentException($"The {parameter} parameter value ({value}) is not valid");
        }
    }

    void CheckMarginParam(string margin)
    {
        if (!ParamInputValidation.CssMarginValid(Margin))
        {
            throw new ArgumentException($"The {nameof(Margin)} parameter value ({margin}) is not valid");
        }
    }

    /// <inheritdoc />
    protected override void OnParametersSet()
    {
        CheckParam("Height", Height);
        CheckParam("Width", Width);
        CheckMarginParam(Margin);
        _componentWrapperStyle =
            new StyleBuilder()
                .AddStyle("height", Height.Trim())
                .AddStyle("width", Width.Trim())
                .AddStyle("margin", Margin.Trim())
                .Build();
    }
}