@component @pill @pill_4

Feature: the pill icon is optional and customisable
    Scenario: the pill has no icon
        Given the user is at the home page
        And the user selects the "Pill" component
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "PillContext: None, ColorStyle: Fill, Size: Medium, Icon: no icon"
        Then the pill component image matches the base image "no icon pill"

    Scenario: the pill has a warning icon
        Given the user is at the home page
        And the user selects the "Pill" component
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "PillContext: Warning, ColorStyle: Fill, Size: Small, Icon: warning"
        Then the pill icon is "warning"
        And the pill icon image matches the base image "warning pill"

    Scenario: the pill has no icon
        Given the user is at the home page
        And the user selects the "Pill" component
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "PillContext: Success, ColorStyle: Fill, Size: Small, Icon: done_outline"
        Then the pill icon is "done_outline"
        And the pill icon image matches the base image "done pill" 




