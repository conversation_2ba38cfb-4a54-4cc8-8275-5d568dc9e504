@component @temporal @date @date_2
Feature: the date component can allow an empty value and an entered value can be optionally cleared
    Scenario: the date component can allow an empty value when allow clear is set to true
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "Format: default, allow clear, null initial value, FeedbackIcon.Error"
        Then the Date component has the value ""

    Scenario: the date component value can be cleared when allow clear is set to true
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: default, allow clear, FeedbackIcon.Error"
        When the user enters "" into the Date component
        Then the Date component has the value ""

    Scenario: the date component value can be cleared via the date picker when allow clear is set to true
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: default, allow clear, FeedbackIcon.Error"
        When the user enters "" into the Date component via the date picker
        Then the Date component has the value ""

    Scenario: the date component can allow an empty value when allow clear is set to false
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "Format: yyyy-MM-dd, null initial value"
        Then the Date component has the value ""

    Scenario: the date component value can not be cleared when allow clear is set to false
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        When the user enters "" into the Date component
        Then the Date component has the value "2022-05-06"

    Scenario: an alert is shown when the date is cleared via the date picker and allow clear is set to false
        Given the user is at the home page
        When the user selects the "Date" component in the container "Input"
        Then on clearing the Date component value via the date picker an alert is displayed with the text "The date is not permitted to be cleared"