@component @temporal @dateandtime @dateandtime_1
Feature: the date and time component date can be typed in as text or can be entered via a date picker. Time can be typed in as text
    Scenario: the date and time component sample page is available
        Given the user is at the home page
        When the user selects the "Date and time" component in the container "Input"
        Then the url ending is "dateandtimesample"

    Scenario: the date and time component date and time can be typed in as text
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        When the user enters "06November2023" into the Date part of the Date and Time component
        And the user enters "1214am" into the Time part of the Date and Time component
        Then the Date part of the Date and Time component has the value "06 November 2023"
        And the Time part of the Date and Time component has the value "12:14 am"

    Scenario: the date and time component date can be entered via a date picker
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        When the user enters "2023-11-06" into the Date part of the Date and Time component via the date picker
        Then the Date part of the Date and Time component has the value "06 November 2023"

