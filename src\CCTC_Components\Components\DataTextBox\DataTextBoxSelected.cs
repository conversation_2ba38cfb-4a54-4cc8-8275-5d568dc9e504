﻿using Microsoft.AspNetCore.Components;

namespace CCTC_Components.Components.DataTextBox;

/// <summary>
/// Represents a selected part of a <see cref="DataTextBox{TData,TKey}"/> path
/// </summary>
/// <typeparam name="TData">The type of data the associated DataTextBox uses</typeparam>
public class DataTextBoxSelected<TData>
{
    /// <summary>
    /// The index of the selected item in the list of selections
    /// </summary>
    /// <remarks>This is set on render and is the index of the item within the list of selections</remarks>
    public int Index { get; set; }

    /// <summary>
    /// The data item to which the this selection relates
    /// </summary>
    public required TData Content { get; init; }

    /// <summary>
    /// The <see cref="ElementReference"/> associated with the item
    /// </summary>
    /// <remarks>This is set on render</remarks>
    public ElementReference ElementReference { get; set; }

    /// <summary>
    /// The separator that should be displayed
    /// </summary>
    public string? Separator { get; set; }

    /// <summary>
    /// Creates a new <see cref="DataTextBoxSelected{TData}"/> with the given content
    /// </summary>
    /// <param name="content">The content to provide</param>
    /// <param name="separator">The separator to use</param>
    /// <returns>A new instance of  <see cref="DataTextBoxSelected{TData}"/></returns>
    public static DataTextBoxSelected<TData> Create(TData content, string separator) => new() { Content = content, Separator = separator };

    /// <summary>
    /// Creates a new <see cref="DataTextBoxSelected{TData}"/> with the given content
    /// </summary>
    /// <param name="content">The content to provide</param>
    /// <returns>A new instance of  <see cref="DataTextBoxSelected{TData}"/></returns>
    public static DataTextBoxSelected<TData> Create(TData content) => new() { Content = content };
}