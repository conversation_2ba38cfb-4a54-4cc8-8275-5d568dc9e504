import { ICustomWorld } from '../../support/custom-world';
import * as Helpers from '../../support/helper-functions';
import * as TextboxRoleFunctions from '../../support/step-functions/textbox-role-functions';
import { Then } from '@cucumber/cucumber';

Then(
  'the Text area component image matches the base image {string}',
  async function (this: ICustomWorld, name: string) {
    const textbox = Helpers.getSamplerTabsSelectedContent(this).getByRole('textbox');
    await textbox.evaluate((element) => {
      element.scrollTo(0, 0);
    });

    await TextboxRoleFunctions.assertImageMatchesBaseImage(
      this,
      'cctc-input[data-cctc-input-type="textarea"]',
      name
    );
  }
);

Then(
  'the Text area component has {int} row(s)',
  async function (this: ICustomWorld, numRows: number) {
    await TextboxRoleFunctions.assertHasAttribute(this, 'rows', numRows.toString());
  }
);
