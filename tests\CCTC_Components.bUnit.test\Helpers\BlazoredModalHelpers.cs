﻿using Blazored.Modal;
using Blazored.Modal.Services;
using static Bunit.ComponentParameterFactory;

namespace CCTC_Components.bUnit.test.Helpers
{
    public static class BlazoredModalHelpers
    {
        public static void ArrangeBlazoredModal(TestContext ctx, out IRenderedComponent<BlazoredModal>? cut, out IModalService? modalService)
        {
            modalService = ctx.Services.GetService<IModalService>();

            if (modalService is not null)
            {
                cut = ctx.RenderComponent<BlazoredModal>(CascadingValue(modalService));
            }
            else
            {
                cut = null;
            }
        }
    }
}
