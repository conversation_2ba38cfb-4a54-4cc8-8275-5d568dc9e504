﻿@using Microsoft.AspNetCore.Components.Routing;
@using Microsoft.JSInterop
@using CCTC_Components.Components.InfoIcon
@inject NavigationManager NavManager
@implements IDisposable
@inherits CCTC_Components.Components.__CCTC.CCTCBase

@if (Visible)
{
    <cctc-panelmenuitem id="@Id" class="@CssClass" style="@Style" data-author="cctc">
        <div class="panelmenu-item @_scopeCls" @onclick="Click">
            <div class="d-flex align-items-center @(Parent is not null ? "ps-2" : "ps-n2")">

                @if (GrandParent.IsCollapsed)
                {
                    <Tooltip
                        Id="@_toolTipId"
                        Content="@Title"
                        TooltipPlacement="@TooltipPlacement.Right">
                        <div class="lh-1">
                            <div class="material-icons">@Icon</div>
                            <div class="panelmenu-item-icon-text">@Title</div>
                        </div>
                    </Tooltip>
                }
                else
                {
                    <div class="material-icons panelmenu-item-icon @(HideIcon ? "panelmenu-item-icon-hide" : "")">
                        @Icon
                    </div>
                }

                @if (!GrandParent.IsCollapsed)
                {
                    <div class="flex-grow-1 panelmenu-item-title">
                        @Title
                    </div>
                }
            </div>

            <div>
                @ChildContent
            </div>

        </div>
    </cctc-panelmenuitem>
}

@code {

    const string InScopeCls = "panelmenu-item-in-scope";
    const string NotInScopeCls = "panelmenu-item-not-in-scope";

    string _scopeCls = NotInScopeCls;

    /// <summary>
    /// The parent <see cref="PanelMenuHeader"/> associated with the PanelMenuItem
    /// </summary>
    [CascadingParameter]
    public PanelMenuHeader? Parent { get; set; }

    /// <summary>
    /// The (grand)parent <see cref="PanelMenu"/> associated with the PanelMenuItem
    /// </summary>
    [CascadingParameter]
    public required PanelMenu GrandParent { get; set; }

    /// <summary>
    /// The title text to display
    /// </summary>
    [Parameter, EditorRequired]
    public required string Title { get; set; }

    /// <summary>
    /// The icon to display
    /// </summary>
    [Parameter]
    public string? Icon { get; set; }

    /// <summary>
    /// The path to navigate to
    /// </summary>
    [Parameter, EditorRequired]
    public required string Path { get; set; }

    /// <summary>
    /// The content of the item
    /// </summary>
    [Parameter]
    public RenderFragment? ChildContent { get; set; }

    /// <summary>
    /// A callback when the item is selected
    /// </summary>
    [Parameter]
    public EventCallback<string> PanelMenuItemSelected { get; set; }

    /// <summary>
    /// Display the item when true
    /// </summary>
    [Parameter]
    public bool Visible { get; set; } = true;

    /// <summary>
    /// Hides the icon when true
    /// </summary>
    [Parameter]
    public bool HideIcon { get; set; }

    void Click()
    {
        PanelMenuItemSelected.InvokeAsync(Id);
        NavManager.NavigateTo(Path);
    }

    bool _isInScope;

    bool IsInScope(string targetLocation)
    {
        _isInScope =
            GrandParent.MatchPathPred?.Invoke((targetLocation, Path))
            ?? PanelMenuMatches.Default((targetLocation, Path));

        return _isInScope;
    }

    async Task SetScopeCls(string location)
    {
        await Task.Run(() =>
        {
            _scopeCls =
                IsInScope(location)
                    ? InScopeCls
                    : NotInScopeCls;
        });
    }

    string _toolTipId = string.Empty;

    /// <inheritdoc cref="OnInitialized"/>
    protected override void OnInitialized()
    {
        _toolTipId = $"{Id}-tooltip";
        PanelMenuMatches.Init(NavManager);

        if (Parent is null)
        {
            GrandParent.RegisterGrandChild(this);
        }
    }

    IDisposable? _registration;

    /// <inheritdoc cref="OnAfterRender(bool)"/>
    protected override void OnAfterRender(bool firstRender)
    {
        if (firstRender)
        {
            if (HideIcon && GrandParent.IsCollapsed)
            {
                throw new InvalidOperationException("a panel menu cannot have both HideIcon and IconsOnly set to true as there is nothing to display if so");
            }

            _registration =
                NavManager.RegisterLocationChangingHandler(OnLocationChanging);
        }
    }

    /// <inheritdoc cref="OnParametersSetAsync"/>
    protected override async Task OnParametersSetAsync()
    {
        await SetScopeCls(NavManager.Uri);
    }

    async ValueTask OnLocationChanging(LocationChangingContext context)
    {
        await SetScopeCls(context.TargetLocation);
        StateHasChanged();
    }

    /// <inheritdoc cref="Dispose"/>
    public void Dispose() => _registration?.Dispose();

}