@component @pill @pill_1

Feature: the pill button can be pressed and this is registered
    Scenario: the pill sample is available
        Given the user is at the home page
        When the user selects the "Pill" component    
        Then the url ending is "pillsample" 

    Scenario: the counter increases when the pill button is pressed
        Given the user is at the home page
        And the user selects the "Pill" component
        And the pill counter number is 0
        And the user clicks the icon within the pill component
        And the pill counter number is 1
        And the user clicks the Label within the pill component
        And the pill counter number is 2
        When the user clicks the the pill component container
        Then the pill counter number is 3
        And the pill component image matches the base image "pill content example"
        And the counter matches the base image "pill clicked 3 different ways, total 3"



        
