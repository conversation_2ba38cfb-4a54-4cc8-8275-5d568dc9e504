﻿namespace CCTC_Components.bUnit.test.HelperTests
{
    using CCTC_Components.Helpers;

    public class ParamInputValidationTests
    {
        [Theory]
        [InlineData("bad format", false)]
        [InlineData("1..2", false)]
        [InlineData("EE4", false)]
        [InlineData("#", true)]
        [InlineData("-#", true)]
        [InlineData("0000", true)]
        [InlineData("-0000", true)]
        [InlineData("0000.00", true)]
        [InlineData("0000.0#", true)]
        [InlineData("-0000.00", true)]
        [InlineData("00.00", true)]
        [InlineData("00.##", true)]
        [InlineData("0", true)]
        [InlineData("-0", true)]
        [InlineData("#000", true)]
        [InlineData("###0", true)]
        [InlineData("0.0", true)]
        [InlineData("-##00", true)]
        [InlineData("#.##", true)]
        [InlineData("-#.##", true)]
        [InlineData("#0.0#", true)]
        [InlineData("#0.0e0", true)]
        [InlineData("0.0##e+00", true)]
        [InlineData("-0.0##e+00", true)]
        [InlineData("0.0e-00", true)]
        [InlineData("-0.0e-00", true)]
        [InlineData("E", true)]
        [InlineData("E10", true)]
        [InlineData("E4", true)]
        [InlineData("-e2", true)]
        [InlineData("0.0##e-0", true)]
        [InlineData("0.0##e+0", true)]
        [InlineData(null, true)]
        public void NumberFormatValidReturnsCorrectly(string? value, bool expected)
        {
            bool actual = ParamInputValidation.NumberFormatValid(value);
            Assert.Equal(expected, actual);
        }

        [Theory]
        [InlineData(null, false)]
        [InlineData("15px 1px", false)]
        [InlineData("15p", false)]
        [InlineData("%", false)]
        [InlineData("1..2rem", false)]
        [InlineData("auto", true)]
        [InlineData("0", true)]
        [InlineData("1rem", true)]
        [InlineData("1.2rem", true)]
        [InlineData("1em", true)]
        [InlineData("1px", true)]
        [InlineData(" 10% ", true)]
        public void CssWidthOrHeightValidReturnsCorrectly(string? value, bool expected)
        {
            bool actual = ParamInputValidation.CssWidthOrHeightValid(value);
            Assert.Equal(expected, actual);
        }

        [Theory]
        [InlineData(null, false)]
        [InlineData("autorem", false)]
        [InlineData("12rem autorem", false)]
        [InlineData("2rem rem", false)]
        [InlineData("10px 2px 2px  10px", false)]
        [InlineData("12rem 5p 5rem 3rem", false)]
        [InlineData("3rem 5% 5px 3rem 4rem", false)]
        [InlineData("3..44px", false)]
        [InlineData("auto", true)]
        [InlineData("0", true)]
        [InlineData("1rem", true)]
        [InlineData("0.25rem", true)]
        [InlineData("1em", true)]
        [InlineData("1px", true)]
        [InlineData(" 10px 2px 2px 10px ", true)]
        [InlineData("0.25rem 10.5px 2px 10px", true)]
        [InlineData("10%", true)]
        public void CssMarginValidReturnsCorrectly(string? value, bool expected)
        {
            bool actual = ParamInputValidation.CssMarginValid(value);
            Assert.Equal(expected, actual);
        }
    }
}
