﻿namespace CCTC_Components.Components.Tooltip
{
    /// <summary>
    /// Tooltip behaviour
    /// </summary>
    public abstract record TooltipBehaviour
    {
        private TooltipBehaviour() { }

        /// <summary>
        /// Enable tooltip
        /// </summary>
        public sealed record Enabled() : TooltipBehaviour;

        /// <summary>
        /// Disable tooltip
        /// </summary>
        public sealed record Disabled() : TooltipBehaviour;

        /// <summary>
        /// Enable tooltip on overflow
        /// </summary>
        /// <param name="EnabledOnOverflowSelector">Enable the tooltip based on the overflow status of the element with the provided selector</param>
        public sealed record EnabledOnOverflow(string EnabledOnOverflowSelector) : TooltipBehaviour;
    }
}
