﻿using System.Collections;

namespace CCTC_Components_UI.Helpers.Services;

public class TreeNode<T> : IEnumerable<TreeNode<T>>
{
    public T Data { get; set; }
    public TreeNode<T>? Parent { get; set; }
    public ICollection<TreeNode<T>> Children { get; set; }

    public bool IsRoot => Parent == null;

    public bool IsLeaf => Children.Count == 0;

    public int Level => IsRoot ? 0 : Parent!.Level + 1;

    public TreeNode(T data)
    {
        Data = data;
        Children = new LinkedList<TreeNode<T>>();
        ElementsIndex = new LinkedList<TreeNode<T>>();
        ElementsIndex.Add(this);
    }

    public TreeNode<T> AddChild(T child)
    {
        TreeNode<T> childNode = new(child) { Parent = this };
        Children.Add(childNode);
        RegisterChildForSearch(childNode);

        return childNode;
    }

    public TreeNode<T> AddChild(TreeNode<T> child)
    {
        Children.Add(child);
        RegisterChildForSearch(child);

        return child;
    }

    public override string ToString()
    {
        return Data!.ToString()!;
    }

    public string AsString => ToString();

    #region searching

    ICollection<TreeNode<T>> ElementsIndex { get; set; }

    void RegisterChildForSearch(TreeNode<T> node)
    {
        ElementsIndex.Add(node);
        Parent?.RegisterChildForSearch(node);
    }

    public TreeNode<T> FindTreeNode(Func<TreeNode<T>, bool> predicate)
    {
        return ElementsIndex.FirstOrDefault(predicate)!;
    }

    #endregion


    #region iterating

    IEnumerator IEnumerable.GetEnumerator()
    {
        return GetEnumerator();
    }

    public IEnumerator<TreeNode<T>> GetEnumerator()
    {
        yield return this;
        foreach (var directChild in this.Children)
        {
            foreach (var anyChild in directChild)
                yield return anyChild;
        }
    }

    #endregion;
}