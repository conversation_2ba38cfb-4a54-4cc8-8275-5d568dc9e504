﻿namespace CCTC_Components.Components.Lister;

/// <summary>
/// The Lister component
/// </summary>
/// <typeparam name="TData">The <see cref="Type"/> of data the component uses</typeparam>
/// <remarks>The Lister component is not designed to have updateable data source. The load of
/// data occurs on the initialization of the component and not when parameters are set. To
/// force a reload (for example, as used in DataTextBox where the data source is updated
/// as a user navigates through a collection of objects), use the public Load(true) method
/// on the Lister. Note, a flag should be used to prevent and continuous rerender.</remarks>
public partial class Lister<TData>
{

}


