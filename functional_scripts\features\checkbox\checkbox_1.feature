@component @checkbox @checkbox_1

Feature: the checkbox component checkbox can be checked or unchecked

    Scenario: the checkbox component sample page is available
        Given the user is at the home page
        When the user selects the "Checkbox" component in the container "Input"
        Then the url ending is "checkboxsample"

    Scenario: the checkbox component can be unchecked using the mouse
        Given the user is at the home page
        And the user selects the "Checkbox" component in the container "Input"
        When the user clicks the Checkbox within the Checkbox component
        Then the Checkbox component is unchecked
        And the Checkbox component image matches the base image "checkbox-unchecked"

    Scenario: the checkbox component can be checked using the mouse
        Given the user is at the home page
        And the user selects the "Checkbox" component in the container "Input"
        And the user clicks the Checkbox within the Checkbox component
        And the Checkbox component is unchecked
        And the Checkbox component image matches the base image "checkbox-unchecked"
        When the user clicks the Checkbox within the Checkbox component
        Then the Checkbox component is checked
        And the Checkbox component image matches the base image "checkbox-checked"

