﻿using CCTC_Components.Components.__CCTC.Models;
using Microsoft.AspNetCore.Components.Web;

namespace CCTC_Components.Components.TextBox.TextInteraction;

/// <summary>
/// A payload for interaction via touch
/// </summary>
/// <param name="TouchEventArgs">The <see cref="TouchEventArgs"/> associated with the event</param>
/// <param name="CursorSelection">The cursor positioning as a <see cref="CursorSelection"/></param>
public record TouchInteractionArgs(TouchEventArgs TouchEventArgs, CursorSelection CursorSelection)
    : InteractionArgs(CursorSelection)
{}