﻿@page "/dateandtimesample"

@{
    var description = new List<string>
    {
        "A component for date and time input. Support Reactive input and format validation"
    };

    var features = new List<(string, string)>
    {
        ("Interaction", "Can be made read-only and / or disabled. The read-only icon is optional"),
        ("Constrained input", "Apply a date format (see <code>Lib.Common.Services.Clock.availableDateFormats</code> for the supported formats), set a min and / or max datetime"),
        ("Binding", "The component throttle speed can be changed"),
        ("Feedback", "Provides configurable feedback on the inputted date and time"),
        ("Clear value", "Allows the current value to be cleared from the UI (depends on the data type - see gotchas). Not enabled by default"),
    };

    var gotchas = new List<(string, string)>
    {
        ("ThrottleMs", "500 ms is the minimum <code>ThrottleMs</code> and will be applied automatically unless a higher value is provided"),
        ("AllowClear", "Can clear only if TValue is of type nullable datetime"),
        ("--cctc-input-webkit-line-clamp", "CSS variable not used by this component")
    };

    var tips = new List<(string, string)>
    {
        ("Target the cctc-input component(s) on a page ignoring any child cctc-input components. For example, setting a uniform input component width adjusting according to screen size",
@"
<code>
    <pre>

    ::deep cctc-input:not(cctc-input cctc-input) {
        width: 100%;
    }

    @media (min-width: 1200px) {
        ::deep cctc-input:not(cctc-input cctc-input) {
            width: 35%;
        }
    }
    </pre>
</code>")
    };

    var usageCode = new List<(string title, string description, string code, RenderFragment fragment)>
    {
        ("Format: dd MMMM yyyy h:mm tt, FeedbackIcon: default", "",
@"<DateAndTime
    Id=""usage1""
    TValue=""DateTime""
    DateFormat=""dd MMMM yyyy h:mm tt""
    Value=""@DateTimeValue""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    ValueChanged=""@(args => DateTimeChanged(args, ""dd MMMM yyyy h:mm tt""))""
    DateTimeChanged=""@(args => Console.WriteLine(args.Value))"">
</DateAndTime>

@code {

    DateTime DateTimeValue { get; set; } = new DateTime(2022, 10, 4, 12, 13, 14);

    void DateTimeChanged(DateTime newValue, string format)
    {
        DateTimeValue = newValue;
        Console.WriteLine($""DateTime new value is: {newValue.ToString(format)}"");
    }
}",
        @<DateAndTime
            Id="usage1"
            TValue="DateTime"
            DateFormat="dd MMMM yyyy h:mm tt"
            Value="@DateTimeValue"
            ThrottleMs="@Constants.DefaultThrottleMs"
            ValueChanged="@(args => DateTimeChanged(args, "dd MMMM yyyy h:mm tt"))"
            DateTimeChanged="@(args => Console.WriteLine(args.Value))">
        </DateAndTime>),      
        ("Format: yyyy-MM-dd HH:mm:ss, FeedbackIcon.Error, CssClass applied", "",
@"<DateAndTime
    Id=""usage2""
    DateFormat=""yyyy-MM-dd HH:mm:ss""
    @bind-Value=""@DateTimeValue""
    CssClass=""red-border""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    DateTimeChanged=""@(args => Console.WriteLine(args.Value))""
    ActionOnDateOrTimeChanged=""OnDateOrTimeChanged""
    FeedbackIcon=""FeedbackIcon.Error"">
</DateAndTime>

@code {

    DateTime DateTimeValue { get; set; } = new DateTime(2022, 10, 4, 12, 13, 14);

    void OnDateOrTimeChanged(DateOnly? dateOnly, TimeOnly? timeOnly)
    {
        Console.WriteLine($""date: {(dateOnly is null ? ""cleared"" : dateOnly.Value.ToString(""yyyy-MM-dd""))}; time: {(timeOnly is null ? ""cleared"" : timeOnly.Value.ToString(""HH:mm:ss""))}"");
    }
}",
        @<DateAndTime
            Id="usage2"
            DateFormat="yyyy-MM-dd HH:mm:ss"
            @bind-Value="@DateTimeValue"
            CssClass="red-border"
            ThrottleMs="@Constants.DefaultThrottleMs"
            DateTimeChanged="@(args => Console.WriteLine(args.Value))"
            ActionOnDateOrTimeChanged="OnDateOrTimeChanged"
            FeedbackIcon="FeedbackIcon.Error">
        </DateAndTime>),
        ("Format: default, FeedbackIcon.Valid", "",
@"<DateAndTime
    Id=""usage3""
    @bind-Value=""@DateTimeValue""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    DateTimeChanged=""@(args => Console.WriteLine(args.Value))""
    FeedbackIcon=""FeedbackIcon.Valid"">
</DateAndTime>

@code {

    DateTime DateTimeValue { get; set; } = new DateTime(2022, 10, 4, 12, 13, 14);
}",
        @<DateAndTime
            Id="usage3"
            @bind-Value="@DateTimeValue"
            ThrottleMs="@Constants.DefaultThrottleMs"
            DateTimeChanged="@(args => Console.WriteLine(args.Value))"
            FeedbackIcon="FeedbackIcon.Valid">
        </DateAndTime>),
    ("Format: default, null initial value, min (2022-01-05 14:33:20) and max (2023-01-05 14:33:22), FeedbackIcon.Both", "",
@"<DateAndTime
    Id=""usage4""
    MinDateTime=""@(new DateTime(2022, 1, 5, 14, 33, 20))""
    MaxDateTime=""@(new DateTime(2023, 1, 5, 14, 33, 22))""
    @bind-Value=""@DateTimeValueNullable""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    DateTimeChanged=""@(args => Console.WriteLine(args.Value))""
    ActionOnDateOrTimeChanged=""OnDateOrTimeChanged""
    FeedbackIcon=""FeedbackIcon.Both"">
</DateAndTime>

@code {

    DateTime? DateTimeValueNullable { get; set; }

    void OnDateOrTimeChanged(DateOnly? dateOnly, TimeOnly? timeOnly)
    {
        Console.WriteLine($""date: {(dateOnly is null ? ""cleared"" : dateOnly.Value.ToString(""yyyy-MM-dd""))}; time: {(timeOnly is null ? ""cleared"" : timeOnly.Value.ToString(""HH:mm:ss""))}"");
    }
}",
        @<DateAndTime
            Id="usage4"
            MinDateTime="@(new DateTime(2022, 1, 5, 14, 33, 20))"
            MaxDateTime="@(new DateTime(2023, 1, 5, 14, 33, 22))"
            @bind-Value="DateTimeValueNullable"
            ThrottleMs="@Constants.DefaultThrottleMs"
            DateTimeChanged="@(args => Console.WriteLine(args.Value))"
            ActionOnDateOrTimeChanged="OnDateOrTimeChanged"
            FeedbackIcon="FeedbackIcon.Both">
        </DateAndTime>),
        ("Format: dd MMMM yyyy h:mm tt, FeedbackIcon: default, read-only", "",
@"<DateAndTime
    Id=""usage5""
    TValue=""DateTime""
    DateFormat=""dd MMMM yyyy h:mm tt""
    Value=""@DateTimeValue""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    ValueChanged=""@(args => DateTimeChanged(args, ""dd MMMM yyyy h:mm tt""))""
    DateTimeChanged=""@(args => Console.WriteLine(args.Value))""
    ReadOnly=""true"">
</DateAndTime>

@code {

    DateTime DateTimeValue { get; set; } = new DateTime(2022, 10, 4, 12, 13, 14);

    void DateTimeChanged(DateTime newValue, string format)
    {
        DateTimeValue = newValue;
        Console.WriteLine($""DateTime new value is: {newValue.ToString(format)}"");
    }
}",
        @<DateAndTime
            Id="usage5"
            TValue="DateTime"
            DateFormat="dd MMMM yyyy h:mm tt"
            Value="@DateTimeValue"
            ThrottleMs="@Constants.DefaultThrottleMs"
            ValueChanged="@(args => DateTimeChanged(args, "dd MMMM yyyy h:mm tt"))"
            DateTimeChanged="@(args => Console.WriteLine(args.Value))"
            ReadOnly="true">
        </DateAndTime>),
    ("Format: dd MMMM yyyy h:mm tt, FeedbackIcon: default, read-only, read-only icon hidden", "",
@"<DateAndTime
    Id=""usage6""
    TValue=""DateTime""
    DateFormat=""dd MMMM yyyy h:mm tt""
    Value=""@DateTimeValue""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    ValueChanged=""@(args => DateTimeChanged(args, ""dd MMMM yyyy h:mm tt""))""
    DateTimeChanged=""@(args => Console.WriteLine(args.Value))""
    ReadOnly=""true""
    HideReadOnlyIcon=""true"">
</DateAndTime>

@code {

    DateTime DateTimeValue { get; set; } = new DateTime(2022, 10, 4, 12, 13, 14);

    void DateTimeChanged(DateTime newValue, string format)
    {
        DateTimeValue = newValue;
        Console.WriteLine($""DateTime new value is: {newValue.ToString(format)}"");
    }
}",
        @<DateAndTime
            Id="usage6"
            TValue="DateTime"
            DateFormat="dd MMMM yyyy h:mm tt"
            Value="@DateTimeValue"
            ThrottleMs="@Constants.DefaultThrottleMs"
            ValueChanged="@(args => DateTimeChanged(args, "dd MMMM yyyy h:mm tt"))"
            DateTimeChanged="@(args => Console.WriteLine(args.Value))"
            ReadOnly="true"
            HideReadOnlyIcon="true">
        </DateAndTime>),
        ("Format: dd MMMM yyyy h:mm tt, FeedbackIcon: default, disabled", "",
@"<DateAndTime
    Id=""usage7""
    TValue=""DateTime""
    DateFormat=""dd MMMM yyyy h:mm tt""
    Value=""@DateTimeValue""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    ValueChanged=""@(args => DateTimeChanged(args, ""dd MMMM yyyy h:mm tt""))""
    DateTimeChanged=""@(args => Console.WriteLine(args.Value))""
    Disabled=""true"">
</DateAndTime>

@code {

    DateTime DateTimeValue { get; set; } = new DateTime(2022, 10, 4, 12, 13, 14);

    void DateTimeChanged(DateTime newValue, string format)
    {
        DateTimeValue = newValue;
        Console.WriteLine($""DateTime new value is: {newValue.ToString(format)}"");
    }
}",
        @<DateAndTime
            Id="usage7"
            TValue="DateTime"
            DateFormat="dd MMMM yyyy h:mm tt"
            Value="@DateTimeValue"
            ThrottleMs="@Constants.DefaultThrottleMs"
            ValueChanged="@(args => DateTimeChanged(args, "dd MMMM yyyy h:mm tt"))"
            DateTimeChanged="@(args => Console.WriteLine(args.Value))"
            Disabled="true">
        </DateAndTime>),
        ("Format: dd MMMM yyyy h:mm tt, FeedbackIcon: default, read-only and disabled", "",
@"<DateAndTime
    Id=""usage8""
    TValue=""DateTime""
    DateFormat=""dd MMMM yyyy h:mm tt""
    Value=""@DateTimeValue""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    ValueChanged=""@(args => DateTimeChanged(args, ""dd MMMM yyyy h:mm tt""))""
    DateTimeChanged=""@(args => Console.WriteLine(args.Value))""
    ReadOnly=""true""
    Disabled=""true"">
</DateAndTime>

@code {

    DateTime DateTimeValue { get; set; } = new DateTime(2022, 10, 4, 12, 13, 14);

    void DateTimeChanged(DateTime newValue, string format)
    {
        DateTimeValue = newValue;
        Console.WriteLine($""DateTime new value is: {newValue.ToString(format)}"");
    }
}",
        @<DateAndTime
            Id="usage8"
            TValue="DateTime"
            DateFormat="dd MMMM yyyy h:mm tt"
            Value="@DateTimeValue"
            ThrottleMs="@Constants.DefaultThrottleMs"
            ValueChanged="@(args => DateTimeChanged(args, "dd MMMM yyyy h:mm tt"))"
            DateTimeChanged="@(args => Console.WriteLine(args.Value))"
            ReadOnly="true"
            Disabled="true">
        </DateAndTime>),
        ("Format: dd MMMM yyyy h:mm tt, FeedbackIcon: default, read-only, disabled and read-only icon hidden", "",
@"<DateAndTime
    Id=""usage9""
    TValue=""DateTime""
    DateFormat=""dd MMMM yyyy h:mm tt""
    Value=""@DateTimeValue""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    ValueChanged=""@(args => DateTimeChanged(args, ""dd MMMM yyyy h:mm tt""))""
    DateTimeChanged=""@(args => Console.WriteLine(args.Value))""
    ReadOnly=""true""
    Disabled=""true""
    HideReadOnlyIcon=""true"">
</DateAndTime>

@code {

    DateTime DateTimeValue { get; set; } = new DateTime(2022, 10, 4, 12, 13, 14);

    void DateTimeChanged(DateTime newValue, string format)
    {
        DateTimeValue = newValue;
        Console.WriteLine($""DateTime new value is: {newValue.ToString(format)}"");
    }
}",
        @<DateAndTime
            Id="usage9"
            TValue="DateTime"
            DateFormat="dd MMMM yyyy h:mm tt"
            Value="@DateTimeValue"
            ThrottleMs="@Constants.DefaultThrottleMs"
            ValueChanged="@(args => DateTimeChanged(args, "dd MMMM yyyy h:mm tt"))"
            DateTimeChanged="@(args => Console.WriteLine(args.Value))"
            ReadOnly="true"
            Disabled="true"
            HideReadOnlyIcon="true">
        </DateAndTime>),
    ("Format: default, allow clear, FeedbackIcon.Error", "",
@"<DateAndTime
    Id=""usage10""
    @bind-Value=""@DateTimeValueNullable2""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    DateTimeChanged=""@(args => Console.WriteLine(args.Value ?? ""values cleared""))""
    ActionOnDateOrTimeChanged=""OnDateOrTimeChanged""
    AllowClear=""true""
    FeedbackIcon=""FeedbackIcon.Error"">
</DateAndTime>

@code {

    DateTime? DateTimeValueNullable2 { get; set; } = new DateTime(2024, 12, 6, 11, 12, 13);

    void OnDateOrTimeChanged(DateOnly? dateOnly, TimeOnly? timeOnly)
    {
        Console.WriteLine($""date: {(dateOnly is null ? ""cleared"" : dateOnly.Value.ToString(""yyyy-MM-dd""))}; time: {(timeOnly is null ? ""cleared"" : timeOnly.Value.ToString(""HH:mm:ss""))}"");
    }
}",
        @<DateAndTime
            Id="usage10"
            @bind-Value="@DateTimeValueNullable2"
            ThrottleMs="@Constants.DefaultThrottleMs"
            DateTimeChanged="@(args => Console.WriteLine(args.Value ?? "values cleared"))"
            ActionOnDateOrTimeChanged="OnDateOrTimeChanged"
            AllowClear="true"
            FeedbackIcon="FeedbackIcon.Error">
        </DateAndTime>),
        ("Format: default, allow clear, null initial value, FeedbackIcon.Error", "",
@"<DateAndTime
    Id=""usage11""
    @bind-Value=""@DateTimeValueNullable""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    DateTimeChanged=""@(args => Console.WriteLine(args.Value ?? ""values cleared""))""
    ActionOnDateOrTimeChanged=""OnDateOrTimeChanged""
    AllowClear=""true""
    FeedbackIcon=""FeedbackIcon.Error"">
</DateAndTime>

@code {

    DateTime? DateTimeValueNullable { get; set; }

    void OnDateOrTimeChanged(DateOnly? dateOnly, TimeOnly? timeOnly)
    {
        Console.WriteLine($""date: {(dateOnly is null ? ""cleared"" : dateOnly.Value.ToString(""yyyy-MM-dd""))}; time: {(timeOnly is null ? ""cleared"" : timeOnly.Value.ToString(""HH:mm:ss""))}"");
    }
}",
        @<DateAndTime
            Id="usage11"
            @bind-Value="@DateTimeValueNullable"
            ThrottleMs="@Constants.DefaultThrottleMs"
            DateTimeChanged="@(args => Console.WriteLine(args.Value ?? "values cleared"))"
            ActionOnDateOrTimeChanged="OnDateOrTimeChanged"
            AllowClear="true"
            FeedbackIcon="FeedbackIcon.Error">
        </DateAndTime>)
    };
}

@* The wrapper div is necessary here for ::deep isolated css rules to be applied to the DateAndTime component *@
<div>
    <Sampler
        ComponentName="DateAndTime"
        ComponentCssName="input"
        ComponentTypeName="dateandtime"
        Description="@description"
        Features="@features"
        UsageText="Typical usages of the <code>DateAndTime</code> component are shown below"
        UsageCodeList="@usageCode"
        Gotchas="@gotchas"
        Tips="@tips"
        ContentHeightPixels="450">
        <ExampleTemplate>
            <DateAndTime
                Id="example1"
                DateFormat="dd MMMM yyyy h:mm tt"
                @bind-Value="@DateTimeValue"
                ThrottleMs="@Constants.DefaultThrottleMs"
                DateTimeChanged="@(args => Console.WriteLine(args.Value))">
            </DateAndTime>
        </ExampleTemplate>
    </Sampler>
</div>

@code {

    DateTime DateTimeValue { get; set; } = new DateTime(2022, 10, 4, 12, 13, 14);

    DateTime? DateTimeValueNullable { get; set; }

    DateTime? DateTimeValueNullable2 { get; set; } = new DateTime(2024, 12, 6, 11, 12, 13);

    void DateTimeChanged(DateTime newValue, string format)
    {
        DateTimeValue = newValue;
        Console.WriteLine($"DateTime new value is: {newValue.ToString(format)}");
    }

    void OnDateOrTimeChanged(DateOnly? dateOnly, TimeOnly? timeOnly)
    {
        Console.WriteLine($"date: {(dateOnly is null ? "cleared" : dateOnly.Value.ToString("yyyy-MM-dd"))}; time: {(timeOnly is null ? "cleared" : timeOnly.Value.ToString("HH:mm:ss"))}");
    }
}
