open System
open System.IO
open System.Text.RegularExpressions

// Function to read the numbered list from a file
let readNumberedListFromFile (inputFilePath: string) =
    if File.Exists(inputFilePath) then
        File.ReadAllText(inputFilePath)
    else
        failwithf "Input file does not exist: %s" inputFilePath

// Function to create files based on the numbered list and component
let createFiles (component: string) (inputFilePath: string) (outputFilePath: string) =
    // Read the numbered list from the input file
    let numberedList = readNumberedListFromFile inputFilePath

    // Create a folder with the name of the component inside the output path
    let componentFolderPath = Path.Combine(outputFilePath, component)
    Directory.CreateDirectory(componentFolderPath) |> ignore

    // Split the numbered list into lines
    let lines = numberedList.Split([|'\n'|], StringSplitOptions.RemoveEmptyEntries)

    // Process each line to extract the number and text
    for line in lines do
        // Use regex to match the pattern "number - description"
        let pattern = @"^\s*(\d+)\s*-\s*(.+)"
        let m = Regex.Match(line, pattern)

        if m.Success then
            // Extract the number and description
            let number = m.Groups.[1].Value
            let description = m.Groups.[2].Value

            // Define the filename
            let fileName = sprintf "%s_%s.feature" component number
            let fullPath = Path.Combine(componentFolderPath, fileName)

            // Define the content
            let content = sprintf "@component @%s @%s_%s\nFeature: %s" component component number description

            // Write the content to the file
            File.WriteAllText(fullPath, content)
            printfn "File created: %s" fullPath
        else
            printfn "Line doesn't match the expected pattern: %s" line

// Example usage
let inputFilePath = "C:\\users\\<USER>\\source\\repos\\cctc_components\\user_specs\\panelmenu.spec"
let component = "panelmenu"
let outputFilePath = "C:\\Users\\<USER>\\source\\repos\\CCTC_Components\\functional_scripts\\features"

// Create files based on the input file
createFiles component inputFilePath outputFilePath