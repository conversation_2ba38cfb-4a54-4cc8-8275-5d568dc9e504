@component @steps @steps_4
Feature: the done button can trigger an event
    Scenario: the done button updates the screen with the progress
        Given the user is at the home page
        And the user selects the "Steps" component in the container "Steps"
        And the user clicks on the next button
        And the user clicks on the current selected dropdown option
        And the user clicks on the dropdown option with the text "MSI PRO B760M-A WIFI DDR4"
        And the user clicks on the next button
        And the user clicks on the next button
        And the user clicks on the next button
        When the user clicks on the next button
        Then the progress step content contains "Not yet complete"
        And the user clicks on the current selected dropdown option
        And the user clicks on the dropdown option with the text "Be Quiet! System Power 10 550W"
        When the user clicks on the done button
        Then the progress step content contains "Completed"



