﻿using CCTC_Components.Components.__CCTC.Models;

namespace CCTC_Components.Components.TextBox.TextInteraction;

/// <summary>
/// A payload used for when interacting with a <see cref="Text"/> component
/// </summary>
/// <param name="CursorSelection">The cursor positioning as a <see cref="CursorSelection"/></param>
/// <remarks>The start and end values represent the start and end positions of the selected text</remarks>
public record InteractionArgs(CursorSelection CursorSelection)
{
    /// <summary>
    /// The cursor start position
    /// </summary>
    public int CursorStart => CursorSelection.Start;

    /// <summary>
    /// The cursor end position
    /// </summary>
    /// <remarks>Use this to get the current cursor position</remarks>
    public int CursorEnd => CursorSelection.End;

    /// <summary>
    /// The length of the selected text
    /// </summary>
    public int SelectionLength => CursorEnd - CursorStart;
}