@component @lister @lister_3
Feature: items within the lister can be selected and deselected
    Scenario: the lister component can have items selected and deselected
        Given the user is at the home page
        And the user selects the "Lister" component
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Preloaded data"
        And the row with content "000000 : TRT | 1 | PaclitaxTrt | 3 | 08/06/2016 e5f5f586" is unchecked
        And the user clicks the checkbox in the row with content "000000 : TRT | 1 | PaclitaxTrt | 3 | 08/06/2016 e5f5f586"
        Then the row with content "000000 : TRT | 1 | PaclitaxTrt | 3 | 08/06/2016 e5f5f586" is checked
        When the user clicks the checkbox in the row with content "000000 : TRT | 1 | PaclitaxTrt | 3 | 08/06/2016 e5f5f586"
        Then the row with content "000000 : TRT | 1 | PaclitaxTrt | 3 | 08/06/2016 e5f5f586" is unchecked
