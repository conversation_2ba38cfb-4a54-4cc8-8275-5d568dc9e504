@component @pill @pill_8

Feature: the pill can display tooltips when required
    Scenario: the pill can have a tooltip
        Given the user is at the home page
        And the user selects the "Pill" component
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "PillContext: None, ColorStyle: Fill, Size: Medium, Icon: circle, MaxWidth: 25rem, PillTooltipBehaviour: EnabledOnTextOverflow, PillTooltipPlacement: Right"
        Then the pill has a tooltip enabled containing the full label text

    Scenario: the pill can have tooltip behaviour set to Disabled
        Given the user is at the home page
        And the user selects the "Pill" component
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "PillContext: None, ColorStyle: Fill, Size: Medium, Icon: circle, MaxWidth: 25rem, PillTooltipBehaviour: Disabled"
        Then the pill has no tooltip
