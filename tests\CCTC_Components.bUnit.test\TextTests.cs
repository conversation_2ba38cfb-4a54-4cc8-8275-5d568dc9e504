﻿using AngleSharp.Html.Dom;
using CCTC_Components.Components.TextBox;
using CCTC_Lib.Enums.UI;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;

namespace CCTC_Components.bUnit.test
{
    public class TextTests : TestContext
    {
        [Theory]
        [InlineData("Text", "input", false, "Test", "Test")]
        [InlineData("Text", "input", false, "", null)]
        [InlineData("Text", "input", true, "Test", "Test")]
        [InlineData("Text", "input", true, "", "Initial value")]
        [InlineData("TextArea", "textarea", false, "Test", "Test")]
        [InlineData("TextArea", "textarea", false, "", null)]
        [InlineData("TextArea", "textarea", true, "Test", "Test")]
        [InlineData("TextArea", "textarea", true, "", "Initial value")]
        public void Text_OnChange_Raises_Callbacks(string componentType, string htmlElement,
            bool preventWhiteSpace, string value, string? expectedValue)
        {
            Services.AddTestSchedulers();


            bool valueChangedRaised = false;
            bool textChangedRaised = false;
            bool focusRaised = false;
            bool blurRaised = false;
            string? valueChangedNewValue = null;
            ChangeEventArgs? textChangedNewValue = null;
            FocusEventArgs? focusNewValue = null;
            FocusEventArgs? blurNewValue = null;

            var parameters = new ComponentParameterCollectionBuilder<TextBase>(parameters => parameters
                .Add(p => p.BindEvent, BindEvent.OnChange)
                .Add(p => p.PreventWhitespace, preventWhiteSpace)
                .Add(p => p.Value, "Initial value")
                .Add(p => p.ValueChanged, args => { valueChangedRaised = true; valueChangedNewValue = args; })
                .Add(p => p.TextChanged, args => { textChangedRaised = true; textChangedNewValue = args; })
                .Add(p => p.Focus, args => { focusRaised = true; focusNewValue = args; })
                .Add(p => p.Blur, args => { blurRaised = true; blurNewValue = args; })
            ).Build();

            var cut = GetRenderedComponent(componentType, TestHelpers.GetComponentParameterArray(parameters));
            var inputElement = cut.Find(htmlElement);
            inputElement.Change(value);
            inputElement.Focus("focus");
            inputElement.Blur("blur");

            Assert.True(valueChangedRaised);
            Assert.True(textChangedRaised);
            Assert.True(focusRaised);
            Assert.True(blurRaised);
            Assert.Equal(expectedValue, valueChangedNewValue);
            Assert.Equal(expectedValue, textChangedNewValue?.Value?.ToString());
            Assert.Equal("focus", focusNewValue?.Type);
            Assert.Equal("blur", blurNewValue?.Type);
        }

        [Theory]
        [InlineData("Text", "input", false, "Test", "Test")]
        [InlineData("Text", "input", false, "", null)]
        [InlineData("Text", "input", true, "Test", "Test")]
        [InlineData("Text", "input", true, "", "Initial value")]
        [InlineData("TextArea", "textarea", false, "Test", "Test")]
        [InlineData("TextArea", "textarea", false, "", null)]
        [InlineData("TextArea", "textarea", true, "Test", "Test")]
        [InlineData("TextArea", "textarea", true, "", "Initial value")]
        public void Text_OnInput_Raises_Callbacks(string componentType, string htmlElement,
            bool preventWhiteSpace, string value, string? expectedValue)
        {
            Services.AddTestSchedulers();

            bool valueChangedRaised = false;
            bool textChangedRaised = false;
            bool focusRaised = false;
            bool blurRaised = false;
            string? valueChangedNewValue = null;
            ChangeEventArgs? textChangedNewValue = null;
            FocusEventArgs? focusNewValue = null;
            FocusEventArgs? blurNewValue = null;

            var parameters = new ComponentParameterCollectionBuilder<TextBase>(parameters => parameters
                .Add(p => p.BindEvent, BindEvent.OnInput)
                .Add(p => p.PreventWhitespace, preventWhiteSpace)
                .Add(p => p.Value, "Initial value")
                .Add(p => p.ValueChanged, args => { valueChangedRaised = true; valueChangedNewValue = args; })
                .Add(p => p.TextChanged, args => { textChangedRaised = true; textChangedNewValue = args; })
                .Add(p => p.Focus, args => { focusRaised = true; focusNewValue = args; })
                .Add(p => p.Blur, args => { blurRaised = true; blurNewValue = args; })
            ).Build();

            var cut = GetRenderedComponent(componentType, TestHelpers.GetComponentParameterArray(parameters));
            var inputElement = cut.Find(htmlElement);
            inputElement.Input(value);
            inputElement.Focus("focus");
            inputElement.Blur("blur");

            Assert.True(valueChangedRaised);
            Assert.True(textChangedRaised);
            Assert.True(focusRaised);
            Assert.True(blurRaised);
            Assert.Equal(expectedValue, valueChangedNewValue);
            Assert.Equal(expectedValue, textChangedNewValue?.Value?.ToString());
            Assert.Equal("focus", focusNewValue?.Type);
            Assert.Equal("blur", blurNewValue?.Type);
        }

        [Theory]
        [InlineData("Text", "input")]
        [InlineData("TextArea", "textarea")]
        public void Text_OnChange_Does_Not_Raise_Callbacks(string componentType, string htmlElement)
        {
            Services.AddTestSchedulers();

            bool valueChangedRaised = false;
            bool textChangedRaised = false;
            string expectedValue = "Test";
            string? valueChangedNewValue = null;
            ChangeEventArgs? textChangedNewValue = null;

            var parameters = new ComponentParameterCollectionBuilder<TextBase>(parameters => parameters
                .Add(p => p.BindEvent, BindEvent.OnInput)
                .Add(p => p.ValueChanged, args => { valueChangedRaised = true; valueChangedNewValue = args; })
                .Add(p => p.TextChanged, args => { textChangedRaised = true; textChangedNewValue = args; })
            ).Build();

            var cut = GetRenderedComponent(componentType, TestHelpers.GetComponentParameterArray(parameters));
            cut.Find(htmlElement).Change(expectedValue);

            Assert.False(valueChangedRaised);
            Assert.False(textChangedRaised);
            Assert.Null(valueChangedNewValue);
            Assert.Null(textChangedNewValue);
        }

        [Theory]
        [InlineData("Text", "input")]
        [InlineData("TextArea", "textarea")]
        public void Text_OnInput_Does_Not_Raise_Callbacks(string componentType, string htmlElement)
        {
            Services.AddTestSchedulers();

            bool valueChangedRaised = false;
            bool textChangedRaised = false;
            string expectedValue = "Test";
            string? valueChangedNewValue = null;
            ChangeEventArgs? textChangedNewValue = null;

            var parameters = new ComponentParameterCollectionBuilder<TextBase>(parameters => parameters
                .Add(p => p.BindEvent, BindEvent.OnChange)
                .Add(p => p.ValueChanged, args => { valueChangedRaised = true; valueChangedNewValue = args; })
                .Add(p => p.TextChanged, args => { textChangedRaised = true; textChangedNewValue = args; })
            ).Build();

            var cut = GetRenderedComponent(componentType, TestHelpers.GetComponentParameterArray(parameters));
            cut.Find(htmlElement).Input(expectedValue);

            Assert.False(valueChangedRaised);
            Assert.False(textChangedRaised);
            Assert.Null(valueChangedNewValue);
            Assert.Null(textChangedNewValue);
        }

        [Theory]
        [InlineData("Text", "input", false, "Test", "Test")]
        [InlineData("Text", "input", false, "", null)]
        [InlineData("Text", "input", true, "Test", "Test")]
        [InlineData("Text", "input", true, "", "Initial value")]
        [InlineData("TextArea", "textarea", false, "Test", "Test")]
        [InlineData("TextArea", "textarea", false, "", null)]
        [InlineData("TextArea", "textarea", true, "Test", "Test")]
        [InlineData("TextArea", "textarea", true, "", "Initial value")]
        public void Text_Reactive_Raises_Correct_Callbacks(string componentType, string htmlElement,
            bool preventWhiteSpace, string value, string? expectedValue)
        {
            var testSchedulers = new TestSchedulers();
            Services.AddTestSchedulers(sp => testSchedulers);

            bool valueChangedRaised = false;
            bool textChangedRaised = false;
            string? valueChangedNewValue = null;
            ChangeEventArgs? textChangedNewValue = null;

            var parameters = new ComponentParameterCollectionBuilder<TextBase>(parameters => parameters
                .Add(p => p.Value, "Initial value")
                .Add(p => p.ThrottleMs, Constants.DefaultThrottleMs)
                .Add(p => p.PreventWhitespace, preventWhiteSpace)
                .Add(p => p.ValueChanged, args => { valueChangedRaised = true; valueChangedNewValue = args; })
                .Add(p => p.TextChanged, args => { textChangedRaised = true; textChangedNewValue = args; })
            ).Build();

            var cut = GetRenderedComponent(componentType, TestHelpers.GetComponentParameterArray(parameters));
            cut.Find(htmlElement).Input(value);
            testSchedulers.Default.AdvanceBy(TimeSpan.FromSeconds(1).Ticks);

            Assert.True(valueChangedRaised);
            Assert.True(textChangedRaised);
            Assert.Equal(expectedValue, valueChangedNewValue);
            Assert.Equal(expectedValue, textChangedNewValue?.Value?.ToString());
        }

        [Theory]
        [InlineData("Text", "input")]
        [InlineData("TextArea", "textarea")]
        public void Text_Masked_Raises_Correct_Callbacks(string componentType, string htmlElement)
        {
            var testSchedulers = new TestSchedulers();
            Services.AddTestSchedulers(sp => testSchedulers);

            bool valueChangedRaised = false;
            bool textChangedRaised = false;
            string expectedValue = "(12-34-56-78)";
            string? valueChangedNewValue = null;
            ChangeEventArgs? textChangedNewValue = null;
            int componentDefaultThrottleMs = 500;

            var parameters = new ComponentParameterCollectionBuilder<TextBase>(parameters => parameters
                .Add(p => p.Mask, "(00-00-00-00)")
                .Add(p => p.ValueChanged, args => { valueChangedRaised = true; valueChangedNewValue = args; })
                .Add(p => p.TextChanged, args => { textChangedRaised = true; textChangedNewValue = args; })
            ).Build();

            var cut = GetRenderedComponent(componentType, TestHelpers.GetComponentParameterArray(parameters));
            cut.Find(htmlElement).Input("12345678");
            testSchedulers.Default.AdvanceBy(TimeSpan.FromMilliseconds(componentDefaultThrottleMs).Ticks);

            Assert.True(valueChangedRaised);
            Assert.True(textChangedRaised);
            Assert.Equal(expectedValue, valueChangedNewValue);
            Assert.Equal(expectedValue, textChangedNewValue?.Value?.ToString());
        }

        [Theory]
        [InlineData("Text", "input")]
        [InlineData("TextArea", "textarea")]
        public void Text_Reactive_Can_Force_Complete(string componentType, string htmlElement)
        {
            Services.AddTestSchedulers();

            bool valueChangedRaised = false;
            string expectedValue = "Test";
            string? valueChangedNewValue = null;

            var parameters = new ComponentParameterCollectionBuilder<TextBase>(parameters => parameters
                .Add(p => p.ThrottleMs, Constants.DefaultThrottleMs)
                .Add(p => p.ValueChanged, args => { valueChangedRaised = true; valueChangedNewValue = args; })
            ).Build();

            var cut = GetRenderedComponent(componentType, TestHelpers.GetComponentParameterArray(parameters));
            cut.Find(htmlElement).Input(expectedValue);
            cut.Instance.ForceComplete();

            Assert.True(valueChangedRaised);
        }

        [Theory]
        [InlineData("Text")]
        [InlineData("TextArea")]
        public void Text_ReservedAttributes_Throws_ArgumentException(string componentType)
        {
            Services.AddTestSchedulers();

            var parameters = new ComponentParameterCollectionBuilder<TextBase>(parameters => parameters
                .Add(p => p.InputAttributes, new() { { "name", "test-name" }, { "class", "testClass" }, { "disabled", true } })
            ).Build();

            var cut = () => GetRenderedComponent(componentType, TestHelpers.GetComponentParameterArray(parameters));

            var actual = Assert.Throws<ArgumentException>(cut);
            string expectedParamName = "InputAttributes";
            string expectedMessage = "One or more reserved attributes have been used and therefore will not be applied. Use a component parameter instead, if available. The following reserved attributes have been used: class, disabled (Parameter 'InputAttributes')";
            Assert.Equal(expectedParamName, actual.ParamName);
            Assert.Equal(expectedMessage, actual.Message);
        }

        [Theory]
        [InlineData(false, false, false)]
        [InlineData(true, true, true)]
        [InlineData(true, false, false)]
        [InlineData(false, true, false)]
        public void TextAttributesConfiguredCorrectly(bool readOnly, bool disabled, bool redactText)
        {
            Services.AddTestSchedulers();

            var cut = RenderComponent<Text>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.CssClass, "w-50")
                .Add(p => p.Style, "color: blue;")
                .Add(p => p.MaxLength, 10)
                .Add(p => p.ReadOnly, readOnly)
                .Add(p => p.Disabled, disabled)
                .Add(p => p.RedactText, redactText)
                .Add(p => p.Placeholder, "00")
                .Add(p => p.InputAttributes, new() { { "name", "test-name" } })
            );

            var textWrapperElement = cut.Find("cctc-input[data-cctc-input-type=\"text\"]");
            var inputElement = (IHtmlInputElement)cut.FindAll("input")[0];

            var expectedTextWrapperAttributes = new Dictionary<string, string?>()
            {
                { "id", "test-id" },
                { "class", "w-50" },
                { "style", "color: blue;" },
                { "data-author", "cctc" }
            };

            var actualTextWrapperAttributes = new Dictionary<string, string?>()
            {
                { "id", textWrapperElement.Id },
                { "class", textWrapperElement.ClassName },
                { "style", textWrapperElement.GetAttribute("style") },
                { "data-author", textWrapperElement.GetAttribute("data-author") }
            };

            var expectedInputAttributes = new Dictionary<string, object?>()
            {
                { "id", "test-id-input" },
                { "maxlength", 10},
                { "type", $"{(redactText ? "password" : "text")}" },
                { "placeholder", "00" },
                { "autocomplete", "off" },
                { "name", "test-name" }
            };

            var actualInputAttributes = new Dictionary<string, object?>()
            {
                { "id", inputElement.Id },
                { "maxlength", inputElement.MaxLength},
                { "type", inputElement.GetAttribute("type") },
                { "placeholder", inputElement.Placeholder },
                { "autocomplete", inputElement.Autocomplete },
                { "name", inputElement.Name }
            };

            Assert.Equal(expectedTextWrapperAttributes, actualTextWrapperAttributes);
            Assert.Equal(expectedInputAttributes, actualInputAttributes);
            Assert.Equal(readOnly, inputElement.IsReadOnly);
            Assert.Equal(disabled, inputElement.IsDisabled);
        }

        [Theory]
        [InlineData(false, false)]
        [InlineData(true, true)]
        [InlineData(true, false)]
        [InlineData(false, true)]
        public void TextAreaAttributesConfiguredCorrectly(bool readOnly, bool disabled)
        {
            Services.AddTestSchedulers();

            var cut = RenderComponent<TextArea>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.CssClass, "w-50")
                .Add(p => p.Style, "color: blue;")
                .Add(p => p.MaxLength, 10)
                .Add(p => p.ReadOnly, readOnly)
                .Add(p => p.Disabled, disabled)
                .Add(p => p.Placeholder, "00")
                .Add(p => p.InputAttributes, new() { { "name", "test-name" } })
            );

            var textAreaWrapperElement = cut.Find("cctc-input[data-cctc-input-type=\"textarea\"]");
            var textAreaElement = (IHtmlTextAreaElement)cut.FindAll("textarea")[0];

            var expectedTextAreaWrapperAttributes = new Dictionary<string, object?>()
            {
                { "id", "test-id" },
                { "class", "w-50" },
                { "style", "color: blue;" },
                { "data-author", "cctc" }
            };

            var actualTextAreaWrapperAttributes = new Dictionary<string, object?>()
            {
                { "id", textAreaWrapperElement.Id },
                { "class", textAreaWrapperElement.ClassName },
                { "style", textAreaWrapperElement.GetAttribute("style") },
                { "data-author", textAreaWrapperElement.GetAttribute("data-author") }
            };

            var expectedTextAreaAttributes = new Dictionary<string, object?>()
            {
                { "id", "test-id-input" },
                { "maxlength", 10},
                { "placeholder", "00" },
                { "autocomplete", "off" },
                { "name", "test-name" }
            };

            var actualTextAreaAttributes = new Dictionary<string, object?>()
            {
                { "id", textAreaElement.Id },
                { "maxlength", textAreaElement.MaxLength},
                { "placeholder", textAreaElement.Placeholder },
                { "autocomplete", textAreaElement.GetAttribute("autocomplete") },
                { "name", textAreaElement.Name }
            };

            Assert.Equal(expectedTextAreaWrapperAttributes, actualTextAreaWrapperAttributes);
            Assert.Equal(expectedTextAreaAttributes, actualTextAreaAttributes);
            Assert.Equal(readOnly, textAreaElement.IsReadOnly);
            Assert.Equal(disabled, textAreaElement.IsDisabled);
        }

        [Theory]
        [InlineData("Text", true, true, 0)]
        [InlineData("Text", true, false, 1)]
        [InlineData("Text", false, false, 0)]
        [InlineData("TextArea", true, true, 0)]
        [InlineData("TextArea", true, false, 1)]
        [InlineData("TextArea", false, false, 0)]
        public void TextReadOnlyIconConfiguredCorrectly(string componentType, bool readOnly, bool hideReadOnlyIcon, int expectedCount)
        {
            Services.AddTestSchedulers();

            var parameters = new ComponentParameterCollectionBuilder<TextBase>(parameters => parameters
                .Add(p => p.ReadOnly, readOnly)
                .Add(p => p.HideReadOnlyIcon, hideReadOnlyIcon)
            ).Build();

            var cut = GetRenderedComponent(componentType, TestHelpers.GetComponentParameterArray(parameters));
            var readOnlyIcon = cut.FindAll(".icon-wrapper .material-icons");
            Assert.Equal(expectedCount, readOnlyIcon.Count);
        }

        IRenderedComponent<TextBase> GetRenderedComponent(string componentType, params ComponentParameter[] parameters)
        {
            if (componentType == "Text")
            {
                return RenderComponent<Text>(parameters);
            }
            else
            {
                return RenderComponent<TextArea>(parameters);
            }
        }
    }
}
