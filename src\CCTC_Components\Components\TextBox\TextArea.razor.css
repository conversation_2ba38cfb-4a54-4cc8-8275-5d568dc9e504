﻿cctc-input {
    width: 100%;
}

.component-wrapper {
    display: flex;
    align-items: start;
}

.material-icons {
    color: var(--cctc-input-readonly-icon-color);
}

/*note: do not set textarea height here as this interferes with the Rows property*/
textarea {
    width: 100%;
    min-height: 3rem;
    padding-left: var(--cctc-input-padding-left);
    padding-right: max(var(--cctc-input-padding-right), 1.5rem);
    color: var(--cctc-input-color);
    background-color: var(--cctc-input-background-color);
    border-radius: var(--cctc-input-border-radius);
    border-color: var(--cctc-input-border-color);
    border-width: var(--cctc-input-border-width);
    border-style: var(--cctc-input-border-style);
    outline: none;
}

textarea:disabled {
    color: var(--cctc-input-disabled-color);
    border-color: var(--cctc-input-disabled-background-color);
    background-color: var(--cctc-input-disabled-background-color);
}

.icon-wrapper {
    margin-top: 0.3rem;
    margin-left: -1.35rem;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar {
    width: 0.4rem;
    height: 0.4rem;
}
