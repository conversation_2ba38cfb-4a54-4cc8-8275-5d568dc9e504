﻿::deep cctc-chart {
    width: 100%;
    padding-left: 0.2rem;
    padding-right: 0.2rem;
}

.example-template-container {
    margin-top: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.example-controls {
    padding: 0.5rem;
    border: 1px solid var(--cctc-inactive-color);
    border-radius: 3px;
    display: flex;
    flex-wrap: wrap;
    gap: 0.8rem;
}

.control-button {
    background-color: transparent;
    cursor: pointer;
}

.control-button:hover {
    color: var(--cctc-link-color);
}


@media (min-width: 36rem) {

}

@media (min-width: 75rem) {
    .example-template-container {
        flex-direction: row;
    }

    .example-controls {
        flex-direction: column;
    }

}

@media (min-width: 120rem) {

}