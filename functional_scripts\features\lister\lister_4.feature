@component @lister @lister_4
Feature: the current counts for the Lister are configurable, and can show totals, total filtered, and total selected
    Scenario: the lister component can show the filtered, total and selected count
        Given the user is at the home page
        And the user selects the "Lister" component
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Preloaded data"
        And the lister display shows "100 of 100 (0 selected)"
        When the user types "PaclitaxTrt" in the filter input
        Then the lister display shows "53 of 100 (0 selected)"
        And the user scrolls to the bottom of the lister
        And the row with content "000098 : TRT | 4 | PaclitaxTrt | 1 | 30/05/2017 ae262e40" has row index 52
        And the row with content "000098 : TRT | 4 | PaclitaxTrt | 1 | 30/05/2017 ae262e40" is unchecked
        When the user clicks the checkbox in the row with content "000098 : TRT | 4 | PaclitaxTrt | 1 | 30/05/2017 ae262e40"
        And the row with content "000098 : TRT | 4 | PaclitaxTrt | 1 | 30/05/2017 ae262e40" is checked
        Then the lister display shows "53 of 100 (1 selected)"



