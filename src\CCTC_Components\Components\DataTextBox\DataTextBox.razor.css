cctc-datatextbox {
    position: relative;
}

::deep .dropdown-visible {
    opacity: 1;
}

::deep .dropdown-hidden {
    opacity: 0;
    pointer-events: none;
}

@keyframes fadeout {
    0% { opacity: 1}
    100% {opacity: 0;}
}

.icon {
    position: absolute;
    top: calc((var(--cctc-input-height) - 1rem) / 2);
    right: 0.5rem;
    font-size: 1rem;
}

cctc-datatextbox-input {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-evenly;

    padding-left: 0.3rem;
    padding-right: 2rem;

    background-color: var(--cctc-datatextbox-input-background-color);
    border-color: var(--cctc-datatextbox-input-border-color);
    border-style: var(--cctc-datatextbox-input-border-style);
    border-width: var(--cctc-datatextbox-input-border-width);
    border-radius:
            var(--cctc-datatextbox-input-border-top-left-radius)
            var(--cctc-datatextbox-input-border-top-right-radius)
            var(--cctc-datatextbox-input-border-bottom-right-radius)
            var(--cctc-datatextbox-input-border-bottom-left-radius);

}

::deep cctc-datatextbox-input cctc-input {
    --cctc-input-background-color: transparent;
    --cctc-input-border-width: 0;
}

::deep cctc-datatextbox-dropdown {
    position: absolute;
    z-index: 1;
    margin-top: calc(var(--cctc-input-height) + 1px);
    color: var(--cctc-datatextbox-dropdown-color);
    border: 1px solid var(--cctc-datatextbox-dropdown-border-color);
    background-color: var(--cctc-datatextbox-dropdown-background-color);
    width: 100%;

    height: var(--cctc-datatextbox-dropdown-height);

    --cctc-lister-hover-background-color: var(--cctc-background-color);
    --cctc-lister-background-color: var(--cctc-datatextbox-dropdown-background-color);
}

cctc-datatextbox-selected {
    position: relative;
    display: flex;
    max-width: 60%;
    white-space: nowrap;
    overflow: hidden;
}

.selected-item {
    cursor: pointer;
    border-bottom: 1px solid var(--cctc-datatextbox-input-background-color);
}

.selected-item:hover {
    border-bottom: 1px dotted var(--cctc-datatextbox-input-color);
}

.selected-item:focus {
    color: var(--cctc-datatextbox-input-background-color);
    background-color: var(--cctc-datatextbox-input-color);
}


.icon-available {
    opacity: 100%;
    cursor: default;
    color: var(--cctc-datatextbox-icon-active-color);
}

.icon-available:hover {
    cursor: pointer;
    color: var(--cctc-datatextbox-icon-hover-color);
}

.icon-not-available {
    opacity: 0;
    cursor: text;
}

