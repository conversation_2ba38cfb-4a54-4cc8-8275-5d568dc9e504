import { ICustomWorld } from '../support/custom-world';
import { compareToBaseImage } from '../utils/compareImages';
import * as Helpers from '../support/helper-functions';
import { When, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';

When(
  'the user clicks on the current selected dropdown option',
  async function (this: ICustomWorld) {
    await Helpers.getSamplerTabsSelectedContent(this)
      .getByRole('combobox')
      .locator('[id$="selected-input"]')
      .click();
  }
);

When(
  'the user clicks on the dropdown option with the text {string}',
  async function (this: ICustomWorld, optionText: string) {
    await Helpers.getSamplerTabsSelectedContent(this)
      .getByRole('option')
      .getByText(optionText)
      .click();
  }
);

When('the user clicks on the dropdown clear icon', async function (this: ICustomWorld) {
  await Helpers.getSamplerTabsSelectedContent(this).locator('.show-clear-icon').click();
});

Then(
  /^the dropdown (is|is not) expanded$/,
  async function (this: ICustomWorld, expandedChoice: string) {
    const combobox = Helpers.getSamplerTabsSelectedContent(this).getByRole('combobox');
    if (expandedChoice === 'is') {
      await expect(combobox).toHaveAttribute('aria-expanded');
    } else {
      await expect(combobox).not.toHaveAttribute('aria-expanded');
    }
  }
);

Then(
  'the current selected dropdown option has the text {string}',
  async function (this: ICustomWorld, optionText: string) {
    await expect(
      Helpers.getSamplerTabsSelectedContent(this)
        .getByRole('combobox')
        .locator('[id$="selected-input"]')
    ).toContainText(optionText);
  }
);

Then('the dropdown clear icon is no longer in view', async function (this: ICustomWorld) {
  await expect(
    Helpers.getSamplerTabsSelectedContent(this).locator('.hide-clear-icon')
  ).toBeHidden();
});

Then(
  'the Dropdown component image matches the base image {string}',
  async function (this: ICustomWorld, name: string) {
    const screenshot = await Helpers.tryGetStableScreenshot(
      Helpers.getSamplerTabsSelectedContent(this).locator(
        'cctc-input[data-cctc-input-type="dropdown"]'
      )
    );
    await compareToBaseImage(this, name, screenshot);
  }
);

Then(
  'the Dropdown component listbox image matches the base image {string}',
  async function (this: ICustomWorld, name: string) {
    const listBox = Helpers.getSamplerTabsSelectedContent(this).getByRole('listbox');
    await listBox.evaluate((element) => {
      element.scrollTo(0, 0);
    });
    const screenshot = await Helpers.tryGetStableScreenshot(listBox);
    await compareToBaseImage(this, name, screenshot);
  }
);

Then(
  'the current selected dropdown option has a tooltip enabled containing the full option text',
  async function (this: ICustomWorld) {
    const currentSelectedOptionText = await Helpers.getSamplerTabsSelectedContent(this)
      .getByRole('combobox')
      .locator('[id$="selected-input"]')
      .innerText();
    await expect(
      Helpers.getSamplerTabsSelectedContent(this).getByRole('combobox').locator('cctc-tooltip')
    ).toHaveAttribute('data-bs-original-title', currentSelectedOptionText);
  }
);

Then(
  'the current selected dropdown option does not have a tooltip enabled',
  async function (this: ICustomWorld) {
    await expect(
      Helpers.getSamplerTabsSelectedContent(this).getByRole('combobox').locator('cctc-tooltip')
    ).toHaveCount(0);
  }
);

Then('the dropdown options can scroll', async function (this: ICustomWorld) {
  const options = Helpers.getSamplerTabsSelectedContent(this).getByRole('option');
  const optionCount = await options.count();
  for (let i = 0; i < optionCount; i++) {
    const option = options.nth(i);
    await expect(option).toHaveCSS('white-space', 'nowrap');
    await expect(option).toHaveCSS('overflow-x', 'auto');
  }
});

Then(
  'the dropdown options have tooltips enabled containing the full option text',
  async function (this: ICustomWorld) {
    const tooltips = Helpers.getSamplerTabsSelectedContent(this)
      .getByRole('listbox')
      .locator('cctc-tooltip');
    const tooltipCount = await tooltips.count();
    for (let i = 0; i < tooltipCount; i++) {
      const tooltip = tooltips.nth(i);
      const currentOptionText = await tooltip.getByRole('option').innerText();
      await expect(tooltip).toHaveAttribute('data-bs-original-title', currentOptionText);
    }
  }
);

Then('the dropdown options do not have tooltips enabled', async function (this: ICustomWorld) {
  await expect(
    Helpers.getSamplerTabsSelectedContent(this).getByRole('listbox').locator('cctc-tooltip')
  ).toHaveCount(0);
});
