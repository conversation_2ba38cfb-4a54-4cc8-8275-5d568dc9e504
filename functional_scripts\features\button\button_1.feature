@component @button @button_1
Feature: clicking the refresh button is registered
    Scenario: the button sample page is available
        Given the user is at the home page
        When the user selects the "Button" component in the container "Buttons"
        Then the url ending is "buttonsample"

    Scenario: the button can be clicked and registers this click
        Given the user is at the home page
        And the user selects the "Button" component in the container "Buttons"
        And the button counter number is 0
        And the user presses the button component
        And the button counter number is 1
        When the user presses the button component
        Then the button counter number is 2
