﻿using AngleSharp.Css.Parser;
using CCTC_Components.Components.Steps;
using CCTC_Components.Components.Tabs;
using Microsoft.AspNetCore.Components;
using Microsoft.FSharp.Core;
using Xunit.Abstractions;

namespace CCTC_Components.bUnit.test;

public class StepsTests : CCTCComponentsTestContext
{
    readonly ITestOutputHelper _output;

    public StepsTests(ITestOutputHelper output)
    {
        _output = output;
    }

    const string StepsId = "steps-example";
    IEnumerable<Step>? _progressCompletedSteps;
    Step? _completedStep;
    Step? _fromStep;

    void SetProgressStep(IRenderedComponent<Steps> steps, Step step)
    {
        var progStep = RenderComponent<ProgressStep>(parameters => parameters
            .Add(p => p.Step, step)
            .Add(p => p.Parent, steps.Instance)
            .AddChildContent("<div>some content</div>")
        ).Markup;

        steps.SetParametersAndRender(parameters => parameters
            .AddChildContent(progStep));
    }

    IRenderedComponent<Steps> Init(List<Step> children, bool? wipeOnPrevious = null)
    {
        var paramBuilder = new ComponentParameterCollectionBuilder<Steps>();

        //add Tabs required parameters
        paramBuilder.Add(p => p.Id, StepsId);
        paramBuilder.Add(p => p.OnProgressCompleted, progComp => _progressCompletedSteps = progComp);
        paramBuilder.Add(p => p.OnStepCompleted, compStep => _completedStep = compStep);
        paramBuilder.Add(p => p.OnMovePrevious, fromStep => _fromStep = fromStep);

        if (wipeOnPrevious is not null)
        {
            paramBuilder.Add(p => p.WipeOnPrevious, wipeOnPrevious.Value);
        }

        var steps = Render<Steps>(paramBuilder.Build().ToRenderFragment<Steps>());

        //add children
        if (children.Any())
        {
            foreach (var step in children)
            {
                SetProgressStep(steps, step);
            }
        }

        return steps;
    }

    [Fact]
    public void CanRegisterProgressStep()
    {
        var steps = new List<Step>
        {
            new Step(0, false, null, "Case"),
        };

        var cut = Init(steps);

        Assert.Equal(0, cut.Instance.GetCurrentStepNum());
    }

    [Fact]
    public void CanRegisterProgressSteps()
    {
        var steps = new List<Step>
        {
            new Step(0, false, null, "Case"),
            new Step(1, true, null, "Motherboard"),
            new Step(2, false, null, "CPU"),
            new Step(3, false, null, "Memory that is much longer than the rest"),
        };

        var cut = Init(steps);
        var children = cut.Instance.GetChildren();

        Assert.Equal(0, children.MinBy(x => x.Step.StepNum)!.Step.StepNum);
        Assert.Equal(3, children.MaxBy(x => x.Step.StepNum)!.Step.StepNum);
    }

    [Fact]
    public void StepNumLessThanZeroThrowsArgumentException()
    {
        var steps = new List<Step>
        {
            new Step(-1, false, null, "Case"),
            new Step(1, true, null, "Motherboard"),
        };

        Assert.Throws<ArgumentException>(() => Init(steps));
    }

    [Fact]
    public void NonContiguousStepNumsThrowsInvalidOperationException()
    {
        var steps = new List<Step>
        {
            new Step(0, false, null, "Case"),
            new Step(1, true, null, "Motherboard"),
            new Step(3, false, null, "CPU"),
        };

        Assert.Throws<InvalidOperationException>(() => Init(steps));
    }

    void MoveNext(IRenderedComponent<Steps> cut)
    {
        cut.Find(".next-control").Click();
    }

    void MovePrev(IRenderedComponent<Steps> cut)
    {
        cut.Find(".prev-control").Click();
    }

    [Fact]
    public void NotRequiredStepAllowsMoveNext()
    {
        AddScrollIntoViewFromQuery();

        var steps = new List<Step>
        {
            new Step(0, false, null, "Case"),
            new Step(1, true, null, "Motherboard"),
            new Step(2, false, null, "CPU"),
        };

        var cut = Init(steps);

        //starts with 0 as current
        Assert.Equal(0, cut.Instance.GetCurrentStepNum());

        //first item is not required so can move next
        MoveNext(cut);

        //now focus on 1
        Assert.Equal(1, cut.Instance.GetCurrentStepNum());
    }

    [Fact]
    public void RequiredStepPreventsMoveNext()
    {
        var steps = new List<Step>
        {
            new Step(0, true, null, "Case"),
            new Step(1, true, null, "Motherboard"),
            new Step(2, false, null, "CPU"),
        };

        var cut = Init(steps);

        //starts with 0 as current
        Assert.Equal(0, cut.Instance.GetCurrentStepNum());

        //first item is required so can't move next
        MoveNext(cut);

        //still focus on 0
        Assert.Equal(0, cut.Instance.GetCurrentStepNum());
    }

    [Fact]
    public void RequiredStepAllowsMoveNextAndPrevWhenDataNotNull()
    {
        AddScrollIntoViewFromQuery();

        var steps = new List<Step>
        {
            new Step(0, true, null, "Case"),
            new Step(1, true, null, "Motherboard"),
            new Step(2, false, null, "CPU"),
        };

        var cut = Init(steps);

        //starts with 0 as current
        Assert.Equal(0, cut.Instance.GetCurrentStepNum());

        //first item is required so can't move next
        MoveNext(cut);

        //still focus on 0
        Assert.Equal(0, cut.Instance.GetCurrentStepNum());

        //add some data
        steps[0].Data = "new data";

        MoveNext(cut);

        //now focus on 1
        Assert.Equal(1, cut.Instance.GetCurrentStepNum());

        MovePrev(cut);

        //now focus on 0
        Assert.Equal(0, cut.Instance.GetCurrentStepNum());

        //add some data back in which was wiped
        steps[0].Data = "new data";

        MoveNext(cut);
        Assert.Equal(1, cut.Instance.GetCurrentStepNum());

        //can't move next as required
        MoveNext(cut);
        Assert.Equal(1, cut.Instance.GetCurrentStepNum());

        //set data
        steps[1].Data = "some more data";

        //can move next
        MoveNext(cut);
        Assert.Equal(2, cut.Instance.GetCurrentStepNum());

        //move previous wipes last
        MovePrev(cut);
        Assert.Null(steps[2].Data);
    }

    [Fact]
    public void LastRequiredStepPreventsDoneUnlessComplete()
    {
        AddScrollIntoViewFromQuery();

        var steps = new List<Step>
        {
            new Step(0, false, null, "Case"),
            new Step(1, false, null, "Motherboard"),
            new Step(2, true, null, "CPU"),
        };

        var cut = Init(steps);

        //starts with 0 as current
        Assert.Equal(0, cut.Instance.GetCurrentStepNum());

        MoveNext(cut);
        MoveNext(cut);
        cut.Render();

        Assert.Equal(2, cut.Instance.GetCurrentStepNum());

        //check shows done
        var nextDoneInner = cut.Find(".next-control h5");
        Assert.Equal("done", nextDoneInner.InnerHtml);

        var nextDone = cut.Find(".next-control");
        Assert.True(nextDone.ClassList.Contains("button-disabled"));

        //update last step and change the name and required
        Assert.Throws<InvalidOperationException>(() => cut.Instance.ReplaceStep(new Step(2, false, null, "CPU NEW NAME")));
    }

    [Fact]
    public void CantReplaceCurrentStep()
    {
        AddScrollIntoViewFromQuery();

        var steps = new List<Step>
        {
            new Step(0, false, null, "Case"),
            new Step(1, false, null, "Motherboard"),
            new Step(2, true, null, "CPU"),
        };

        var cut = Init(steps);

        //starts with 0 as current
        Assert.Equal(0, cut.Instance.GetCurrentStepNum());

        MoveNext(cut);

        //fails as current
        Assert.Throws<InvalidOperationException>(() => cut.Instance.ReplaceStep(new Step(1, false, null, "Motherboard new name")));
    }

    [Fact]
    public void CanReplaceStepAndSetState()
    {
        AddScrollIntoViewFromQuery();

        var steps = new List<Step>
        {
            new Step(0, false, null, "Case"),
            new Step(1, false, null, "Motherboard"),
            new Step(2, true, null, "CPU"),
        };

        var cut = Init(steps);

        //starts with 0 as current
        Assert.Equal(0, cut.Instance.GetCurrentStepNum());

        MoveNext(cut);
        MoveNext(cut);

        //num 2 selected
        Assert.Equal(2, cut.Instance.GetCurrentStepNum());

        steps[2].Data = "some data in stepnum 2";

        //succeeds as not current
        cut.Instance.ReplaceStep(new Step(1, false, null, "Motherboard new name"));
        cut.Render();

        //reverts current step to replaced step
        Assert.Equal(1, cut.Instance.GetCurrentStepNum());

        //steps updated
        Assert.False(steps[1].Required);
    }

    [Fact]
    public void LaterStepsAreNotWipedOnPreviousWhenSetToFalse()
    {
        AddScrollIntoViewFromQuery();

        var steps = new List<Step>
        {
            new Step(0, false, null, "Case"),
            new Step(1, false, null, "Motherboard"),
            new Step(2, true, null, "CPU"),
        };

        var cut = Init(steps, false);

        //starts with 0 as current
        steps[0].Data = "some data 0";
        steps[0].Value = "some value 0";
        Assert.Equal(0, cut.Instance.GetCurrentStepNum());

        MoveNext(cut);
        steps[1].Data = "some data 1";
        steps[1].Value = "some value 1";

        MoveNext(cut);
        steps[2].Data = "some data 2";
        steps[2].Value = "some value 2";

        //num 2 selected
        Assert.Equal(2, cut.Instance.GetCurrentStepNum());

        MovePrev(cut);
        Assert.Equal("some data 2", steps[2].Data!.ToString());
        Assert.Equal("some value 2", steps[2].Value);

        MovePrev(cut);
        Assert.Equal("some data 1", steps[1].Data!.ToString());
        Assert.Equal("some value 1", steps[1].Value);
    }

    [Fact]
    public void LaterStepsAreWipedOnPreviousWhenSetToTrue()
    {
        AddScrollIntoViewFromQuery();

        var steps = new List<Step>
        {
            new Step(0, false, null, "Case"),
            new Step(1, false, null, "Motherboard"),
            new Step(2, true, null, "CPU"),
        };

        var cut = Init(steps, true);

        //starts with 0 as current
        steps[0].Data = "some data 0";
        steps[0].Value = "some value 0";
        Assert.Equal(0, cut.Instance.GetCurrentStepNum());

        MoveNext(cut);
        steps[1].Data = "some data 1";
        steps[1].Value = "some value 1";

        MoveNext(cut);
        steps[2].Data = "some data 2";
        steps[2].Value = "some value 2";

        //num 2 selected
        Assert.Equal(2, cut.Instance.GetCurrentStepNum());

        MovePrev(cut);
        Assert.Null(steps[2].Data);
        Assert.Null(steps[2].Value);

        MovePrev(cut);
        Assert.Null(steps[1].Data);
        Assert.Null(steps[1].Value);

        MovePrev(cut);
        Assert.Null(steps[0].Data);
        Assert.Null(steps[0].Value);
    }
}