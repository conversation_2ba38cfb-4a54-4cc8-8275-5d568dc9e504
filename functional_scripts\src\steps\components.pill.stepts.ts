import { ICustomWorld } from '../support/custom-world';
import { compareToBaseImage } from '../utils/compareImages';
import * as Helpers from '../support/helper-functions';
import { When, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';

When('the user clicks the icon within the pill component', async function (this: ICustomWorld) {
  await Helpers.getSamplerTabsSelectedContent(this)
    .locator('cctc-pill cctc-pill-content-container .pill-icon')
    .click();
});

When('the user clicks the Label within the pill component', async function (this: ICustomWorld) {
  await Helpers.getSamplerTabsSelectedContent(this)
    .locator('cctc-pill cctc-pill-content-container .pill-content')
    .click();
});

When('the user clicks the the pill component container', async function (this: ICustomWorld) {
  await Helpers.getSamplerTabsSelectedContent(this)
    .locator('cctc-pill cctc-pill-content-container')
    .click();
});

Then(
  'the pill component image matches the base image {string}',
  async function (this: ICustomWorld, name: string) {
    const screenshot = await Helpers.tryGetStableScreenshot(
      Helpers.getSamplerTabsSelectedContent(this).locator('cctc-pill')
    );
    await compareToBaseImage(this, name, screenshot);
  }
);

Then(
  'the counter matches the base image {string}',
  async function (this: ICustomWorld, name: string) {
    const screenshot = await Helpers.tryGetStableScreenshot(
      Helpers.getSamplerTabsSelectedContent(this).getByText('counter:')
    );
    await compareToBaseImage(this, name, screenshot);
  }
);

Then(
  'the pill icon image matches the base image {string}',
  async function (this: ICustomWorld, name: string) {
    const screenshot = await Helpers.tryGetStableScreenshot(
      Helpers.getSamplerTabsSelectedContent(this).locator(
        'cctc-pill cctc-pill-content-container .pill-icon'
      )
    );
    await compareToBaseImage(this, name, screenshot);
  }
);

Then('the pill counter number is {int}', async function (this: ICustomWorld, pillCount: number) {
  const confirmResultOutcome = Helpers.getSamplerTabsSelectedContent(this).getByText(`Counter: `);
  await expect(confirmResultOutcome).toHaveText(`Counter: ${pillCount}`);
});

Then('the pill max width is {string}', async function (this: ICustomWorld, pillWidth: string) {
  const pillWidthLimiter = Helpers.getSamplerTabsSelectedContent(this).locator(
    'cctc-pill cctc-pill-content-container'
  );
  await expect(pillWidthLimiter).toHaveCSS('max-width', pillWidth);
});

Then('the pill font size is {string}', async function (this: ICustomWorld, pillSize: string) {
  const pillFontSize = Helpers.getSamplerTabsSelectedContent(this).locator(
    'cctc-pill cctc-pill-content-container .pill-content'
  );
  await expect(pillFontSize).toHaveCSS('font-size', pillSize);
});

Then('the pill is outline', async function (this: ICustomWorld) {
  await expect(
    Helpers.getSamplerTabsSelectedContent(this).locator('cctc-pill cctc-pill-content-container')
  ).toHaveClass(/.*\bpill-outline\b.*/);
});

Then('the pill is filled', async function (this: ICustomWorld) {
  await expect(
    Helpers.getSamplerTabsSelectedContent(this).locator('cctc-pill cctc-pill-content-container')
  ).not.toHaveClass(/.*\bpill-outline\b.*/);
});

Then('the pill icon is {string}', async function (this: ICustomWorld, pillIcon: string) {
  await expect(
    Helpers.getSamplerTabsSelectedContent(this).locator(
      'cctc-pill cctc-pill-content-container .pill-icon'
    )
  ).toHaveText(pillIcon);
});

Then('the pill text is {string}', async function (this: ICustomWorld, pillContent: string) {
  const header = Helpers.getSamplerTabsSelectedContent(this)
    .locator('cctc-pill cctc-pill-content-container')
    .getByText(pillContent, { exact: true });
  await expect(header).toHaveText(pillContent);
});

Then('the pill component is disabled', async function (this: ICustomWorld) {
  await expect(Helpers.getSamplerTabsSelectedContent(this).locator('cctc-pill')).toHaveClass(
    /^(?=.*disabled).*$/
  );
});
// above doesnt work because nothing is disabled, dont know if its needed or not.

Then('the pill context(s) is {string}', async function (this: ICustomWorld, pillContext: string) {
  await expect(
    Helpers.getSamplerTabsSelectedContent(this).locator('cctc-pill cctc-pill-content-container')
  ).toHaveClass(pillContext);
});

Then('the pill has no tooltip', async function (this: ICustomWorld) {
  const tooltipLocator = Helpers.getSamplerTabsSelectedContent(this).locator(
    'cctc-pill cctc-pill-content-container cctc-tooltip'
  );
  await expect(tooltipLocator).toHaveCount(0);
});

Then('the pill has a tooltip', async function (this: ICustomWorld) {
  const tooltipLocator = Helpers.getSamplerTabsSelectedContent(this).locator(
    'cctc-pill cctc-pill-content-container cctc-tooltip'
  );
  await expect(tooltipLocator).toHaveCount(1);
});

Then(
  'the pill has a tooltip enabled containing the full label text',
  async function (this: ICustomWorld) {
    const pillLabelText = await Helpers.getSamplerTabsSelectedContent(this)
      .locator('cctc-pill cctc-tooltip .pill-content')
      .innerText();
    await expect(
      Helpers.getSamplerTabsSelectedContent(this).locator('cctc-tooltip')
    ).toHaveAttribute('data-bs-original-title', pillLabelText);
  }
);
